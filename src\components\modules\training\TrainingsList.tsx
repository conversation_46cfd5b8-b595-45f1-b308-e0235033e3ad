"use client";

import { TrainingRecord } from "@/types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Edit, Trash2, Award, Calendar, MapPin, School, CheckCircle,
  Clock, XCircle, AlertCircle, Shield, AlertTriangle, Skull
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useState } from "react";

interface TrainingsListProps {
  trainings: TrainingRecord[];
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
}

export function TrainingsList({ trainings, onEdit, onDelete }: TrainingsListProps) {
  const [trainingToDelete, setTrainingToDelete] = useState<string | null>(null);

  const handleDeleteClick = (id: string) => {
    setTrainingToDelete(id);
  };

  const handleConfirmDelete = () => {
    if (trainingToDelete) {
      onDelete(trainingToDelete);
      setTrainingToDelete(null);
    }
  };

  const handleCancelDelete = () => {
    setTrainingToDelete(null);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "ongoing":
        return <Clock className="h-4 w-4 text-blue-500" />;
      case "planned":
        return <Calendar className="h-4 w-4 text-yellow-500" />;
      case "abandoned":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "failed":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "Dokončeno";
      case "ongoing":
        return "Probíhá";
      case "planned":
        return "Plánováno";
      case "abandoned":
        return "Přerušeno";
      case "failed":
        return "Neúspěšné";
      default:
        return status;
    }
  };

  const getDangerIcon = (dangerLevel?: string) => {
    switch (dangerLevel) {
      case "none":
        return <Shield className="h-4 w-4 text-green-500" />;
      case "low":
        return <Shield className="h-4 w-4 text-blue-500" />;
      case "medium":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case "high":
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case "extreme":
        return <Skull className="h-4 w-4 text-red-500" />;
      case "unknown":
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
      default:
        return null;
    }
  };

  const getDangerText = (dangerLevel?: string) => {
    switch (dangerLevel) {
      case "none":
        return "Žádná";
      case "low":
        return "Nízká";
      case "medium":
        return "Střední";
      case "high":
        return "Vysoká";
      case "extreme":
        return "Extrémní";
      case "unknown":
        return "Neznámá";
      default:
        return "-";
    }
  };

  const getTrainingTypeText = (type: string) => {
    switch (type) {
      case "professional":
        return "Profesní";
      case "military":
        return "Vojenský";
      case "security":
        return "Bezpečnostní";
      case "weapons":
        return "Zbraně";
      case "martial_arts":
        return "Bojová umění";
      case "driving":
        return "Řidičský";
      case "aviation":
        return "Letecký";
      case "diving":
        return "Potápěčský";
      case "survival":
        return "Přežití";
      case "medical":
        return "Zdravotnický";
      case "technical":
        return "Technický";
      case "language":
        return "Jazykový";
      case "police":
        return "Policejní";
      case "intelligence":
        return "Zpravodajský";
      case "special_forces":
        return "Speciální jednotky";
      case "combat":
        return "Bojový";
      case "tactical":
        return "Taktický";
      case "explosives":
        return "Výbušniny";
      case "sniper":
        return "Odstřelovač";
      case "reconnaissance":
        return "Průzkumný";
      case "other":
        return "Jiný";
      default:
        return type;
    }
  };

  if (trainings.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <School className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">Žádná školení</h3>
        <p className="text-sm text-muted-foreground mt-2">
          Zatím nebyly přidány žádné záznamy o školeních nebo výcvicích.
        </p>
      </div>
    );
  }

  return (
    <>
      <ScrollArea className="h-[500px]">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Název</TableHead>
              <TableHead>Typ</TableHead>
              <TableHead>Instituce</TableHead>
              <TableHead>Období</TableHead>
              <TableHead>Stav</TableHead>
              <TableHead>Certifikát</TableHead>
              <TableHead>Nebezpečnost</TableHead>
              <TableHead className="text-right">Akce</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {trainings.map((training) => (
              <TableRow key={training.id}>
                <TableCell className="font-medium">{training.name}</TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {getTrainingTypeText(training.trainingType)}
                  </Badge>
                </TableCell>
                <TableCell>
                  {training.institution ? (
                    <div className="flex items-center">
                      <School className="h-4 w-4 mr-1" />
                      {training.institution}
                    </div>
                  ) : (
                    <span className="text-muted-foreground">-</span>
                  )}
                </TableCell>
                <TableCell>
                  {training.startDate || training.endDate ? (
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {training.startDate && training.endDate
                        ? `${training.startDate} - ${training.endDate}`
                        : training.startDate || training.endDate}
                    </div>
                  ) : (
                    <span className="text-muted-foreground">-</span>
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    {getStatusIcon(training.status)}
                    <span className="ml-1">{getStatusText(training.status)}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {training.certificateObtained ? (
                    <div className="flex items-center text-green-600">
                      <Award className="h-4 w-4 mr-1" />
                      {training.certificateName || "Ano"}
                    </div>
                  ) : (
                    <span className="text-muted-foreground">Ne</span>
                  )}
                </TableCell>
                <TableCell>
                  {training.dangerAssessment ? (
                    <div className="flex items-center">
                      {getDangerIcon(training.dangerAssessment)}
                      <span className="ml-1">{getDangerText(training.dangerAssessment)}</span>
                    </div>
                  ) : (
                    <span className="text-muted-foreground">-</span>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onEdit(training.id)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteClick(training.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </ScrollArea>

      <AlertDialog open={!!trainingToDelete} onOpenChange={() => setTrainingToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Opravdu chcete smazat toto školení?</AlertDialogTitle>
            <AlertDialogDescription>
              Tato akce je nevratná. Záznam o školení bude trvale odstraněn.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelDelete}>Zrušit</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmDelete} className="bg-destructive text-destructive-foreground">
              Smazat
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
