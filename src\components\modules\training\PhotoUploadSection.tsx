"use client";

import { useState } from "react";
import { Control, useFieldArray } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Image, Upload, X, Camera, Calendar, MapPin, FileText } from "lucide-react";
import { FormItemRHF } from "./FormComponents";

interface PhotoUploadSectionProps {
  control: Control<any>;
  name: string;
  title?: string;
  description?: string;
}

export function PhotoUploadSection({
  control,
  name,
  title = "Fotodokumentace",
  description = "Přidejte fotografie související s tímto <PERSON>",
}: PhotoUploadSectionProps) {
  const { fields, append, remove } = useFieldArray({
    control,
    name,
  });

  const [selectedPhotoIndex, setSelectedPhotoIndex] = useState<number | null>(null);

  const handleAddPhoto = () => {
    append({
      id: uuidv4(),
      fileName: "",
      storagePath: "",
      downloadURL: "",
      description: "",
      dateTaken: "",
      sourceURL: "",
      location: "",
      notes: "",
    });
    // Automaticky vybrat nově přidanou fotografii
    setSelectedPhotoIndex(fields.length);
  };

  const handleRemovePhoto = (index: number) => {
    remove(index);
    if (selectedPhotoIndex === index) {
      setSelectedPhotoIndex(null);
    } else if (selectedPhotoIndex !== null && selectedPhotoIndex > index) {
      setSelectedPhotoIndex(selectedPhotoIndex - 1);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="md:col-span-1 space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-sm font-medium">Seznam fotografií</h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddPhoto}
              >
                <Upload className="mr-2 h-4 w-4" />
                Přidat fotografii
              </Button>
            </div>
            <ScrollArea className="h-[400px] border rounded-md p-2">
              {fields.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-center p-4">
                  <Camera className="h-12 w-12 text-muted-foreground mb-2" />
                  <p className="text-sm text-muted-foreground">
                    Zatím nebyly přidány žádné fotografie
                  </p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    className="mt-4"
                    onClick={handleAddPhoto}
                  >
                    <Upload className="mr-2 h-4 w-4" />
                    Přidat první fotografii
                  </Button>
                </div>
              ) : (
                <div className="space-y-2">
                  {fields.map((field, index) => (
                    <div
                      key={field.id}
                      className={`flex items-center p-2 rounded-md cursor-pointer ${
                        selectedPhotoIndex === index
                          ? "bg-primary/10 border border-primary/30"
                          : "hover:bg-accent"
                      }`}
                      onClick={() => setSelectedPhotoIndex(index)}
                    >
                      <div className="w-12 h-12 bg-muted rounded-md flex items-center justify-center mr-3">
                        <Image className="h-6 w-6 text-muted-foreground" />
                      </div>
                      <div className="flex-grow min-w-0">
                        <p className="text-sm font-medium truncate">
                          {field.description || field.fileName || `Fotografie ${index + 1}`}
                        </p>
                        <p className="text-xs text-muted-foreground truncate">
                          {field.dateTaken || "Datum neuvedeno"}
                        </p>
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRemovePhoto(index);
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </ScrollArea>
          </div>

          <div className="md:col-span-2">
            {selectedPhotoIndex !== null && selectedPhotoIndex < fields.length ? (
              <div className="space-y-4">
                <h3 className="text-sm font-medium">Detail fotografie</h3>
                <div className="border rounded-md p-4 bg-muted/30 flex items-center justify-center h-[200px]">
                  <div className="text-center">
                    <Image className="h-12 w-12 mx-auto text-muted-foreground" />
                    <p className="text-sm text-muted-foreground mt-2">
                      Náhled fotografie (implementace nahrávání bude přidána později)
                    </p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItemRHF
                    control={control}
                    name={`${name}.${selectedPhotoIndex}.description`}
                    label="Popis fotografie"
                    placeholder="Zadejte popis fotografie"
                  />
                  <FormItemRHF
                    control={control}
                    name={`${name}.${selectedPhotoIndex}.dateTaken`}
                    label="Datum pořízení"
                    type="date"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItemRHF
                    control={control}
                    name={`${name}.${selectedPhotoIndex}.sourceURL`}
                    label="Zdroj (URL)"
                    placeholder="Zadejte URL zdroje fotografie"
                  />
                  <FormItemRHF
                    control={control}
                    name={`${name}.${selectedPhotoIndex}.location`}
                    label="Místo pořízení"
                    placeholder="Zadejte místo pořízení fotografie"
                  />
                </div>

                <FormItemRHF
                  control={control}
                  name={`${name}.${selectedPhotoIndex}.notes`}
                  label="Poznámky"
                  placeholder="Zadejte další poznámky k fotografii"
                  as="textarea"
                  rows={3}
                />
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-center p-8 border rounded-md">
                <FileText className="h-12 w-12 text-muted-foreground mb-2" />
                <h3 className="text-lg font-medium">Žádná fotografie není vybrána</h3>
                <p className="text-sm text-muted-foreground mt-2">
                  Vyberte fotografii ze seznamu nebo přidejte novou
                </p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  className="mt-4"
                  onClick={handleAddPhoto}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Přidat fotografii
                </Button>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
