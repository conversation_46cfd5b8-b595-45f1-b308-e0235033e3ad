"use client";

import { Control, useFieldArray } from "react-hook-form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { TrainingModuleFormValues, trainingTypes, trainingStatuses, certificateValidities, trainingSources } from "./schemas";
import { FormItemRHF } from "./FormComponents";
import { PhotoUploadSection } from "./PhotoUploadSection";
import { PlusCircle, X } from "lucide-react";
import { useState } from "react";

interface TrainingDetailProps {
  control: Control<TrainingModuleFormValues>;
  trainingIndex: number;
}

export function TrainingDetail({ control, trainingIndex }: TrainingDetailProps) {
  const [activeTab, setActiveTab] = useState("basic");

  const { fields: skillsFields, append: appendSkill, remove: removeSkill } = useFieldArray({
    control,
    name: `trainings.${trainingIndex}.skills`,
  });

  const handleAddSkill = () => {
    appendSkill("");
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Detail školení</CardTitle>
        <CardDescription>
          Informace o školení, výcviku nebo certifikaci
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="basic">Základní informace</TabsTrigger>
            <TabsTrigger value="certificate">Certifikace</TabsTrigger>
            <TabsTrigger value="martial_arts">Bojová umění</TabsTrigger>
            <TabsTrigger value="military">Vojenský výcvik</TabsTrigger>
            <TabsTrigger value="police">Policejní výcvik</TabsTrigger>
            <TabsTrigger value="danger">Nebezpečnost</TabsTrigger>
            <TabsTrigger value="verification">Ověření</TabsTrigger>
            <TabsTrigger value="photos">Fotodokumentace</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemRHF
                control={control}
                name={`trainings.${trainingIndex}.name`}
                label="Název školení/výcviku"
                placeholder="Zadejte název školení nebo výcviku"
              />

              <FormField
                control={control}
                name={`trainings.${trainingIndex}.trainingType`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Typ školení</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Vyberte typ školení" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="professional">Profesní</SelectItem>
                        <SelectItem value="military">Vojenský</SelectItem>
                        <SelectItem value="security">Bezpečnostní</SelectItem>
                        <SelectItem value="weapons">Zbraně</SelectItem>
                        <SelectItem value="martial_arts">Bojová umění</SelectItem>
                        <SelectItem value="driving">Řidičský</SelectItem>
                        <SelectItem value="aviation">Letecký</SelectItem>
                        <SelectItem value="diving">Potápěčský</SelectItem>
                        <SelectItem value="survival">Přežití</SelectItem>
                        <SelectItem value="medical">Zdravotnický</SelectItem>
                        <SelectItem value="technical">Technický</SelectItem>
                        <SelectItem value="language">Jazykový</SelectItem>
                        <SelectItem value="other">Jiný</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormItemRHF
              control={control}
              name={`trainings.${trainingIndex}.otherTrainingTypeDetail`}
              label="Upřesnění typu školení"
              placeholder="Upřesněte typ školení, pokud jste vybrali 'Jiný'"
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemRHF
                control={control}
                name={`trainings.${trainingIndex}.institution`}
                label="Instituce/Poskytovatel"
                placeholder="Zadejte název instituce nebo poskytovatele"
              />

              <FormItemRHF
                control={control}
                name={`trainings.${trainingIndex}.location`}
                label="Místo konání"
                placeholder="Zadejte místo konání školení"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemRHF
                control={control}
                name={`trainings.${trainingIndex}.startDate`}
                label="Datum zahájení"
                type="date"
              />

              <FormItemRHF
                control={control}
                name={`trainings.${trainingIndex}.endDate`}
                label="Datum ukončení"
                type="date"
              />
            </div>

            <FormField
              control={control}
              name={`trainings.${trainingIndex}.status`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Stav školení</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Vyberte stav školení" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="completed">Dokončeno</SelectItem>
                      <SelectItem value="ongoing">Probíhá</SelectItem>
                      <SelectItem value="planned">Plánováno</SelectItem>
                      <SelectItem value="abandoned">Přerušeno</SelectItem>
                      <SelectItem value="failed">Neúspěšné</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormItemRHF
              control={control}
              name={`trainings.${trainingIndex}.description`}
              label="Popis školení"
              placeholder="Zadejte podrobný popis školení nebo výcviku"
              as="textarea"
              rows={4}
            />

            <div className="space-y-2">
              <FormLabel>Získané dovednosti</FormLabel>
              <div className="space-y-2">
                {skillsFields.map((field, index) => (
                  <div key={field.id} className="flex items-center space-x-2">
                    <FormItemRHF
                      control={control}
                      name={`trainings.${trainingIndex}.skills.${index}`}
                      label=""
                      placeholder="Zadejte získanou dovednost"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => removeSkill(index)}
                      className="flex-shrink-0"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleAddSkill}
                className="mt-2"
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Přidat dovednost
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="certificate" className="space-y-4">
            <FormField
              control={control}
              name={`trainings.${trainingIndex}.certificateObtained`}
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Certifikát získán</FormLabel>
                    <FormDescription>
                      Označte, pokud bylo školení zakončeno získáním certifikátu
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemRHF
                control={control}
                name={`trainings.${trainingIndex}.certificateName`}
                label="Název certifikátu"
                placeholder="Zadejte název získaného certifikátu"
              />

              <FormItemRHF
                control={control}
                name={`trainings.${trainingIndex}.certificateId`}
                label="Číslo certifikátu"
                placeholder="Zadejte identifikační číslo certifikátu"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={control}
                name={`trainings.${trainingIndex}.certificateValidity`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Platnost certifikátu</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Vyberte platnost certifikátu" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="valid">Platný</SelectItem>
                        <SelectItem value="expired">Vypršel</SelectItem>
                        <SelectItem value="revoked">Odebrán</SelectItem>
                        <SelectItem value="unknown">Neznámá</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormItemRHF
                control={control}
                name={`trainings.${trainingIndex}.certificateValidUntil`}
                label="Platnost do"
                type="date"
              />
            </div>
          </TabsContent>

          <TabsContent value="martial_arts" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Bojová umění a sporty</CardTitle>
                <CardDescription>
                  Podrobné informace o výcviku v bojových uměních a sportech
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormItemRHF
                  control={control}
                  name={`trainings.${trainingIndex}.martialArtStyle`}
                  label="Styl bojového umění"
                  placeholder="Např. Karate, Judo, Krav Maga, MMA, Box, atd."
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={control}
                    name={`trainings.${trainingIndex}.beltColor`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Barva pásku</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Vyberte barvu pásku" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="white">Bílý</SelectItem>
                            <SelectItem value="yellow">Žlutý</SelectItem>
                            <SelectItem value="orange">Oranžový</SelectItem>
                            <SelectItem value="green">Zelený</SelectItem>
                            <SelectItem value="blue">Modrý</SelectItem>
                            <SelectItem value="purple">Fialový</SelectItem>
                            <SelectItem value="brown">Hnědý</SelectItem>
                            <SelectItem value="black">Černý</SelectItem>
                            <SelectItem value="red">Červený</SelectItem>
                            <SelectItem value="other">Jiný</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={control}
                    name={`trainings.${trainingIndex}.beltDegree`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Stupeň (Dan/Kyu)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min={0}
                            max={10}
                            placeholder="Zadejte stupeň (0-10)"
                            {...field}
                            onChange={(e) => field.onChange(e.target.valueAsNumber)}
                            value={field.value || ""}
                          />
                        </FormControl>
                        <FormDescription>
                          Např. 1. Dan, 2. Kyu, atd. (0-10)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormItemRHF
                  control={control}
                  name={`trainings.${trainingIndex}.competitionAchievements`}
                  label="Úspěchy v soutěžích"
                  placeholder="Např. 1. místo na mistrovství ČR, medaile z mezinárodních soutěží, atd."
                  as="textarea"
                  rows={3}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="military" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Vojenský výcvik</CardTitle>
                <CardDescription>
                  Podrobné informace o vojenském výcviku a službě
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItemRHF
                    control={control}
                    name={`trainings.${trainingIndex}.militaryBranch`}
                    label="Složka ozbrojených sil"
                    placeholder="Např. Pozemní síly, Letectvo, Námořnictvo, atd."
                  />

                  <FormItemRHF
                    control={control}
                    name={`trainings.${trainingIndex}.militarySpecialization`}
                    label="Specializace"
                    placeholder="Např. Pěchota, Dělostřelectvo, Průzkum, atd."
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={control}
                    name={`trainings.${trainingIndex}.militaryRank`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Vojenská hodnost</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Vyberte vojenskou hodnost" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="private">Vojín</SelectItem>
                            <SelectItem value="corporal">Desátník</SelectItem>
                            <SelectItem value="sergeant">Četař/Rotný</SelectItem>
                            <SelectItem value="lieutenant">Poručík</SelectItem>
                            <SelectItem value="captain">Kapitán</SelectItem>
                            <SelectItem value="major">Major</SelectItem>
                            <SelectItem value="colonel">Plukovník</SelectItem>
                            <SelectItem value="general">Generál</SelectItem>
                            <SelectItem value="other">Jiná</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormItemRHF
                    control={control}
                    name={`trainings.${trainingIndex}.militaryUnit`}
                    label="Vojenská jednotka"
                    placeholder="Např. 4. brigáda rychlého nasazení, 43. výsadkový pluk, atd."
                  />
                </div>

                <FormField
                  control={control}
                  name={`trainings.${trainingIndex}.conscriptionService`}
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Povinná vojenská služba</FormLabel>
                        <FormDescription>
                          Označte, pokud subjekt absolvoval povinnou vojenskou službu
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <FormItemRHF
                  control={control}
                  name={`trainings.${trainingIndex}.conscriptionYears`}
                  label="Období povinné vojenské služby"
                  placeholder="Např. 1995-1997"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="police" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Policejní výcvik</CardTitle>
                <CardDescription>
                  Podrobné informace o policejním výcviku a službě
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItemRHF
                    control={control}
                    name={`trainings.${trainingIndex}.policeDepartment`}
                    label="Oddělení policie"
                    placeholder="Např. Městská policie, Státní policie, atd."
                  />

                  <FormItemRHF
                    control={control}
                    name={`trainings.${trainingIndex}.policeSpecialization`}
                    label="Specializace"
                    placeholder="Např. Dopravní policie, Kriminální policie, atd."
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={control}
                    name={`trainings.${trainingIndex}.policeRank`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Policejní hodnost</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Vyberte policejní hodnost" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="rotny">Rotný</SelectItem>
                            <SelectItem value="strazmistr">Strážmistr</SelectItem>
                            <SelectItem value="nadstrazmistr">Nadstrážmistr</SelectItem>
                            <SelectItem value="podpraporcik">Podpraporčík</SelectItem>
                            <SelectItem value="praporcik">Praporčík</SelectItem>
                            <SelectItem value="nadpraporcik">Nadpraporčík</SelectItem>
                            <SelectItem value="podporucik">Podporučík</SelectItem>
                            <SelectItem value="porucik">Poručík</SelectItem>
                            <SelectItem value="nadporucik">Nadporučík</SelectItem>
                            <SelectItem value="kapitan">Kapitán</SelectItem>
                            <SelectItem value="major">Major</SelectItem>
                            <SelectItem value="podplukovnik">Podplukovník</SelectItem>
                            <SelectItem value="plukovnik">Plukovník</SelectItem>
                            <SelectItem value="general">Generálská hodnost</SelectItem>
                            <SelectItem value="other">Jiná</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormItemRHF
                    control={control}
                    name={`trainings.${trainingIndex}.policeUnit`}
                    label="Policejní jednotka"
                    placeholder="Např. URNA, Pořádková jednotka, atd."
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>



          <TabsContent value="danger" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Hodnocení nebezpečnosti</CardTitle>
                <CardDescription>
                  Hodnocení potenciální nebezpečnosti subjektu na základě výcviku
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={control}
                  name={`trainings.${trainingIndex}.dangerAssessment`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Úroveň nebezpečnosti</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Vyberte úroveň nebezpečnosti" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="none">Žádná</SelectItem>
                          <SelectItem value="low">Nízká</SelectItem>
                          <SelectItem value="medium">Střední</SelectItem>
                          <SelectItem value="high">Vysoká</SelectItem>
                          <SelectItem value="extreme">Extrémní</SelectItem>
                          <SelectItem value="unknown">Neznámá</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Hodnocení potenciální nebezpečnosti subjektu na základě výcviku
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormItemRHF
                  control={control}
                  name={`trainings.${trainingIndex}.dangerAssessmentReason`}
                  label="Zdůvodnění hodnocení nebezpečnosti"
                  placeholder="Zadejte zdůvodnění hodnocení nebezpečnosti"
                  as="textarea"
                  rows={3}
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="verification" className="space-y-4">
            <FormField
              control={control}
              name={`trainings.${trainingIndex}.source`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Zdroj informací</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Vyberte zdroj informací" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="self_reported">Vlastní tvrzení</SelectItem>
                      <SelectItem value="verified">Ověřeno</SelectItem>
                      <SelectItem value="social_media">Sociální sítě</SelectItem>
                      <SelectItem value="witness">Svědek</SelectItem>
                      <SelectItem value="intelligence">Zpravodajské informace</SelectItem>
                      <SelectItem value="public_records">Veřejné záznamy</SelectItem>
                      <SelectItem value="other">Jiný</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormItemRHF
              control={control}
              name={`trainings.${trainingIndex}.otherSourceDetail`}
              label="Upřesnění zdroje"
              placeholder="Upřesněte zdroj informací, pokud jste vybrali 'Jiný'"
            />

            <FormField
              control={control}
              name={`trainings.${trainingIndex}.verificationStatus`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Stav ověření</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Vyberte stav ověření" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="yes">Ověřeno</SelectItem>
                      <SelectItem value="no">Neověřeno</SelectItem>
                      <SelectItem value="unknown">Neznámý</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormItemRHF
              control={control}
              name={`trainings.${trainingIndex}.verificationNotes`}
              label="Poznámky k ověření"
              placeholder="Zadejte poznámky k ověření informací"
              as="textarea"
              rows={3}
            />

            <FormItemRHF
              control={control}
              name={`trainings.${trainingIndex}.documentReferences`}
              label="Reference na dokumenty"
              placeholder="Zadejte odkazy na související dokumenty"
              as="textarea"
              rows={3}
            />

            <FormItemRHF
              control={control}
              name={`trainings.${trainingIndex}.notes`}
              label="Poznámky"
              placeholder="Zadejte další poznámky k školení"
              as="textarea"
              rows={3}
            />
          </TabsContent>

          <TabsContent value="photos">
            <PhotoUploadSection
              control={control}
              name={`trainings.${trainingIndex}.photos`}
              title="Fotodokumentace"
              description="Přidejte fotografie související se školením nebo výcvikem"
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
