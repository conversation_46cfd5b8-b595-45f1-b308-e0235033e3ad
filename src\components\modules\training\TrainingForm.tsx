"use client";

import { useState } from "react";
import { useF<PERSON>, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { v4 as uuidv4 } from "uuid";
import { doc, setDoc, serverTimestamp } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { TrainingModuleFormValues, trainingModuleSchema } from "./schemas";
import { TrainingR<PERSON>ord, TrainingModuleData, Subject } from "@/types";
import { TrainingsList } from "./TrainingsList";
import { TrainingDetail } from "./TrainingDetail";
import { GraduationCap, Save, Plus, ArrowLeft } from "lucide-react";

interface TrainingFormProps {
  caseId: string;
  subject: Subject;
  existingData: TrainingModuleData | null;
  onSave: (moduleId: string, wasNew: boolean) => void;
}

export function TrainingForm({ caseId, subject, existingData, onSave }: TrainingFormProps) {
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [selectedTrainingId, setSelectedTrainingId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>("list");
  
  const moduleId = "training";
  
  const form = useForm<TrainingModuleFormValues>({
    resolver: zodResolver(trainingModuleSchema),
    defaultValues: {
      trainings: existingData?.trainings || [],
      generalNotes: existingData?.generalNotes || "",
    },
  });
  
  const { control, handleSubmit, watch, setValue, getValues, formState } = form;
  const { isDirty } = formState;
  
  const trainings = watch("trainings");
  
  const handleAddTraining = () => {
    const newTraining: TrainingRecord = {
      id: uuidv4(),
      name: "",
      trainingType: "professional",
      status: "completed",
      certificateObtained: false,
      skills: [],
      photos: [],
    };
    
    setValue("trainings", [...trainings, newTraining], { shouldDirty: true });
    setSelectedTrainingId(newTraining.id);
    setActiveTab("detail");
  };
  
  const handleEditTraining = (id: string) => {
    setSelectedTrainingId(id);
    setActiveTab("detail");
  };
  
  const handleDeleteTraining = (id: string) => {
    setValue(
      "trainings",
      trainings.filter((training) => training.id !== id),
      { shouldDirty: true }
    );
    
    if (selectedTrainingId === id) {
      setSelectedTrainingId(null);
      setActiveTab("list");
    }
  };
  
  const handleBackToList = () => {
    setSelectedTrainingId(null);
    setActiveTab("list");
  };
  
  const onSubmitHandler: SubmitHandler<TrainingModuleFormValues> = async (data) => {
    setIsSaving(true);
    try {
      const moduleDocRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);
      const dataToSave: TrainingModuleData = {
        ...data,
        subjectId: subject.id,
        lastUpdatedAt: serverTimestamp(),
        createdAt: existingData?.createdAt || serverTimestamp(),
      };
      await setDoc(moduleDocRef, dataToSave, { merge: true });
      toast({ title: "Data modulu Školení a výcvik uložena." });
      const wasNew = !existingData || !existingData.createdAt;
      onSave(moduleId, wasNew);
    } catch (error: any) {
      toast({
        title: "Chyba ukládání dat modulu",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };
  
  const selectedTraining = trainings.find((training) => training.id === selectedTrainingId);
  
  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(onSubmitHandler)}>
        <div className="space-y-4 pb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <GraduationCap className="h-6 w-6 text-primary" />
              <h2 className="text-2xl font-bold">Školení a výcvik</h2>
            </div>
            <Button type="submit" disabled={!isDirty || isSaving}>
              {isSaving ? (
                <>Ukládám...</>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Uložit
                </>
              )}
            </Button>
          </div>
          
          <Separator />
          
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="list">Seznam školení a výcviků</TabsTrigger>
              <TabsTrigger value="detail" disabled={!selectedTrainingId}>
                Detail školení
              </TabsTrigger>
              <TabsTrigger value="notes">Poznámky</TabsTrigger>
            </TabsList>
            
            <TabsContent value="list" className="space-y-4">
              <div className="flex justify-end">
                <Button type="button" onClick={handleAddTraining}>
                  <Plus className="mr-2 h-4 w-4" />
                  Přidat školení
                </Button>
              </div>
              
              <Card>
                <CardHeader>
                  <CardTitle>Seznam školení a výcviků</CardTitle>
                  <CardDescription>
                    Přehled všech školení, výcviků a certifikací subjektu
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <TrainingsList
                    trainings={trainings}
                    onEdit={handleEditTraining}
                    onDelete={handleDeleteTraining}
                  />
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="detail" className="space-y-4">
              {selectedTraining && (
                <>
                  <div className="flex items-center space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleBackToList}
                    >
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Zpět na seznam
                    </Button>
                  </div>
                  
                  <TrainingDetail
                    control={control}
                    trainingIndex={trainings.findIndex(
                      (t) => t.id === selectedTrainingId
                    )}
                  />
                </>
              )}
            </TabsContent>
            
            <TabsContent value="notes">
              <Card>
                <CardHeader>
                  <CardTitle>Obecné poznámky</CardTitle>
                  <CardDescription>
                    Další informace o školeních a výcvicích subjektu
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    {...form.register("generalNotes")}
                    placeholder="Zadejte obecné poznámky k modulu..."
                    className="min-h-[200px]"
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </form>
    </Form>
  );
}
