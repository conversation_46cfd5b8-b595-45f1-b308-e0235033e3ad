"use client";

import { useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { PhoneNumbersModuleFormValues, phoneNumbersModuleSchema } from "./schemas";
import { PhoneRecord, PhoneNumbersModuleData, Subject } from "@/types";
import { PhonesList } from "./PhonesList";
import { PhoneDetail } from "./PhoneDetail";
import { Phone, Save, Plus, ArrowLeft } from "lucide-react";

interface PhoneNumbersFormProps {
  caseId: string;
  subject: Subject;
  initialData?: PhoneNumbersModuleData;
  onSave: (data: PhoneNumbersModuleFormValues) => Promise<void>;
  onBack: () => void;
}

export function PhoneNumbersForm({ caseId, subject, initialData, onSave, onBack }: PhoneNumbersFormProps) {
  const [activeTab, setActiveTab] = useState("list");
  const [selectedPhoneId, setSelectedPhoneId] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  const form = useForm<PhoneNumbersModuleFormValues>({
    resolver: zodResolver(phoneNumbersModuleSchema),
    defaultValues: {
      phones: initialData?.phones || [],
      generalNotes: initialData?.generalNotes || "",
    },
  });

  const phones = form.watch("phones");

  const handleSelectPhone = (index: number) => {
    if (index >= 0 && index < phones.length) {
      // Použijeme setTimeout, abychom zabránili aktualizaci stavu během renderování
      setTimeout(() => {
        setSelectedPhoneId(phones[index].id);
        setActiveTab("detail");
      }, 0);
    }
  };

  const handleAddPhone = () => {
    const newPhone: PhoneRecord = {
      id: uuidv4(),
      phoneNumber: "",
      source: "osint",
      verificationStatus: "unverified",
      discoveryDate: new Date().toISOString().split('T')[0],
    };

    form.setValue("phones", [...phones, newPhone]);

    // Použijeme setTimeout, abychom zabránili aktualizaci stavu během renderování
    setTimeout(() => {
      setSelectedPhoneId(newPhone.id);
      setActiveTab("detail");
    }, 0);
  };

  const handleSubmit = async (data: PhoneNumbersModuleFormValues) => {
    setIsSaving(true);
    try {
      await onSave(data);
    } catch (error) {
      console.error("Error saving phone numbers data:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const selectedPhoneIndex = phones.findIndex(phone => phone.id === selectedPhoneId);

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)}>
        <div className="space-y-4 pb-10">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={onBack}
                className="mr-2"
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              <div>
                <h2 className="text-2xl font-bold">Telefonní čísla</h2>
                <p className="text-muted-foreground">
                  {subject.type === "physical"
                    ? `${subject.firstName} ${subject.lastName}`
                    : subject.name}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-2">
              <Button
                type="submit"
                disabled={isSaving || !form.formState.isDirty}
              >
                <Save className="mr-2 h-4 w-4" />
                {isSaving ? "Ukládání..." : "Uložit změny"}
              </Button>
            </div>
          </div>

          <Separator />

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="list">Seznam telefonních čísel</TabsTrigger>
              <TabsTrigger value="detail" disabled={!selectedPhoneId}>
                Detail telefonního čísla
              </TabsTrigger>
              <TabsTrigger value="notes">Poznámky</TabsTrigger>
            </TabsList>

            <TabsContent value="list" className="space-y-4">
              <div className="flex justify-end">
                <Button type="button" onClick={handleAddPhone}>
                  <Plus className="mr-2 h-4 w-4" />
                  Přidat telefonní číslo
                </Button>
              </div>

              <PhonesList
                control={form.control}
                onSelectPhone={handleSelectPhone}
                onAddPhone={handleAddPhone}
              />
            </TabsContent>

            <TabsContent value="detail" className="space-y-4">
              {selectedPhoneId && selectedPhoneIndex !== -1 ? (
                <>
                  <div className="flex justify-between items-center">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setActiveTab("list")}
                    >
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Zpět na seznam
                    </Button>
                  </div>

                  <ScrollArea className="h-[calc(100vh-250px)]">
                    <PhoneDetail
                      control={form.control}
                      phoneIndex={selectedPhoneIndex}
                    />
                  </ScrollArea>
                </>
              ) : (
                <Card>
                  <CardContent className="py-10 text-center">
                    <Phone className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      Vyberte telefonní číslo ze seznamu nebo přidejte nové
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      className="mt-4"
                      onClick={handleAddPhone}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Přidat telefonní číslo
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="notes">
              <Card>
                <CardHeader>
                  <CardTitle>Obecné poznámky</CardTitle>
                  <CardDescription>
                    Další informace o telefonních číslech subjektu
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    {...form.register("generalNotes")}
                    placeholder="Zadejte obecné poznámky k modulu..."
                    className="min-h-[200px]"
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </form>
    </FormProvider>
  );
}
