/**
 * Finanční monitoring - funkce pro finanční analýzu
 */

/**
 * Získání finančních dat
 */
function getFinancialData() {
    console.log('Získání finančních dat');
    
    // Najít aktuální modul finančního monitoringu
    const financialModule = document.querySelector('.module[id^="module-financni-monitoring"]');
    if (!financialModule) {
        console.error('Modul finančního monitoringu nebyl nalezen');
        return;
    }
    
    // Získání IČO z tlačítka
    const button = financialModule.querySelector('.get-financial-data');
    if (!button) {
        console.error('Tlačítko pro získání finančních dat nebylo nalezeno');
        return;
    }
    
    const ico = button.getAttribute('data-ico');
    if (!ico) {
        alert('Nebylo nalezeno IČO firmy.');
        return;
    }
    
    // Zobrazení načítání
    const resultsContainer = financialModule.querySelector('#financial-data-results');
    if (!resultsContainer) {
        console.error('Kontejner pro výsledky finančních dat nebyl nalezen');
        return;
    }
    
    resultsContainer.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Načítání finančních dat...</span>
        </div>
    `;
    resultsContainer.style.display = 'block';
    
    // Získání finančních dat z Justice.cz (Sbírka listin)
    fetchFinancialStatements(ico)
        .then(data => {
            displayFinancialData(data);
        })
        .catch(error => {
            console.error('Chyba při získávání finančních dat:', error);
            resultsContainer.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>Nepodařilo se získat finanční data. Zkuste to prosím znovu.</span>
                </div>
                <div class="external-links">
                    <p>Finanční data můžete získat z následujících zdrojů:</p>
                    <a href="https://or.justice.cz/ias/ui/vypis-sl-firma?subjektId=&ico=${ico}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Sbírka listin (Justice.cz)
                    </a>
                    <a href="https://www.mfcr.cz/cs/o-ministerstvu/informacni-systemy/ares/ekonomicke-subjekty/vyhledavani-ekonomickeho-subjektu-podle-ico/${ico}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> ARES
                    </a>
                </div>
            `;
        });
}

/**
 * Získání finančních výkazů z Justice.cz (Sbírka listin)
 * @param {string} ico - IČO firmy
 * @returns {Promise<Object>} - Data o finančních výkazech
 */
function fetchFinancialStatements(ico) {
    console.log('Získání finančních výkazů pro IČO:', ico);
    
    // Poznámka: Justice.cz nemá veřejné API pro získání finančních výkazů
    // Pro reálnou implementaci by bylo potřeba použít web scraping nebo placené API
    // Pro účely tohoto příkladu použijeme simulovaná data
    
    // Místo toho můžeme použít ARES API pro získání základních informací o firmě
    return fetch(`https://ares.gov.cz/ekonomicke-subjekty-v-be/rest/ekonomicke-subjekty/${ico}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se získat data z ARES.');
            }
            return response.json();
        })
        .then(data => {
            // Vytvoření odkazu na Sbírku listin
            const justiceUrl = `https://or.justice.cz/ias/ui/vypis-sl-firma?subjektId=&ico=${ico}`;
            
            // Vrátíme základní informace o firmě a odkaz na Sbírku listin
            return {
                ico: ico,
                name: data.obchodniJmeno || '',
                justiceUrl: justiceUrl,
                // Finanční výkazy nejsou dostupné přes API, pouze odkaz na Sbírku listin
                financialStatements: []
            };
        });
}

/**
 * Zobrazení finančních dat
 * @param {Object} data - Data o finančních výkazech
 */
function displayFinancialData(data) {
    console.log('Zobrazení finančních dat:', data);
    
    // Najít aktuální modul finančního monitoringu
    const financialModule = document.querySelector('.module[id^="module-financni-monitoring"]');
    if (!financialModule) {
        console.error('Modul finančního monitoringu nebyl nalezen');
        return;
    }
    
    const resultsContainer = financialModule.querySelector('#financial-data-results');
    if (!resultsContainer) {
        console.error('Kontejner pro výsledky finančních dat nebyl nalezen');
        return;
    }
    
    // Zobrazení výsledků
    let html = `
        <div class="results-header">
            <h4>Finanční data - ${data.name}</h4>
        </div>
        <div class="financial-data">
            <div class="financial-data-section">
                <h5>Účetní závěrky</h5>
                <p>Pro zobrazení účetních závěrek navštivte Sbírku listin:</p>
                <div class="external-links">
                    <a href="${data.justiceUrl}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Sbírka listin (Justice.cz)
                    </a>
                </div>
            </div>
            
            <div class="financial-data-section">
                <h5>Finanční ukazatele</h5>
                <p>Pro získání finančních ukazatelů je potřeba analyzovat účetní závěrky. Můžete použít následující služby:</p>
                <div class="external-links">
                    <a href="https://www.bisnode.cz/" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Bisnode
                    </a>
                    <a href="https://www.informaceofirmach.cz/vyhledavani/?ic=${data.ico}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Informace o firmách
                    </a>
                </div>
            </div>
            
            <div class="financial-data-section">
                <h5>Veřejné zakázky</h5>
                <p>Pro zobrazení veřejných zakázek navštivte:</p>
                <div class="external-links">
                    <a href="https://www.vestnikverejnychzakazek.cz/SearchForm/SearchContract?contractNumber=&contractName=&contractType=&cpvCode=&dateFrom=&dateTo=&price=&deadline=&documentType=&submitterName=&submitterIco=${data.ico}&sortBy=0" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Věstník veřejných zakázek
                    </a>
                    <a href="https://smlouvy.gov.cz/vyhledavani?q=${data.ico}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Registr smluv
                    </a>
                </div>
            </div>
            
            <div class="financial-data-section">
                <h5>Dotace</h5>
                <p>Pro zobrazení přijatých dotací navštivte:</p>
                <div class="external-links">
                    <a href="https://www.dotinfo.cz/prijemci?ico=${data.ico}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> DotInfo
                    </a>
                    <a href="https://cedr.mfcr.cz/cedr3internetv2/seznam?ico=${data.ico}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> CEDR
                    </a>
                </div>
            </div>
        </div>
    `;
    
    resultsContainer.innerHTML = html;
}

/**
 * Kontrola rizik
 */
function checkRisks() {
    console.log('Kontrola rizik');
    
    // Najít aktuální modul finančního monitoringu
    const financialModule = document.querySelector('.module[id^="module-financni-monitoring"]');
    if (!financialModule) {
        console.error('Modul finančního monitoringu nebyl nalezen');
        return;
    }
    
    // Získání IČO z tlačítka
    const button = financialModule.querySelector('.check-risks');
    if (!button) {
        console.error('Tlačítko pro kontrolu rizik nebylo nalezeno');
        return;
    }
    
    const ico = button.getAttribute('data-ico');
    if (!ico) {
        alert('Nebylo nalezeno IČO firmy.');
        return;
    }
    
    // Zobrazení načítání
    const resultsContainer = financialModule.querySelector('#risk-check-results');
    if (!resultsContainer) {
        console.error('Kontejner pro výsledky kontroly rizik nebyl nalezen');
        return;
    }
    
    resultsContainer.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Kontrola rizik...</span>
        </div>
    `;
    resultsContainer.style.display = 'block';
    
    // Kontrola rizik
    fetchRiskData(ico)
        .then(data => {
            displayRiskData(data);
        })
        .catch(error => {
            console.error('Chyba při kontrole rizik:', error);
            resultsContainer.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>Nepodařilo se zkontrolovat rizika. Zkuste to prosím znovu.</span>
                </div>
            `;
        });
}

/**
 * Získání dat o rizicích
 * @param {string} ico - IČO firmy
 * @returns {Promise<Object>} - Data o rizicích
 */
function fetchRiskData(ico) {
    console.log('Získání dat o rizicích pro IČO:', ico);
    
    // Kontrola insolvence
    const insolvencyPromise = fetch(`https://isir.justice.cz/isir/ueu/vysledek_lustrace.do?ic=${ico}`)
        .then(response => {
            // Poznámka: ISIR nemá veřejné API, toto je pouze ukázka
            // V reálné implementaci by bylo potřeba použít web scraping nebo placené API
            return { insolvency: false };
        })
        .catch(() => {
            return { insolvency: false };
        });
    
    // Kontrola exekucí
    const executionPromise = fetch(`https://www.ceecr.cz/vyhledavani?ico=${ico}`)
        .then(response => {
            // Poznámka: CEE nemá veřejné API, toto je pouze ukázka
            // V reálné implementaci by bylo potřeba použít web scraping nebo placené API
            return { execution: false };
        })
        .catch(() => {
            return { execution: false };
        });
    
    // Kontrola nespolehlivého plátce DPH
    const vatPromise = fetch(`https://adisrws.mfcr.cz/dpr/apl/DphReg?ico=${ico}`)
        .then(response => {
            // Poznámka: ADIS nemá veřejné API, toto je pouze ukázka
            // V reálné implementaci by bylo potřeba použít web scraping nebo placené API
            return { vatReliable: true };
        })
        .catch(() => {
            return { vatReliable: true };
        });
    
    // Vrácení všech dat o rizicích
    return Promise.all([insolvencyPromise, executionPromise, vatPromise])
        .then(([insolvencyData, executionData, vatData]) => {
            return {
                ico: ico,
                insolvency: insolvencyData.insolvency,
                execution: executionData.execution,
                vatReliable: vatData.vatReliable,
                // Odkazy na externí služby
                insolvencyUrl: `https://isir.justice.cz/isir/ueu/vysledek_lustrace.do?ic=${ico}`,
                executionUrl: `https://www.ceecr.cz/vyhledavani?ico=${ico}`,
                vatUrl: `https://adisrws.mfcr.cz/dpr/apl/DphReg?ico=${ico}`
            };
        });
}

/**
 * Zobrazení dat o rizicích
 * @param {Object} data - Data o rizicích
 */
function displayRiskData(data) {
    console.log('Zobrazení dat o rizicích:', data);
    
    // Najít aktuální modul finančního monitoringu
    const financialModule = document.querySelector('.module[id^="module-financni-monitoring"]');
    if (!financialModule) {
        console.error('Modul finančního monitoringu nebyl nalezen');
        return;
    }
    
    const resultsContainer = financialModule.querySelector('#risk-check-results');
    if (!resultsContainer) {
        console.error('Kontejner pro výsledky kontroly rizik nebyl nalezen');
        return;
    }
    
    // Zobrazení výsledků
    let html = `
        <div class="results-header">
            <h4>Kontrola rizik - IČO: ${data.ico}</h4>
        </div>
        <div class="risk-data">
            <div class="risk-data-section">
                <h5>Insolvence</h5>
                <div class="risk-item">
                    <div class="risk-status ${data.insolvency ? 'risk-high' : 'risk-low'}">
                        <i class="fas ${data.insolvency ? 'fa-exclamation-circle' : 'fa-check-circle'}"></i>
                        <span>${data.insolvency ? 'Nalezeno v insolvenčním rejstříku' : 'Nenalezeno v insolvenčním rejstříku'}</span>
                    </div>
                    <div class="risk-actions">
                        <a href="${data.insolvencyUrl}" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> Insolvenční rejstřík
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="risk-data-section">
                <h5>Exekuce</h5>
                <div class="risk-item">
                    <div class="risk-status ${data.execution ? 'risk-high' : 'risk-low'}">
                        <i class="fas ${data.execution ? 'fa-exclamation-circle' : 'fa-check-circle'}"></i>
                        <span>${data.execution ? 'Nalezeno v Centrální evidenci exekucí' : 'Nenalezeno v Centrální evidenci exekucí'}</span>
                    </div>
                    <div class="risk-actions">
                        <a href="${data.executionUrl}" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> Centrální evidence exekucí
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="risk-data-section">
                <h5>Nespolehlivý plátce DPH</h5>
                <div class="risk-item">
                    <div class="risk-status ${data.vatReliable ? 'risk-low' : 'risk-high'}">
                        <i class="fas ${data.vatReliable ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i>
                        <span>${data.vatReliable ? 'Spolehlivý plátce DPH' : 'Nespolehlivý plátce DPH'}</span>
                    </div>
                    <div class="risk-actions">
                        <a href="${data.vatUrl}" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> Registr plátců DPH
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="risk-data-section">
                <h5>Další kontroly</h5>
                <p>Pro další kontroly rizik můžete použít následující služby:</p>
                <div class="external-links">
                    <a href="https://www.justice.cz/web/msp/vysledky-vyhledavani?query=${data.ico}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Rejstřík trestů právnických osob
                    </a>
                    <a href="https://www.uohs.cz/cs/verejne-zakazky/seznam-kvalifikovanych-dodavatelu.html" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Seznam kvalifikovaných dodavatelů
                    </a>
                    <a href="https://www.uohs.cz/cs/verejne-zakazky/seznam-zakazanych-dodavatelu.html" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Seznam zakázaných dodavatelů
                    </a>
                </div>
            </div>
        </div>
    `;
    
    resultsContainer.innerHTML = html;
}
