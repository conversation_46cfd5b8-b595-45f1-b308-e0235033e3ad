/**
 * Akt<PERSON><PERSON><PERSON><PERSON> události - funkce pro fotogalerii a export
 */

/**
 * Inicializace fotogalerie pro aktuální události
 * @param {HTMLElement} moduleElement - Element modulu aktuálních událostí
 */
function initNewsPhotoGallery(moduleElement) {
    console.log('Inicializace fotogalerie pro aktuální události');
    
    if (!moduleElement) {
        console.error('Modul aktuálních událostí nebyl předán do initNewsPhotoGallery');
        return;
    }
    
    // Najít galerii v modulu
    const gallery = moduleElement.querySelector('.news-photo-gallery');
    if (!gallery) {
        console.error('Fotogalerie nebyla nalezena v modulu aktuálních událostí');
        return;
    }
    
    // Najít input pro nahrávání fotografií
    const uploadInput = moduleElement.querySelector('#news-photo-upload');
    if (!uploadInput) {
        console.error('Input pro nahrávání fotografií nebyl nalezen');
        return;
    }
    
    // Přidání event listeneru pro tlačítko přidání fotografie
    const addButton = gallery.querySelector('.photo-gallery-add');
    if (addButton) {
        console.log('Nalezeno tlačítko pro přidání fotografie');
        
        // Odstranění existujících event listenerů
        const newAddButton = addButton.cloneNode(true);
        addButton.parentNode.replaceChild(newAddButton, addButton);
        
        // Přidání nového event listeneru
        newAddButton.addEventListener('click', function() {
            uploadInput.click();
        });
    }
    
    // Přidání event listenerů pro tlačítka nahrávání
    const uploadButtons = moduleElement.querySelectorAll('.upload-photo[data-gallery="news-photo-gallery"]');
    uploadButtons.forEach(button => {
        button.addEventListener('click', function() {
            uploadInput.click();
        });
    });
    
    // Přidání event listeneru pro změnu input[type=file]
    uploadInput.addEventListener('change', function(event) {
        handleNewsPhotoUpload(event, gallery);
    });
    
    // Přidání event listeneru pro tlačítko vložení ze schránky
    const pasteButtons = moduleElement.querySelectorAll('.paste-photo[data-gallery="news-photo-gallery"]');
    pasteButtons.forEach(button => {
        button.addEventListener('click', function() {
            alert('Pro vložení obrázku ze schránky stiskněte Ctrl+V kdekoli v modulu aktuálních událostí (mimo textová pole).');
        });
    });
    
    // Přidání event listeneru pro tlačítko přidání z URL
    const urlButtons = moduleElement.querySelectorAll('.add-photo-url[data-gallery="news-photo-gallery"]');
    urlButtons.forEach(button => {
        button.addEventListener('click', function() {
            addNewsPhotoFromUrl(gallery);
        });
    });
    
    // Přidání event listeneru pro vložení ze schránky (Ctrl+V)
    moduleElement.addEventListener('paste', function(event) {
        const activeElement = document.activeElement;
        
        // Kontrola, zda kurzor není v textovém poli
        if (!activeElement.tagName.match(/input|textarea/i)) {
            // Zabránit výchozímu chování
            event.preventDefault();
            event.stopPropagation();
            
            handleNewsPhotoPaste(event, gallery);
        }
    });
}

/**
 * Zpracování nahrání fotografie z počítače
 * @param {Event} event - Událost změny input[type=file]
 * @param {HTMLElement} gallery - Element galerie
 */
function handleNewsPhotoUpload(event, gallery) {
    console.log('Zpracování nahrání fotografie z počítače pro aktuální události');
    
    // Resetování input[type=file] pro možnost nahrání stejného souboru znovu
    const input = event.target;
    
    const file = input.files[0];
    if (!file) {
        console.error('Nebyl vybrán žádný soubor');
        return;
    }
    
    console.log('Vybraný soubor:', file.name, file.type, file.size);
    
    // Kontrola, zda jde o obrázek
    if (!file.type.match('image.*')) {
        alert('Vybraný soubor není obrázek.');
        input.value = ''; // Reset input
        return;
    }
    
    // Vytvoření URL pro náhled
    const imageUrl = URL.createObjectURL(file);
    
    // Přidání fotografie do galerie
    addNewsPhotoToGallery(gallery, file.name, imageUrl);
    
    // Reset input pro možnost nahrání stejného souboru znovu
    input.value = '';
}

/**
 * Zpracování vložení obrázku ze schránky
 * @param {Event} event - Událost vložení
 * @param {HTMLElement} gallery - Element galerie
 */
function handleNewsPhotoPaste(event, gallery) {
    console.log('Zpracování vložení obrázku ze schránky pro aktuální události');
    
    // Kontrola, zda schránka obsahuje obrázek
    const clipboardData = event.clipboardData || window.clipboardData;
    if (!clipboardData) {
        console.error('Clipboard data nejsou k dispozici');
        return;
    }
    
    const items = clipboardData.items;
    if (!items) {
        console.error('Clipboard items nejsou k dispozici');
        return;
    }
    
    let blob = null;
    
    // Procházení položek ve schránce
    for (let i = 0; i < items.length; i++) {
        console.log('Clipboard item:', items[i].type);
        if (items[i].type.indexOf('image') === 0) {
            blob = items[i].getAsFile();
            console.log('Nalezen obrázek ve schránce:', blob);
            break;
        }
    }
    
    if (!blob) {
        console.error('Schránka neobsahuje obrázek');
        alert('Schránka neobsahuje obrázek. Zkopírujte obrázek do schránky a zkuste to znovu.');
        return;
    }
    
    // Vytvoření URL pro náhled
    const imageUrl = URL.createObjectURL(blob);
    console.log('Vytvořeno URL pro náhled:', imageUrl);
    
    // Přidání fotografie do galerie
    addNewsPhotoToGallery(gallery, 'Vložený obrázek', imageUrl);
}

/**
 * Přidání fotografie z URL
 * @param {HTMLElement} gallery - Element galerie
 */
function addNewsPhotoFromUrl(gallery) {
    console.log('Přidání fotografie z URL pro aktuální události');
    
    // Zobrazení dialogu pro zadání URL
    const url = prompt('Zadejte URL obrázku:');
    if (!url) return;
    
    // Kontrola, zda URL je platná
    if (!url.match(/^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i)) {
        alert('Zadejte platnou URL obrázku (končící na .jpg, .png, .gif nebo .webp).');
        return;
    }
    
    console.log('Přidání obrázku z URL:', url);
    
    // Přidání fotografie do galerie
    addNewsPhotoToGallery(gallery, 'Obrázek z URL', url);
}

/**
 * Přidání fotografie do galerie
 * @param {HTMLElement} gallery - Element galerie
 * @param {string} title - Název fotografie
 * @param {string} imageUrl - URL obrázku
 */
function addNewsPhotoToGallery(gallery, title, imageUrl) {
    console.log('Přidání fotografie do galerie aktuálních událostí:', title, imageUrl);
    
    if (!gallery) {
        console.error('Galerie nebyla předána do addNewsPhotoToGallery');
        return;
    }
    
    // Vytvoření nové fotografie
    const photoId = `news-photo-${Date.now()}`;
    const photoHTML = `
        <div class="photo-item" id="${photoId}" data-image-url="${imageUrl}">
            <div class="photo-preview">
                <img src="${imageUrl}" alt="${title}" onerror="this.src='https://via.placeholder.com/150x150?text=Chyba+načítání'">
            </div>
            <div class="photo-info">
                <div class="photo-title">${title}</div>
                <div class="photo-actions">
                    <button type="button" class="photo-action-btn view-photo" data-photo-id="${photoId}" title="Zobrazit fotografii">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" class="photo-action-btn delete-photo" data-photo-id="${photoId}" title="Odstranit fotografii">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    // Přidání fotografie do galerie
    const addButton = gallery.querySelector('.photo-gallery-add');
    if (addButton) {
        gallery.insertBefore(document.createRange().createContextualFragment(photoHTML), addButton);
    } else {
        gallery.insertAdjacentHTML('beforeend', photoHTML);
    }
    
    // Přidání event listenerů pro tlačítka
    const photoElement = document.getElementById(photoId);
    if (!photoElement) {
        console.error('Nově přidaná fotografie nebyla nalezena v DOM');
        return;
    }
    
    const viewButton = photoElement.querySelector('.view-photo');
    if (viewButton) {
        viewButton.addEventListener('click', function() {
            const photoId = this.getAttribute('data-photo-id');
            const photoElement = document.getElementById(photoId);
            if (photoElement) {
                const imageUrl = photoElement.getAttribute('data-image-url');
                const photoTitle = photoElement.querySelector('.photo-title').textContent;
                showNewsPhotoViewer(imageUrl, photoTitle);
            }
        });
    }
    
    const deleteButton = photoElement.querySelector('.delete-photo');
    if (deleteButton) {
        deleteButton.addEventListener('click', function() {
            const photoId = this.getAttribute('data-photo-id');
            const photoElement = document.getElementById(photoId);
            if (photoElement && confirm('Opravdu chcete odstranit tuto fotografii?')) {
                photoElement.remove();
            }
        });
    }
}

/**
 * Zobrazení prohlížeče fotografií
 * @param {string} imageUrl - URL obrázku
 * @param {string} title - Název fotografie
 */
function showNewsPhotoViewer(imageUrl, title) {
    console.log('Zobrazení prohlížeče fotografií pro aktuální události:', title, imageUrl);
    
    // Vytvoření prohlížeče fotografií
    const viewer = document.createElement('div');
    viewer.className = 'photo-viewer';
    viewer.innerHTML = `
        <div class="photo-viewer-content">
            <div class="photo-viewer-header">
                <div class="photo-viewer-title">${title}</div>
                <div class="photo-viewer-actions">
                    <button type="button" class="photo-viewer-action-btn close-viewer" title="Zavřít">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="photo-viewer-body">
                <img src="${imageUrl}" alt="${title}" onerror="this.src='https://via.placeholder.com/800x600?text=Chyba+načítání'">
            </div>
        </div>
    `;
    
    // Přidání prohlížeče do stránky
    document.body.appendChild(viewer);
    
    // Přidání event listenerů pro tlačítka
    viewer.querySelector('.close-viewer').addEventListener('click', function() {
        viewer.remove();
    });
    
    // Přidání event listeneru pro kliknutí mimo obsah prohlížeče
    viewer.addEventListener('click', function(event) {
        if (event.target === viewer) {
            viewer.remove();
        }
    });
}

/**
 * Přidání zprávy do fotogalerie
 * @param {Object} news - Zpráva
 */
function addNewsToPhotoGallery(news) {
    console.log('Přidání zprávy do fotogalerie:', news);
    
    // Najít aktuální modul aktuálních událostí
    const newsModule = document.querySelector('.module[id^="module-aktualni-udalosti"]');
    if (!newsModule) {
        console.error('Modul aktuálních událostí nebyl nalezen');
        return;
    }
    
    // Najít galerii v modulu
    const gallery = newsModule.querySelector('.news-photo-gallery');
    if (!gallery) {
        console.error('Fotogalerie nebyla nalezena v modulu aktuálních událostí');
        return;
    }
    
    // Kontrola, zda zpráva obsahuje obrázek
    if (!news.imageUrl) {
        alert('Zpráva neobsahuje obrázek.');
        return;
    }
    
    // Přidání fotografie do galerie
    addNewsPhotoToGallery(gallery, news.title, news.imageUrl);
}

/**
 * Nastavení monitoringu
 */
function setupMonitoring() {
    console.log('Nastavení monitoringu');
    
    // Vytvoření dialogu pro nastavení monitoringu
    const dialog = document.createElement('div');
    dialog.className = 'monitoring-setup-dialog';
    dialog.innerHTML = `
        <div class="monitoring-setup-content">
            <div class="monitoring-setup-header">
                <h3>Nastavení monitoringu</h3>
                <button type="button" class="monitoring-setup-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="monitoring-setup-body">
                <div class="monitoring-setup-section">
                    <h4>Přidat klíčové slovo</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <input type="text" id="new-keyword" class="form-control" placeholder="Zadejte klíčové slovo">
                        </div>
                        <button type="button" class="btn-inline add-keyword">
                            <i class="fas fa-plus"></i> Přidat
                        </button>
                    </div>
                </div>
                
                <div class="monitoring-setup-section">
                    <h4>Přidat entitu</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <input type="text" id="new-entity" class="form-control" placeholder="Zadejte entitu">
                        </div>
                        <div class="form-group">
                            <select id="entity-type" class="form-control">
                                <option value="person">Osoba</option>
                                <option value="organization">Organizace</option>
                                <option value="location">Lokace</option>
                            </select>
                        </div>
                        <button type="button" class="btn-inline add-entity">
                            <i class="fas fa-plus"></i> Přidat
                        </button>
                    </div>
                </div>
                
                <div class="monitoring-setup-section">
                    <h4>Nastavení alertů</h4>
                    <div class="form-group">
                        <label>E-mail pro notifikace:</label>
                        <input type="email" id="alert-email" class="form-control" placeholder="Zadejte e-mail" value="${alertSettings.email}">
                    </div>
                    <div class="form-group">
                        <label>Frekvence notifikací:</label>
                        <select id="alert-frequency" class="form-control">
                            <option value="realtime" ${alertSettings.frequency === 'realtime' ? 'selected' : ''}>V reálném čase</option>
                            <option value="hourly" ${alertSettings.frequency === 'hourly' ? 'selected' : ''}>Každou hodinu</option>
                            <option value="daily" ${alertSettings.frequency === 'daily' ? 'selected' : ''}>Denně</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Minimální počet zmínek pro odeslání alertu:</label>
                        <input type="number" id="alert-threshold" class="form-control" min="1" max="100" value="${alertSettings.threshold}">
                    </div>
                </div>
                
                <div class="monitoring-setup-actions">
                    <button type="button" class="btn-inline save-monitoring-settings">
                        <i class="fas fa-save"></i> Uložit nastavení
                    </button>
                </div>
            </div>
        </div>
    `;
    
    // Přidání dialogu do stránky
    document.body.appendChild(dialog);
    
    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.monitoring-setup-close').addEventListener('click', function() {
        dialog.remove();
    });
    
    // Event listener pro přidání klíčového slova
    const addKeywordButton = dialog.querySelector('.add-keyword');
    if (addKeywordButton) {
        addKeywordButton.addEventListener('click', function() {
            const keywordInput = dialog.querySelector('#new-keyword');
            if (keywordInput && keywordInput.value.trim()) {
                const keyword = keywordInput.value.trim();
                
                // Přidání klíčového slova do monitoringu
                if (!activeNewsFilters.keywords.includes(keyword)) {
                    activeNewsFilters.keywords.push(keyword);
                    
                    // Aktualizace UI
                    updateMonitoringUI();
                    
                    // Vyčištění inputu
                    keywordInput.value = '';
                    
                    // Informace pro uživatele
                    alert(`Klíčové slovo "${keyword}" bylo přidáno do monitoringu.`);
                } else {
                    alert(`Klíčové slovo "${keyword}" je již v monitoringu.`);
                }
            } else {
                alert('Zadejte klíčové slovo.');
            }
        });
    }
    
    // Event listener pro přidání entity
    const addEntityButton = dialog.querySelector('.add-entity');
    if (addEntityButton) {
        addEntityButton.addEventListener('click', function() {
            const entityInput = dialog.querySelector('#new-entity');
            const entityTypeSelect = dialog.querySelector('#entity-type');
            
            if (entityInput && entityInput.value.trim() && entityTypeSelect) {
                const entity = entityInput.value.trim();
                const type = entityTypeSelect.value;
                
                // Přidání entity do monitoringu
                addEntityToMonitoring(entity, type);
                
                // Vyčištění inputu
                entityInput.value = '';
            } else {
                alert('Zadejte entitu a vyberte typ.');
            }
        });
    }
    
    // Event listener pro uložení nastavení alertů
    const saveSettingsButton = dialog.querySelector('.save-monitoring-settings');
    if (saveSettingsButton) {
        saveSettingsButton.addEventListener('click', function() {
            const emailInput = dialog.querySelector('#alert-email');
            const frequencySelect = dialog.querySelector('#alert-frequency');
            const thresholdInput = dialog.querySelector('#alert-threshold');
            
            if (emailInput && frequencySelect && thresholdInput) {
                // Aktualizace nastavení alertů
                alertSettings.email = emailInput.value.trim();
                alertSettings.frequency = frequencySelect.value;
                alertSettings.threshold = parseInt(thresholdInput.value) || 3;
                
                // Informace pro uživatele
                alert('Nastavení monitoringu bylo uloženo.');
                
                // Zavření dialogu
                dialog.remove();
            }
        });
    }
    
    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}
