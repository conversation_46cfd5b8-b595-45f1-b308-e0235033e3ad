"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, Server, CheckCircle, XCircle, AlertTriangle, Globe } from "lucide-react";

interface DomainValidatorProps {
  initialDomain: string;
  onValidationComplete: (results: any) => void;
  savedResults?: any;
}

export function DomainValidator({ initialDomain, onValidationComplete, savedResults }: DomainValidatorProps) {
  const [domain, setDomain] = useState(initialDomain);
  const [isValidating, setIsValidating] = useState(false);
  const [validationResults, setValidationResults] = useState<any>(savedResults || null);
  const [error, setError] = useState<string | null>(null);

  // Použití uložených v<PERSON>d<PERSON> validace, pokud jsou k dispozici
  useEffect(() => {
    if (savedResults) {
      setValidationResults(savedResults);
    }
  }, [savedResults]);

  const validateDomain = async () => {
    if (!domain) {
      setError("Zadejte doménu pro validaci");
      return;
    }

    setIsValidating(true);
    setError(null);

    try {
      // Validace formátu domény
      const domainRegex = /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/i;
      const isValid = domainRegex.test(domain);

      let results: any = {
        isValid: isValid,
        domain: domain
      };

      if (!isValid) {
        setValidationResults(results);
        onValidationComplete(results);
        return;
      }

      // Kontrola existence domény pomocí DNS lookup
      try {
        // Použijeme Google DNS API pro kontrolu existence domény
        const response = await fetch(`https://dns.google/resolve?name=${domain}`);
        const data = await response.json();

        const exists = data.Status === 0; // Status 0 znamená, že doména existuje
        results.exists = exists;

        // Pokud doména existuje, získáme informace o IP adrese
        if (exists && data.Answer && data.Answer.length > 0) {
          const ipAddresses = data.Answer
            .filter((record: any) => record.type === 1 || record.type === 28) // A nebo AAAA záznamy
            .map((record: any) => record.data);

          results.ipAddresses = ipAddresses;

          if (ipAddresses.length > 0) {
            // Získání informací o první IP adrese
            const ipResponse = await fetch(`https://ipinfo.io/${ipAddresses[0]}/json`);
            const ipData = await ipResponse.json();

            if (ipData) {
              results.geoLocation = {
                country: ipData.country || '',
                region: ipData.region || '',
                city: ipData.city || '',
                organization: ipData.org || ''
              };
            }
          }

          // Získání MX záznamů
          const mxResponse = await fetch(`https://dns.google/resolve?name=${domain}&type=MX`);
          const mxData = await mxResponse.json();

          if (mxData.Status === 0 && mxData.Answer && mxData.Answer.length > 0) {
            results.mxRecords = mxData.Answer.map((record: any) => record.data);
          }

          // Získání NS záznamů
          const nsResponse = await fetch(`https://dns.google/resolve?name=${domain}&type=NS`);
          const nsData = await nsResponse.json();

          if (nsData.Status === 0 && nsData.Answer && nsData.Answer.length > 0) {
            results.nsRecords = nsData.Answer.map((record: any) => record.data);
          }
        }
      } catch (dnsError) {
        console.error("Chyba při DNS lookup:", dnsError);
        results.exists = false;
        results.dnsError = true;
      }

      setValidationResults(results);
      onValidationComplete(results);
    } catch (err) {
      setError("Chyba při validaci domény");
      console.error("Domain validation error:", err);
    } finally {
      setIsValidating(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Validace domény</CardTitle>
        <CardDescription>
          Ověřte platnost domény a získejte informace o jejích DNS záznamech
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-4">
          <div className="flex space-x-2">
            <Input
              value={domain}
              onChange={(e) => setDomain(e.target.value)}
              placeholder="Zadejte doménu (např. example.com)"
              disabled={isValidating}
            />
            <Button onClick={validateDomain} disabled={isValidating}>
              {isValidating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Validace...
                </>
              ) : (
                "Validovat"
              )}
            </Button>
          </div>

          {error && (
            <div className="bg-destructive/10 p-3 rounded-md text-destructive flex items-center">
              <XCircle className="h-5 w-5 mr-2" />
              {error}
            </div>
          )}

          {validationResults && (
            <div className="border rounded-md p-4 space-y-4">
              <div className="flex items-center">
                <Server className="h-5 w-5 mr-2 text-primary" />
                <h3 className="text-lg font-semibold">Výsledky validace</h3>
              </div>

              <div className="flex items-center space-x-2">
                <span className="font-medium">Formát:</span>
                {validationResults.isValid ? (
                  <div className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                    <Badge className="bg-green-500">Platný formát</Badge>
                  </div>
                ) : (
                  <div className="flex items-center">
                    <XCircle className="h-4 w-4 text-destructive mr-1" />
                    <Badge variant="destructive">Neplatný formát</Badge>
                  </div>
                )}
              </div>

              {validationResults.isValid && (
                <>
                  <div className="flex items-center space-x-2">
                    <span className="font-medium">Existence:</span>
                    {validationResults.exists ? (
                      <div className="flex items-center">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                        <Badge className="bg-green-500">Doména existuje</Badge>
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <XCircle className="h-4 w-4 text-destructive mr-1" />
                        <Badge variant="destructive">Doména neexistuje</Badge>
                      </div>
                    )}
                  </div>

                  {validationResults.exists && validationResults.ipAddresses && validationResults.ipAddresses.length > 0 && (
                    <>
                      <div className="border-t pt-3">
                        <h4 className="font-semibold mb-2">IP adresy</h4>
                        <div className="space-y-2">
                          {validationResults.ipAddresses.map((ip: string, index: number) => (
                            <div key={index} className="flex items-center space-x-2">
                              <Globe className="h-4 w-4 text-primary" />
                              <span>{ip}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {validationResults.geoLocation && (
                        <div className="border-t pt-3">
                          <h4 className="font-semibold mb-2">Geolokace (hlavní IP)</h4>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                            <div>
                              <span className="font-medium">Země:</span> {validationResults.geoLocation.country || 'Neuvedeno'}
                            </div>
                            <div>
                              <span className="font-medium">Region:</span> {validationResults.geoLocation.region || 'Neuvedeno'}
                            </div>
                            <div>
                              <span className="font-medium">Město:</span> {validationResults.geoLocation.city || 'Neuvedeno'}
                            </div>
                            <div>
                              <span className="font-medium">Organizace:</span> {validationResults.geoLocation.organization || 'Neuvedeno'}
                            </div>
                          </div>
                        </div>
                      )}

                      {validationResults.nsRecords && validationResults.nsRecords.length > 0 && (
                        <div className="border-t pt-3">
                          <h4 className="font-semibold mb-2">NS záznamy</h4>
                          <div className="space-y-2">
                            {validationResults.nsRecords.map((ns: string, index: number) => (
                              <div key={index} className="flex items-center space-x-2">
                                <Server className="h-4 w-4 text-primary" />
                                <span>{ns}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {validationResults.mxRecords && validationResults.mxRecords.length > 0 && (
                        <div className="border-t pt-3">
                          <h4 className="font-semibold mb-2">MX záznamy</h4>
                          <div className="space-y-2">
                            {validationResults.mxRecords.map((mx: string, index: number) => (
                              <div key={index} className="flex items-center space-x-2">
                                <Server className="h-4 w-4 text-primary" />
                                <span>{mx}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </>
              )}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <p className="text-sm text-muted-foreground">
          Poznámka: Validace používá Google DNS API pro ověření existence domény.
        </p>
      </CardFooter>
    </Card>
  );
}
