"use client";

import type { SVGProps } from "react";
import { cn } from "@/lib/utils";

interface ProgressRingProps extends SVGProps<SVGSVGElement> {
  radius?: number;
  strokeWidth?: number;
  progress: number; // 0 to 100
  progressColorClass?: string;
  bgColorClass?: string;
  textColorClass?: string;
  showText?: boolean;
}

export function ProgressRing({
  radius = 26,
  strokeWidth = 4,
  progress,
  className,
  progressColorClass = "stroke-success", // Corresponds to var(--success) in HTML example
  bgColorClass = "stroke-border", // Corresponds to var(--gray-200)
  textColorClass = "fill-foreground", // Corresponds to var(--gray-700)
  showText = true,
  ...props
}: ProgressRingProps) {
  const normalizedRadius = radius;
  const circumference = normalizedRadius * 2 * Math.PI;
  const strokeDashoffset = circumference - (progress / 100) * circumference;

  const size = (radius + strokeWidth) * 2;

  return (
    <div className={cn("relative", className)} style={{ width: size, height: size }}>
      <svg height={size} width={size} viewBox={`0 0 ${size} ${size}`} {...props} className="-rotate-90">
        <circle
          className={cn("transition-all duration-300 ease-linear", bgColorClass)}
          strokeWidth={strokeWidth}
          fill="transparent"
          r={normalizedRadius}
          cx={radius + strokeWidth}
          cy={radius + strokeWidth}
        />
        <circle
          className={cn("transition-all duration-300 ease-linear", progressColorClass)}
          strokeWidth={strokeWidth}
          strokeDasharray={`${circumference} ${circumference}`}
          style={{ strokeDashoffset }}
          strokeLinecap="round"
          fill="transparent"
          r={normalizedRadius}
          cx={radius + strokeWidth}
          cy={radius + strokeWidth}
        />
      </svg>
      {showText && (
        <div
          className={cn(
            "absolute inset-0 flex items-center justify-center text-xs font-semibold",
            textColorClass
          )}
        >
          {`${Math.round(progress)}%`}
        </div>
      )}
    </div>
  );
}
