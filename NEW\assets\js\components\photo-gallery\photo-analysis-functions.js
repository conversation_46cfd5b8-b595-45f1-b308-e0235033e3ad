/**
 * Fotografická analýza - analytick<PERSON>
 */

/**
 * Analýza fotografie
 * @param {string} photoId - ID fotografie
 * @param {string} imageUrl - URL obrázku
 */
function analyzePhoto(photoId, imageUrl) {
    console.log('Analýza fotografie:', photoId, imageUrl);

    // Zobrazení dialogu pro analýzu fotografie
    const dialog = document.createElement('div');
    dialog.className = 'photo-analysis-dialog';
    dialog.innerHTML = `
        <div class="photo-analysis-dialog-content">
            <div class="photo-analysis-dialog-header">
                <h3>Analýza fotografie</h3>
                <button type="button" class="photo-analysis-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="photo-analysis-dialog-body">
                <div class="photo-analysis-preview">
                    <img src="${imageUrl}" alt="Analyzovaná fotografie" onerror="this.src='https://via.placeholder.com/400x300?text=Chyba+načítání'">
                </div>
                <div class="photo-analysis-options">
                    <div class="photo-analysis-option" data-analysis="exif">
                        <div class="photo-analysis-option-icon">
                            <i class="fas fa-info-circle"></i>
                        </div>
                        <div class="photo-analysis-option-title">EXIF Metadata</div>
                        <div class="photo-analysis-option-description">Extrakce EXIF dat, GPS souřadnic a technických informací</div>
                    </div>
                    <div class="photo-analysis-option" data-analysis="reverse">
                        <div class="photo-analysis-option-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="photo-analysis-option-title">Reverzní vyhledávání</div>
                        <div class="photo-analysis-option-description">Vyhledání podobných obrázků na internetu</div>
                    </div>
                    <div class="photo-analysis-option" data-analysis="faces">
                        <div class="photo-analysis-option-icon">
                            <i class="fas fa-user"></i>
                        </div>
                        <div class="photo-analysis-option-title">Detekce obličejů</div>
                        <div class="photo-analysis-option-description">Identifikace obličejů a odhad věku, pohlaví a emocí</div>
                    </div>
                    <div class="photo-analysis-option" data-analysis="objects">
                        <div class="photo-analysis-option-icon">
                            <i class="fas fa-cube"></i>
                        </div>
                        <div class="photo-analysis-option-title">Objektová analýza</div>
                        <div class="photo-analysis-option-description">Identifikace objektů, scén a aktivit na fotografii</div>
                    </div>
                    <div class="photo-analysis-option" data-analysis="text">
                        <div class="photo-analysis-option-icon">
                            <i class="fas fa-font"></i>
                        </div>
                        <div class="photo-analysis-option-title">Extrakce textu (OCR)</div>
                        <div class="photo-analysis-option-description">Rozpoznání a extrakce textu z fotografie</div>
                    </div>
                    <div class="photo-analysis-option" data-analysis="geolocation">
                        <div class="photo-analysis-option-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="photo-analysis-option-title">Geolokace</div>
                        <div class="photo-analysis-option-description">Analýza lokace na základě vizuálních prvků</div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Přidání dialogu do stránky
    document.body.appendChild(dialog);

    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.photo-analysis-dialog-close').addEventListener('click', function() {
        dialog.remove();
    });

    // Přidání event listenerů pro možnosti analýzy
    dialog.querySelectorAll('.photo-analysis-option').forEach(option => {
        option.addEventListener('click', function() {
            const analysisType = this.getAttribute('data-analysis');
            dialog.remove();

            // Provedení vybrané analýzy
            switch (analysisType) {
                case 'exif':
                    extractExifDataFromUrl(imageUrl);
                    break;
                case 'reverse':
                    reverseImageSearchUrl(imageUrl);
                    break;
                case 'faces':
                    detectFacesInImage(imageUrl);
                    break;
                case 'objects':
                    analyzeObjectsInImage(imageUrl);
                    break;
                case 'text':
                    extractTextFromImage(imageUrl);
                    break;
                case 'geolocation':
                    geolocateImageByContent(imageUrl);
                    break;
            }
        });
    });

    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}

/**
 * Extrakce EXIF dat
 */
function extractExifData() {
    console.log('Extrakce EXIF dat');

    // Najít aktuální modul fotografické analýzy
    const photoModule = document.querySelector('.module[id^="module-foto-analyza"]');
    if (!photoModule) {
        console.error('Modul fotografické analýzy nebyl nalezen');
        return;
    }

    // Kontrola, zda je vybrána fotografie
    // Nejprve zkusíme najít vybranou fotografii
    let selectedPhoto = photoModule.querySelector('.photo-item.selected');

    // Pokud není vybrána žádná fotografie, zkusíme najít první fotografii v galerii
    if (!selectedPhoto) {
        selectedPhoto = photoModule.querySelector('.photo-item');
        if (!selectedPhoto) {
            alert('Nejprve přidejte fotografii do galerie.');
            return;
        }

        // Označíme první fotografii jako vybranou
        selectedPhoto.classList.add('selected');
    }

    const imageUrl = selectedPhoto.getAttribute('data-image-url');
    if (!imageUrl) {
        alert('Nelze získat URL fotografie.');
        return;
    }

    // Extrakce EXIF dat z URL
    extractExifDataFromUrl(imageUrl);
}

/**
 * Extrakce EXIF dat z URL
 * @param {string} imageUrl - URL obrázku
 */
function extractExifDataFromUrl(imageUrl) {
    console.log('Extrakce EXIF dat z URL:', imageUrl);

    // Zobrazení načítání
    const loadingDialog = document.createElement('div');
    loadingDialog.className = 'loading-dialog';
    loadingDialog.innerHTML = `
        <div class="loading-dialog-content">
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
            <div class="loading-message">Extrakce EXIF dat...</div>
        </div>
    `;
    document.body.appendChild(loadingDialog);

    // Pro lokální soubory (blob URL) je potřeba jiný přístup
    if (imageUrl.startsWith('blob:')) {
        // Pro blob URL nemůžeme přímo extrahovat EXIF data
        // Musíme použít File API nebo Canvas
        loadingDialog.remove();
        alert('Pro extrakci EXIF dat z lokálního souboru použijte funkci "Extrahovat EXIF data" přímo na nahraném souboru.');
        return;
    }

    // Pro online obrázky můžeme použít externí služby
    // Zde použijeme exif.tools API (veřejná služba)
    fetch(`https://exif.tools/api/metadata?url=${encodeURIComponent(imageUrl)}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se získat EXIF data.');
            }
            return response.json();
        })
        .then(data => {
            loadingDialog.remove();
            displayExifData(data, imageUrl);
        })
        .catch(error => {
            console.error('Chyba při extrakci EXIF dat:', error);
            loadingDialog.remove();

            // Zobrazení alternativních možností
            const errorDialog = document.createElement('div');
            errorDialog.className = 'error-dialog';
            errorDialog.innerHTML = `
                <div class="error-dialog-content">
                    <div class="error-dialog-header">
                        <h3>Nepodařilo se extrahovat EXIF data</h3>
                        <button type="button" class="error-dialog-close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="error-dialog-body">
                        <p>Pro extrakci EXIF dat můžete použít některou z následujících online služeb:</p>
                        <div class="external-links">
                            <a href="https://exif.tools/" target="_blank" class="btn-inline">
                                <i class="fas fa-external-link-alt"></i> EXIF.tools
                            </a>
                            <a href="https://exifdata.com/" target="_blank" class="btn-inline">
                                <i class="fas fa-external-link-alt"></i> ExifData.com
                            </a>
                            <a href="http://exif-viewer.com/" target="_blank" class="btn-inline">
                                <i class="fas fa-external-link-alt"></i> EXIF-Viewer.com
                            </a>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(errorDialog);

            // Přidání event listenerů pro tlačítka
            errorDialog.querySelector('.error-dialog-close').addEventListener('click', function() {
                errorDialog.remove();
            });

            // Přidání event listeneru pro kliknutí mimo obsah dialogu
            errorDialog.addEventListener('click', function(event) {
                if (event.target === errorDialog) {
                    errorDialog.remove();
                }
            });
        });
}

/**
 * Extrakce EXIF dat z File objektu
 * @param {File} file - File objekt
 */
function extractExifDataFromFile(file) {
    console.log('Extrakce EXIF dat z File objektu:', file.name);

    // Použití FileReader pro čtení souboru
    const reader = new FileReader();
    reader.onload = function(e) {
        const arrayBuffer = e.target.result;

        // Použití exif-js knihovny (musí být načtena)
        if (typeof EXIF !== 'undefined') {
            EXIF.getData(file, function() {
                const exifData = EXIF.getAllTags(this);
                displayExifData(exifData, URL.createObjectURL(file));
            });
        } else {
            // Pokud není k dispozici exif-js knihovna, zobrazíme alternativní možnosti
            const errorDialog = document.createElement('div');
            errorDialog.className = 'error-dialog';
            errorDialog.innerHTML = `
                <div class="error-dialog-content">
                    <div class="error-dialog-header">
                        <h3>Nepodařilo se extrahovat EXIF data</h3>
                        <button type="button" class="error-dialog-close">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="error-dialog-body">
                        <p>Pro extrakci EXIF dat můžete použít některou z následujících online služeb:</p>
                        <div class="external-links">
                            <a href="https://exif.tools/" target="_blank" class="btn-inline">
                                <i class="fas fa-external-link-alt"></i> EXIF.tools
                            </a>
                            <a href="https://exifdata.com/" target="_blank" class="btn-inline">
                                <i class="fas fa-external-link-alt"></i> ExifData.com
                            </a>
                            <a href="http://exif-viewer.com/" target="_blank" class="btn-inline">
                                <i class="fas fa-external-link-alt"></i> EXIF-Viewer.com
                            </a>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(errorDialog);

            // Přidání event listenerů pro tlačítka
            errorDialog.querySelector('.error-dialog-close').addEventListener('click', function() {
                errorDialog.remove();
            });

            // Přidání event listeneru pro kliknutí mimo obsah dialogu
            errorDialog.addEventListener('click', function(event) {
                if (event.target === errorDialog) {
                    errorDialog.remove();
                }
            });
        }
    };
    reader.readAsArrayBuffer(file);
}

/**
 * Zobrazení EXIF dat
 * @param {Object} exifData - EXIF data
 * @param {string} imageUrl - URL obrázku
 */
function displayExifData(exifData, imageUrl) {
    console.log('Zobrazení EXIF dat:', exifData);

    // Vytvoření dialogu pro zobrazení EXIF dat
    const dialog = document.createElement('div');
    dialog.className = 'exif-dialog';

    // Příprava obsahu dialogu
    let exifContent = '';

    // Kontrola, zda jsou k dispozici EXIF data
    if (!exifData || Object.keys(exifData).length === 0) {
        exifContent = `
            <div class="exif-empty">
                <i class="fas fa-exclamation-circle"></i>
                <p>Nebyly nalezeny žádné EXIF metadata.</p>
            </div>
        `;
    } else {
        // Zpracování GPS souřadnic, pokud jsou k dispozici
        let gpsHtml = '';
        if (exifData.GPSLatitude && exifData.GPSLongitude) {
            const lat = convertDMSToDD(exifData.GPSLatitude, exifData.GPSLatitudeRef);
            const lng = convertDMSToDD(exifData.GPSLongitude, exifData.GPSLongitudeRef);

            gpsHtml = `
                <div class="exif-gps">
                    <h4>GPS Lokace</h4>
                    <div class="exif-gps-coordinates">
                        <div class="exif-gps-label">Souřadnice:</div>
                        <div class="exif-gps-value">${lat}, ${lng}</div>
                    </div>
                    <div class="exif-gps-map">
                        <iframe width="100%" height="300" frameborder="0" scrolling="no" marginheight="0" marginwidth="0"
                        src="https://www.openstreetmap.org/export/embed.html?bbox=${lng-0.01},${lat-0.01},${lng+0.01},${lat+0.01}&layer=mapnik&marker=${lat},${lng}"
                        style="border: 1px solid #ddd; border-radius: 4px;"></iframe>
                    </div>
                </div>
            `;
        }

        // Zpracování základních informací
        const basicInfo = `
            <div class="exif-section">
                <h4>Základní informace</h4>
                <table class="exif-table">
                    <tr>
                        <td>Datum a čas pořízení:</td>
                        <td>${exifData.DateTimeOriginal || exifData.DateTime || 'Není k dispozici'}</td>
                    </tr>
                    <tr>
                        <td>Výrobce fotoaparátu:</td>
                        <td>${exifData.Make || 'Není k dispozici'}</td>
                    </tr>
                    <tr>
                        <td>Model fotoaparátu:</td>
                        <td>${exifData.Model || 'Není k dispozici'}</td>
                    </tr>
                    <tr>
                        <td>Software:</td>
                        <td>${exifData.Software || 'Není k dispozici'}</td>
                    </tr>
                    <tr>
                        <td>Autor:</td>
                        <td>${exifData.Artist || exifData.Copyright || 'Není k dispozici'}</td>
                    </tr>
                </table>
            </div>
        `;

        // Zpracování technických informací
        const technicalInfo = `
            <div class="exif-section">
                <h4>Technické informace</h4>
                <table class="exif-table">
                    <tr>
                        <td>Rozměry:</td>
                        <td>${exifData.PixelXDimension || '?'} x ${exifData.PixelYDimension || '?'} px</td>
                    </tr>
                    <tr>
                        <td>Clona:</td>
                        <td>${exifData.FNumber ? 'f/' + exifData.FNumber : 'Není k dispozici'}</td>
                    </tr>
                    <tr>
                        <td>Expoziční čas:</td>
                        <td>${exifData.ExposureTime || 'Není k dispozici'}</td>
                    </tr>
                    <tr>
                        <td>ISO:</td>
                        <td>${exifData.ISOSpeedRatings || 'Není k dispozici'}</td>
                    </tr>
                    <tr>
                        <td>Ohnisková vzdálenost:</td>
                        <td>${exifData.FocalLength ? exifData.FocalLength + ' mm' : 'Není k dispozici'}</td>
                    </tr>
                </table>
            </div>
        `;

        // Sestavení obsahu dialogu
        exifContent = `
            <div class="exif-preview">
                <img src="${imageUrl}" alt="Fotografie" onerror="this.src='https://via.placeholder.com/400x300?text=Chyba+načítání'">
            </div>
            ${gpsHtml}
            ${basicInfo}
            ${technicalInfo}
        `;
    }

    // Sestavení dialogu
    dialog.innerHTML = `
        <div class="exif-dialog-content">
            <div class="exif-dialog-header">
                <h3>EXIF Metadata</h3>
                <button type="button" class="exif-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="exif-dialog-body">
                ${exifContent}
            </div>
        </div>
    `;

    // Přidání dialogu do stránky
    document.body.appendChild(dialog);

    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.exif-dialog-close').addEventListener('click', function() {
        dialog.remove();
    });

    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}

/**
 * Konverze souřadnic z formátu DMS (Degrees, Minutes, Seconds) do DD (Decimal Degrees)
 * @param {Array} dms - Pole hodnot [stupně, minuty, vteřiny]
 * @param {string} ref - Reference (N, S, E, W)
 * @returns {number} - Souřadnice ve formátu DD
 */
function convertDMSToDD(dms, ref) {
    if (!dms || dms.length < 3) return 0;

    let degrees = dms[0];
    let minutes = dms[1];
    let seconds = dms[2];

    let dd = degrees + (minutes / 60) + (seconds / 3600);

    // Jižní a západní souřadnice jsou záporné
    if (ref === 'S' || ref === 'W') {
        dd = -dd;
    }

    return dd;
}
