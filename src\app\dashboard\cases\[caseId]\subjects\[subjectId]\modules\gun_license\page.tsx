"use client";

import { useEffect, useState } from 'react';
import { GunLicenseForm } from '@/components/modules/gun-license/GunLicenseForm';
import { Button } from '@/components/ui/button';
import { ChevronLeft, Target } from 'lucide-react';
import Link from 'next/link';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { PhysicalPersonSubject, GunLicenseModuleData } from '@/types';
import { useRouter, useParams } from 'next/navigation';
import { toast } from '@/hooks/use-toast';

export default function GunLicensePage() {
  const params = useParams();
  const caseId = params.caseId as string;
  const subjectId = params.subjectId as string;

  const router = useRouter();
  const [subject, setSubject] = useState<PhysicalPersonSubject | null>(null);
  const [existingData, setExistingData] = useState<GunLicenseModuleData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        // Načtení informací o subjektu
        const subjectDoc = await getDoc(doc(db, 'cases', caseId, 'subjects', subjectId));
        if (subjectDoc.exists()) {
          setSubject(subjectDoc.data() as PhysicalPersonSubject);
        }

        // Načtení existujících dat modulu
        const moduleDoc = await getDoc(doc(db, 'cases', caseId, 'subjects', subjectId, 'modules', 'gun_license'));
        if (moduleDoc.exists()) {
          setExistingData(moduleDoc.data() as GunLicenseModuleData);
        }

      } catch (error) {
        console.error('Chyba při načítání dat:', error);
        toast({
          title: "Chyba",
          description: "Nepodařilo se načíst data. Zkuste to prosím znovu.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [caseId, subjectId]);

  const handleSave = (moduleId: string, wasNew: boolean) => {
    toast({
      title: wasNew ? "Modul vytvořen" : "Modul aktualizován",
      description: `Zbrojní průkaz byl úspěšně ${wasNew ? 'vytvořen' : 'aktualizován'}.`,
    });
    
    // Přesměrování zpět na detail subjektu
    router.push(`/dashboard/cases/${caseId}/subjects/${subjectId}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <Target className="mx-auto h-8 w-8 animate-spin text-primary" />
          <p className="mt-2 text-sm text-muted-foreground">Načítám data modulu...</p>
        </div>
      </div>
    );
  }

  if (!subject) {
    return (
      <div className="text-center py-8">
        <p className="text-destructive">Subjekt nebyl nalezen.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4 mb-6">
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/dashboard/cases/${caseId}/subjects/${subjectId}`}>
            <ChevronLeft className="h-4 w-4 mr-1" />
            Zpět na detail subjektu
          </Link>
        </Button>
        <div className="flex items-center space-x-2">
          <Target className="h-5 w-5 text-primary" />
          <h1 className="text-2xl font-bold">Zbrojní průkaz</h1>
        </div>
      </div>

      <GunLicenseForm
        caseId={caseId}
        subject={subject}
        existingData={existingData}
        onSave={handleSave}
      />
    </div>
  );
} 