"use client";

import { Control, useFieldArray } from "react-hook-form";
import { NetworkAnalysisModuleFormValues } from "./schemas";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Globe, MoreHorizontal, Plus, Trash2, Edit, Eye } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { formatDate } from "@/lib/utils";

interface IpAddressListProps {
  control: Control<NetworkAnalysisModuleFormValues>;
  onSelectIp: (index: number) => void;
  onAddIp: () => void;
}

export function IpAddressList({ control, onSelectIp, onAddIp }: IpAddressListProps) {
  const { fields, remove } = useFieldArray({
    control,
    name: "ipAddresses",
  });

  const handleDeleteIp = (index: number) => {
    if (confirm("Opravdu chcete smazat tuto IP adresu?")) {
      remove(index);
    }
  };

  const getVerificationStatusBadge = (status: string) => {
    switch (status) {
      case "verified":
        return <Badge className="bg-green-500">Ověřeno</Badge>;
      case "unverified":
        return <Badge variant="outline">Neověřeno</Badge>;
      case "invalid":
        return <Badge variant="destructive">Neplatné</Badge>;
      case "suspicious":
        return <Badge className="bg-yellow-500">Podezřelé</Badge>;
      default:
        return <Badge variant="secondary">Neznámý stav</Badge>;
    }
  };

  const getIpTypeBadge = (type: string) => {
    switch (type) {
      case "ipv4":
        return <Badge variant="secondary">IPv4</Badge>;
      case "ipv6":
        return <Badge variant="secondary">IPv6</Badge>;
      default:
        return <Badge variant="secondary">Neznámý typ</Badge>;
    }
  };

  const getUsageBadge = (usage?: string) => {
    if (!usage) return null;
    
    switch (usage) {
      case "residential":
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-300">Rezidenční</Badge>;
      case "business":
        return <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-300">Firemní</Badge>;
      case "hosting":
        return <Badge variant="outline" className="bg-green-100 text-green-800 border-green-300">Hosting</Badge>;
      case "vpn":
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">VPN</Badge>;
      case "proxy":
        return <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-300">Proxy</Badge>;
      case "tor_exit_node":
        return <Badge variant="outline" className="bg-red-100 text-red-800 border-red-300">Tor Exit Node</Badge>;
      case "mobile":
        return <Badge variant="outline" className="bg-indigo-100 text-indigo-800 border-indigo-300">Mobilní</Badge>;
      case "datacenter":
        return <Badge variant="outline" className="bg-teal-100 text-teal-800 border-teal-300">Datacenter</Badge>;
      default:
        return <Badge variant="outline">Jiné</Badge>;
    }
  };

  if (fields.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>IP adresy</CardTitle>
          <CardDescription>
            Zatím nebyly přidány žádné IP adresy
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <Globe className="h-16 w-16 text-muted-foreground mb-4" />
          <p className="text-muted-foreground mb-4 text-center">
            Přidejte IP adresy spojené s tímto subjektem pro analýzu síťové aktivity
          </p>
          <Button onClick={onAddIp}>
            <Plus className="mr-2 h-4 w-4" />
            Přidat IP adresu
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>IP adresy</CardTitle>
        <CardDescription>
          Seznam IP adres spojených se subjektem
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>IP adresa</TableHead>
              <TableHead>Typ</TableHead>
              <TableHead>Využití</TableHead>
              <TableHead>Stav ověření</TableHead>
              <TableHead>Datum objevení</TableHead>
              <TableHead>Lokalita</TableHead>
              <TableHead className="text-right">Akce</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {fields.map((ip, index) => (
              <TableRow key={ip.id} className="cursor-pointer hover:bg-muted/50" onClick={() => {
                setTimeout(() => {
                  onSelectIp(index);
                }, 0);
              }}>
                <TableCell className="font-medium">{ip.ipAddress || "Neuvedeno"}</TableCell>
                <TableCell>{getIpTypeBadge(ip.ipType)}</TableCell>
                <TableCell>{getUsageBadge(ip.usage)}</TableCell>
                <TableCell>{getVerificationStatusBadge(ip.verificationStatus)}</TableCell>
                <TableCell>{ip.discoveryDate ? formatDate(ip.discoveryDate) : "Neuvedeno"}</TableCell>
                <TableCell>
                  {ip.geoLocation?.country ? 
                    `${ip.geoLocation.country}${ip.geoLocation.city ? `, ${ip.geoLocation.city}` : ''}` : 
                    "Neuvedeno"}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        setTimeout(() => {
                          onSelectIp(index);
                        }, 0);
                      }}>
                        <Eye className="mr-2 h-4 w-4" />
                        Zobrazit detail
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        setTimeout(() => {
                          onSelectIp(index);
                        }, 0);
                      }}>
                        <Edit className="mr-2 h-4 w-4" />
                        Upravit
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        className="text-destructive focus:text-destructive"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteIp(index);
                        }}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Smazat
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
