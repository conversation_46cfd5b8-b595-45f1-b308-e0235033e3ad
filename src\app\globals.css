@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 0 0% 94%; /* #f0f0f0 light gray */
    --foreground: 0 0% 10%; /* dark gray for contrast */

    --card: 0 0% 100%; /* white */
    --card-foreground: 0 0% 10%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 10%;

    --primary: 220 100% 30%; /* #003399 deep blue */
    --primary-foreground: 0 0% 98%; /* light color for dark primary */

    --secondary: 0 0% 90%; /* slightly darker than background */
    --secondary-foreground: 0 0% 10%;

    --muted: 0 0% 85%;
    --muted-foreground: 0 0% 40%; /* Adjusted for better contrast on muted */

    --accent: 195 37% 44%; /* #468499 muted teal */
    --accent-foreground: 0 0% 98%; /* light color for accent */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 87%; /* Adjusted for better visibility */
    --input: 0 0% 87%; /* Adjusted for better visibility */
    --ring: 220 100% 30%; /* primary color for focus rings */

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    /* Sidebar variables remain for component compatibility, but might not be used immediately */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Basic dark theme, can be expanded later */
    --background: 0 0% 10%;
    --foreground: 0 0% 94%;

    --card: 0 0% 12%;
    --card-foreground: 0 0% 94%;

    --popover: 0 0% 12%;
    --popover-foreground: 0 0% 94%;

    --primary: 220 100% 50%; /* Lighter blue for dark mode */
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 15%;
    --secondary-foreground: 0 0% 94%;

    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 60%;

    --accent: 195 37% 54%; /* Lighter teal for dark mode */
    --accent-foreground: 0 0% 98%;

    --destructive: 0 70% 50%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 25%;
    --input: 0 0% 25%;
    --ring: 220 100% 50%;

    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Leaflet CSS */
.leaflet-container {
  width: 100%;
  height: 100%;
  z-index: 1;
}

.leaflet-control-container .leaflet-control {
  z-index: 1000 !important;
}

.leaflet-popup-content-wrapper {
  @apply bg-card text-card-foreground rounded-md shadow-md;
}

.leaflet-popup-content {
  @apply p-2;
}

.leaflet-popup-tip {
  @apply bg-card;
}

.leaflet-container a.leaflet-popup-close-button {
  @apply text-muted-foreground hover:text-foreground;
}

/* Oprava pro Leaflet v Next.js */
.leaflet-pane,
.leaflet-control,
.leaflet-top,
.leaflet-bottom {
  z-index: 400 !important;
}

.leaflet-container {
  font-family: inherit !important;
}

.leaflet-container .leaflet-control-attribution {
  background-color: rgba(255, 255, 255, 0.8);
  font-size: 10px;
  color: #333;
}
