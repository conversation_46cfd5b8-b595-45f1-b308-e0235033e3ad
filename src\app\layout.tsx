import type {Metadata} from 'next';
import { Geist } from 'next/font/google';
import './globals.css';
import { Toaster } from "@/components/ui/toaster";
import { AuthProvider } from '@/context/AuthContext';
import Script from 'next/script';

const geistSans = Geist({
  subsets: ['latin'],
  variable: '--font-geist-sans',
});

export const metadata: Metadata = {
  title: 'P&R Solutions OSINT',
  description: 'OSINT Analytická platforma od P&R Solutions',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="cs">
      <head>
        <link
          rel="stylesheet"
          href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css"
          integrity="sha512-xodZBNTC5n17Xt2atTPuE1HxjVMSvLVW9ocqUKLsCC5CXdbqCmblAshOMAS6/keqq/sMZMZ19scR4PsZChSR7A=="
          crossOrigin=""
        />
      </head>
      <body className={`${geistSans.variable} font-sans antialiased`}>
        <Script
          id="leaflet-css-fix"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              var style = document.createElement('style');
              style.innerHTML = \`
                .leaflet-container {
                  width: 100%;
                  height: 100%;
                  z-index: 1;
                }

                .leaflet-control-container .leaflet-control {
                  z-index: 1000;
                }

                .leaflet-popup-content-wrapper {
                  background-color: white;
                  border-radius: 4px;
                  box-shadow: 0 1px 5px rgba(0,0,0,0.65);
                }

                .leaflet-popup-content {
                  padding: 8px;
                }

                .leaflet-popup-tip {
                  background-color: white;
                }

                .leaflet-container a.leaflet-popup-close-button {
                  color: #999;
                }

                .leaflet-container a.leaflet-popup-close-button:hover {
                  color: #333;
                }
              \`;
              document.head.appendChild(style);
            `
          }}
        />
        <AuthProvider>
          {children}
          <Toaster />
        </AuthProvider>
      </body>
    </html>
  );
}
