"use client";

import { Control } from "react-hook-form";
import { NetworkAnalysisModuleFormValues } from "./schemas";
import { IpAddressRecord, DomainRecord, NetworkDevice } from "@/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Share2 } from "lucide-react";

interface NetworkMapProps {
  control: Control<NetworkAnalysisModuleFormValues>;
  ipAddresses: IpAddressRecord[];
  domains: DomainRecord[];
  devices: NetworkDevice[];
}

export function NetworkMap({ control, ipAddresses, domains, devices }: NetworkMapProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Síťová mapa (Stub)</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center mb-4">
          <Share2 className="mr-2 h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold"><PERSON><PERSON><PERSON><PERSON> mapa</h3>
        </div>
        <p><PERSON><PERSON> komponenta je zatím ve vývoji.</p>
      </CardContent>
    </Card>
  );
}
