"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { CadastreModuleData, Subject } from "@/types";
import { CadastreForm } from "@/components/modules/cadastre/CadastreForm";
import { ModulePageSkeleton } from "@/components/modules/ModulePageSkeleton";
import { useRouter } from "next/navigation";

export default function CadastreModulePage() {
  const params = useParams();
  const router = useRouter();
  const caseId = params.caseId as string;
  const subjectId = params.subjectId as string;
  
  const [subject, setSubject] = useState<Subject | null>(null);
  const [moduleData, setModuleData] = useState<CadastreModuleData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch subject data
        const subjectDocRef = doc(db, "cases", caseId, "subjects", subjectId);
        const subjectDoc = await getDoc(subjectDocRef);
        
        if (!subjectDoc.exists()) {
          setError("Subjekt nebyl nalezen");
          setIsLoading(false);
          return;
        }
        
        const subjectData = { id: subjectDoc.id, ...subjectDoc.data() } as Subject;
        setSubject(subjectData);
        
        // Fetch module data if it exists
        const moduleDocRef = doc(db, "cases", caseId, "subjects", subjectId, "moduleData", "cadastre");
        const moduleDoc = await getDoc(moduleDocRef);
        
        if (moduleDoc.exists()) {
          const data = { id: moduleDoc.id, ...moduleDoc.data() } as CadastreModuleData;
          setModuleData(data);
        }
        
      } catch (err: any) {
        console.error("Error fetching data:", err);
        setError(err.message || "Nastala chyba při načítání dat");
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [caseId, subjectId]);

  const handleSave = (moduleId: string, wasNew: boolean) => {
    // Redirect back to subject detail
    router.push(`/dashboard/cases/${caseId}/subjects/${subjectId}`);
  };

  if (isLoading) {
    return <ModulePageSkeleton />;
  }

  if (error || !subject) {
    return (
      <div className="container mx-auto p-4">
        <div className="bg-destructive/10 border border-destructive text-destructive px-4 py-3 rounded-md">
          <h2 className="text-lg font-semibold">Chyba</h2>
          <p>{error || "Nepodařilo se načíst data subjektu"}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <CadastreForm
        caseId={caseId}
        subject={subject}
        existingData={moduleData}
        onSave={handleSave}
      />
    </div>
  );
}
