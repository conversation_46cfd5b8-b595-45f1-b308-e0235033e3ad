"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, Card<PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Mail, CheckCircle, XCircle, AlertTriangle, Shield, 
  Search, Server, Globe, RefreshCw, AlertCircle, 
  CheckSquare, Clock, Zap, Database
} from "lucide-react";

interface EmailValidatorProps {
  onValidationComplete?: (results: EmailValidationResults) => void;
  initialEmail?: string;
}

export interface EmailValidationResults {
  email: string;
  syntaxValid: boolean;
  domainValid: boolean;
  mxRecordsValid: boolean;
  isDisposable: boolean;
  mailboxExists: boolean;
  isActive: boolean;
  score: number;
  validationDate: string;
  validationDetails: {
    formatCheck: boolean;
    typoCheck: boolean;
    domainCheck: boolean;
    mxRecordsCheck: boolean;
    disposableCheck: boolean;
    mailboxCheck: boolean;
    catchAllCheck: boolean;
    freeEmailCheck: boolean;
    roleEmailCheck: boolean;
  };
}

export function EmailValidator({ onValidationComplete, initialEmail = "" }: EmailValidatorProps) {
  const [email, setEmail] = useState(initialEmail);
  const [isValidating, setIsValidating] = useState(false);
  const [validationResults, setValidationResults] = useState<EmailValidationResults | null>(null);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [validationProgress, setValidationProgress] = useState(0);
  const [validationStep, setValidationStep] = useState("");
  const [advancedOptions, setAdvancedOptions] = useState({
    checkMailbox: true,
    checkDisposable: true,
    suggestCorrections: true,
    deepValidation: false
  });

  const validateEmail = async () => {
    if (!email.trim()) {
      setValidationError("Zadejte emailovou adresu");
      return;
    }

    setIsValidating(true);
    setValidationError(null);
    setValidationProgress(0);
    setValidationStep("Kontrola syntaxe emailu...");

    try {
      // Simulace postupu validace
      await simulateValidationStep(15, "Kontrola syntaxe emailu...");
      
      // Základní validace syntaxe emailu
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      const syntaxValid = emailRegex.test(email);
      
      if (!syntaxValid) {
        throw new Error("Neplatná syntaxe emailové adresy");
      }
      
      await simulateValidationStep(30, "Kontrola domény...");
      
      // Simulace kontroly domény
      const domain = email.split('@')[1];
      const domainValid = true; // V reálné implementaci by zde byla skutečná kontrola
      
      await simulateValidationStep(50, "Kontrola MX záznamů...");
      
      // Simulace kontroly MX záznamů
      const mxRecordsValid = true; // V reálné implementaci by zde byla skutečná kontrola
      
      await simulateValidationStep(70, "Kontrola jednorázové adresy...");
      
      // Simulace kontroly jednorázové adresy
      const isDisposable = domain.includes('temp') || domain.includes('disposable'); // Jednoduchá simulace
      
      await simulateValidationStep(85, "Kontrola existence schránky...");
      
      // Simulace kontroly existence schránky
      const mailboxExists = true; // V reálné implementaci by zde byla skutečná kontrola
      
      await simulateValidationStep(95, "Finalizace výsledků...");
      
      // Vytvoření výsledků validace
      const results: EmailValidationResults = {
        email,
        syntaxValid,
        domainValid,
        mxRecordsValid,
        isDisposable,
        mailboxExists,
        isActive: syntaxValid && domainValid && mxRecordsValid && mailboxExists && !isDisposable,
        score: calculateScore(syntaxValid, domainValid, mxRecordsValid, isDisposable, mailboxExists),
        validationDate: new Date().toISOString(),
        validationDetails: {
          formatCheck: syntaxValid,
          typoCheck: !email.includes('gmial') && !email.includes('yaho'),
          domainCheck: domainValid,
          mxRecordsCheck: mxRecordsValid,
          disposableCheck: !isDisposable,
          mailboxCheck: mailboxExists,
          catchAllCheck: false,
          freeEmailCheck: domain.includes('gmail') || domain.includes('yahoo') || domain.includes('hotmail'),
          roleEmailCheck: email.startsWith('info@') || email.startsWith('support@') || email.startsWith('admin@')
        }
      };
      
      setValidationResults(results);
      setValidationProgress(100);
      setValidationStep("Validace dokončena");
      
      if (onValidationComplete) {
        onValidationComplete(results);
      }
    } catch (error: any) {
      setValidationError(error.message || "Chyba při validaci emailu");
      setValidationProgress(0);
    } finally {
      setIsValidating(false);
    }
  };

  const simulateValidationStep = async (progress: number, step: string) => {
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        setValidationProgress(progress);
        setValidationStep(step);
        resolve();
      }, 500);
    });
  };

  const calculateScore = (
    syntaxValid: boolean, 
    domainValid: boolean, 
    mxRecordsValid: boolean, 
    isDisposable: boolean, 
    mailboxExists: boolean
  ): number => {
    let score = 0;
    if (syntaxValid) score += 20;
    if (domainValid) score += 20;
    if (mxRecordsValid) score += 20;
    if (!isDisposable) score += 20;
    if (mailboxExists) score += 20;
    return score;
  };

  const getScoreColor = (score: number): string => {
    if (score >= 80) return "text-green-500";
    if (score >= 60) return "text-yellow-500";
    if (score >= 40) return "text-orange-500";
    return "text-red-500";
  };

  const getScoreText = (score: number): string => {
    if (score >= 80) return "Výborná";
    if (score >= 60) return "Dobrá";
    if (score >= 40) return "Průměrná";
    if (score >= 20) return "Nízká";
    return "Velmi nízká";
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Mail className="mr-2 h-5 w-5 text-primary" />
          Validace emailové adresy
        </CardTitle>
        <CardDescription>
          Ověřte platnost a funkčnost emailové adresy
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col space-y-2">
          <Label htmlFor="email-input">Emailová adresa</Label>
          <div className="flex space-x-2">
            <Input
              id="email-input"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={isValidating}
              className="flex-1"
            />
            <Button 
              onClick={validateEmail} 
              disabled={isValidating || !email.trim()}
            >
              {isValidating ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Validuji...
                </>
              ) : (
                <>
                  <Search className="mr-2 h-4 w-4" />
                  Validovat
                </>
              )}
            </Button>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox 
            id="check-mailbox" 
            checked={advancedOptions.checkMailbox}
            onCheckedChange={(checked) => 
              setAdvancedOptions({...advancedOptions, checkMailbox: checked === true})
            }
          />
          <Label htmlFor="check-mailbox">Kontrolovat existenci schránky</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox 
            id="check-disposable" 
            checked={advancedOptions.checkDisposable}
            onCheckedChange={(checked) => 
              setAdvancedOptions({...advancedOptions, checkDisposable: checked === true})
            }
          />
          <Label htmlFor="check-disposable">Kontrolovat jednorázové adresy</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox 
            id="deep-validation" 
            checked={advancedOptions.deepValidation}
            onCheckedChange={(checked) => 
              setAdvancedOptions({...advancedOptions, deepValidation: checked === true})
            }
          />
          <Label htmlFor="deep-validation">Hloubková validace (může trvat déle)</Label>
        </div>

        {validationError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Chyba validace</AlertTitle>
            <AlertDescription>{validationError}</AlertDescription>
          </Alert>
        )}

        {isValidating && (
          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span>{validationStep}</span>
              <span>{validationProgress}%</span>
            </div>
            <Progress value={validationProgress} />
          </div>
        )}

        {validationResults && (
          <div className="space-y-4 mt-4">
            <Separator />
            
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Výsledky validace</h3>
              <Badge variant={validationResults.isActive ? "success" : "destructive"}>
                {validationResults.isActive ? "Platný email" : "Neplatný email"}
              </Badge>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex flex-col space-y-1">
                <span className="text-sm text-muted-foreground">Skóre spolehlivosti</span>
                <div className="flex items-center">
                  <span className={`text-2xl font-bold ${getScoreColor(validationResults.score)}`}>
                    {validationResults.score}%
                  </span>
                  <span className="ml-2 text-sm">({getScoreText(validationResults.score)})</span>
                </div>
              </div>
              
              <div className="flex flex-col space-y-1">
                <span className="text-sm text-muted-foreground">Datum validace</span>
                <span>
                  {new Date(validationResults.validationDate).toLocaleString('cs-CZ')}
                </span>
              </div>
            </div>
            
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid grid-cols-3 mb-4">
                <TabsTrigger value="basic">Základní kontroly</TabsTrigger>
                <TabsTrigger value="advanced">Pokročilé kontroly</TabsTrigger>
                <TabsTrigger value="details">Detaily</TabsTrigger>
              </TabsList>
              
              <TabsContent value="basic" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    {validationResults.syntaxValid ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    <span>Syntaxe emailu</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {validationResults.domainValid ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    <span>Platnost domény</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {validationResults.mxRecordsValid ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    <span>MX záznamy</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {!validationResults.isDisposable ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-5 w-5 text-orange-500" />
                    )}
                    <span>{validationResults.isDisposable ? "Jednorázová adresa" : "Není jednorázová adresa"}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {validationResults.mailboxExists ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500" />
                    )}
                    <span>Existence schránky</span>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="advanced" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2">
                    {validationResults.validationDetails.typoCheck ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-5 w-5 text-orange-500" />
                    )}
                    <span>Kontrola překlepů</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {!validationResults.validationDetails.catchAllCheck ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertTriangle className="h-5 w-5 text-orange-500" />
                    )}
                    <span>{validationResults.validationDetails.catchAllCheck ? "Catch-all doména" : "Není catch-all doména"}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {!validationResults.validationDetails.freeEmailCheck ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-blue-500" />
                    )}
                    <span>{validationResults.validationDetails.freeEmailCheck ? "Bezplatný email" : "Firemní email"}</span>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {!validationResults.validationDetails.roleEmailCheck ? (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-blue-500" />
                    )}
                    <span>{validationResults.validationDetails.roleEmailCheck ? "Rolový email" : "Personální email"}</span>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="details" className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Email</span>
                    <span className="font-medium">{validationResults.email}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Doména</span>
                    <span className="font-medium">{validationResults.email.split('@')[1]}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Uživatelské jméno</span>
                    <span className="font-medium">{validationResults.email.split('@')[0]}</span>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-end">
        {validationResults && (
          <Button 
            variant="outline" 
            onClick={() => {
              setValidationResults(null);
              setValidationError(null);
              setValidationProgress(0);
            }}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Nová validace
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
