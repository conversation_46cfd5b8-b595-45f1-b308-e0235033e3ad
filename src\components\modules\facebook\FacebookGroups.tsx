import { FacebookModuleData, FacebookGroup } from "@/types/facebook";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, ExternalLink, Lock, Unlock, Shield } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Image from "next/image";

interface FacebookGroupsProps {
  data: FacebookModuleData | null;
}

export default function FacebookGroups({ data }: FacebookGroupsProps) {
  if (!data || !data.groups || data.groups.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="text-muted-foreground mb-2"><PERSON><PERSON><PERSON><PERSON> skupiny k zobrazení</div>
        <p className="text-sm text-muted-foreground">
          Přidejte skupiny v režimu úprav.
        </p>
      </div>
    );
  }

  return (
    <div>
      <h3 className="text-lg font-semibold mb-4">Skupiny ({data.groups.length})</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {data.groups.map((group) => (
          <GroupCard key={group.id} group={group} />
        ))}
      </div>
    </div>
  );
}

function GroupCard({ group }: { group: FacebookGroup }) {
  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center gap-3">
          {/* Group Image */}
          <div className="relative w-12 h-12 rounded-md overflow-hidden flex-shrink-0">
            {group.imageUrl ? (
              <Image 
                src={group.imageUrl} 
                alt={group.name} 
                fill 
                className="object-cover"
                unoptimized
              />
            ) : (
              <div className="w-full h-full bg-blue-100 flex items-center justify-center">
                <span className="text-blue-600 font-bold">
                  {group.name.charAt(0)}
                </span>
              </div>
            )}
          </div>
          
          {/* Group Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <h4 className="font-medium truncate">{group.name}</h4>
              <Badge variant="outline" className={
                group.privacy === 'public' ? "bg-green-50" : 
                group.privacy === 'private' ? "bg-amber-50" : "bg-red-50"
              }>
                {group.privacy === 'public' ? (
                  <><Unlock className="h-3 w-3 mr-1 text-green-600" /> Veřejná</>
                ) : group.privacy === 'private' ? (
                  <><Lock className="h-3 w-3 mr-1 text-amber-600" /> Soukromá</>
                ) : (
                  <><Lock className="h-3 w-3 mr-1 text-red-600" /> Tajná</>
                )}
              </Badge>
              
              {group.isAdmin && (
                <Badge variant="outline" className="bg-blue-50">
                  <Shield className="h-3 w-3 mr-1 text-blue-600" /> Admin
                </Badge>
              )}
            </div>
            
            <div className="flex flex-wrap gap-2 text-xs text-muted-foreground mt-1">
              {group.members !== undefined && (
                <span className="flex items-center">
                  <Users className="h-3 w-3 inline mr-1" /> 
                  {group.members.toLocaleString()} členů
                </span>
              )}
            </div>
          </div>
          
          {/* External Link */}
          <Button variant="ghost" size="icon" asChild className="flex-shrink-0">
            <a href={group.groupUrl} target="_blank" rel="noopener noreferrer">
              <ExternalLink className="h-4 w-4" />
            </a>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
