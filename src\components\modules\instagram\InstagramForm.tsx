"use client";

import { useState, useMemo } from "react";
import { useForm, useFieldArray, UseFormReturn } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Form } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { db } from "@/lib/firebase";
import { doc, setDoc, Timestamp } from "firebase/firestore";
import { LoadingSpinner } from "@/components/ui/loading";
import { 
  Instagram, 
  Users, 
  MessageSquare, 
  Calendar, 
  Star,
  StarOff,
  PlusCircle,
  Trash2,
  Copy,
  Check,
  Play,
  Heart,
  Hash,
  MapPin,
  Camera,
  Video,
  Eye,
  Bookmark,
  ImageIcon
} from "lucide-react";
import { InstagramModuleData, InstagramPost, InstagramStory, InstagramReel, InstagramFollower } from "@/types/instagram";
import { PhotoDocumentationSection } from "./PhotoDocumentationSection";

// Schema pro validaci
const instagramPostSchema = z.object({
  id: z.string(),
  url: z.string().optional(),
  caption: z.string().optional(),
  hashtags: z.string().optional(),
  likesCount: z.number().optional(),
  commentsCount: z.number().optional(),
  timestamp: z.string().optional(),
  location: z.string().optional(),
  isSponsored: z.boolean().optional(),
  taggedUsers: z.string().optional(),
  notes: z.string().optional(),
  photos: z.array(z.any()).optional(),
});

const instagramStorySchema = z.object({
  id: z.string(),
  type: z.enum(["photo", "video", "text", "boomerang", "live"]).optional(),
  timestamp: z.string().optional(),
  content: z.string().optional(),
  location: z.string().optional(),
  taggedUsers: z.string().optional(),
  notes: z.string().optional(),
  photos: z.array(z.any()).optional(),
});

const instagramReelSchema = z.object({
  id: z.string(),
  url: z.string().optional(),
  caption: z.string().optional(),
  hashtags: z.string().optional(),
  likesCount: z.number().optional(),
  commentsCount: z.number().optional(),
  sharesCount: z.number().optional(),
  timestamp: z.string().optional(),
  audioTrack: z.string().optional(),
  duration: z.number().optional(),
  notes: z.string().optional(),
  photos: z.array(z.any()).optional(),
});

const instagramFollowerSchema = z.object({
  id: z.string(),
  username: z.string().min(1, "Uživatelské jméno je povinné"),
  displayName: z.string().optional(),
  profileUrl: z.string().optional(),
  isVerified: z.boolean().optional(),
  followersCount: z.number().optional(),
  isPersonOfInterest: z.boolean().optional(),
  notes: z.string().optional(),
});

const instagramModuleSchema = z.object({
  displayName: z.string().optional(),
  username: z.string().optional(),
  profileUrl: z.string().optional(),
  bio: z.string().optional(),
  website: z.string().optional(),
  contactInfo: z.object({
    email: z.string().optional(),
    phone: z.string().optional(),
  }).optional(),
  followersCount: z.number().optional(),
  followingCount: z.number().optional(),
  postsCount: z.number().optional(),
  isVerified: z.boolean().optional(),
  isPrivate: z.boolean().optional(),
  isBusinessAccount: z.boolean().optional(),
  category: z.string().optional(),
  posts: z.array(instagramPostSchema).optional(),
  stories: z.array(instagramStorySchema).optional(),
  reels: z.array(instagramReelSchema).optional(),
  followers: z.array(instagramFollowerSchema).optional(),
  following: z.array(instagramFollowerSchema).optional(),
  topHashtags: z.string().optional(),
  mentions: z.string().optional(),
  recentLocations: z.string().optional(),
  postingFrequency: z.enum(["multiple-daily", "daily", "weekly", "monthly", "rarely", "irregular"]).optional(),
  activeTime: z.enum(["morning", "midday", "afternoon", "evening", "night", "weekend", "workdays", "irregular"]).optional(),
  contentType: z.enum(["personal", "professional", "lifestyle", "travel", "food", "fitness", "fashion", "business", "entertainment", "mixed"]).optional(),
  activityLevel: z.enum(["very-active", "active", "moderate", "low", "inactive"]).optional(),
  interactionLevel: z.enum(["very-interactive", "interactive", "passive", "lurker", "none"]).optional(),
  analysisNotes: z.string().optional(),
  contentAnalysis: z.string().optional(),
  patterns: z.string().optional(),
  changes: z.string().optional(),
  photos: z.array(z.any()).optional(),
  osintNotes: z.string().optional(),
  investigationNotes: z.string().optional(),
});

type InstagramFormValues = z.infer<typeof instagramModuleSchema>;

interface InstagramFormProps {
  caseId: string;
  subject: {
    id: string;
    firstName: string;
    lastName: string;
    type: "person" | "company";
  };
  moduleId: string;
  existingData: InstagramModuleData | null;
  onSave: (data: InstagramModuleData, wasNew: boolean) => void;
  onCancel: () => void;
}

export default function InstagramForm({ caseId, subject, moduleId, existingData, onSave, onCancel }: InstagramFormProps) {
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);

  const defaultValues = useMemo(() => ({
    displayName: existingData?.displayName || "",
    username: existingData?.username || "",
    profileUrl: existingData?.profileUrl || "",
    bio: existingData?.bio || "",
    website: existingData?.website || "",
    contactInfo: {
      email: existingData?.contactInfo?.email || "",
      phone: existingData?.contactInfo?.phone || "",
    },
    followersCount: existingData?.followersCount || 0,
    followingCount: existingData?.followingCount || 0,
    postsCount: existingData?.postsCount || 0,
    isVerified: existingData?.isVerified || false,
    isPrivate: existingData?.isPrivate || false,
    isBusinessAccount: existingData?.isBusinessAccount || false,
    category: existingData?.category || "",
    posts: existingData?.posts?.map((post: any) => ({
      ...post,
      hashtags: Array.isArray(post.hashtags) ? post.hashtags.join(' ') : post.hashtags || '',
      taggedUsers: Array.isArray(post.taggedUsers) ? post.taggedUsers.join(' ') : post.taggedUsers || '',
      timestamp: typeof post.date === 'string' ? post.date : post.date?.toDate?.()?.toISOString().split('T')[0] || '',
      photos: post.photos || [],
    })) || [],
    stories: existingData?.stories?.map((story: any) => ({
      ...story,
      taggedUsers: Array.isArray(story.taggedUsers) ? story.taggedUsers.join(' ') : story.taggedUsers || '',
      timestamp: typeof story.date === 'string' ? story.date : story.date?.toDate?.()?.toISOString().split('T')[0] || '',
      photos: story.photos || [],
    })) || [],
    reels: existingData?.reels?.map((reel: any) => ({
      ...reel,
      hashtags: Array.isArray(reel.hashtags) ? reel.hashtags.join(' ') : reel.hashtags || '',
      timestamp: typeof reel.date === 'string' ? reel.date : reel.date?.toDate?.()?.toISOString().split('T')[0] || '',
      photos: reel.photos || [],
    })) || [],
    followers: existingData?.followers?.map((follower: any) => ({
      ...follower,
      isPersonOfInterest: false,
    })) || [],
    following: existingData?.following?.map((following: any) => ({
      ...following,
      isPersonOfInterest: false,
    })) || [],
    topHashtags: existingData?.topHashtags?.map(h => h.hashtag).join(' ') || "",
    mentions: "",
    recentLocations: existingData?.recentLocations?.join(', ') || "",
    postingFrequency: "irregular" as const,
    activeTime: "irregular" as const,
    contentType: "mixed" as const,
    activityLevel: "moderate" as const,
    interactionLevel: "passive" as const,
    analysisNotes: "",
    contentAnalysis: "",
    patterns: "",
    changes: "",
    photos: [],
    osintNotes: existingData?.osintNotes || "",
    investigationNotes: existingData?.notes || "",
  }), [existingData]);

  const form = useForm<InstagramFormValues>({
    resolver: zodResolver(instagramModuleSchema),
    defaultValues,
  });

  const { fields: postFields, append: appendPost, remove: removePost } = useFieldArray({
    control: form.control,
    name: "posts"
  });

  const { fields: storyFields, append: appendStory, remove: removeStory } = useFieldArray({
    control: form.control,
    name: "stories"
  });

  const { fields: reelFields, append: appendReel, remove: removeReel } = useFieldArray({
    control: form.control,
    name: "reels"
  });

  const { fields: followerFields, append: appendFollower, remove: removeFollower } = useFieldArray({
    control: form.control,
    name: "followers"
  });

  const { fields: followingFields, append: appendFollowing, remove: removeFollowing } = useFieldArray({
    control: form.control,
    name: "following"
  });

  // Funkce pro označení uživatele jako zájmovou osobu
  const togglePersonOfInterest = (fieldName: "followers" | "following", index: number) => {
    const users = form.watch(fieldName) || [];
    const updatedUsers = [...users];
    updatedUsers[index].isPersonOfInterest = !updatedUsers[index].isPersonOfInterest;
    form.setValue(fieldName, updatedUsers);
  };

  // Funkce pro kopírování textu do schránky
  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Zkopírováno",
        description: `${type} byl zkopírován do schránky`,
      });
    } catch (error) {
      toast({
        title: "Chyba kopírování",
        description: "Nepodařilo se zkopírovat text do schránky",
        variant: "destructive"
      });
    }
  };

  const onSubmitHandler = async (data: InstagramFormValues) => {
    setIsSaving(true);
    try {
      const moduleDocRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);

      // Funkce pro odstranění undefined hodnot z objektu (rekurzivně)
      const removeUndefinedValues = (obj: any): any => {
        if (obj === null || obj === undefined) {
          return null;
        }
        
        if (Array.isArray(obj)) {
          return obj.map(removeUndefinedValues).filter(item => item !== undefined);
        }
        
        if (typeof obj === 'object') {
          const cleaned: any = {};
          for (const [key, value] of Object.entries(obj)) {
            if (value !== undefined) {
              const cleanedValue = removeUndefinedValues(value);
              if (cleanedValue !== undefined) {
                cleaned[key] = cleanedValue;
              }
            }
          }
          return cleaned;
        }
        
        return obj;
      };

      const saveData: InstagramModuleData = {
        subjectId: subject.id,
        displayName: data.displayName || "",
        username: data.username || "",
        profileUrl: data.profileUrl || "",
        bio: data.bio || "",
        website: data.website || "",
        contactInfo: {
          email: data.contactInfo?.email || "",
          phone: data.contactInfo?.phone || "",
        },
        followersCount: data.followersCount || 0,
        followingCount: data.followingCount || 0,
        postsCount: data.postsCount || 0,
        isVerified: data.isVerified || false,
        isPrivate: data.isPrivate || false,
        isBusinessAccount: data.isBusinessAccount || false,
        category: data.category || "",
        posts: data.posts?.map((post: any) => ({
          ...post,
          date: post.timestamp,
          hashtags: post.hashtags ? post.hashtags.split(' ').filter((h: string) => h.trim()) : [],
          taggedUsers: post.taggedUsers ? post.taggedUsers.split(' ').filter((u: string) => u.trim()) : [],
          likes: post.likesCount || 0,
          comments: post.commentsCount || 0,
        })) || [],
        stories: data.stories?.map((story: any) => ({
          ...story,
          date: story.timestamp,
          taggedUsers: story.taggedUsers ? story.taggedUsers.split(' ').filter((u: string) => u.trim()) : [],
        })) || [],
        reels: data.reels?.map((reel: any) => ({
          ...reel,
          date: reel.timestamp,
          hashtags: reel.hashtags ? reel.hashtags.split(' ').filter((h: string) => h.trim()) : [],
          likes: reel.likesCount || 0,
          comments: reel.commentsCount || 0,
          views: 0,
        })) || [],
        followers: data.followers?.map((follower: any) => ({
          ...follower,
          followersCount: follower.followersCount || 0,
        })) || [],
        following: data.following?.map((following: any) => ({
          ...following,
          followersCount: following.followersCount || 0,
        })) || [],
        topHashtags: data.topHashtags ? data.topHashtags.split(' ').filter(h => h.trim()).map(h => ({ hashtag: h, count: 1 })) : [],
        recentLocations: data.recentLocations ? data.recentLocations.split(',').map(l => l.trim()).filter(l => l) : [],
        notes: [
          data.investigationNotes || "",
          `Mentions: ${data.mentions || ""}`,
          `Posting Frequency: ${data.postingFrequency || ""}`,
          `Active Time: ${data.activeTime || ""}`,
          `Content Type: ${data.contentType || ""}`,
          `Activity Level: ${data.activityLevel || ""}`,
          `Interaction Level: ${data.interactionLevel || ""}`,
          `Analysis Notes: ${data.analysisNotes || ""}`,
          `Content Analysis: ${data.contentAnalysis || ""}`,
          `Patterns: ${data.patterns || ""}`,
          `Changes: ${data.changes || ""}`
        ].filter(note => note.trim() && !note.includes(": ")).join('\n\n'),
        osintNotes: data.osintNotes || "",
        lastUpdatedAt: Timestamp.now(),
        createdAt: existingData?.createdAt || Timestamp.now(),
      };

      const cleanedData = removeUndefinedValues(saveData);
      await setDoc(moduleDocRef, cleanedData);
      
      toast({
        title: "Data uložena",
        description: "Instagram modul byl úspěšně uložen"
      });

      const wasNew = !existingData || !existingData.createdAt;
      onSave(cleanedData, wasNew);

    } catch (error: any) {
      console.error("Error saving Instagram data:", error);
      toast({
        title: "Chyba ukládání",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmitHandler)} className="space-y-6">
        {/* Základní údaje profilu */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Instagram className="mr-2 h-5 w-5 text-pink-600" />
              Základní údaje Instagram profilu
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="displayName">Jméno na profilu</Label>
                <Input
                  id="displayName"
                  {...form.register("displayName")}
                  placeholder="Jméno zobrazené na profilu"
                />
              </div>
              <div>
                <Label htmlFor="username">Uživatelské jméno</Label>
                <Input
                  id="username"
                  {...form.register("username")}
                  placeholder="@uživatelské_jméno"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="profileUrl">URL profilu</Label>
              <Input
                id="profileUrl"
                {...form.register("profileUrl")}
                placeholder="https://instagram.com/..."
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="followersCount">Počet sledujících</Label>
                <Input
                  id="followersCount"
                  type="number"
                  {...form.register("followersCount", { valueAsNumber: true })}
                  placeholder="Počet"
                />
              </div>
              <div>
                <Label htmlFor="followingCount">Sleduje účtů</Label>
                <Input
                  id="followingCount"
                  type="number"
                  {...form.register("followingCount", { valueAsNumber: true })}
                  placeholder="Počet"
                />
              </div>
              <div>
                <Label htmlFor="postsCount">Počet příspěvků</Label>
                <Input
                  id="postsCount"
                  type="number"
                  {...form.register("postsCount", { valueAsNumber: true })}
                  placeholder="Počet"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="bio">Bio/popis profilu</Label>
              <Textarea
                id="bio"
                {...form.register("bio")}
                placeholder="Biografické informace z profilu"
                rows={3}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="website">URL v profilu</Label>
                <Input
                  id="website"
                  {...form.register("website")}
                  placeholder="URL uvedená v profilu"
                />
              </div>
              <div>
                <Label htmlFor="category">Kategorie účtu</Label>
                <Input
                  id="category"
                  {...form.register("category")}
                  placeholder="Kategorie (Business, Creator, atd.)"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="contactInfo.email">E-mail v profilu</Label>
                <Input
                  id="contactInfo.email"
                  type="email"
                  {...form.register("contactInfo.email")}
                  placeholder="E-mailová adresa"
                />
              </div>
              <div>
                <Label htmlFor="contactInfo.phone">Telefon v profilu</Label>
                <Input
                  id="contactInfo.phone"
                  {...form.register("contactInfo.phone")}
                  placeholder="Telefonní číslo"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="isVerified"
                  {...form.register("isVerified")}
                />
                <Label htmlFor="isVerified">Ověřený účet</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isPrivate"
                  {...form.register("isPrivate")}
                />
                <Label htmlFor="isPrivate">Soukromý účet</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isBusinessAccount"
                  {...form.register("isBusinessAccount")}
                />
                <Label htmlFor="isBusinessAccount">Business účet</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Příspěvky */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <Camera className="mr-2 h-5 w-5 text-primary" />
                Klíčové příspěvky
              </span>
              <Button
                type="button"
                onClick={() => appendPost({
                  id: crypto.randomUUID(),
                  url: "",
                  caption: "",
                  hashtags: "",
                  likesCount: 0,
                  commentsCount: 0,
                  timestamp: "",
                  location: "",
                  isSponsored: false,
                  taggedUsers: "",
                  notes: "",
                  photos: []
                })}
                size="sm"
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Přidat příspěvek
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {postFields.map((field, index) => (
              <Card key={field.id} className="p-4">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium">Příspěvek {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removePost(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <Label>URL příspěvku</Label>
                    <Input
                      {...form.register(`posts.${index}.url`)}
                      placeholder="https://instagram.com/p/..."
                    />
                  </div>
                  
                  <div>
                    <Label>Datum příspěvku</Label>
                    <Input
                      type="date"
                      {...form.register(`posts.${index}.timestamp`)}
                    />
                  </div>
                  
                  <div>
                    <Label>Popis příspěvku</Label>
                    <Textarea
                      {...form.register(`posts.${index}.caption`)}
                      placeholder="Text/popis příspěvku..."
                      rows={3}
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Počet lajků</Label>
                      <Input
                        type="number"
                        {...form.register(`posts.${index}.likesCount`, { valueAsNumber: true })}
                        placeholder="Počet lajků"
                      />
                    </div>
                    <div>
                      <Label>Počet komentářů</Label>
                      <Input
                        type="number"
                        {...form.register(`posts.${index}.commentsCount`, { valueAsNumber: true })}
                        placeholder="Počet komentářů"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label>Hashtagy</Label>
                    <Textarea
                      {...form.register(`posts.${index}.hashtags`)}
                      placeholder="#hashtag1 #hashtag2 #hashtag3"
                      rows={2}
                    />
                  </div>
                  
                  <div>
                    <Label>Lokace</Label>
                    <Input
                      {...form.register(`posts.${index}.location`)}
                      placeholder="Označená lokace"
                    />
                  </div>
                  
                  <div>
                    <Label>Označené osoby</Label>
                    <Textarea
                      {...form.register(`posts.${index}.taggedUsers`)}
                      placeholder="@uzivatel1 @uzivatel2"
                      rows={2}
                    />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      {...form.register(`posts.${index}.isSponsored`)}
                    />
                    <Label>Sponzorovaný příspěvek</Label>
                  </div>
                  
                  <div>
                    <Label>Poznámky k příspěvku</Label>
                    <Textarea
                      {...form.register(`posts.${index}.notes`)}
                      placeholder="Další důležité informace o příspěvku..."
                      rows={2}
                    />
                  </div>

                  {/* Fotodokumentace pro příspěvek */}
                  <div className="border-t pt-4">
                    <PhotoDocumentationSection
                      form={form as any}
                      title={`Fotodokumentace příspěvku ${index + 1}`}
                      description="Nahrajte snímky obrazovky tohoto příspěvku"
                      namePrefix={`posts.${index}.photos`}
                      photoHint="instagram post social media screenshot"
                      caseId={caseId}
                      subjectId={subject.id}
                      moduleId={moduleId}
                    />
                  </div>
                </div>
              </Card>
            ))}
          </CardContent>
        </Card>

        {/* Stories */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <MessageSquare className="mr-2 h-5 w-5 text-primary" />
                Významné Stories
              </span>
              <Button
                type="button"
                onClick={() => appendStory({
                  id: crypto.randomUUID(),
                  type: "photo",
                  timestamp: "",
                  content: "",
                  location: "",
                  taggedUsers: "",
                  notes: "",
                  photos: []
                })}
                size="sm"
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Přidat Story
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {storyFields.map((field, index) => (
              <Card key={field.id} className="p-4">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium">Story {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeStory(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Typ Story</Label>
                      <Select
                        value={form.watch(`stories.${index}.type`)}
                        onValueChange={(value) => form.setValue(`stories.${index}.type`, value as any)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Vyberte typ" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="photo">Fotografie</SelectItem>
                          <SelectItem value="video">Video</SelectItem>
                          <SelectItem value="text">Text</SelectItem>
                          <SelectItem value="boomerang">Boomerang</SelectItem>
                          <SelectItem value="live">Live</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>Datum Story</Label>
                      <Input
                        type="date"
                        {...form.register(`stories.${index}.timestamp`)}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label>Obsah/Popis Story</Label>
                    <Textarea
                      {...form.register(`stories.${index}.content`)}
                      placeholder="Popis obsahu story..."
                      rows={3}
                    />
                  </div>
                  
                  <div>
                    <Label>Lokace</Label>
                    <Input
                      {...form.register(`stories.${index}.location`)}
                      placeholder="Označená lokace"
                    />
                  </div>
                  
                  <div>
                    <Label>Označené osoby</Label>
                    <Input
                      {...form.register(`stories.${index}.taggedUsers`)}
                      placeholder="@uzivatel1 @uzivatel2"
                    />
                  </div>
                  
                  <div>
                    <Label>Poznámky</Label>
                    <Textarea
                      {...form.register(`stories.${index}.notes`)}
                      placeholder="Další důležité informace..."
                      rows={2}
                    />
                  </div>

                  {/* Fotodokumentace pro story */}
                  <div className="border-t pt-4">
                    <PhotoDocumentationSection
                      form={form as any}
                      title={`Fotodokumentace Story ${index + 1}`}
                      description="Nahrajte snímky obrazovky tohoto story"
                      namePrefix={`stories.${index}.photos`}
                      photoHint="instagram story social media screenshot"
                      caseId={caseId}
                      subjectId={subject.id}
                      moduleId={moduleId}
                    />
                  </div>
                </div>
              </Card>
            ))}
          </CardContent>
        </Card>

        {/* Reels */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <Video className="mr-2 h-5 w-5 text-primary" />
                Významné Reels
              </span>
              <Button
                type="button"
                onClick={() => appendReel({
                  id: crypto.randomUUID(),
                  url: "",
                  caption: "",
                  hashtags: "",
                  likesCount: 0,
                  commentsCount: 0,
                  sharesCount: 0,
                  timestamp: "",
                  audioTrack: "",
                  duration: 0,
                  notes: "",
                  photos: []
                })}
                size="sm"
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Přidat Reel
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {reelFields.map((field, index) => (
              <Card key={field.id} className="p-4">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium">Reel {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeReel(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <Label>URL Reelu</Label>
                    <Input
                      {...form.register(`reels.${index}.url`)}
                      placeholder="https://instagram.com/reel/..."
                    />
                  </div>
                  
                  <div>
                    <Label>Datum Reelu</Label>
                    <Input
                      type="date"
                      {...form.register(`reels.${index}.timestamp`)}
                    />
                  </div>
                  
                  <div>
                    <Label>Popis Reelu</Label>
                    <Textarea
                      {...form.register(`reels.${index}.caption`)}
                      placeholder="Text/popis reelu..."
                      rows={3}
                    />
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label>Počet lajků</Label>
                      <Input
                        type="number"
                        {...form.register(`reels.${index}.likesCount`, { valueAsNumber: true })}
                        placeholder="Lajky"
                      />
                    </div>
                    <div>
                      <Label>Komentáře</Label>
                      <Input
                        type="number"
                        {...form.register(`reels.${index}.commentsCount`, { valueAsNumber: true })}
                        placeholder="Komentáře"
                      />
                    </div>
                    <div>
                      <Label>Sdílení</Label>
                      <Input
                        type="number"
                        {...form.register(`reels.${index}.sharesCount`, { valueAsNumber: true })}
                        placeholder="Sdílení"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Audio/Hudba</Label>
                      <Input
                        {...form.register(`reels.${index}.audioTrack`)}
                        placeholder="Název použité hudby"
                      />
                    </div>
                    <div>
                      <Label>Délka (sekundy)</Label>
                      <Input
                        type="number"
                        {...form.register(`reels.${index}.duration`, { valueAsNumber: true })}
                        placeholder="Délka v sekundách"
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label>Hashtagy</Label>
                    <Textarea
                      {...form.register(`reels.${index}.hashtags`)}
                      placeholder="#hashtag1 #hashtag2"
                      rows={2}
                    />
                  </div>
                  
                  <div>
                    <Label>Poznámky</Label>
                    <Textarea
                      {...form.register(`reels.${index}.notes`)}
                      placeholder="Další důležité informace o reelu..."
                      rows={2}
                    />
                  </div>

                  {/* Fotodokumentace pro reel */}
                  <div className="border-t pt-4">
                    <PhotoDocumentationSection
                      form={form as any}
                      title={`Fotodokumentace Reel ${index + 1}`}
                      description="Nahrajte snímky obrazovky tohoto reelu"
                      namePrefix={`reels.${index}.photos`}
                      photoHint="instagram reel social media screenshot video"
                      caseId={caseId}
                      subjectId={subject.id}
                      moduleId={moduleId}
                    />
                  </div>
                </div>
              </Card>
            ))}
          </CardContent>
        </Card>

        {/* Sledující */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <Users className="mr-2 h-5 w-5 text-primary" />
                Významní sledující
              </span>
              <Button
                type="button"
                onClick={() => appendFollower({
                  id: crypto.randomUUID(),
                  username: "",
                  displayName: "",
                  profileUrl: "",
                  isVerified: false,
                  followersCount: 0,
                  isPersonOfInterest: false,
                  notes: ""
                })}
                size="sm"
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Přidat sledujícího
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {followerFields.map((field, index) => (
              <Card key={field.id} className={`p-4 ${form.watch(`followers.${index}.isPersonOfInterest`) ? 'border-yellow-500 bg-yellow-50' : ''}`}>
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium">Sledující {index + 1}</h4>
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => togglePersonOfInterest("followers", index)}
                      className={form.watch(`followers.${index}.isPersonOfInterest`) ? "text-yellow-600" : "text-gray-400"}
                      title={form.watch(`followers.${index}.isPersonOfInterest`) ? "Odstranit z zájmových osob" : "Označit jako zájmovou osobu"}
                    >
                      {form.watch(`followers.${index}.isPersonOfInterest`) ? (
                        <Star className="h-4 w-4 fill-current" />
                      ) : (
                        <StarOff className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFollower(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Uživatelské jméno</Label>
                      <Input
                        {...form.register(`followers.${index}.username`)}
                        placeholder="@uzivatelske_jmeno"
                      />
                    </div>
                    <div>
                      <Label>Zobrazované jméno</Label>
                      <Input
                        {...form.register(`followers.${index}.displayName`)}
                        placeholder="Jméno na profilu"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>URL profilu</Label>
                      <Input
                        {...form.register(`followers.${index}.profileUrl`)}
                        placeholder="https://instagram.com/..."
                      />
                    </div>
                    <div>
                      <Label>Počet sledujících</Label>
                      <Input
                        type="number"
                        {...form.register(`followers.${index}.followersCount`, { valueAsNumber: true })}
                        placeholder="Počet"
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      {...form.register(`followers.${index}.isVerified`)}
                    />
                    <Label>Ověřený účet</Label>
                  </div>
                  
                  <div>
                    <Label>Poznámky</Label>
                    <Textarea
                      {...form.register(`followers.${index}.notes`)}
                      placeholder="Proč je tento sledující významný..."
                      rows={2}
                    />
                  </div>
                </div>
              </Card>
            ))}
          </CardContent>
        </Card>

        {/* Sledované účty */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <Eye className="mr-2 h-5 w-5 text-primary" />
                Sledované účty
              </span>
              <Button
                type="button"
                onClick={() => appendFollowing({
                  id: crypto.randomUUID(),
                  username: "",
                  displayName: "",
                  profileUrl: "",
                  isVerified: false,
                  followersCount: 0,
                  isPersonOfInterest: false,
                  notes: ""
                })}
                size="sm"
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Přidat sledovaný účet
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {followingFields.map((field, index) => (
              <Card key={field.id} className={`p-4 ${form.watch(`following.${index}.isPersonOfInterest`) ? 'border-yellow-500 bg-yellow-50' : ''}`}>
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium">Sledovaný účet {index + 1}</h4>
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => togglePersonOfInterest("following", index)}
                      className={form.watch(`following.${index}.isPersonOfInterest`) ? "text-yellow-600" : "text-gray-400"}
                      title={form.watch(`following.${index}.isPersonOfInterest`) ? "Odstranit z zájmových osob" : "Označit jako zájmovou osobu"}
                    >
                      {form.watch(`following.${index}.isPersonOfInterest`) ? (
                        <Star className="h-4 w-4 fill-current" />
                      ) : (
                        <StarOff className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFollowing(index)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>Uživatelské jméno</Label>
                      <Input
                        {...form.register(`following.${index}.username`)}
                        placeholder="@uzivatelske_jmeno"
                      />
                    </div>
                    <div>
                      <Label>Zobrazované jméno</Label>
                      <Input
                        {...form.register(`following.${index}.displayName`)}
                        placeholder="Jméno na profilu"
                      />
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label>URL profilu</Label>
                      <Input
                        {...form.register(`following.${index}.profileUrl`)}
                        placeholder="https://instagram.com/..."
                      />
                    </div>
                    <div>
                      <Label>Počet sledujících</Label>
                      <Input
                        type="number"
                        {...form.register(`following.${index}.followersCount`, { valueAsNumber: true })}
                        placeholder="Počet"
                      />
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch
                      {...form.register(`following.${index}.isVerified`)}
                    />
                    <Label>Ověřený účet</Label>
                  </div>
                  
                  <div>
                    <Label>Poznámky</Label>
                    <Textarea
                      {...form.register(`following.${index}.notes`)}
                      placeholder="Proč je tento účet zajímavý..."
                      rows={2}
                    />
                  </div>
                </div>
              </Card>
            ))}
          </CardContent>
        </Card>

        {/* Hashtagy, Mentions, Lokace */}
        <Card>
          <CardHeader>
            <CardTitle>Hashtagy, zmínky a lokace</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="topHashtags">Často používané hashtagy</Label>
              <Textarea
                id="topHashtags"
                {...form.register("topHashtags")}
                placeholder="Zadejte hashtagy oddělené mezerami (např. #fitness #travel #food)"
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="mentions">Často označované osoby</Label>
              <Textarea
                id="mentions"
                {...form.register("mentions")}
                placeholder="@osoba1 @osoba2 @osoba3"
                rows={3}
              />
            </div>
            <div>
              <Label htmlFor="recentLocations">Nalezené lokace</Label>
              <Textarea
                id="recentLocations"
                {...form.register("recentLocations")}
                placeholder="Seznam často navštěvovaných nebo označovaných míst (oddělené čárkami)"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Analýza aktivity */}
        <Card>
          <CardHeader>
            <CardTitle>Analýza aktivity</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Pravidelnost přidávání příspěvků</Label>
                <Select
                  value={form.watch("postingFrequency")}
                  onValueChange={(value) => form.setValue("postingFrequency", value as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Vyberte" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="multiple-daily">Několikrát denně</SelectItem>
                    <SelectItem value="daily">Denně</SelectItem>
                    <SelectItem value="weekly">Několikrát týdně</SelectItem>
                    <SelectItem value="monthly">Několikrát měsíčně</SelectItem>
                    <SelectItem value="rarely">Zřídka</SelectItem>
                    <SelectItem value="irregular">Nepravidelně</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Obvyklá doba aktivity</Label>
                <Select
                  value={form.watch("activeTime")}
                  onValueChange={(value) => form.setValue("activeTime", value as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Vyberte" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="morning">Ráno (6-10h)</SelectItem>
                    <SelectItem value="midday">Dopoledne (10-13h)</SelectItem>
                    <SelectItem value="afternoon">Odpoledne (13-17h)</SelectItem>
                    <SelectItem value="evening">Večer (17-22h)</SelectItem>
                    <SelectItem value="night">V noci (22-6h)</SelectItem>
                    <SelectItem value="weekend">Převážně víkendy</SelectItem>
                    <SelectItem value="workdays">Převážně pracovní dny</SelectItem>
                    <SelectItem value="irregular">Nepravidelně</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Typ obsahu</Label>
                <Select
                  value={form.watch("contentType")}
                  onValueChange={(value) => form.setValue("contentType", value as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Vyberte" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="personal">Osobní</SelectItem>
                    <SelectItem value="professional">Profesní</SelectItem>
                    <SelectItem value="lifestyle">Životní styl</SelectItem>
                    <SelectItem value="travel">Cestování</SelectItem>
                    <SelectItem value="food">Jídlo</SelectItem>
                    <SelectItem value="fitness">Fitness</SelectItem>
                    <SelectItem value="fashion">Móda</SelectItem>
                    <SelectItem value="business">Byznys</SelectItem>
                    <SelectItem value="entertainment">Zábava</SelectItem>
                    <SelectItem value="mixed">Smíšený</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Úroveň aktivity</Label>
                <Select
                  value={form.watch("activityLevel")}
                  onValueChange={(value) => form.setValue("activityLevel", value as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Vyberte" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="very-active">Velmi aktivní</SelectItem>
                    <SelectItem value="active">Aktivní</SelectItem>
                    <SelectItem value="moderate">Středně aktivní</SelectItem>
                    <SelectItem value="low">Málo aktivní</SelectItem>
                    <SelectItem value="inactive">Neaktivní</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label>Interakce s ostatními uživateli</Label>
              <Select
                value={form.watch("interactionLevel")}
                onValueChange={(value) => form.setValue("interactionLevel", value as any)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Vyberte" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="very-interactive">Velmi interaktivní (časté komentáře, odpovědi)</SelectItem>
                  <SelectItem value="interactive">Interaktivní (občasné komentáře)</SelectItem>
                  <SelectItem value="passive">Pasivní (převážně jen lajky)</SelectItem>
                  <SelectItem value="lurker">Pozorovatel (minimální interakce)</SelectItem>
                  <SelectItem value="none">Žádná viditelná interakce</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="analysisNotes">Další postřehy o aktivitě</Label>
              <Textarea
                id="analysisNotes"
                {...form.register("analysisNotes")}
                placeholder="Zadejte další informace o aktivitě uživatele na Instagramu..."
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Analýza obsahu */}
        <Card>
          <CardHeader>
            <CardTitle>Analýza obsahu</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="contentAnalysis">Převažující témata</Label>
              <Textarea
                id="contentAnalysis"
                {...form.register("contentAnalysis")}
                placeholder="Hlavní témata obsahu..."
                rows={2}
              />
            </div>
            <div>
              <Label htmlFor="patterns">Opakující se vzorce</Label>
              <Textarea
                id="patterns"
                {...form.register("patterns")}
                placeholder="Opakující se vzorce v obsahu nebo aktivitě..."
                rows={2}
              />
            </div>
            <div>
              <Label htmlFor="changes">Změny v aktivitě</Label>
              <Textarea
                id="changes"
                {...form.register("changes")}
                placeholder="Významné změny v aktivitě nebo obsahu v průběhu času..."
                rows={2}
              />
            </div>
          </CardContent>
        </Card>

        {/* Fotodokumentace */}
        <Card className="shadow-md">
          <PhotoDocumentationSection
            form={form as any}
            namePrefix="photos"
            title="Fotodokumentace Instagram profilu"
            description="Nahrajte a spravujte snímky obrazovky Instagram profilu. Fotografie se ukládají trvale do aplikace."
            photoHint="instagram profile social media screenshot"
            caseId={caseId}
            subjectId={subject.id}
            moduleId={moduleId}
          />
        </Card>

        {/* OSINT poznámky */}
        <Card>
          <CardHeader>
            <CardTitle>OSINT poznámky</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="osintNotes">OSINT poznámky</Label>
              <Textarea
                id="osintNotes"
                {...form.register("osintNotes")}
                placeholder="Poznámky z OSINT vyšetřování Instagram profilu..."
                rows={4}
              />
            </div>
            <div>
              <Label htmlFor="investigationNotes">Vyšetřovací poznámky</Label>
              <Textarea
                id="investigationNotes"
                {...form.register("investigationNotes")}
                placeholder="Interní poznámky vyšetřovatele..."
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Tlačítko pro uložení */}
        <div className="flex justify-end pt-6">
          <Button type="submit" disabled={isSaving}>
            {isSaving ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Ukládání...
              </>
            ) : (
              "Uložit Instagram modul"
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
} 