
'use server';
/**
 * @fileOverview A Genkit flow to fetch company data directly from ARES API.
 *
 * - lookupCompanyInAresDirectly - Fetches company data from ARES based on IČO.
 * - AresLookupInput - The input type for the lookupCompanyInAresDirectly function.
 * - AresLookupOutput - The return type for the lookupCompanyInAresDirectly function.
 */

import { ai } from '@/ai/genkit';
import { z } from 'genkit';

const AresLookupInputSchema = z.object({
  companyId: z.string()
    .length(8, "IČO musí mít přesně 8 znaků.")
    .regex(/^\d{8}$/, "IČO musí obsahovat pouze číslice.")
    .describe('The IČO (company identification number) of the company to look up.'),
});
export type AresLookupInput = z.infer<typeof AresLookupInputSchema>;

const AresLookupOutputSchema = z.object({
  ico: z.string().describe('The IČO of the company.'),
  name: z.string().describe('The official name of the company.'),
  address: z.string().describe('The registered address of the company.'),
  legalForm: z.string().optional().describe('The legal form of the company (e.g., s.r.o., a.s.).'),
  establishmentDate: z.string().optional().describe('The date of establishment of the company.'),
  dic: z.string().optional().describe('The tax identification number (DIČ) of the company.'),
  isVatPayer: z.boolean().optional().describe('Whether the company is a VAT payer.'),
  businessScope: z.string().optional().describe('The business scope/activities of the company.'),
  status: z.string().optional().describe('The current status of the company (active, in liquidation, etc.).'),
  statutoryRepresentatives: z.array(z.string()).optional().describe('List of statutory representatives of the company.'),
});
export type AresLookupOutput = z.infer<typeof AresLookupOutputSchema>;

// Helper function to process data from ARES, extracting more detailed information
function processAresApiResponse(data: any): AresLookupOutput {
  if (!data) {
    throw new Error('Data z ARES neobsahují potřebné informace.');
  }

  // Basic address formatting, can be more complex if needed
  let formattedAddress = 'Adresa není k dispozici';
  if (data.sidlo) {
    if (data.sidlo.textovaAdresa) {
      formattedAddress = data.sidlo.textovaAdresa;
    } else {
        // Attempt to construct from parts if textovaAdresa is missing
        const { ulice, cisloDomovni, cisloOrientacni, nazevObce, psc, stat } = data.sidlo;
        let addressParts = [];
        if (ulice && (cisloDomovni || cisloOrientacni)) {
            addressParts.push(`${ulice} ${cisloDomovni || ''}${cisloOrientacni ? `/${cisloOrientacni}` : ''}`.trim());
        } else if (ulice) {
            addressParts.push(ulice);
        }
        if (nazevObce) addressParts.push(nazevObce);
        if (psc) addressParts.push(psc);
        if (stat) addressParts.push(stat);
        if (addressParts.length > 0) {
            formattedAddress = addressParts.join(', ');
        }
    }
  }

  // Extract DIČ (tax ID) if available
  let dic = '';
  if (data.dic) {
    dic = data.dic;
  }

  // Determine if the company is a VAT payer
  let isVatPayer = false;
  if (data.dic && data.dic.startsWith('CZ')) {
    isVatPayer = true;
  }

  // Extract business scope/activities
  let businessScope = '';
  if (data.primarniCinnosti && data.primarniCinnosti.length > 0) {
    businessScope = data.primarniCinnosti.map((activity: any) => activity.nazev || '').filter(Boolean).join('; ');
  } else if (data.obory && data.obory.length > 0) {
    businessScope = data.obory.map((field: any) => field.nazev || '').filter(Boolean).join('; ');
  }

  // Determine company status
  let status = 'Aktivní';
  if (data.stavSubjektu) {
    status = data.stavSubjektu;
  } else if (data.datumZaniku) {
    status = 'Zaniklý';
  } else if (data.datumVstupu && data.datumVstupu.includes('likvidace')) {
    status = 'V likvidaci';
  }

  // Extract statutory representatives
  let statutoryRepresentatives: string[] = [];
  if (data.statutarniOrgan && data.statutarniOrgan.length > 0) {
    statutoryRepresentatives = data.statutarniOrgan.map((person: any) => {
      if (person.jmeno && person.prijmeni) {
        return `${person.jmeno} ${person.prijmeni}${person.funkce ? ` (${person.funkce})` : ''}`;
      } else if (person.nazev) {
        return person.nazev;
      }
      return '';
    }).filter(Boolean);
  }

  return {
    ico: data.ico || '',
    name: data.obchodniJmeno || '',
    legalForm: data.pravniForma?.nazev || '',
    address: formattedAddress,
    establishmentDate: data.datumVzniku || undefined,
    dic: dic || undefined,
    isVatPayer: isVatPayer || undefined,
    businessScope: businessScope || undefined,
    status: status || undefined,
    statutoryRepresentatives: statutoryRepresentatives.length > 0 ? statutoryRepresentatives : undefined,
  };
}

export async function lookupCompanyInAresDirectly(input: AresLookupInput): Promise<AresLookupOutput> {
  return aresDirectLookupFlow(input);
}

const aresDirectLookupFlow = ai.defineFlow(
  {
    name: 'aresDirectLookupFlow',
    inputSchema: AresLookupInputSchema,
    outputSchema: AresLookupOutputSchema,
  },
  async ({ companyId }: AresLookupInput) => {
    console.log('aresDirectLookupFlow: Vyhledání firmy podle IČO v ARES:', companyId);

    // Additional validation to ensure IČO is in correct format
    if (!/^\d{8}$/.test(companyId)) {
      throw new Error("IČO musí obsahovat přesně 8 číslic.");
    }

    const url = `https://ares.gov.cz/ekonomicke-subjekty-v-be/rest/ekonomicke-subjekty/${companyId}`;

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error(`Společnost s IČO ${companyId} nebyla v ARES nalezena.`);
        }
        const errorBody = await response.text();
        console.error('ARES API error response:', errorBody);
        throw new Error(`Chyba při komunikaci s ARES API: ${response.status} ${response.statusText}. Detail: ${errorBody}`);
      }

      const data = await response.json();
      console.log('aresDirectLookupFlow: Data z ARES API:', data);

      const processedData = processAresApiResponse(data);
      console.log('aresDirectLookupFlow: Zpracovaná data:', processedData);
      return processedData;

    } catch (error: any) {
      console.error('aresDirectLookupFlow: Selhání fetch operace nebo zpracování:', error);
      if (error instanceof Error) {
        throw error; // Re-throw known errors
      }
      throw new Error('Nepodařilo se navázat spojení s ARES API nebo zpracovat odpověď.');
    }
  }
);
