import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

// Základní typy
export const ipAddressTypes = ["ipv4", "ipv6"] as const;
export const ipAddressSources = [
  "osint", "investigation", "public_data", "social_media",
  "direct_communication", "server_logs", "email_headers",
  "network_scan", "other"
] as const;

export const ipAddressUsages = [
  "residential", "business", "hosting", "vpn", "proxy",
  "tor_exit_node", "mobile", "datacenter", "other"
] as const;

export const ipAddressVerificationStatuses = [
  "verified", "unverified", "invalid", "suspicious", "unknown"
] as const;

export const networkProtocols = [
  "http", "https", "ftp", "ssh", "telnet", "smtp",
  "pop3", "imap", "dns", "rdp", "vnc", "other"
] as const;

export const networkDeviceTypes = [
  "router", "switch", "firewall", "server", "desktop",
  "laptop", "mobile", "iot", "other"
] as const;

export const networkConnectionTypes = [
  "direct", "vpn", "proxy", "tor", "other"
] as const;

export const malwareIndicatorTypes = [
  "hash", "filename", "url", "ip", "domain", "other"
] as const;

export const malwareSeverityLevels = [
  "low", "medium", "high", "critical"
] as const;

export const portStatuses = [
  "open", "closed", "filtered"
] as const;

// Schémata pro validaci
const geoLocationSchema = z.object({
  country: z.string().optional(),
  region: z.string().optional(),
  city: z.string().optional(),
  postalCode: z.string().optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  timezone: z.string().optional(),
  isp: z.string().optional(),
  organization: z.string().optional(),
  asn: z.string().optional(),
  asnOrganization: z.string().optional(),
}).optional();

const portScanResultSchema = z.object({
  id: z.string().default(() => uuidv4()),
  port: z.number().min(1).max(65535),
  protocol: z.enum(networkProtocols),
  service: z.string().optional(),
  version: z.string().optional(),
  status: z.enum(portStatuses),
  banner: z.string().optional(),
  scanDate: z.string(),
  notes: z.string().optional(),
});

const networkActivitySchema = z.object({
  id: z.string().default(() => uuidv4()),
  date: z.string(),
  activityType: z.string(),
  description: z.string(),
  sourceIp: z.string().optional(),
  destinationIp: z.string().optional(),
  protocol: z.enum(networkProtocols).optional(),
  port: z.number().min(1).max(65535).optional(),
  userAgent: z.string().optional(),
  notes: z.string().optional(),
});

const malwareIndicatorSchema = z.object({
  id: z.string().default(() => uuidv4()),
  indicatorType: z.enum(malwareIndicatorTypes),
  value: z.string(),
  malwareFamily: z.string().optional(),
  detectionDate: z.string().optional(),
  source: z.string().optional(),
  severity: z.enum(malwareSeverityLevels).optional(),
  notes: z.string().optional(),
});

const photoMetadataSchema = z.object({
  id: z.string().default(() => uuidv4()),
  fileName: z.string().optional(),
  storagePath: z.string().optional(),
  downloadURL: z.string().optional(),
  description: z.string().optional(),
  dateTaken: z.string().optional(),
  sourceURL: z.string().optional(),
});

export const ipAddressRecordSchema = z.object({
  id: z.string().default(() => uuidv4()),
  ipAddress: z.string(),
  ipType: z.enum(ipAddressTypes),
  source: z.enum(ipAddressSources),
  otherSourceDetail: z.string().optional(),
  discoveryDate: z.string().optional(),
  verificationStatus: z.enum(ipAddressVerificationStatuses),
  verificationDate: z.string().optional(),
  usage: z.enum(ipAddressUsages).optional(),
  otherUsageDetail: z.string().optional(),
  hostname: z.string().optional(),
  macAddress: z.string().optional(),
  connectionType: z.enum(networkConnectionTypes).optional(),
  otherConnectionTypeDetail: z.string().optional(),
  owner: z.string().optional(),
  associatedOrganization: z.string().optional(),
  geoLocation: geoLocationSchema,
  portScanResults: z.array(portScanResultSchema).optional().default([]),
  activities: z.array(networkActivitySchema).optional().default([]),
  malwareIndicators: z.array(malwareIndicatorSchema).optional().default([]),
  relatedDomains: z.array(z.string()).optional().default([]),
  relatedDevices: z.array(z.string()).optional().default([]),
  notes: z.string().optional(),
  photos: z.array(photoMetadataSchema).optional().default([]),
});

export const domainRecordSchema = z.object({
  id: z.string().default(() => uuidv4()),
  domainName: z.string(),
  registrationDate: z.string().optional(),
  expirationDate: z.string().optional(),
  registrar: z.string().optional(),
  registrantName: z.string().optional(),
  registrantOrganization: z.string().optional(),
  registrantEmail: z.string().optional(),
  registrantPhone: z.string().optional(),
  nameservers: z.array(z.string()).optional().default([]),
  ipAddresses: z.array(z.string()).optional().default([]),
  notes: z.string().optional(),
  photos: z.array(photoMetadataSchema).optional().default([]),
});

export const networkDeviceSchema = z.object({
  id: z.string().default(() => uuidv4()),
  name: z.string().optional(),
  deviceType: z.enum(networkDeviceTypes),
  otherDeviceTypeDetail: z.string().optional(),
  macAddress: z.string().optional(),
  ipAddress: z.string().optional(),
  manufacturer: z.string().optional(),
  model: z.string().optional(),
  operatingSystem: z.string().optional(),
  firmwareVersion: z.string().optional(),
  discoveryDate: z.string().optional(),
  lastSeenDate: z.string().optional(),
  vulnerabilities: z.array(z.string()).optional().default([]),
  notes: z.string().optional(),
  photos: z.array(photoMetadataSchema).optional().default([]),
});

export const networkAnalysisModuleSchema = z.object({
  ipAddresses: z.array(ipAddressRecordSchema).default([]),
  domains: z.array(domainRecordSchema).default([]),
  devices: z.array(networkDeviceSchema).default([]),
  networkMap: z.string().optional(),
  generalNotes: z.string().optional(),
});

export type NetworkAnalysisModuleFormValues = z.infer<typeof networkAnalysisModuleSchema>;
