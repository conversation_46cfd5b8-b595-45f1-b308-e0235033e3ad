import { Timestamp } from "firebase/firestore";
import { PhotoMetadata } from "."; // Assuming PhotoMetadata is in index.ts

export interface TwitterTweet {
  id: string;
  text: string;
  date: string | Timestamp;
  likes: number;
  retweets: number;
  replies?: number;
  source?: string; // e.g., "Twitter Web App", "Twitter for iPhone"
  inReplyToStatusId?: string;
  inReplyToUserId?: string;
  inReplyToScreenName?: string;
  hashtags?: string[];
  mentions?: string[]; // screen names
  urls?: string[];
  media?: {
    type: "photo" | "video" | "animated_gif";
    url: string;
    storagePath?: string; // If uploaded to our storage
  }[];
  location?: {
    name?: string;
    coordinates?: [number, number]; // [longitude, latitude]
  };
  language?: string;
}

export interface TwitterUser {
  id: string;
  screenName: string;
  name: string;
  profileUrl: string;
  profileImageUrl?: string;
  isVerified?: boolean;
  isProtected?: boolean;
  notes?: string;
}

export interface TwitterModuleData {
  subjectId: string;
  profileUrl?: string;
  username?: string; // @username
  displayName?: string;
  userId?: string; // Twitter User ID
  bio?: string;
  location?: string;
  website?: string;
  joinDate?: string | Timestamp;
  tweetsCount?: number;
  followingCount?: number;
  followersCount?: number;
  tweets?: TwitterTweet[];
  // Potentially lists of followers, following, likes, etc.
  notes?: string;
  osintNotes?: string;
  lastUpdatedAt: Timestamp | null;
  createdAt: Timestamp | null;
} 