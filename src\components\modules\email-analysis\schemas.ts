"use client";

import { z } from "zod";
import { YesNoUnknown } from "@/types";

// Photo Metadata Schema
export const photoMetadataSchema = z.object({
  id: z.string(),
  fileName: z.string().optional(),
  storagePath: z.string().optional(),
  downloadURL: z.string().optional(),
  description: z.string().optional(),
  dateTaken: z.string().optional(),
  sourceURL: z.string().optional(),
  location: z.string().optional(),
  notes: z.string().optional(),
});
export type PhotoMetadata = z.infer<typeof photoMetadataSchema>;

// Email Sources
export const emailSources = [
  "osint", 
  "investigation", 
  "public_data", 
  "social_media", 
  "data_breach", 
  "direct_communication", 
  "other"
] as const;

// Email Verification Statuses
export const emailVerificationStatuses = [
  "verified", 
  "unverified", 
  "invalid", 
  "disposable", 
  "unknown"
] as const;

// Data Breach Severities
export const dataBreachSeverities = [
  "none", 
  "low", 
  "medium", 
  "high", 
  "critical", 
  "unknown"
] as const;

// Platform Types
export const platformTypes = [
  "social", 
  "forum", 
  "ecommerce", 
  "service", 
  "professional", 
  "dating", 
  "gaming", 
  "other"
] as const;

// Data Breach Record Schema
export const dataBreachRecordSchema = z.object({
  id: z.string(),
  source: z.string().min(1, "Zdroj úniku dat je povinný"),
  date: z.string().optional(),
  recordsCount: z.string().optional(),
  compromisedData: z.array(z.string()).default([]),
  description: z.string().optional(),
});
export type DataBreachRecord = z.infer<typeof dataBreachRecordSchema>;

// Connected Profile Schema
export const connectedProfileSchema = z.object({
  id: z.string(),
  platformType: z.string().min(1, "Typ platformy je povinný"),
  platformName: z.string().min(1, "Název platformy je povinný"),
  username: z.string().min(1, "Uživatelské jméno je povinné"),
  profileUrl: z.string().url("Neplatná URL adresa").optional().or(z.literal("")),
  notes: z.string().optional(),
  verificationStatus: z.enum(["yes", "no", "unknown"] as [YesNoUnknown, ...YesNoUnknown[]]).optional(),
});
export type ConnectedProfile = z.infer<typeof connectedProfileSchema>;

// Email Headers Analysis Schema
export const emailHeadersAnalysisSchema = z.object({
  senderIp: z.string().optional(),
  ipGeolocation: z.string().optional(),
  emailClient: z.string().optional(),
  authResults: z.string().optional(),
  emailRoute: z.array(z.string()).optional().default([]),
  rawHeaders: z.string().optional(),
  analysisDate: z.string().optional(),
});
export type EmailHeadersAnalysis = z.infer<typeof emailHeadersAnalysisSchema>;

// Email Record Schema
export const emailRecordSchema = z.object({
  id: z.string(),
  emailAddress: z.string().email("Neplatná emailová adresa"),
  source: z.enum(emailSources),
  otherSourceDetail: z.string().optional(),
  discoveryDate: z.string().optional(),
  verificationStatus: z.enum(emailVerificationStatuses),
  verificationDate: z.string().optional(),
  probableName: z.string().optional(),
  associatedOrganization: z.string().optional(),
  dataBreaches: z.array(dataBreachRecordSchema).optional().default([]),
  connectedProfiles: z.array(connectedProfileSchema).optional().default([]),
  headersAnalysis: emailHeadersAnalysisSchema.optional(),
  notes: z.string().optional(),
  photos: z.array(photoMetadataSchema).optional().default([]),
}).superRefine((data, ctx) => {
  if (data.source === 'other' && !data.otherSourceDetail?.trim()) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Upřesnění zdroje je povinné.", path: ['otherSourceDetail'] });
  }
});
export type EmailRecord = z.infer<typeof emailRecordSchema>;

// Email Analysis Module Schema
export const emailAnalysisModuleSchema = z.object({
  emails: z.array(emailRecordSchema).optional().default([]),
  generalNotes: z.string().optional(),
});

export type EmailAnalysisModuleFormValues = z.infer<typeof emailAnalysisModuleSchema>;
