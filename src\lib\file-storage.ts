import { writeFile, mkdir } from 'fs/promises';
import path from 'path';

export async function saveImageToPublicFolder(
  base64Data: string, 
  caseId: string, 
  subjectId: string, 
  moduleId: string, 
  fileName: string
): Promise<string> {
  try {
    // Odstraníme data:image/... prefix z base64
    const base64Image = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
    
    // Vytvoříme unique filename
    const timestamp = Date.now();
    const extension = fileName.split('.').pop() || 'jpg';
    const uniqueFileName = `${caseId}_${subjectId}_${moduleId}_${timestamp}.${extension}`;
    
    // Cesta k uploads složce
    const uploadsDir = path.join(process.cwd(), 'public', 'uploads', 'evidence-obyvatel');
    const filePath = path.join(uploadsDir, uniqueFileName);
    
    // Vytvoříme složku pokud neexistuje
    await mkdir(uploadsDir, { recursive: true });
    
    // Uložíme soubor
    await writeFile(filePath, base64Image, 'base64');
    
    // Vratíme relativní cestu pro použití v aplikaci
    return `/uploads/evidence-obyvatel/${uniqueFileName}`;
    
  } catch (error) {
    console.error('Chyba při ukládání obrázku:', error);
    throw new Error('Nepodařilo se uložit obrázek');
  }
} 