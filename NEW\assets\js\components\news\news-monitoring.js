/**
 * Aktu<PERSON><PERSON><PERSON> události - funkce pro monitoring zpráv a událostí
 */

// API klíče pro různé zpravodajské služby
const NEWS_API_KEYS = {
    newsapi: '', // NewsAPI.org - vyžaduje registraci
    gdelt: '',   // GDELT Project - není potřeba API klíč pro základní použití
    ctk: '',     // ČTK API - vyžaduje registraci a předplatné
    seznam: ''   // Seznam Zprávy API - vyžaduje registraci
};

// Globální proměnné pro modul
let newsCache = {};
let activeNewsFilters = {
    keywords: [],
    sources: [],
    categories: [],
    languages: ['cs', 'en'],
    fromDate: '',
    toDate: '',
    entities: []
};
let monitoringIntervals = {};
let alertSettings = {
    email: '',
    keywords: [],
    entities: [],
    frequency: 'daily', // 'realtime', 'hourly', 'daily'
    threshold: 3 // minimální počet zmínek pro odeslání alertu
};

/**
 * Inicializace funkcí pro monitoring aktuálních událostí
 */
function initNewsMonitoring() {
    console.log('Inicializace modulu aktuálních událostí');
    
    // Najít aktuální modul aktuálních událostí
    const newsModule = document.querySelector('.module[id^="module-aktualni-udalosti"]');
    if (!newsModule) {
        console.error('Modul aktuálních událostí nebyl nalezen');
        return;
    }
    
    console.log('Nalezen modul aktuálních událostí:', newsModule.id);
    
    // Inicializace filtračních prvků
    initNewsFilters(newsModule);
    
    // Přidání event listenerů pro tlačítka v modulu aktuálních událostí
    newsModule.addEventListener('click', function(event) {
        // Vyhledání zpráv
        if (event.target.closest('.search-news')) {
            searchNews();
        }
        
        // Nastavení monitoringu
        if (event.target.closest('.setup-monitoring')) {
            setupMonitoring();
        }
        
        // Nastavení alertů
        if (event.target.closest('.setup-alerts')) {
            setupAlerts();
        }
        
        // Export zpráv
        if (event.target.closest('.export-news')) {
            exportNews();
        }
        
        // Přidání zdroje
        if (event.target.closest('.add-source')) {
            addNewsSource();
        }
        
        // Přidání klíčového slova
        if (event.target.closest('.add-keyword')) {
            addKeyword();
        }
        
        // Přidání entity
        if (event.target.closest('.add-entity')) {
            addEntity();
        }
        
        // Zobrazení detailu zprávy
        if (event.target.closest('.news-item')) {
            const newsId = event.target.closest('.news-item').getAttribute('data-news-id');
            if (newsId) {
                showNewsDetail(newsId);
            }
        }
    });
    
    // Přidání event listenerů pro formuláře
    const newsSearchForm = newsModule.querySelector('#news-search-form');
    if (newsSearchForm) {
        newsSearchForm.addEventListener('submit', function(event) {
            event.preventDefault();
            searchNews();
        });
    }
    
    // Inicializace fotogalerie
    initNewsPhotoGallery(newsModule);
    
    // Načtení výchozích zpráv
    loadDefaultNews();
}

/**
 * Inicializace filtračních prvků
 * @param {HTMLElement} moduleElement - Element modulu aktuálních událostí
 */
function initNewsFilters(moduleElement) {
    console.log('Inicializace filtračních prvků');
    
    // Inicializace datepickeru pro filtrování podle data
    const fromDateInput = moduleElement.querySelector('#news-from-date');
    const toDateInput = moduleElement.querySelector('#news-to-date');
    
    if (fromDateInput && toDateInput) {
        // Nastavení výchozích hodnot (posledních 7 dní)
        const today = new Date();
        const weekAgo = new Date();
        weekAgo.setDate(today.getDate() - 7);
        
        fromDateInput.valueAsDate = weekAgo;
        toDateInput.valueAsDate = today;
        
        // Aktualizace filtrů
        activeNewsFilters.fromDate = formatDate(weekAgo);
        activeNewsFilters.toDate = formatDate(today);
    }
    
    // Inicializace výběru zdrojů
    const sourcesSelect = moduleElement.querySelector('#news-sources');
    if (sourcesSelect) {
        // Přidání dostupných zdrojů
        const availableSources = getAvailableNewsSources();
        
        availableSources.forEach(source => {
            const option = document.createElement('option');
            option.value = source.id;
            option.textContent = source.name;
            sourcesSelect.appendChild(option);
        });
        
        // Event listener pro změnu výběru zdrojů
        sourcesSelect.addEventListener('change', function() {
            const selectedSources = Array.from(this.selectedOptions).map(option => option.value);
            activeNewsFilters.sources = selectedSources;
        });
    }
    
    // Inicializace výběru kategorií
    const categoriesSelect = moduleElement.querySelector('#news-categories');
    if (categoriesSelect) {
        // Přidání dostupných kategorií
        const availableCategories = getAvailableNewsCategories();
        
        availableCategories.forEach(category => {
            const option = document.createElement('option');
            option.value = category.id;
            option.textContent = category.name;
            categoriesSelect.appendChild(option);
        });
        
        // Event listener pro změnu výběru kategorií
        categoriesSelect.addEventListener('change', function() {
            const selectedCategories = Array.from(this.selectedOptions).map(option => option.value);
            activeNewsFilters.categories = selectedCategories;
        });
    }
    
    // Inicializace výběru jazyků
    const languagesSelect = moduleElement.querySelector('#news-languages');
    if (languagesSelect) {
        // Přidání dostupných jazyků
        const availableLanguages = getAvailableNewsLanguages();
        
        availableLanguages.forEach(language => {
            const option = document.createElement('option');
            option.value = language.code;
            option.textContent = language.name;
            
            // Předvybrání češtiny a angličtiny
            if (language.code === 'cs' || language.code === 'en') {
                option.selected = true;
            }
            
            languagesSelect.appendChild(option);
        });
        
        // Event listener pro změnu výběru jazyků
        languagesSelect.addEventListener('change', function() {
            const selectedLanguages = Array.from(this.selectedOptions).map(option => option.value);
            activeNewsFilters.languages = selectedLanguages;
        });
    }
}

/**
 * Formátování data do formátu YYYY-MM-DD
 * @param {Date} date - Datum
 * @returns {string} - Formátované datum
 */
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return `${year}-${month}-${day}`;
}

/**
 * Získání dostupných zpravodajských zdrojů
 * @returns {Array} - Pole dostupných zdrojů
 */
function getAvailableNewsSources() {
    return [
        { id: 'ctk', name: 'ČTK' },
        { id: 'idnes', name: 'iDNES.cz' },
        { id: 'seznam-zpravy', name: 'Seznam Zprávy' },
        { id: 'aktualne', name: 'Aktuálně.cz' },
        { id: 'novinky', name: 'Novinky.cz' },
        { id: 'ct24', name: 'ČT24' },
        { id: 'denik', name: 'Deník.cz' },
        { id: 'irozhlas', name: 'iROZHLAS' },
        { id: 'lidovky', name: 'Lidovky.cz' },
        { id: 'e15', name: 'E15' },
        { id: 'ihned', name: 'iHNed.cz' },
        { id: 'echo24', name: 'Echo24' },
        { id: 'respekt', name: 'Respekt' },
        { id: 'reflex', name: 'Reflex' },
        { id: 'blesk', name: 'Blesk.cz' }
    ];
}

/**
 * Získání dostupných kategorií zpráv
 * @returns {Array} - Pole dostupných kategorií
 */
function getAvailableNewsCategories() {
    return [
        { id: 'general', name: 'Hlavní zprávy' },
        { id: 'business', name: 'Byznys' },
        { id: 'technology', name: 'Technologie' },
        { id: 'entertainment', name: 'Zábava' },
        { id: 'health', name: 'Zdraví' },
        { id: 'science', name: 'Věda' },
        { id: 'sports', name: 'Sport' },
        { id: 'politics', name: 'Politika' },
        { id: 'crime', name: 'Kriminalita' },
        { id: 'world', name: 'Svět' },
        { id: 'domestic', name: 'Domácí' }
    ];
}

/**
 * Získání dostupných jazyků
 * @returns {Array} - Pole dostupných jazyků
 */
function getAvailableNewsLanguages() {
    return [
        { code: 'cs', name: 'Čeština' },
        { code: 'en', name: 'Angličtina' },
        { code: 'sk', name: 'Slovenština' },
        { code: 'de', name: 'Němčina' },
        { code: 'fr', name: 'Francouzština' },
        { code: 'es', name: 'Španělština' },
        { code: 'it', name: 'Italština' },
        { code: 'ru', name: 'Ruština' },
        { code: 'pl', name: 'Polština' }
    ];
}
