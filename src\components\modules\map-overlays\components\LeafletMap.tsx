"use client";

import { useEffect, useRef, useState } from "react";
import { MapOverlaysModuleFormValues, MapPointFormValues } from "../schemas";

// Rozšíříme Window interface pro naše potřeby
declare global {
  interface Window {
    drawingInstructions: HTMLDivElement | null;
  }
}

interface LeafletMapProps {
  formValues: MapOverlaysModuleFormValues;
  visibleLayers: Record<string, boolean>;
  onPointSelect?: (pointId: string) => void;
  onMapClick?: (lat: number, lng: number) => void;
  onAreaClick?: (areaId: string) => void;
  onRouteClick?: (routeId: string) => void;
  selectedPointId?: string | null;
  selectedAreaId?: string | null;
  selectedRouteId?: string | null;
  drawingMode?: "point" | "area" | "route" | null;
  onAreaDraw?: (coordinates: Array<{lat: number, lng: number}>) => void;
  onRouteDraw?: (coordinates: Array<{lat: number, lng: number}>) => void;
}

export default function LeafletMap({
  formValues,
  visibleLayers,
  onPointSelect,
  onMapClick,
  onAreaClick,
  onRouteClick,
  selectedPointId,
  selectedAreaId,
  selectedRouteId,
  drawingMode,
  onAreaDraw,
  onRouteDraw
}: LeafletMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const drawingLayerRef = useRef<any>(null);
  const drawingPointsRef = useRef<Array<{lat: number, lng: number}>>([]);
  const [isMapInitialized, setIsMapInitialized] = useState(false);
  const [isDrawing, setIsDrawing] = useState(false);

  // Reference na tlačítko pro dokončení kreslení
  const completeButtonRef = useRef<HTMLButtonElement | null>(null);

  // Inicializace mapy - použijeme useEffect s potřebnými závislostmi
  useEffect(() => {
    console.log("Initializing map with formValues:", formValues);

    // Odstraníme existující tlačítko pro dokončení, pokud existuje
    if (completeButtonRef.current) {
      try {
        // Zkusíme najít a odstranit tlačítko z různých možných rodičů
        if (completeButtonRef.current.parentNode) {
          completeButtonRef.current.parentNode.removeChild(completeButtonRef.current);
        }
      } catch (error) {
        console.warn("Error removing complete button during cleanup:", error);
      }
      completeButtonRef.current = null;
    }
    // Funkce pro inicializaci mapy
    const initMap = async () => {
      // Počkáme, až se DOM plně načte
      if (typeof window === 'undefined') return;

      // Počkáme, až bude mapRef.current k dispozici
      if (!mapRef.current) {
        console.log("Kontejner pro mapu není k dispozici, zkusíme to později");
        return;
      }

      try {
        // Dynamicky importujeme Leaflet
        const L = (await import('leaflet')).default;
        await import('leaflet/dist/leaflet.css');

        // Importujeme vlastní styly pro mapu
        const mapStylesLink = document.createElement('link');
        mapStylesLink.rel = 'stylesheet';
        mapStylesLink.href = '/map-styles.css';
        document.head.appendChild(mapStylesLink);

        // Přidáme Leaflet CSS
        if (!document.querySelector('link[href*="leaflet.css"]')) {
          const link = document.createElement('link');
          link.rel = 'stylesheet';
          link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
          document.head.appendChild(link);
        }

        // Opravíme ikony Leaflet
        delete L.Icon.Default.prototype._getIconUrl;
        L.Icon.Default.mergeOptions({
          iconRetinaUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png',
          iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
          shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
        });

        // Ověříme, že Leaflet je správně načten
        if (!L || !L.map) {
          console.error("Leaflet není správně načten");
          return;
        }

        // Přidáme explicitní typové anotace pro Leaflet
        const LeafletPolygon = L.Polygon;
        const LeafletPolyline = L.Polyline;

        // Zničíme existující mapu, pokud existuje
        if (mapInstanceRef.current) {
          mapInstanceRef.current.remove();
          mapInstanceRef.current = null;
        }

        // Ověříme, že mapRef.current stále existuje
        if (!mapRef.current) {
          console.error("Kontejner pro mapu přestal existovat během inicializace");
          return;
        }

        // Vytvoříme novou mapu
        const map = L.map(mapRef.current, {
          center: formValues.defaultCenter || [50.0755, 14.4378],
          zoom: formValues.defaultZoom || 10,
          zoomControl: false,
          attributionControl: true,
        });

        console.log("Map created successfully:", map);
        console.log("Map container:", map.getContainer());
        console.log("Map panes:", map.getPanes());

        // Uložíme instanci mapy
        mapInstanceRef.current = map;

        // Přidáme podkladovou mapu podle vybraného typu
        const basemapUrl =
          formValues.defaultBasemap === "satellite" ? 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}' :
          formValues.defaultBasemap === "terrain" ? 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png' :
          formValues.defaultBasemap === "dark" ? 'https://{s}.basemaps.cartocdn.com/dark_all/{z}/{x}/{y}{r}.png' :
          formValues.defaultBasemap === "hybrid" ? 'https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}' :
          formValues.defaultBasemap === "topo" ? 'https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png' :
          'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png';

        const attribution =
          formValues.defaultBasemap === "satellite" || formValues.defaultBasemap === "hybrid" ? '&copy; <a href="https://www.esri.com/">Esri</a>' :
          formValues.defaultBasemap === "terrain" || formValues.defaultBasemap === "topo" ? '&copy; <a href="https://opentopomap.org">OpenTopoMap</a> contributors' :
          formValues.defaultBasemap === "dark" ? '&copy; <a href="https://carto.com/">CARTO</a>' :
          '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors';

        L.tileLayer(basemapUrl, { attribution }).addTo(map);

        // Změníme kurzor podle režimu kreslení
        if (drawingMode === "area" || drawingMode === "route" || drawingMode === "point") {
          map.getContainer().style.cursor = 'crosshair';
        } else {
          map.getContainer().style.cursor = '';
        }

        // Přidáme událost kliknutí na mapu podle aktuálního režimu kreslení
        map.on('click', (e) => {
          // Zastavíme propagaci události, aby se formulář neodeslal
          if (e.originalEvent) {
            e.originalEvent.preventDefault();
            e.originalEvent.stopPropagation();
          }

          console.log("Map click event, drawing mode:", drawingMode);

          if (drawingMode === "point" && onMapClick) {
            // Režim přidávání bodů
            // Přidáme vizuální marker na místo kliknutí
            L.circleMarker([e.latlng.lat, e.latlng.lng], {
              radius: 8,
              color: "#3b82f6",
              fillColor: "#3b82f6",
              fillOpacity: 1,
              weight: 2
            }).addTo(map).bindTooltip("Nový bod").openTooltip();

            // Zavoláme callback pro přidání bodu
            onMapClick(e.latlng.lat, e.latlng.lng);
          }
          else if (drawingMode === "area" || drawingMode === "route") {
            // Režim kreslení oblasti nebo trasy
            console.log("Drawing mode active:", drawingMode, "isDrawing:", isDrawing);

            // Odstraníme existující tlačítko pro dokončení, pokud existuje
            if (completeButtonRef.current) {
              try {
                if (completeButtonRef.current.parentNode) {
                  completeButtonRef.current.parentNode.removeChild(completeButtonRef.current);
                }
              } catch (error) {
                console.warn("Error removing existing complete button:", error);
              }
              completeButtonRef.current = null;
            }

            if (!isDrawing) {
              // Začínáme kreslit - vytvoříme novou vrstvu
              setIsDrawing(true);
              drawingPointsRef.current = [{lat: e.latlng.lat, lng: e.latlng.lng}];
              console.log("Starting drawing, first point:", e.latlng.lat, e.latlng.lng);

              // Vytvoříme novou vrstvu pro kreslení
              if (drawingLayerRef.current) {
                map.removeLayer(drawingLayerRef.current);
                drawingLayerRef.current = null;
              }

              // Vytvoříme novou vrstvu a explicitně ji přidáme na mapu
              drawingLayerRef.current = L.layerGroup();
              drawingLayerRef.current.addTo(map);
              console.log("Drawing layer created and added to map:", drawingLayerRef.current);

              // Přidáme první bod s popiskem a zvýrazněním - VŽDY přímo na mapu pro jistotu
              try {
                console.log("Adding first point at:", e.latlng.lat, e.latlng.lng);

                // Přidáme marker přímo na mapu (ne do vrstvy) pro jistotu, že bude viditelný
                const marker = L.circleMarker([e.latlng.lat, e.latlng.lng], {
                  radius: 20,  // Ještě větší radius pro lepší viditelnost
                  color: '#ffffff',
                  fillColor: drawingMode === "area" ? "#10b981" : "#3b82f6",
                  fillOpacity: 1,
                  weight: 5,   // Silnější okraj
                  pane: 'overlayPane',  // Explicitně nastavíme pane
                  interactive: false    // Neinteraktivní, aby nepřekážel
                }).addTo(map);  // Přidáme PŘÍMO na mapu

                // Přidáme tooltip
                marker.bindTooltip("Bod 1", {
                  permanent: true,
                  direction: 'top',
                  offset: [0, -25],
                  className: 'drawing-tooltip'
                }).openTooltip();

                console.log("First point marker added directly to map");

                // Přidáme také do drawing layer pro správu
                if (drawingLayerRef.current) {
                  marker.addTo(drawingLayerRef.current);
                  console.log("First point marker also added to drawing layer");
                }

                // Přidáme zvýraznění bodu - také přímo na mapu
                const circle = L.circle([e.latlng.lat, e.latlng.lng], {
                  color: drawingMode === "area" ? "#10b981" : "#3b82f6",
                  fillColor: drawingMode === "area" ? "#10b981" : "#3b82f6",
                  fillOpacity: 0.3,
                  radius: 100,  // Větší radius pro lepší viditelnost
                  weight: 2,
                  pane: 'overlayPane',
                  interactive: false
                }).addTo(map);  // Přidáme PŘÍMO na mapu

                console.log("First point highlight added directly to map");

                // Přidáme také do drawing layer pro správu
                if (drawingLayerRef.current) {
                  circle.addTo(drawingLayerRef.current);
                }
              } catch (error) {
                console.error("Error adding first point:", error);
              }

              // Přidáme text s instrukcemi přímo na mapu - výraznější a lépe viditelné
              const instructionDiv = L.DomUtil.create('div', 'drawing-instructions');
              instructionDiv.innerHTML = drawingMode === "area"
                ? '<strong>KRESLENÍ OBLASTI:</strong> Klikněte na další místa na mapě pro přidání bodů oblasti (min. 3 body)'
                : '<strong>KRESLENÍ TRASY:</strong> Klikněte na další místa na mapě pro přidání bodů trasy (min. 2 body)';
              instructionDiv.style.position = 'absolute';
              instructionDiv.style.bottom = '30px';  // Posuneme výše od spodního okraje
              instructionDiv.style.left = '50%';
              instructionDiv.style.transform = 'translateX(-50%)';
              instructionDiv.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';  // Tmavší pozadí pro lepší kontrast
              instructionDiv.style.color = 'white';
              instructionDiv.style.padding = '12px 20px';  // Větší padding
              instructionDiv.style.borderRadius = '8px';  // Zaoblenější rohy
              instructionDiv.style.zIndex = '1000';
              instructionDiv.style.pointerEvents = 'none';
              instructionDiv.style.fontWeight = 'bold';
              instructionDiv.style.fontSize = '16px';  // Větší písmo
              instructionDiv.style.textAlign = 'center';
              instructionDiv.style.maxWidth = '80%';  // Omezíme šířku
              instructionDiv.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.3)';  // Přidáme stín
              instructionDiv.style.border = '2px solid ' + (drawingMode === "area" ? '#10b981' : '#3b82f6');  // Barevný okraj
              map.getContainer().appendChild(instructionDiv);

              // Uložíme referenci na instrukce
              window.drawingInstructions = instructionDiv;

              // Přidáme tlačítko pro dokončení kreslení - výraznější a lépe umístěné
              const completeButton = document.createElement('button');
              completeButton.className = 'complete-drawing-btn';
              completeButton.innerHTML = drawingMode === "area"
                ? '<strong>DOKONČIT OBLAST</strong> (min. 3 body)'
                : '<strong>DOKONČIT TRASU</strong> (min. 2 body)';
              completeButton.style.position = 'absolute';
              completeButton.style.top = '10px';  // Umístíme na horní část mapy
              completeButton.style.left = '50%';
              completeButton.style.transform = 'translateX(-50%)';
              completeButton.style.zIndex = '9999999';  // Velmi vysoký z-index, aby bylo nad vším
              completeButton.style.padding = '15px 30px';  // Větší padding pro lepší viditelnost
              completeButton.style.backgroundColor = drawingMode === "area" ? '#10b981' : '#3b82f6';
              completeButton.style.color = 'white';
              completeButton.style.border = '3px solid white';  // Silnější okraj
              completeButton.style.borderRadius = '8px';  // Zaoblenější rohy
              completeButton.style.cursor = 'pointer';
              completeButton.style.fontWeight = 'bold';
              completeButton.style.fontSize = '18px';  // Větší písmo
              completeButton.style.boxShadow = '0 6px 12px rgba(0, 0, 0, 0.4)';  // Výraznější stín
              completeButton.style.textAlign = 'center';
              completeButton.style.lineHeight = '1.2';
              completeButton.style.fontFamily = 'Arial, sans-serif';
              completeButton.style.textTransform = 'uppercase';
              completeButton.style.letterSpacing = '1px';

              // Přidáme hover efekt
              completeButton.onmouseover = function() {
                this.style.backgroundColor = drawingMode === "area" ? '#0e9f6e' : '#2563eb';
                this.style.transform = 'translateX(-50%) scale(1.05)';
              };
              completeButton.onmouseout = function() {
                this.style.backgroundColor = drawingMode === "area" ? '#10b981' : '#3b82f6';
                this.style.transform = 'translateX(-50%)';
              };

              // Uložíme referenci na tlačítko
              completeButtonRef.current = completeButton;

              // Aktualizujeme tlačítko podle počtu bodů
              const updateCompleteButton = () => {
                const pointCount = drawingPointsRef.current.length;

                if (drawingMode === "area") {
                  if (pointCount >= 3) {
                    completeButton.innerHTML = '<strong>DOKONČIT OBLAST</strong><br>(' + pointCount + ' bodů)';
                    completeButton.style.backgroundColor = '#10b981';
                    completeButton.style.opacity = '1';
                    completeButton.style.cursor = 'pointer';
                    completeButton.disabled = false;
                  } else {
                    completeButton.innerHTML = '<strong>NELZE DOKONČIT</strong><br>Potřeba ještě ' + (3 - pointCount) + ' bod(y)';
                    completeButton.style.backgroundColor = '#9ca3af';
                    completeButton.style.opacity = '0.7';
                    completeButton.style.cursor = 'not-allowed';
                    completeButton.disabled = true;
                  }
                } else if (drawingMode === "route") {
                  if (pointCount >= 2) {
                    completeButton.innerHTML = '<strong>DOKONČIT TRASU</strong><br>(' + pointCount + ' bodů)';
                    completeButton.style.backgroundColor = '#3b82f6';
                    completeButton.style.opacity = '1';
                    completeButton.style.cursor = 'pointer';
                    completeButton.disabled = false;
                  } else {
                    completeButton.innerHTML = '<strong>NELZE DOKONČIT</strong><br>Potřeba ještě ' + (2 - pointCount) + ' bod(y)';
                    completeButton.style.backgroundColor = '#9ca3af';
                    completeButton.style.opacity = '0.7';
                    completeButton.style.cursor = 'not-allowed';
                    completeButton.disabled = true;
                  }
                }
              };

              // Inicializujeme tlačítko
              updateCompleteButton();

              completeButton.onclick = (e) => {
                e.preventDefault();
                e.stopPropagation();

                if (drawingMode === "area") {
                  console.log("Completing area with points:", drawingPointsRef.current);

                  if (onAreaDraw && drawingPointsRef.current.length >= 3) {
                    onAreaDraw(drawingPointsRef.current);
                  } else {
                    console.log("Oblast musí mít alespoň 3 body");
                    alert("Oblast musí mít alespoň 3 body pro dokončení.");
                    return;
                  }
                } else if (drawingMode === "route") {
                  console.log("Completing route with points:", drawingPointsRef.current);

                  if (onRouteDraw && drawingPointsRef.current.length >= 2) {
                    onRouteDraw(drawingPointsRef.current);
                  } else {
                    console.log("Trasa musí mít alespoň 2 body");
                    alert("Trasa musí mít alespoň 2 body pro dokončení.");
                    return;
                  }
                }

                // Odstraněno: Aktualizace tlačítka při každém přidání bodu
                // Tato funkce není potřeba, protože tlačítko aktualizujeme přímo při přidání bodu

                // Resetujeme kreslení
                setIsDrawing(false);
                drawingPointsRef.current = [];
                if (drawingLayerRef.current) {
                  map.removeLayer(drawingLayerRef.current);
                  drawingLayerRef.current = null;
                }

                // Odstraníme tlačítko bezpečně
                if (completeButtonRef.current) {
                  try {
                    // Použijeme parentNode pro bezpečné odstranění
                    if (completeButtonRef.current.parentNode) {
                      completeButtonRef.current.parentNode.removeChild(completeButtonRef.current);
                      console.log("Complete button removed successfully");
                    }
                  } catch (error) {
                    console.warn("Error removing complete button:", error);
                  }
                  completeButtonRef.current = null;
                }

                // Odstraníme instrukce bezpečně
                if (window.drawingInstructions) {
                  try {
                    if (window.drawingInstructions.parentNode) {
                      window.drawingInstructions.parentNode.removeChild(window.drawingInstructions);
                    }
                  } catch (error) {
                    console.warn("Error removing drawing instructions:", error);
                  }
                  window.drawingInstructions = null;
                }

                // Vrátíme kurzor na výchozí
                map.getContainer().style.cursor = '';
              };

              // Přidáme tlačítko přímo do kontejneru mapy místo do body
              const mapContainer = map.getContainer();
              mapContainer.appendChild(completeButton);
              console.log("Complete button added to map container");
            } else {
              // Pokračujeme v kreslení - přidáme další bod
              drawingPointsRef.current.push({lat: e.latlng.lat, lng: e.latlng.lng});
              const pointIndex = drawingPointsRef.current.length;
              console.log("Adding point:", e.latlng.lat, e.latlng.lng, "Total points:", pointIndex);

              // Aktualizujeme tlačítko pro dokončení kreslení
              if (completeButtonRef.current) {
                if (drawingMode === "area") {
                  if (pointIndex >= 3) {
                    completeButtonRef.current.innerHTML = '<strong>DOKONČIT OBLAST</strong><br>(' + pointIndex + ' bodů)';
                    completeButtonRef.current.style.backgroundColor = '#10b981';
                    completeButtonRef.current.style.opacity = '1';
                    completeButtonRef.current.style.cursor = 'pointer';
                    completeButtonRef.current.disabled = false;
                  } else {
                    completeButtonRef.current.innerHTML = '<strong>NELZE DOKONČIT</strong><br>Potřeba ještě ' + (3 - pointIndex) + ' bod(y)';
                    completeButtonRef.current.style.backgroundColor = '#9ca3af';
                    completeButtonRef.current.style.opacity = '0.7';
                    completeButtonRef.current.style.cursor = 'not-allowed';
                    completeButtonRef.current.disabled = true;
                  }
                } else if (drawingMode === "route") {
                  if (pointIndex >= 2) {
                    completeButtonRef.current.innerHTML = '<strong>DOKONČIT TRASU</strong><br>(' + pointIndex + ' bodů)';
                    completeButtonRef.current.style.backgroundColor = '#3b82f6';
                    completeButtonRef.current.style.opacity = '1';
                    completeButtonRef.current.style.cursor = 'pointer';
                    completeButtonRef.current.disabled = false;
                  } else {
                    completeButtonRef.current.innerHTML = '<strong>NELZE DOKONČIT</strong><br>Potřeba ještě ' + (2 - pointIndex) + ' bod(y)';
                    completeButtonRef.current.style.backgroundColor = '#9ca3af';
                    completeButtonRef.current.style.opacity = '0.7';
                    completeButtonRef.current.style.cursor = 'not-allowed';
                    completeButtonRef.current.disabled = true;
                  }
                }
              }

              // Vyčistíme vrstvu pro kreslení
              if (drawingLayerRef.current) {
                drawingLayerRef.current.clearLayers();
                console.log("Drawing layer cleared");
              } else {
                // Pokud vrstva neexistuje, vytvoříme ji
                drawingLayerRef.current = L.layerGroup();
                drawingLayerRef.current.addTo(map);
                console.log("Drawing layer created because it didn't exist");
              }

              const points = drawingPointsRef.current;

              // Přidáme všechny body s popisky a zvýrazněním - VŽDY přímo na mapu
              points.forEach((point, index) => {
                try {
                  console.log(`Adding point ${index + 1} at:`, point.lat, point.lng);

                  // Přidáme marker přímo na mapu pro jistotu, že bude viditelný
                  const marker = L.circleMarker([point.lat, point.lng], {
                    radius: 20,  // Větší radius pro lepší viditelnost
                    color: '#ffffff',
                    fillColor: drawingMode === "area" ? "#10b981" : "#3b82f6",
                    fillOpacity: 1,
                    weight: 5,   // Silnější okraj
                    pane: 'overlayPane',
                    interactive: false
                  }).addTo(map);  // Přidáme PŘÍMO na mapu

                  // Přidáme tooltip
                  marker.bindTooltip(`Bod ${index + 1}`, {
                    permanent: true,
                    direction: 'top',
                    offset: [0, -25],
                    className: 'drawing-tooltip'
                  }).openTooltip();

                  console.log(`Point marker ${index + 1} added directly to map`);

                  // Přidáme také do drawing layer pro správu
                  if (drawingLayerRef.current) {
                    marker.addTo(drawingLayerRef.current);
                  }

                  // Přidáme zvýraznění bodu - také přímo na mapu
                  const circle = L.circle([point.lat, point.lng], {
                    color: drawingMode === "area" ? "#10b981" : "#3b82f6",
                    fillColor: drawingMode === "area" ? "#10b981" : "#3b82f6",
                    fillOpacity: 0.3,
                    radius: 100,  // Větší radius pro lepší viditelnost
                    weight: 2,
                    pane: 'overlayPane',
                    interactive: false
                  }).addTo(map);  // Přidáme PŘÍMO na mapu

                  console.log(`Point highlight ${index + 1} added directly to map`);

                  // Přidáme také do drawing layer pro správu
                  if (drawingLayerRef.current) {
                    circle.addTo(drawingLayerRef.current);
                  }
                } catch (error) {
                  console.error(`Error adding point ${index + 1}:`, error);
                }
              });

              // Aktualizujeme text s instrukcemi - výraznější a informativnější
              if (window.drawingInstructions) {
                if (drawingMode === "area") {
                  if (pointIndex >= 3) {
                    window.drawingInstructions.innerHTML = `<strong>OBLAST MÁ ${pointIndex} BODŮ</strong><br>Klikněte na další místa pro přidání bodů nebo na tlačítko <span style="color:#10b981">DOKONČIT OBLAST</span> nahoře.`;
                    window.drawingInstructions.style.border = '2px solid #10b981';
                    // Změníme barvu pozadí na zelenou pro indikaci, že oblast je platná
                    window.drawingInstructions.style.backgroundColor = 'rgba(16, 185, 129, 0.2)';
                  } else {
                    window.drawingInstructions.innerHTML = `<strong>OBLAST MÁ ${pointIndex} BODŮ</strong><br>Přidejte ještě alespoň <span style="color:#ef4444">${3 - pointIndex} bod(y)</span> pro vytvoření platné oblasti.`;
                    window.drawingInstructions.style.border = '2px solid #ef4444';
                    // Změníme barvu pozadí na červenou pro indikaci, že oblast ještě není platná
                    window.drawingInstructions.style.backgroundColor = 'rgba(239, 68, 68, 0.2)';
                  }
                } else {
                  if (pointIndex >= 2) {
                    window.drawingInstructions.innerHTML = `<strong>TRASA MÁ ${pointIndex} BODŮ</strong><br>Klikněte na další místa pro přidání bodů nebo na tlačítko <span style="color:#3b82f6">DOKONČIT TRASU</span> nahoře.`;
                    window.drawingInstructions.style.border = '2px solid #3b82f6';
                    // Změníme barvu pozadí na modrou pro indikaci, že trasa je platná
                    window.drawingInstructions.style.backgroundColor = 'rgba(59, 130, 246, 0.2)';
                  } else {
                    window.drawingInstructions.innerHTML = `<strong>TRASA MÁ ${pointIndex} BODŮ</strong><br>Přidejte ještě alespoň <span style="color:#ef4444">${2 - pointIndex} bod(y)</span> pro vytvoření platné trasy.`;
                    window.drawingInstructions.style.border = '2px solid #ef4444';
                    // Změníme barvu pozadí na červenou pro indikaci, že trasa ještě není platná
                    window.drawingInstructions.style.backgroundColor = 'rgba(239, 68, 68, 0.2)';
                  }
                }
              }

              // Pokud je to oblast a máme alespoň 3 body, vykreslíme polygon
              if (drawingMode === "area" && points.length >= 3) {
                try {
                  // Přidáme polygon - důležité: musíme použít správný formát souřadnic pro Leaflet
                  // Leaflet očekává pole párů [lat, lng], ne objekty {lat, lng}
                  const polygonCoords = points.map(p => [p.lat, p.lng]);
                  console.log("Creating polygon with coordinates:", polygonCoords);

                  // Nejprve odstraníme existující polygon, pokud existuje
                  drawingLayerRef.current.eachLayer((layer: any) => {
                    // Kontrolujeme, zda je vrstva typu Polygon
                    if (layer.options && layer.options.fill && layer._latlngs && !layer.options.noClip) {
                      drawingLayerRef.current.removeLayer(layer);
                      console.log("Removed existing polygon");
                    }
                  });

                  // Vytvoříme nový polygon s výraznějším stylem
                  // Explicitně konvertujeme souřadnice do formátu, který Leaflet očekává
                  // Důležité: Leaflet očekává pole párů [lat, lng], ne objekty {lat, lng}

                  // Nejprve vytvoříme pole souřadnic ve formátu, který Leaflet očekává
                  const latLngs = [];
                  for (const point of points) {
                    latLngs.push([point.lat, point.lng]);
                  }

                  console.log("Creating polygon with raw coordinates:", latLngs);

                  // Vytvoříme polygon s ještě výraznějším stylem
                  const polygon = L.polygon(latLngs, {
                    color: "#10b981",
                    fillColor: "#10b981",
                    fillOpacity: 0.4,  // Mírně průhlednější pro lepší viditelnost bodů
                    weight: 4,         // Tloušťka okraje
                    dashArray: null,
                    interactive: false,  // Neinteraktivní, aby nepřekážel
                    pane: 'overlayPane'
                  });

                  // Přidáme polygon PŘÍMO na mapu pro jistotu
                  if (polygon) {
                    try {
                      polygon.addTo(map);
                      console.log("Polygon added directly to map successfully");

                      // Přidáme také do drawing layer pro správu
                      if (drawingLayerRef.current) {
                        polygon.addTo(drawingLayerRef.current);
                        console.log("Polygon also added to drawing layer");
                      }
                    } catch (error) {
                      console.error("Error adding polygon:", error);
                    }
                  } else {
                    console.error("Failed to create polygon");
                  }

                  // Přidáme také čáry mezi body pro lepší viditelnost
                  // Nejprve odstraníme existující čáry
                  drawingLayerRef.current.eachLayer((layer: any) => {
                    // Kontrolujeme, zda je vrstva typu Polyline, ale ne Polygon
                    if (layer._latlngs && layer.options && !layer.options.fill) {
                      drawingLayerRef.current.removeLayer(layer);
                      console.log("Removed existing polyline");
                    }
                  });

                  // Přidáme nové čáry
                  for (let i = 0; i < points.length; i++) {
                    const current = points[i];
                    const next = points[(i + 1) % points.length]; // Cyklicky se vracíme k prvnímu bodu

                    try {
                      // Vytvoříme čáru mezi body s ještě výraznějším stylem
                      const line = L.polyline([
                        [current.lat, current.lng],
                        [next.lat, next.lng]
                      ], {
                        color: "#10b981",
                        weight: 4,         // Tloušťka čáry
                        opacity: 1,
                        dashArray: null,
                        lineCap: 'round',  // Zaoblené konce čar
                        lineJoin: 'round', // Zaoblené spoje čar
                        interactive: false,
                        pane: 'overlayPane'
                      });

                      // Přidáme čáru PŘÍMO na mapu pro jistotu
                      if (line) {
                        line.addTo(map);
                        console.log(`Line ${i} added directly to map`);

                        // Přidáme také do drawing layer pro správu
                        if (drawingLayerRef.current) {
                          line.addTo(drawingLayerRef.current);
                        }
                      }

                      // Přidáme také šipku nebo značku směru
                      const midPoint = {
                        lat: (current.lat + next.lat) / 2,
                        lng: (current.lng + next.lng) / 2
                      };

                      // Přidáme malý kruh uprostřed čáry pro lepší viditelnost
                      L.circleMarker([midPoint.lat, midPoint.lng], {
                        radius: 3,
                        color: "#10b981",
                        fillColor: "#10b981",
                        fillOpacity: 1,
                        weight: 2
                      }).addTo(drawingLayerRef.current || map);
                    } catch (error) {
                      console.error(`Error creating line ${i}:`, error);
                    }
                  }
                } catch (error) {
                  console.error("Error creating polygon:", error);
                }
              }
              // Pokud je to trasa, vykreslíme celou trasu
              else if (drawingMode === "route" && points.length >= 2) {
                try {
                  // Přidáme polyline pro celou trasu
                  const routeCoords = points.map(p => [p.lat, p.lng]);
                  console.log("Creating route with coordinates:", routeCoords);

                  const route = L.polyline(routeCoords, {
                    color: "#3b82f6",
                    weight: 4,
                    opacity: 1,
                    lineCap: 'round',
                    lineJoin: 'round',
                    interactive: false,
                    pane: 'overlayPane'
                  });

                  // Přidáme trasu PŘÍMO na mapu pro jistotu
                  if (route) {
                    route.addTo(map);
                    console.log("Route added directly to map");

                    // Přidáme také do drawing layer pro správu
                    if (drawingLayerRef.current) {
                      route.addTo(drawingLayerRef.current);
                    }
                  } else {
                    console.error("Failed to create route");
                  }
                } catch (error) {
                  console.error("Error creating route:", error);
                }
              }
            }
          }
          else if (onMapClick) {
            // Výchozí režim - přidávání bodů
            onMapClick(e.latlng.lat, e.latlng.lng);
          }
        });

        // Přidáme ovládací prvky
        L.control.zoom({ position: 'topright' }).addTo(map);

        // Přidáme body z formuláře
        if (visibleLayers.points && formValues.points && formValues.points.length > 0) {
          formValues.points.forEach(point => {
            const markerColor =
              point.pointType === "crime" ? "red" :
              point.pointType === "person" ? "green" :
              point.pointType === "vehicle" ? "orange" :
              point.pointType === "building" ? "purple" :
              point.pointType === "surveillance" ? "blue" :
              point.pointType === "meeting" ? "yellow" :
              point.pointType === "evidence" ? "pink" : "blue";

            // Vytvoříme vlastní ikonu podle typu bodu
            const customIcon = L.divIcon({
              className: 'custom-div-icon',
              html: `<div style="background-color: ${point.color || markerColor}; width: 12px; height: 12px; border-radius: 50%; border: 2px solid white;"></div>`,
              iconSize: [15, 15],
              iconAnchor: [7, 7]
            });

            // Vytvoříme marker
            const marker = L.marker([point.latitude, point.longitude], {
              icon: customIcon,
              title: point.name
            })
              .bindPopup(`
                <div>
                  <h3 style="font-weight: bold;">${point.name}</h3>
                  ${point.description ? `<p>${point.description}</p>` : ''}
                  ${point.address ? `<p style="font-size: 0.8em; color: #666;">${point.address}</p>` : ''}
                </div>
              `);

            // Přidáme marker na mapu
            marker.addTo(map);

            // Přidáme událost kliknutí na marker
            if (onPointSelect) {
              marker.on('click', () => {
                onPointSelect(point.id);
              });
            }

            // Zvýrazníme vybraný bod
            if (selectedPointId === point.id) {
              marker.setZIndexOffset(1000); // Posuneme vybraný bod nad ostatní
              marker.openPopup(); // Otevřeme popup pro vybraný bod

              // Přidáme zvýraznění vybraného bodu
              L.circle([point.latitude, point.longitude], {
                color: 'rgba(66, 153, 225, 0.5)',
                fillColor: 'rgba(66, 153, 225, 0.2)',
                fillOpacity: 0.5,
                radius: 100
              }).addTo(map);

              // Vycentrujeme mapu na vybraný bod
              map.setView([point.latitude, point.longitude], 14);
            }
          });
        }

        // Nebudeme přidávat ukázkové body, i když nejsou žádné body
        // Uživatel si může přidat vlastní body

        // Přidáme trasy z formuláře
        if (visibleLayers.routes && formValues.routes && formValues.routes.length > 0) {
          console.log("Rendering routes:", formValues.routes);

          formValues.routes.forEach(route => {
            if (!route.coordinates || route.coordinates.length < 2) {
              console.warn(`Route ${route.id} (${route.name}) has invalid coordinates:`, route.coordinates);
              return;
            }

            const routeColor =
              route.routeType === "vehicle_route" ? "#3b82f6" : // modrá
              route.routeType === "person_route" ? "#10b981" : // zelená
              route.routeType === "escape_route" ? "#ef4444" : // červená
              route.routeType === "patrol" ? "#f59e0b" : // žlutá
              route.routeType === "surveillance" ? "#8b5cf6" : // fialová
              route.routeType === "other" ? "#6b7280" : // šedá
              "#3b82f6"; // výchozí modrá

            try {
              // Převedeme souřadnice na formát pro Leaflet
              const coordinates = route.coordinates.map(coord => [coord.lat, coord.lng]);

              console.log(`Rendering route ${route.id} (${route.name}) with coordinates:`, coordinates);

              // Vytvoříme polyline
              const polyline = L.polyline(coordinates, {
                color: route.color || routeColor,
                weight: selectedRouteId === route.id ? 6 : 4,
                opacity: selectedRouteId === route.id ? 1 : 0.8,
                lineCap: 'round',
                lineJoin: 'round'
              }).bindPopup(`
                <div>
                  <h3 style="font-weight: bold;">${route.name}</h3>
                  ${route.description ? `<p>${route.description}</p>` : ''}
                  ${route.startAddress && route.endAddress ?
                    `<p style="font-size: 0.8em; color: #666;">${route.startAddress} → ${route.endAddress}</p>` : ''}
                </div>
              `);

              // Přidáme událost kliknutí na trasu
              if (onRouteClick) {
                polyline.on('click', () => {
                  console.log(`Route ${route.id} clicked`);
                  onRouteClick(route.id);
                });
              }

              // Zvýrazníme vybranou trasu
              if (selectedRouteId === route.id) {
                polyline.setStyle({
                  color: route.color || routeColor,
                  weight: 6,
                  opacity: 1
                });
                polyline.openPopup();

                // Vycentrujeme mapu na vybranou trasu
                const bounds = polyline.getBounds();
                map.fitBounds(bounds, { padding: [50, 50] });
              }

              // Přidáme trasu na mapu
              polyline.addTo(map);

              // Přidáme markery pro začátek a konec trasy
              const startPoint = route.coordinates[0];
              const endPoint = route.coordinates[route.coordinates.length - 1];

              // Marker pro začátek trasy
              L.circleMarker([startPoint.lat, startPoint.lng], {
                radius: 6,
                color: '#ffffff',
                fillColor: route.color || routeColor,
                fillOpacity: 1,
                weight: 2
              }).bindTooltip("Začátek trasy").addTo(map);

              // Marker pro konec trasy
              L.circleMarker([endPoint.lat, endPoint.lng], {
                radius: 6,
                color: '#ffffff',
                fillColor: route.color || routeColor,
                fillOpacity: 1,
                weight: 2
              }).bindTooltip("Konec trasy").addTo(map);

              // Přidáme markery pro body trasy
              route.coordinates.forEach((coord, index) => {
                if (index > 0 && index < route.coordinates.length - 1) {
                  L.circleMarker([coord.lat, coord.lng], {
                    radius: 3,
                    color: route.color || routeColor,
                    fillColor: route.color || routeColor,
                    fillOpacity: 1,
                    weight: 1
                  }).bindTooltip(`Bod ${index + 1}`).addTo(map);
                }
              });
            } catch (error) {
              console.error(`Error rendering route ${route.id} (${route.name}):`, error);
            }
          });
        }

        // Přidáme oblasti z formuláře
        if (visibleLayers.areas && formValues.areas && formValues.areas.length > 0) {
          console.log("Rendering areas:", formValues.areas);

          formValues.areas.forEach(area => {
            if (!area.coordinates || area.coordinates.length < 3) {
              console.warn(`Area ${area.id} (${area.name}) has invalid coordinates:`, area.coordinates);
              return;
            }

            const areaColor =
              area.areaType === "crime_area" ? "#ef4444" : // červená
              area.areaType === "surveillance" ? "#3b82f6" : // modrá
              area.areaType === "restricted" ? "#f59e0b" : // žlutá
              area.areaType === "search" ? "#10b981" : // zelená
              area.areaType === "operation" ? "#8b5cf6" : // fialová
              area.areaType === "other" ? "#6b7280" : // šedá
              "#10b981"; // výchozí zelená

            try {
              // Převedeme souřadnice na formát pro Leaflet
              const coordinates = area.coordinates.map(coord => [coord.lat, coord.lng]);

              console.log(`Rendering area ${area.id} (${area.name}) with coordinates:`, coordinates);

              // Vytvoříme polygon
              const polygon = L.polygon(coordinates, {
                color: area.color || areaColor,
                fillOpacity: 0.4,
                weight: 3,
                dashArray: selectedAreaId === area.id ? null : '5, 5'
              }).bindPopup(`
                <div>
                  <h3 style="font-weight: bold;">${area.name}</h3>
                  ${area.description ? `<p>${area.description}</p>` : ''}
                  ${area.address ? `<p style="font-size: 0.8em; color: #666;">${area.address}</p>` : ''}
                </div>
              `);

              // Přidáme událost kliknutí na oblast
              if (onAreaClick) {
                polygon.on('click', () => {
                  console.log(`Area ${area.id} clicked`);
                  onAreaClick(area.id);
                });
              }

              // Zvýrazníme vybranou oblast
              if (selectedAreaId === area.id) {
                polygon.setStyle({
                  color: area.color || areaColor,
                  fillOpacity: 0.6,
                  weight: 4,
                  dashArray: null
                });
                polygon.openPopup();

                // Vycentrujeme mapu na vybranou oblast
                const bounds = polygon.getBounds();
                map.fitBounds(bounds, { padding: [50, 50] });
              }

              // Přidáme oblast na mapu
              polygon.addTo(map);

              // Přidáme markery pro vrcholy oblasti
              area.coordinates.forEach((coord, index) => {
                L.circleMarker([coord.lat, coord.lng], {
                  radius: 4,
                  color: area.color || areaColor,
                  fillColor: area.color || areaColor,
                  fillOpacity: 1,
                  weight: 2
                }).bindTooltip(`Bod ${index + 1}`).addTo(map);
              });
            } catch (error) {
              console.error(`Error rendering area ${area.id} (${area.name}):`, error);
            }
          });
        }

        // Označíme mapu jako inicializovanou
        setIsMapInitialized(true);
      } catch (error) {
        console.error('Chyba při inicializaci mapy:', error);
      }
    };

    // Inicializujeme mapu s malým zpožděním, aby se DOM stačil načíst
    const timer = setTimeout(() => {
      initMap();
    }, 500);

    // Cleanup funkce
    return () => {
      clearTimeout(timer);

      // Odstraníme tlačítko pro dokončení
      if (completeButtonRef.current) {
        try {
          if (completeButtonRef.current.parentNode) {
            completeButtonRef.current.parentNode.removeChild(completeButtonRef.current);
          }
        } catch (error) {
          console.warn("Error removing complete button during cleanup:", error);
        }
        completeButtonRef.current = null;
      }

      // Odstraníme instrukce
      if (window.drawingInstructions) {
        try {
          if (window.drawingInstructions.parentNode) {
            window.drawingInstructions.parentNode.removeChild(window.drawingInstructions);
          }
        } catch (error) {
          console.warn("Error removing drawing instructions during cleanup:", error);
        }
        window.drawingInstructions = null;
      }

      // Odstraníme mapu
      if (mapInstanceRef.current) {
        try {
          mapInstanceRef.current.remove();
          mapInstanceRef.current = null;
        } catch (error) {
          console.warn("Error removing map during cleanup:", error);
        }
      }
    };
  }, [formValues.defaultCenter, formValues.defaultZoom, visibleLayers, formValues.points, formValues.areas, formValues.routes, formValues.defaultBasemap, selectedPointId, selectedAreaId, selectedRouteId, onPointSelect, onAreaClick, onRouteClick, onMapClick, drawingMode, onAreaDraw, onRouteDraw, isDrawing]);

  return (
    <div className="w-full h-full relative">
      <div
        ref={mapRef}
        className="w-full h-full"
        style={{ zIndex: 0 }}
      />
      {!isMapInitialized && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted bg-opacity-50">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      )}
    </div>
  );
}
