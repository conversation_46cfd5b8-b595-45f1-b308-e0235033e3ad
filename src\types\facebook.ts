import { Timestamp } from "firebase/firestore";
import { PhotoMetadata } from ".";

export interface FacebookPost {
  id: string;
  date?: string | Timestamp;
  text?: string;
  likesCount?: number;
  sharesCount?: number;
  reactionsList?: string; // Seznam osob které reagovaly
  sharesList?: string; // Seznam osob které sdílely
  comments?: string; // Komentáře k příspěvku
  photos?: PhotoMetadata[];
  notes?: string;
}

export interface FacebookFriend {
  id: string;
  name: string;
  profileUrl?: string;
  extraInfo?: string[]; // Pracuje ve společnosti XYZ, atd.
  isPersonOfInterest?: boolean; // Označen jako zájmová osoba
  notes?: string;
}

export interface FacebookPage {
  id: string;
  name: string;
  pageUrl?: string;
  category?: string;
  notes?: string;
}

export interface FacebookGroup {
  id: string;
  name: string;
  groupUrl?: string;
  memberType?: "member" | "admin" | "moderator";
  notes?: string;
}

export interface FacebookEvent {
  id: string;
  eventName?: string;
  organizer?: string;
  eventDate?: string | Timestamp;
  eventTime?: string;
  location?: string;
  participation?: "going" | "interested" | "invited" | "declined" | "unknown";
  description?: string;
  eventUrl?: string;
  participantsCount?: number;
  interestedCount?: number;
  photos?: PhotoMetadata[];
  notes?: string;
}

export interface FacebookModuleData {
  id?: string;
  subjectId: string;
  
  // Základní údaje profilu
  profileName?: string;
  username?: string; // např. jan.novak.123
  profileUrl?: string;
  
  // Detaily profilu
  currentLocation?: string;
  currentJob?: string;
  education?: string;
  relationshipStatus?: string;
  aboutMe?: string;
  contactInfo?: string;
  otherPersonalInfo?: string;
  
  // Přátelé
  friends?: FacebookFriend[];
  friendsCount?: number;
  friendsRawData?: string; // Surový text zkopírovaný z Facebooku
  
  // Sledované stránky a skupiny
  likedPages?: FacebookPage[];
  likedPagesRawData?: string;
  groups?: FacebookGroup[];
  groupsRawData?: string;
  
  // Příspěvky
  posts?: FacebookPost[];
  
  // Události
  events?: FacebookEvent[];
  
  // Fotodokumentace
  photos?: PhotoMetadata[];
  
  // OSINT poznámky
  osintNotes?: string;
  investigationNotes?: string;
  
  lastUpdatedAt?: Timestamp | null;
  createdAt?: Timestamp | null;
}
