"use client";

import React, { useState, useEffect, useRef, useCallback } from "react";
import { Control, UseFormSetValue, UseFormGetValues } from "react-hook-form";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Globe, Search, Loader2 } from "lucide-react";
import { CadastreModuleFormValues } from "./schemas";

import { FormDescription } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

// Import Leaflet directly
import L from "leaflet";

// Import Leaflet CSS
import "leaflet/dist/leaflet.css";

interface PropertyMapFieldProps {
  control: Control<CadastreModuleFormValues>;
  index: number;
  setValue: UseFormSetValue<CadastreModuleFormValues>;
  getValues: UseFormGetValues<CadastreModuleFormValues>;
}



// Pomocná komponenta pro mapu
const MapWrapper = React.memo(function MapWrapperInner({
  parsedCoords,
  defaultCenter,
  index,
  getValues,
  setValue
}: {
  parsedCoords: [number, number] | null;
  defaultCenter: [number, number];
  index: number;
  getValues: UseFormGetValues<CadastreModuleFormValues>;
  setValue: UseFormSetValue<CadastreModuleFormValues>;
}) {
  // Použijeme useRef pro referenci na div element a mapu
  const mapContainerRef = React.useRef<HTMLDivElement>(null);
  const mapRef = React.useRef<L.Map | null>(null);
  const markerRef = React.useRef<L.Marker | null>(null);

  // Použijeme useEffect pro inicializaci mapy - pouze jednou
  const initRef = useRef(false);

  useEffect(() => {
    // Inicializujeme mapu pouze jednou
    if (initRef.current || !mapContainerRef.current || mapRef.current) return;

    // Označíme, že jsme již inicializovali mapu
    initRef.current = true;

    // Vytvoříme mapu pouze pokud ještě neexistuje
    const map = L.map(mapContainerRef.current).setView(
      parsedCoords || defaultCenter,
      parsedCoords ? 16 : 8
    );

    // Uložíme referenci na mapu
    mapRef.current = map;

    // Přidáme tile layer
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
    }).addTo(map);

    // Přidáme event listener pro kliknutí na mapu
    const clickHandler = (e: L.LeafletMouseEvent) => {
      const { lat, lng } = e.latlng;
      setValue(
        `properties.${index}.gpsCoordinates`,
        `${lat.toFixed(6)}, ${lng.toFixed(6)}`,
        { shouldValidate: true }
      );
    };

    map.on('click', clickHandler);

    // Přidáme marker a kruh, pokud máme souřadnice
    if (parsedCoords) {
      // Vytvoříme marker
      const marker = L.marker(parsedCoords).addTo(map);
      const name = getValues(`properties.${index}.name`);
      const address = getValues(`properties.${index}.address`);
      marker.bindPopup(name || address || "Zadaná nemovitost");

      // Vytvoříme kruh pro zvýraznění oblasti
      const circle = L.circle(parsedCoords, {
        color: 'blue',
        fillColor: '#3b82f6',
        fillOpacity: 0.2,
        radius: 100 // 100 metrů
      }).addTo(map);

      // Uložíme reference
      markerRef.current = marker;
      circleRef.current = circle;

      // Otevřeme popup automaticky
      marker.openPopup();
    }

    // Cleanup
    return () => {
      if (mapRef.current) {
        mapRef.current.off('click', clickHandler);
        mapRef.current.remove();
        mapRef.current = null;
        initRef.current = false;
      }

      // Vyčistíme i ostatní reference
      if (markerRef.current) {
        markerRef.current.remove();
        markerRef.current = null;
      }

      if (circleRef.current) {
        circleRef.current.remove();
        circleRef.current = null;
      }
    };
  }, [parsedCoords, defaultCenter, index, setValue, getValues]);

  // Použijeme useEffect pro aktualizaci markeru při změně souřadnic
  // Použijeme useRef pro sledování, zda se souřadnice změnily
  const lastCoordsRef = useRef<string | null>(null);
  const circleRef = useRef<L.Circle | null>(null);

  useEffect(() => {
    if (!mapRef.current) return;

    // Převedeme souřadnice na string pro porovnání
    const coordsString = parsedCoords ? `${parsedCoords[0]},${parsedCoords[1]}` : null;

    // Aktualizujeme pouze pokud se souřadnice změnily
    if (coordsString !== lastCoordsRef.current) {
      lastCoordsRef.current = coordsString;

      // Aktualizujeme pohled mapy
      mapRef.current.setView(
        parsedCoords || defaultCenter,
        parsedCoords ? 16 : 8
      );

      // Odstraníme starý marker a kruh, pokud existují
      if (markerRef.current) {
        markerRef.current.remove();
        markerRef.current = null;
      }

      if (circleRef.current) {
        circleRef.current.remove();
        circleRef.current = null;
      }

      // Přidáme nový marker a kruh, pokud máme souřadnice
      if (parsedCoords) {
        // Vytvoříme marker
        const marker = L.marker(parsedCoords).addTo(mapRef.current);
        const name = getValues(`properties.${index}.name`);
        const address = getValues(`properties.${index}.address`);
        marker.bindPopup(name || address || "Zadaná nemovitost");

        // Vytvoříme kruh pro zvýraznění oblasti
        const circle = L.circle(parsedCoords, {
          color: 'blue',
          fillColor: '#3b82f6',
          fillOpacity: 0.2,
          radius: 100 // 100 metrů
        }).addTo(mapRef.current);

        // Uložíme reference
        markerRef.current = marker;
        circleRef.current = circle;

        // Otevřeme popup automaticky
        marker.openPopup();
      }
    }
  }, [parsedCoords, defaultCenter, index, getValues]);

  return <div ref={mapContainerRef} style={{ height: "100%", width: "100%" }} />;
});

// Hlavní komponenta pro mapu
const MapView = ({
  parsedCoords,
  defaultCenter,
  index,
  getValues,
  setValue
}: {
  parsedCoords: [number, number] | null;
  defaultCenter: [number, number];
  index: number;
  getValues: UseFormGetValues<CadastreModuleFormValues>;
  setValue: UseFormSetValue<CadastreModuleFormValues>;
}) => {
  // Použijeme useState pro client-side renderování
  const [isClient, setIsClient] = useState(false);

  // Použijeme useEffect pro nastavení isClient na true po prvním renderování
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Použijeme key pro vynucení nového renderování při změně souřadnic
  const mapKey = parsedCoords ? `map-${parsedCoords[0]}-${parsedCoords[1]}` : 'map-default';

  // Renderujeme mapu pouze na klientské straně
  if (!isClient) {
    return <div style={{ height: "350px", width: "100%" }} />;
  }

  return (
    <div style={{ height: "350px", width: "100%" }}>
      <MapWrapper
        key={mapKey}
        parsedCoords={parsedCoords}
        defaultCenter={defaultCenter}
        index={index}
        getValues={getValues}
        setValue={setValue}
      />
    </div>
  );
};

// Hlavní komponenta
export function PropertyMapField({ control, index, setValue, getValues }: PropertyMapFieldProps) {
  const [mapApiReady, setMapApiReady] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const { toast } = useToast();

  // Pomocné funkce
  const parseGps = (gpsString?: string): [number, number] | null => {
    if (!gpsString) return null;
    const parts = gpsString.split(",").map((p) => parseFloat(p.trim()));
    if (parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
      return [parts[0], parts[1]];
    }
    return null;
  };

  // Konstanty
  const defaultCenter: [number, number] = [50.0755, 14.4378]; // Prague as default center

  // Použijeme useState pro získání hodnot
  const [gpsCoordsString, setGpsCoordsString] = useState("");
  const [parsedCoords, setParsedCoords] = useState<[number, number] | null>(null);

  // Načteme hodnoty při prvním renderování a při změně indexu
  // Použijeme useRef pro sledování, zda se hodnoty změnily
  const coordsRef = useRef<string | null>(null);

  useEffect(() => {
    const coords = getValues(`properties.${index}.gpsCoordinates`);

    // Aktualizujeme stav pouze pokud se hodnoty změnily
    if (coords !== coordsRef.current) {
      coordsRef.current = coords || "";
      setGpsCoordsString(coords || "");
      setParsedCoords(parseGps(coords));
    }
  }, [getValues, index, parseGps]);

  // Efekty
  useEffect(() => {
    // Check if window is defined (client-side) before trying to use Leaflet
    if (typeof window !== "undefined") {
      setMapApiReady(true);
    }
  }, []);

  // Použijeme useCallback pro zabránění zbytečných re-renderů
  const handleSearchAddress = useCallback(async () => {
    const address = getValues(`properties.${index}.address`);
    if (!address) {
      toast({
        title: "Chybí adresa",
        description: "Pro vyhledání zadejte adresu nemovitosti.",
        variant: "destructive",
      });
      return;
    }

    setIsSearching(true);
    try {
      // Using Nominatim OpenStreetMap API for geocoding
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&limit=1&countrycodes=cz`
      );
      const data = await response.json();

      if (data && data.length > 0) {
        const { lat, lon } = data[0];
        // Převedeme na čísla
        const latNum = parseFloat(lat);
        const lonNum = parseFloat(lon);

        // Nastavíme souřadnice ve formuláři
        setValue(`properties.${index}.gpsCoordinates`, `${lat}, ${lon}`, {
          shouldValidate: true,
        });

        // Aktualizujeme přímo i lokální stav pro okamžité zobrazení markeru
        setGpsCoordsString(`${lat}, ${lon}`);
        setParsedCoords([latNum, lonNum]);

        // Přidáme malé zpoždění pro jistotu, že se stav aktualizuje
        setTimeout(() => {
          // Znovu nastavíme souřadnice pro jistotu
          setParsedCoords([latNum, lonNum]);
        }, 100);

        toast({
          title: "Adresa nalezena",
          description: "Souřadnice byly úspěšně nastaveny.",
        });
      } else {
        toast({
          title: "Adresa nenalezena",
          description: "Zkuste zadat přesnější adresu nebo souřadnice ručně.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Chyba při vyhledávání",
        description: "Nepodařilo se vyhledat adresu. Zkuste to znovu později.",
        variant: "destructive",
      });
    } finally {
      setIsSearching(false);
    }
  }, [getValues, index, setValue, toast]);

  if (!mapApiReady) {
    return (
      <Card className="my-4">
        <CardHeader className="pb-2 pt-3">
          <CardTitle className="text-md flex items-center">
            <Globe className="mr-2 h-5 w-5 text-primary/80" />
            Mapa
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
          <span className="ml-2 text-muted-foreground">Načítání mapy...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="my-4">
      <CardHeader className="pb-2 pt-3">
        <CardTitle className="text-md flex items-center">
          <Globe className="mr-2 h-5 w-5 text-primary/80" />
          Mapa nemovitosti
        </CardTitle>
        <CardDescription>
          Kliknutím do mapy můžete nastavit přesnou polohu nemovitosti
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-end gap-2">
          <div className="flex-grow">
            <Button
              type="button"
              variant="outline"
              onClick={handleSearchAddress}
              disabled={isSearching}
              className={cn("w-full", isSearching && "opacity-70")}
            >
              {isSearching ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                <Search className="mr-2 h-4 w-4" />
              )}
              Vyhledat adresu na mapě
            </Button>
          </div>
          {parsedCoords && (
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                // Nastavíme prázdné souřadnice ve formuláři
                setValue(`properties.${index}.gpsCoordinates`, "", {
                  shouldValidate: true,
                });

                // Aktualizujeme přímo i lokální stav pro okamžité odstranění markeru
                setGpsCoordsString("");
                setParsedCoords(null);
              }}
              className="flex-shrink-0"
            >
              Vymazat souřadnice
            </Button>
          )}
        </div>

        <MapView
          parsedCoords={parsedCoords}
          defaultCenter={defaultCenter}
          index={index}
          getValues={getValues}
          setValue={setValue}
        />

        <div className="flex justify-between text-xs text-muted-foreground">
          <span>
            Souřadnice: {parsedCoords ? `${parsedCoords[0]}, ${parsedCoords[1]}` : "Nezadáno"}
          </span>
          <a
            href={parsedCoords ? `https://mapy.cz/zakladni?x=${parsedCoords[1]}&y=${parsedCoords[0]}&z=17` : "https://mapy.cz"}
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary hover:underline"
          >
            Otevřít v Mapy.cz
          </a>
        </div>

        <FormDescription className="text-xs">
          Pro vyhledání v katastru nemovitostí můžete použít{" "}
          <a
            href="https://nahlizenidokn.cuzk.cz/"
            target="_blank"
            rel="noopener noreferrer"
            className="text-primary hover:underline"
          >
            Nahlížení do katastru nemovitostí
          </a>
        </FormDescription>
      </CardContent>
    </Card>
  );
}
