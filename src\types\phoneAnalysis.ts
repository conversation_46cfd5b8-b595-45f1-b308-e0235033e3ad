import { Timestamp } from "firebase/firestore";

export interface PhoneActivity {
  id: string;
  date: string;
  activity: string;
  source: string;
  notes?: string;
}

export interface ConnectedDevice {
  id: string;
  deviceType: string;
  deviceModel?: string;
  lastSeen?: string;
  notes?: string;
}

export interface ServiceRegistration {
  whatsapp: boolean;
  telegram: boolean;
  signal: boolean;
  viber: boolean;
  facebook: boolean;
}

export interface PhoneValidationResult {
  isValid: boolean;
  internationalFormat?: string;
  country?: string;
  carrier?: string;
  phoneType?: string;
}

export interface ReverseSearchResult {
  id: string;
  source: string;
  associatedName?: string;
  associatedData?: string;
  confidence: 'high' | 'medium' | 'low';
  notes?: string;
}

export interface PhoneAnalysisModuleData {
  subjectId: string;
  phoneNumber: string;
  source: string;
  otherSource?: string;
  discoveryDate?: string;
  activity: 'active' | 'inactive' | 'unknown';
  
  // Validace
  validationResult?: PhoneValidationResult;
  
  // Reverzní vyhledávání
  reverseSearchResults: ReverseSearchResult[];
  reverseSearchNotes?: string;
  
  // Služby
  serviceRegistrations: ServiceRegistration;
  
  // Historie aktivit
  phoneActivities: PhoneActivity[];
  
  // Připojená zařízení
  connectedDevices: ConnectedDevice[];
  
  // Fotodokumentace
  photos: Array<{
    id: string;
    downloadURL: string;
    description: string;
    dateTaken?: string;
    fileName: string;
    sourceURL?: string;
  }>;
  
  // Poznámky
  technicalNotes?: string;
  investigationNotes?: string;
  osintNotes?: string;
  
  // Metadata
  createdAt: Timestamp;
  lastUpdatedAt: Timestamp;
} 