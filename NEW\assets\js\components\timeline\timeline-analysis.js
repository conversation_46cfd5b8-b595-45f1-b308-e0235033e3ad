/**
 * <PERSON><PERSON><PERSON> osa - funkce pro analýzu
 */

/**
 * Spuštění analýzy časové osy
 */
function runTimelineAnalysis() {
    console.log('Spuštění analýzy časové osy');

    // Analýza vzorců
    analyzePatterns();

    // Analýza mezer
    analyzeGaps();

    // Analýza statistik
    analyzeStatistics();

    // Analýza predikcí
    analyzePredictions();

    // Zobrazení notifikace
    showNotification('Analýza časové osy byla úspěšně dokončena.', 'success');
}

/**
 * Analýza vzorců v časové ose
 */
function analyzePatterns() {
    console.log('Analýza vzorců v časové ose');

    const timelineModule = document.querySelector('.module[id^="module-casova-osa"]');
    if (!timelineModule) return;

    const patternsTab = timelineModule.querySelector('#patterns-tab');
    if (!patternsTab) return;

    // Získání filtrovaných událostí
    const filteredEvents = filterEvents();

    // Ko<PERSON>rola, zda existují události k analýze
    if (filteredEvents.length === 0) {
        patternsTab.innerHTML = `
            <div class="analysis-empty">
                <i class="fas fa-exclamation-circle"></i>
                <p>Nebyly nalezeny žádné události k analýze.</p>
            </div>
        `;
        return;
    }

    // Analýza typů událostí
    const eventTypes = {};
    filteredEvents.forEach(event => {
        if (!eventTypes[event.type]) {
            eventTypes[event.type] = 0;
        }
        eventTypes[event.type]++;
    });

    // Analýza zdrojů událostí
    const eventSources = {};
    filteredEvents.forEach(event => {
        if (!eventSources[event.source]) {
            eventSources[event.source] = 0;
        }
        eventSources[event.source]++;
    });

    // Analýza časových vzorců
    const timePatterns = analyzeTimePatterns(filteredEvents);

    // Analýza opakujících se lokací
    const locationPatterns = analyzeLocationPatterns(filteredEvents);

    // Analýza souvisejících osob
    const personPatterns = analyzePersonPatterns(filteredEvents);

    // Vytvoření HTML pro výsledky analýzy
    let patternsHTML = `
        <div class="analysis-results">
            <div class="analysis-section">
                <h5>Rozložení typů událostí</h5>
                <div class="analysis-chart">
                    <div class="chart-placeholder">
                        <div class="chart-bars">
                            ${Object.entries(eventTypes).map(([type, count]) => `
                                <div class="chart-bar-container">
                                    <div class="chart-bar-label">${getTypeName(type)}</div>
                                    <div class="chart-bar" style="width: ${Math.min(100, count / filteredEvents.length * 100)}%;">
                                        <span class="chart-bar-value">${count}</span>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>

            <div class="analysis-section">
                <h5>Rozložení zdrojů událostí</h5>
                <div class="analysis-chart">
                    <div class="chart-placeholder">
                        <div class="chart-bars">
                            ${Object.entries(eventSources).map(([source, count]) => `
                                <div class="chart-bar-container">
                                    <div class="chart-bar-label">${getSourceName(source)}</div>
                                    <div class="chart-bar" style="width: ${Math.min(100, count / filteredEvents.length * 100)}%;">
                                        <span class="chart-bar-value">${count}</span>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>

            <div class="analysis-section">
                <h5>Časové vzorce</h5>
                <div class="analysis-results-list">
                    ${timePatterns.length > 0 ? timePatterns.map(pattern => `
                        <div class="analysis-result-item">
                            <div class="analysis-result-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="analysis-result-content">
                                <div class="analysis-result-title">${pattern.title}</div>
                                <div class="analysis-result-description">${pattern.description}</div>
                            </div>
                        </div>
                    `).join('') : `
                        <div class="analysis-empty-result">
                            <p>Nebyly nalezeny žádné významné časové vzorce.</p>
                        </div>
                    `}
                </div>
            </div>

            <div class="analysis-section">
                <h5>Lokační vzorce</h5>
                <div class="analysis-results-list">
                    ${locationPatterns.length > 0 ? locationPatterns.map(pattern => `
                        <div class="analysis-result-item">
                            <div class="analysis-result-icon">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div class="analysis-result-content">
                                <div class="analysis-result-title">${pattern.title}</div>
                                <div class="analysis-result-description">${pattern.description}</div>
                            </div>
                        </div>
                    `).join('') : `
                        <div class="analysis-empty-result">
                            <p>Nebyly nalezeny žádné významné lokační vzorce.</p>
                        </div>
                    `}
                </div>
            </div>

            <div class="analysis-section">
                <h5>Vzorce souvisejících osob</h5>
                <div class="analysis-results-list">
                    ${personPatterns.length > 0 ? personPatterns.map(pattern => `
                        <div class="analysis-result-item">
                            <div class="analysis-result-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="analysis-result-content">
                                <div class="analysis-result-title">${pattern.title}</div>
                                <div class="analysis-result-description">${pattern.description}</div>
                            </div>
                        </div>
                    `).join('') : `
                        <div class="analysis-empty-result">
                            <p>Nebyly nalezeny žádné významné vzorce souvisejících osob.</p>
                        </div>
                    `}
                </div>
            </div>
        </div>
    `;

    patternsTab.innerHTML = patternsHTML;
}

/**
 * Analýza časových vzorců v událostech
 * @param {Array} events - Pole událostí k analýze
 * @returns {Array} - Pole nalezených vzorců
 */
function analyzeTimePatterns(events) {
    if (events.length < 3) {
        return [];
    }

    const patterns = [];

    // Analýza denní aktivity
    const hourCounts = {};
    events.forEach(event => {
        const date = new Date(event.datetime);
        const hour = date.getHours();

        if (!hourCounts[hour]) {
            hourCounts[hour] = 0;
        }
        hourCounts[hour]++;
    });

    // Nalezení nejaktivnější hodiny
    let maxHour = 0;
    let maxCount = 0;

    Object.entries(hourCounts).forEach(([hour, count]) => {
        if (count > maxCount) {
            maxHour = parseInt(hour);
            maxCount = count;
        }
    });

    if (maxCount > events.length * 0.3) {
        patterns.push({
            title: 'Vysoká aktivita v určitou denní dobu',
            description: `${maxCount} událostí (${Math.round(maxCount / events.length * 100)}%) se odehrálo mezi ${maxHour}:00 a ${maxHour + 1}:00.`
        });
    }

    // Analýza dnů v týdnu
    const dayCounts = {};
    events.forEach(event => {
        const date = new Date(event.datetime);
        const day = date.getDay();

        if (!dayCounts[day]) {
            dayCounts[day] = 0;
        }
        dayCounts[day]++;
    });

    // Nalezení nejaktivnějšího dne
    let maxDay = 0;
    maxCount = 0;

    Object.entries(dayCounts).forEach(([day, count]) => {
        if (count > maxCount) {
            maxDay = parseInt(day);
            maxCount = count;
        }
    });

    const dayNames = ['neděli', 'pondělí', 'úterý', 'středu', 'čtvrtek', 'pátek', 'sobotu'];

    if (maxCount > events.length * 0.3) {
        patterns.push({
            title: 'Vysoká aktivita v určitý den v týdnu',
            description: `${maxCount} událostí (${Math.round(maxCount / events.length * 100)}%) se odehrálo v ${dayNames[maxDay]}.`
        });
    }

    // Analýza pravidelných intervalů
    const sortedEvents = [...events].sort((a, b) => new Date(a.datetime) - new Date(b.datetime));
    const intervals = [];

    for (let i = 1; i < sortedEvents.length; i++) {
        const prevDate = new Date(sortedEvents[i - 1].datetime);
        const currDate = new Date(sortedEvents[i].datetime);

        const diffHours = (currDate - prevDate) / (1000 * 60 * 60);
        intervals.push(diffHours);
    }

    // Kontrola, zda existují podobné intervaly
    const intervalGroups = {};
    intervals.forEach(interval => {
        // Zaokrouhlení na nejbližší hodinu
        const roundedInterval = Math.round(interval);

        if (!intervalGroups[roundedInterval]) {
            intervalGroups[roundedInterval] = 0;
        }
        intervalGroups[roundedInterval]++;
    });

    // Nalezení nejčastějšího intervalu
    let maxInterval = 0;
    maxCount = 0;

    Object.entries(intervalGroups).forEach(([interval, count]) => {
        if (count > maxCount && parseInt(interval) > 0) {
            maxInterval = parseInt(interval);
            maxCount = count;
        }
    });

    if (maxCount > intervals.length * 0.3 && maxInterval > 0) {
        let intervalDescription = '';

        if (maxInterval < 24) {
            intervalDescription = `${maxInterval} hodin`;
        } else if (maxInterval === 24) {
            intervalDescription = '1 den';
        } else if (maxInterval % 24 === 0) {
            intervalDescription = `${maxInterval / 24} dní`;
        } else {
            intervalDescription = `${maxInterval} hodin`;
        }

        patterns.push({
            title: 'Pravidelné intervaly mezi událostmi',
            description: `${maxCount} po sobě jdoucích událostí (${Math.round(maxCount / intervals.length * 100)}%) má interval přibližně ${intervalDescription}.`
        });
    }

    return patterns;
}

/**
 * Analýza lokačních vzorců v událostech
 * @param {Array} events - Pole událostí k analýze
 * @returns {Array} - Pole nalezených vzorců
 */
function analyzeLocationPatterns(events) {
    // Filtrování událostí s lokací
    const eventsWithLocation = events.filter(event => event.location && event.location.trim() !== '');

    if (eventsWithLocation.length < 2) {
        return [];
    }

    const patterns = [];

    // Počítání výskytů lokací
    const locationCounts = {};
    eventsWithLocation.forEach(event => {
        if (!locationCounts[event.location]) {
            locationCounts[event.location] = 0;
        }
        locationCounts[event.location]++;
    });

    // Nalezení nejčastějších lokací
    const sortedLocations = Object.entries(locationCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3);

    sortedLocations.forEach(([location, count]) => {
        if (count > 1) {
            const percentage = Math.round(count / eventsWithLocation.length * 100);

            patterns.push({
                title: `Častý výskyt na lokaci: ${location}`,
                description: `${count} událostí (${percentage}% z událostí s lokací) se odehrálo na místě: ${location}.`
            });
        }
    });

    // Analýza pohybu mezi lokacemi
    if (eventsWithLocation.length > 2) {
        const sortedLocationEvents = [...eventsWithLocation].sort((a, b) => new Date(a.datetime) - new Date(b.datetime));

        // Hledání opakujících se sekvencí lokací
        const locationSequences = {};

        for (let i = 0; i < sortedLocationEvents.length - 1; i++) {
            const currentLocation = sortedLocationEvents[i].location;
            const nextLocation = sortedLocationEvents[i + 1].location;

            if (currentLocation !== nextLocation) {
                const sequence = `${currentLocation} -> ${nextLocation}`;

                if (!locationSequences[sequence]) {
                    locationSequences[sequence] = 0;
                }
                locationSequences[sequence]++;
            }
        }

        // Nalezení nejčastějších sekvencí
        const sortedSequences = Object.entries(locationSequences)
            .sort((a, b) => b[1] - a[1])
            .slice(0, 2);

        sortedSequences.forEach(([sequence, count]) => {
            if (count > 1) {
                patterns.push({
                    title: `Opakovaný pohyb mezi lokacemi`,
                    description: `Sekvence "${sequence}" se opakuje ${count}x.`
                });
            }
        });
    }

    return patterns;
}

/**
 * Analýza vzorců souvisejících osob v událostech
 * @param {Array} events - Pole událostí k analýze
 * @returns {Array} - Pole nalezených vzorců
 */
function analyzePersonPatterns(events) {
    // Filtrování událostí s osobami
    const eventsWithPersons = events.filter(event => event.relatedPersons && event.relatedPersons.length > 0);

    if (eventsWithPersons.length < 2) {
        return [];
    }

    const patterns = [];

    // Počítání výskytů osob
    const personCounts = {};
    eventsWithPersons.forEach(event => {
        event.relatedPersons.forEach(person => {
            if (!personCounts[person]) {
                personCounts[person] = 0;
            }
            personCounts[person]++;
        });
    });

    // Nalezení nejčastějších osob
    const sortedPersons = Object.entries(personCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 3);

    sortedPersons.forEach(([person, count]) => {
        if (count > 1) {
            const percentage = Math.round(count / eventsWithPersons.length * 100);

            patterns.push({
                title: `Častý výskyt osoby: ${person}`,
                description: `${person} se vyskytuje v ${count} událostech (${percentage}% z událostí s osobami).`
            });
        }
    });

    // Analýza společného výskytu osob
    const personPairs = {};

    eventsWithPersons.forEach(event => {
        if (event.relatedPersons.length > 1) {
            for (let i = 0; i < event.relatedPersons.length; i++) {
                for (let j = i + 1; j < event.relatedPersons.length; j++) {
                    const person1 = event.relatedPersons[i];
                    const person2 = event.relatedPersons[j];

                    // Seřazení jmen pro konzistentní klíč
                    const pair = [person1, person2].sort().join(' & ');

                    if (!personPairs[pair]) {
                        personPairs[pair] = 0;
                    }
                    personPairs[pair]++;
                }
            }
        }
    });

    // Nalezení nejčastějších párů
    const sortedPairs = Object.entries(personPairs)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 2);

    sortedPairs.forEach(([pair, count]) => {
        if (count > 1) {
            patterns.push({
                title: `Častý společný výskyt osob`,
                description: `Osoby ${pair} se společně vyskytují v ${count} událostech.`
            });
        }
    });

    return patterns;
}

/**
 * Analýza mezer v časové ose
 */
function analyzeGaps() {
    console.log('Analýza mezer v časové ose');

    const timelineModule = document.querySelector('.module[id^="module-casova-osa"]');
    if (!timelineModule) return;

    const gapsTab = timelineModule.querySelector('#gaps-tab');
    if (!gapsTab) return;

    // Získání filtrovaných událostí
    const filteredEvents = filterEvents();

    // Kontrola, zda existují události k analýze
    if (filteredEvents.length < 2) {
        gapsTab.innerHTML = `
            <div class="analysis-empty">
                <i class="fas fa-exclamation-circle"></i>
                <p>Pro analýzu mezer je potřeba alespoň 2 události.</p>
            </div>
        `;
        return;
    }

    // Seřazení událostí podle data
    const sortedEvents = [...filteredEvents].sort((a, b) => new Date(a.datetime) - new Date(b.datetime));

    // Analýza mezer mezi událostmi
    const gaps = [];

    for (let i = 1; i < sortedEvents.length; i++) {
        const prevEvent = sortedEvents[i - 1];
        const currEvent = sortedEvents[i];

        const prevDate = new Date(prevEvent.datetime);
        const currDate = new Date(currEvent.datetime);

        // Výpočet rozdílu v hodinách
        const diffHours = (currDate - prevDate) / (1000 * 60 * 60);

        // Pokud je mezera větší než 24 hodin, přidáme ji do seznamu
        if (diffHours > 24) {
            gaps.push({
                start: prevDate,
                end: currDate,
                duration: diffHours,
                prevEvent: prevEvent,
                currEvent: currEvent
            });
        }
    }

    // Seřazení mezer podle velikosti (od největší)
    gaps.sort((a, b) => b.duration - a.duration);

    // Vytvoření HTML pro výsledky analýzy
    let gapsHTML = '';

    if (gaps.length === 0) {
        gapsHTML = `
            <div class="analysis-empty">
                <i class="fas fa-check-circle"></i>
                <p>Nebyly nalezeny žádné významné mezery v časové ose.</p>
            </div>
        `;
    } else {
        gapsHTML = `
            <div class="analysis-results">
                <div class="analysis-section">
                    <h5>Nalezené mezery v časové ose</h5>
                    <div class="analysis-disclaimer">
                        <i class="fas fa-info-circle"></i>
                        <p>Mezery větší než 24 hodin mohou indikovat chybějící události nebo období bez aktivity.</p>
                    </div>
                    <div class="analysis-results-list">
                        ${gaps.map(gap => {
                            // Formátování doby trvání
                            let durationText = '';
                            if (gap.duration < 48) {
                                durationText = `${Math.round(gap.duration)} hodin`;
                            } else {
                                const days = Math.floor(gap.duration / 24);
                                const hours = Math.round(gap.duration % 24);
                                durationText = `${days} ${days === 1 ? 'den' : 'dny'}`;
                                if (hours > 0) {
                                    durationText += ` a ${hours} hodin`;
                                }
                            }

                            return `
                                <div class="analysis-result-item">
                                    <div class="analysis-result-icon">
                                        <i class="fas fa-hourglass-half"></i>
                                    </div>
                                    <div class="analysis-result-content">
                                        <div class="analysis-result-title">Mezera: ${durationText}</div>
                                        <div class="analysis-result-description">
                                            Od: ${formatDate(gap.start)} (${gap.prevEvent.title})<br>
                                            Do: ${formatDate(gap.end)} (${gap.currEvent.title})
                                        </div>
                                        <div class="analysis-result-actions">
                                            <button type="button" class="btn-inline btn-sm add-gap-event"
                                                data-start="${gap.start.toISOString()}"
                                                data-end="${gap.end.toISOString()}">
                                                <i class="fas fa-plus"></i> Přidat událost do mezery
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            `;
                        }).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    gapsTab.innerHTML = gapsHTML;

    // Přidání event listenerů pro tlačítka přidání události do mezery
    const addGapButtons = gapsTab.querySelectorAll('.add-gap-event');
    addGapButtons.forEach(button => {
        button.addEventListener('click', function() {
            const startDate = new Date(this.getAttribute('data-start'));
            const endDate = new Date(this.getAttribute('data-end'));

            // Výpočet středu mezery
            const midDate = new Date(startDate.getTime() + (endDate.getTime() - startDate.getTime()) / 2);

            // Zobrazení dialogu pro přidání události s předvyplněným datem
            showAddEventDialog(midDate.toISOString());
        });
    });

    // Kontrola, zda existují události k analýze
    if (filteredEvents.length < 2) {
        gapsTab.innerHTML = `
            <div class="analysis-empty">
                <i class="fas fa-exclamation-circle"></i>
                <p>Pro analýzu mezer je potřeba alespoň dvě události.</p>
            </div>
        `;
        return;
    }

    // Seřazení událostí podle data
    const sortedEventsForGaps = sortEvents(filteredEvents);

    // Analýza mezer mezi událostmi
    let gapsArray = [];
    for (let i = 1; i < sortedEventsForGaps.length; i++) {
        const prevEvent = sortedEventsForGaps[i - 1];
        const currEvent = sortedEventsForGaps[i];

        const prevDate = new Date(prevEvent.datetime);
        const currDate = new Date(currEvent.datetime);

        const diffMs = currDate - prevDate;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        // Pokud je mezera větší než 7 dní, považujeme ji za významnou
        if (diffDays > 7) {
            gapsArray.push({
                start: prevEvent,
                end: currEvent,
                diffDays: diffDays,
                startDate: prevDate,
                endDate: currDate
            });
        }
    }

    // Seřazení mezer podle velikosti (sestupně)
    gapsArray.sort((a, b) => b.diffDays - a.diffDays);

    // Vytvoření HTML pro výsledky analýzy
    let gapsHTML = `
        <div class="analysis-results">
            <div class="analysis-section">
                <h5>Významné mezery v časové ose</h5>
                <div class="analysis-results-list">
                    ${gapsArray.length > 0 ? gapsArray.map(gap => `
                        <div class="analysis-result-item">
                            <div class="analysis-result-icon">
                                <i class="fas fa-calendar-minus"></i>
                            </div>
                            <div class="analysis-result-content">
                                <div class="analysis-result-title">Mezera ${gap.diffDays} dní</div>
                                <div class="analysis-result-description">
                                    Od události "${gap.start.title}" (${formatDate(gap.startDate)})
                                    do události "${gap.end.title}" (${formatDate(gap.endDate)}).
                                </div>
                                <div class="analysis-result-actions">
                                    <button type="button" class="btn-inline btn-sm add-event-in-gap"
                                        data-start="${gap.start.datetime}"
                                        data-end="${gap.end.datetime}">
                                        <i class="fas fa-plus"></i> Přidat událost
                                    </button>
                                </div>
                            </div>
                        </div>
                    `).join('') : `
                        <div class="analysis-empty-result">
                            <p>Nebyly nalezeny žádné významné mezery v časové ose.</p>
                        </div>
                    `}
                </div>
            </div>

            <div class="analysis-section">
                <h5>Doporučení pro doplnění dat</h5>
                <div class="analysis-recommendations">
                    ${gapsArray.length > 0 ? `
                        <ul class="analysis-recommendations-list">
                            <li>Zkuste doplnit události v období ${formatDate(gapsArray[0].startDate)} - ${formatDate(gapsArray[0].endDate)}.</li>
                            <li>Prověřte, zda v mezerách nedošlo k významným událostem, které by měly být zaznamenány.</li>
                            <li>Zvažte import dat z dalších zdrojů pro doplnění mezer.</li>
                        </ul>
                    ` : `
                        <p>Časová osa je relativně kompletní bez významných mezer.</p>
                    `}
                </div>
            </div>
        </div>
    `;

    gapsTab.innerHTML = gapsHTML;

    // Přidání event listenerů pro tlačítka přidání události v mezeře
    const addEventButtons = gapsTab.querySelectorAll('.add-event-in-gap');
    addEventButtons.forEach(button => {
        button.addEventListener('click', function() {
            const startDate = new Date(this.getAttribute('data-start'));
            const endDate = new Date(this.getAttribute('data-end'));

            // Výpočet středu mezery
            const midDate = new Date(startDate.getTime() + (endDate.getTime() - startDate.getTime()) / 2);

            // Zobrazení dialogu pro přidání události s předvyplněným datem
            showAddEventInGapDialog(midDate);
        });
    });
}

/**
 * Analýza statistik časové osy
 */
function analyzeStatistics() {
    console.log('Analýza statistik časové osy');

    const timelineModule = document.querySelector('.module[id^="module-casova-osa"]');
    if (!timelineModule) return;

    const statisticsTab = timelineModule.querySelector('#statistics-tab');
    if (!statisticsTab) return;

    // Získání filtrovaných událostí
    const filteredEvents = filterEvents();

    // Kontrola, zda existují události k analýze
    if (filteredEvents.length === 0) {
        statisticsTab.innerHTML = `
            <div class="analysis-empty">
                <i class="fas fa-exclamation-circle"></i>
                <p>Nebyly nalezeny žádné události k analýze.</p>
            </div>
        `;
        return;
    }

    // Výpočet základních statistik
    const stats = calculateBasicStats(filteredEvents);

    // Analýza četnosti událostí v čase
    const frequencyStats = analyzeFrequency(filteredEvents);

    // Analýza důležitosti událostí
    const importanceStats = analyzeImportance(filteredEvents);

    // Vytvoření HTML pro výsledky analýzy
    let statisticsHTML = `
        <div class="analysis-results">
            <div class="analysis-section">
                <h5>Základní statistiky</h5>
                <div class="analysis-stats-grid">
                    <div class="analysis-stat-item">
                        <div class="analysis-stat-value">${filteredEvents.length}</div>
                        <div class="analysis-stat-label">Celkový počet událostí</div>
                    </div>
                    <div class="analysis-stat-item">
                        <div class="analysis-stat-value">${stats.uniqueDays}</div>
                        <div class="analysis-stat-label">Počet unikátních dní</div>
                    </div>
                    <div class="analysis-stat-item">
                        <div class="analysis-stat-value">${stats.timeSpan} dní</div>
                        <div class="analysis-stat-label">Časové rozpětí</div>
                    </div>
                    <div class="analysis-stat-item">
                        <div class="analysis-stat-value">${stats.avgEventsPerDay.toFixed(2)}</div>
                        <div class="analysis-stat-label">Průměrný počet událostí za den</div>
                    </div>
                </div>
            </div>

            <div class="analysis-section">
                <h5>Četnost událostí v čase</h5>
                <div class="analysis-chart">
                    <div class="chart-placeholder">
                        <div class="chart-bars">
                            ${Object.entries(frequencyStats.byMonth).map(([month, count]) => `
                                <div class="chart-bar-container">
                                    <div class="chart-bar-label">${month}</div>
                                    <div class="chart-bar" style="width: ${Math.min(100, count / Math.max(...Object.values(frequencyStats.byMonth)) * 100)}%;">
                                        <span class="chart-bar-value">${count}</span>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>

            <div class="analysis-section">
                <h5>Rozložení důležitosti událostí</h5>
                <div class="analysis-chart">
                    <div class="chart-placeholder">
                        <div class="chart-bars">
                            ${Object.entries(importanceStats).map(([importance, count]) => `
                                <div class="chart-bar-container">
                                    <div class="chart-bar-label">${getImportanceName(importance)}</div>
                                    <div class="chart-bar importance-${importance}" style="width: ${Math.min(100, count / filteredEvents.length * 100)}%;">
                                        <span class="chart-bar-value">${count}</span>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    statisticsTab.innerHTML = statisticsHTML;
}

/**
 * Výpočet základních statistik pro události
 * @param {Array} events - Pole událostí k analýze
 * @returns {Object} - Objekt se základními statistikami
 */
function calculateBasicStats(events) {
    // Kontrola, zda existují události
    if (events.length === 0) {
        return {
            uniqueDays: 0,
            timeSpan: 0,
            avgEventsPerDay: 0
        };
    }

    // Seřazení událostí podle data
    const sortedEvents = [...events].sort((a, b) => new Date(a.datetime) - new Date(b.datetime));

    // Získání prvního a posledního data
    const firstDate = new Date(sortedEvents[0].datetime);
    const lastDate = new Date(sortedEvents[sortedEvents.length - 1].datetime);

    // Výpočet časového rozpětí ve dnech
    const timeSpanMs = lastDate - firstDate;
    const timeSpanDays = Math.ceil(timeSpanMs / (1000 * 60 * 60 * 24));

    // Počet unikátních dní
    const uniqueDays = new Set(events.map(event => {
        const date = new Date(event.datetime);
        return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
    })).size;

    // Průměrný počet událostí za den
    const avgEventsPerDay = uniqueDays > 0 ? events.length / uniqueDays : 0;

    return {
        uniqueDays: uniqueDays,
        timeSpan: timeSpanDays,
        avgEventsPerDay: avgEventsPerDay
    };
}

/**
 * Analýza četnosti událostí v čase
 * @param {Array} events - Pole událostí k analýze
 * @returns {Object} - Objekt s analýzou četnosti
 */
function analyzeFrequency(events) {
    // Kontrola, zda existují události
    if (events.length === 0) {
        return {
            byMonth: {},
            byDay: {},
            byHour: {}
        };
    }

    // Počítání událostí podle měsíce
    const monthCounts = {};
    const monthNames = [
        'Leden', 'Únor', 'Březen', 'Duben', 'Květen', 'Červen',
        'Červenec', 'Srpen', 'Září', 'Říjen', 'Listopad', 'Prosinec'
    ];

    events.forEach(event => {
        const date = new Date(event.datetime);
        const monthYear = `${monthNames[date.getMonth()]} ${date.getFullYear()}`;

        if (!monthCounts[monthYear]) {
            monthCounts[monthYear] = 0;
        }
        monthCounts[monthYear]++;
    });

    // Počítání událostí podle dne v týdnu
    const dayCounts = {};
    const dayNames = ['Neděle', 'Pondělí', 'Úterý', 'Středa', 'Čtvrtek', 'Pátek', 'Sobota'];

    events.forEach(event => {
        const date = new Date(event.datetime);
        const day = dayNames[date.getDay()];

        if (!dayCounts[day]) {
            dayCounts[day] = 0;
        }
        dayCounts[day]++;
    });

    // Počítání událostí podle hodiny
    const hourCounts = {};

    events.forEach(event => {
        const date = new Date(event.datetime);
        const hour = date.getHours();

        if (!hourCounts[hour]) {
            hourCounts[hour] = 0;
        }
        hourCounts[hour]++;
    });

    return {
        byMonth: monthCounts,
        byDay: dayCounts,
        byHour: hourCounts
    };
}

/**
 * Analýza důležitosti událostí
 * @param {Array} events - Pole událostí k analýze
 * @returns {Object} - Objekt s počty událostí podle důležitosti
 */
function analyzeImportance(events) {
    // Kontrola, zda existují události
    if (events.length === 0) {
        return {};
    }

    // Počítání událostí podle důležitosti
    const importanceCounts = {
        low: 0,
        medium: 0,
        high: 0,
        critical: 0
    };

    events.forEach(event => {
        if (event.importance && importanceCounts.hasOwnProperty(event.importance)) {
            importanceCounts[event.importance]++;
        } else {
            // Pokud není důležitost definována, považujeme ji za střední
            importanceCounts.medium++;
        }
    });

    return importanceCounts;
}

/**
 * Analýza predikcí budoucích událostí
 */
function analyzePredictions() {
    console.log('Analýza predikcí budoucích událostí');

    const timelineModule = document.querySelector('.module[id^="module-casova-osa"]');
    if (!timelineModule) return;

    const predictionsTab = timelineModule.querySelector('#predictions-tab');
    if (!predictionsTab) return;

    // Získání filtrovaných událostí
    const filteredEvents = filterEvents();

    // Kontrola, zda existují události k analýze
    if (filteredEvents.length < 5) {
        predictionsTab.innerHTML = `
            <div class="analysis-empty">
                <i class="fas fa-exclamation-circle"></i>
                <p>Pro analýzu predikcí je potřeba alespoň 5 událostí.</p>
            </div>
        `;
        return;
    }

    // Generování predikcí
    const predictions = generatePredictions(filteredEvents);

    // Vytvoření HTML pro výsledky analýzy
    let predictionsHTML = `
        <div class="analysis-results">
            <div class="analysis-section">
                <h5>Predikce budoucích událostí</h5>
                <div class="analysis-disclaimer">
                    <i class="fas fa-info-circle"></i>
                    <p>Predikce jsou založeny na analýze historických vzorců a mají pouze informativní charakter.</p>
                </div>
                <div class="analysis-results-list">
                    ${predictions.length > 0 ? predictions.map(prediction => `
                        <div class="analysis-result-item">
                            <div class="analysis-result-icon">
                                <i class="fas fa-crystal-ball"></i>
                            </div>
                            <div class="analysis-result-content">
                                <div class="analysis-result-title">${prediction.title}</div>
                                <div class="analysis-result-description">${prediction.description}</div>
                                <div class="analysis-result-meta">
                                    <span class="analysis-result-confidence">Spolehlivost: ${prediction.confidence}%</span>
                                    <span class="analysis-result-date">Očekávaný čas: ${prediction.expectedDate}</span>
                                </div>
                                <div class="analysis-result-actions">
                                    <button type="button" class="btn-inline btn-sm add-predicted-event"
                                        data-title="${prediction.title}"
                                        data-date="${prediction.expectedDateIso}">
                                        <i class="fas fa-plus"></i> Přidat do časové osy
                                    </button>
                                </div>
                            </div>
                        </div>
                    `).join('') : `
                        <div class="analysis-empty-result">
                            <p>Nebylo možné vygenerovat žádné predikce na základě dostupných dat.</p>
                        </div>
                    `}
                </div>
            </div>
        </div>
    `;

    predictionsTab.innerHTML = predictionsHTML;

    // Přidání event listenerů pro tlačítka přidání predikované události
    const addEventButtons = predictionsTab.querySelectorAll('.add-predicted-event');
    addEventButtons.forEach(button => {
        button.addEventListener('click', function() {
            const title = this.getAttribute('data-title');
            const date = this.getAttribute('data-date');

            // Zobrazení dialogu pro přidání události s předvyplněnými údaji
            showAddPredictedEventDialog(title, date);
        });
    });
}

/**
 * Generování predikcí budoucích událostí na základě historických dat
 * @param {Array} events - Pole událostí k analýze
 * @returns {Array} - Pole vygenerovaných predikcí
 */
function generatePredictions(events) {
    if (events.length < 5) {
        return [];
    }

    const predictions = [];
    const now = new Date();

    // Seřazení událostí podle data
    const sortedEvents = [...events].sort((a, b) => new Date(a.datetime) - new Date(b.datetime));

    // Analýza pravidelných událostí
    const regularEvents = analyzeRegularEvents(sortedEvents);

    // Analýza lokačních vzorců
    const locationPatterns = analyzeLocationPatterns(sortedEvents);

    // Analýza typů událostí
    const eventTypePatterns = analyzeEventTypePatterns(sortedEvents);

    // Generování predikcí na základě pravidelných událostí
    regularEvents.forEach(pattern => {
        if (pattern.interval && pattern.interval > 0) {
            // Poslední událost v sekvenci
            const lastEvent = pattern.events[pattern.events.length - 1];
            const lastDate = new Date(lastEvent.datetime);

            // Výpočet očekávaného data další události
            const expectedDate = new Date(lastDate);
            expectedDate.setHours(expectedDate.getHours() + pattern.interval);

            // Kontrola, zda očekávané datum je v budoucnosti
            if (expectedDate > now) {
                predictions.push({
                    title: `Očekávaná ${getTypeName(lastEvent.type).toLowerCase()} událost`,
                    description: `Na základě analýzy ${pattern.events.length} předchozích událostí s pravidelným intervalem přibližně ${formatInterval(pattern.interval)}.`,
                    confidence: Math.min(70 + pattern.events.length * 5, 95),
                    expectedDate: formatDate(expectedDate),
                    expectedDateIso: expectedDate.toISOString(),
                    type: lastEvent.type,
                    source: lastEvent.source
                });
            }
        }
    });

    // Generování predikcí na základě lokačních vzorců
    if (locationPatterns.length > 0) {
        locationPatterns.forEach(pattern => {
            if (pattern.title.includes('Častý výskyt na lokaci')) {
                const location = pattern.title.replace('Častý výskyt na lokaci: ', '');

                // Najít poslední událost na této lokaci
                const lastEventAtLocation = [...events]
                    .filter(event => event.location === location)
                    .sort((a, b) => new Date(b.datetime) - new Date(a.datetime))[0];

                if (lastEventAtLocation) {
                    // Výpočet průměrného intervalu mezi návštěvami této lokace
                    const eventsAtLocation = events.filter(event => event.location === location);
                    const intervals = [];

                    for (let i = 1; i < eventsAtLocation.length; i++) {
                        const prevDate = new Date(eventsAtLocation[i - 1].datetime);
                        const currDate = new Date(eventsAtLocation[i].datetime);

                        const diffHours = Math.abs((currDate - prevDate) / (1000 * 60 * 60));
                        intervals.push(diffHours);
                    }

                    // Výpočet průměrného intervalu
                    const avgInterval = intervals.length > 0 ?
                        intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length :
                        168; // Výchozí interval 1 týden (168 hodin)

                    // Výpočet očekávaného data další návštěvy
                    const lastDate = new Date(lastEventAtLocation.datetime);
                    const expectedDate = new Date(lastDate);
                    expectedDate.setHours(expectedDate.getHours() + avgInterval);

                    // Kontrola, zda očekávané datum je v budoucnosti
                    if (expectedDate > now) {
                        predictions.push({
                            title: `Očekávaná návštěva lokace: ${location}`,
                            description: `Na základě analýzy ${eventsAtLocation.length} předchozích návštěv této lokace s průměrným intervalem přibližně ${formatInterval(avgInterval)}.`,
                            confidence: Math.min(65 + eventsAtLocation.length * 3, 90),
                            expectedDate: formatDate(expectedDate),
                            expectedDateIso: expectedDate.toISOString(),
                            type: 'movement',
                            source: 'location'
                        });
                    }
                }
            }
        });
    }

    // Generování predikcí na základě typů událostí
    if (eventTypePatterns.length > 0) {
        const typeCounts = {};
        events.forEach(event => {
            if (!typeCounts[event.type]) {
                typeCounts[event.type] = 0;
            }
            typeCounts[event.type]++;
        });

        // Najít nejčastější typ události
        let maxType = '';
        let maxCount = 0;

        Object.entries(typeCounts).forEach(([type, count]) => {
            if (count > maxCount) {
                maxType = type;
                maxCount = count;
            }
        });

        if (maxType && maxCount >= 3) {
            // Najít události tohoto typu
            const eventsOfType = events.filter(event => event.type === maxType);

            // Výpočet průměrného intervalu mezi událostmi tohoto typu
            const intervals = [];

            for (let i = 1; i < eventsOfType.length; i++) {
                const prevDate = new Date(eventsOfType[i - 1].datetime);
                const currDate = new Date(eventsOfType[i].datetime);

                const diffHours = Math.abs((currDate - prevDate) / (1000 * 60 * 60));
                intervals.push(diffHours);
            }

            // Výpočet průměrného intervalu
            const avgInterval = intervals.length > 0 ?
                intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length :
                72; // Výchozí interval 3 dny (72 hodin)

            // Výpočet očekávaného data další události tohoto typu
            const lastEvent = eventsOfType.sort((a, b) => new Date(b.datetime) - new Date(a.datetime))[0];
            const lastDate = new Date(lastEvent.datetime);
            const expectedDate = new Date(lastDate);
            expectedDate.setHours(expectedDate.getHours() + avgInterval);

            // Kontrola, zda očekávané datum je v budoucnosti
            if (expectedDate > now) {
                predictions.push({
                    title: `Očekávaná událost typu: ${getTypeName(maxType)}`,
                    description: `Na základě analýzy ${eventsOfType.length} předchozích událostí tohoto typu s průměrným intervalem přibližně ${formatInterval(avgInterval)}.`,
                    confidence: Math.min(60 + eventsOfType.length * 2, 85),
                    expectedDate: formatDate(expectedDate),
                    expectedDateIso: expectedDate.toISOString(),
                    type: maxType,
                    source: lastEvent.source
                });
            }
        }
    }

    // Seřazení predikcí podle očekávaného data
    predictions.sort((a, b) => new Date(a.expectedDateIso) - new Date(b.expectedDateIso));

    return predictions;
}

/**
 * Analýza pravidelných událostí
 * @param {Array} events - Pole událostí k analýze
 * @returns {Array} - Pole nalezených vzorců
 */
function analyzeRegularEvents(events) {
    if (events.length < 3) {
        return [];
    }

    const patterns = [];

    // Analýza intervalů mezi událostmi
    const eventsByType = {};

    // Seskupení událostí podle typu
    events.forEach(event => {
        if (!eventsByType[event.type]) {
            eventsByType[event.type] = [];
        }
        eventsByType[event.type].push(event);
    });

    // Analýza každého typu události
    Object.entries(eventsByType).forEach(([type, typeEvents]) => {
        if (typeEvents.length >= 3) {
            // Seřazení událostí podle data
            const sortedEvents = [...typeEvents].sort((a, b) => new Date(a.datetime) - new Date(b.datetime));

            // Výpočet intervalů mezi událostmi
            const intervals = [];

            for (let i = 1; i < sortedEvents.length; i++) {
                const prevDate = new Date(sortedEvents[i - 1].datetime);
                const currDate = new Date(sortedEvents[i].datetime);

                const diffHours = (currDate - prevDate) / (1000 * 60 * 60);
                intervals.push(diffHours);
            }

            // Kontrola, zda existují podobné intervaly
            const intervalGroups = {};
            intervals.forEach((interval, index) => {
                // Zaokrouhlení na nejbližší hodinu
                const roundedInterval = Math.round(interval);

                if (!intervalGroups[roundedInterval]) {
                    intervalGroups[roundedInterval] = {
                        count: 0,
                        indices: []
                    };
                }

                intervalGroups[roundedInterval].count++;
                intervalGroups[roundedInterval].indices.push(index);
            });

            // Nalezení nejčastějšího intervalu
            let maxInterval = 0;
            let maxCount = 0;
            let maxIndices = [];

            Object.entries(intervalGroups).forEach(([interval, data]) => {
                if (data.count > maxCount && parseInt(interval) > 0) {
                    maxInterval = parseInt(interval);
                    maxCount = data.count;
                    maxIndices = data.indices;
                }
            });

            // Pokud je nalezen významný interval
            if (maxCount >= 2 && maxInterval > 0) {
                // Vytvoření sekvence událostí s tímto intervalem
                const sequenceEvents = [];

                // Přidání první události v sekvenci
                sequenceEvents.push(sortedEvents[maxIndices[0]]);

                // Přidání dalších událostí v sekvenci
                maxIndices.forEach(index => {
                    sequenceEvents.push(sortedEvents[index + 1]);
                });

                patterns.push({
                    type: type,
                    interval: maxInterval,
                    events: sequenceEvents
                });
            }
        }
    });

    return patterns;
}

/**
 * Analýza vzorců typů událostí
 * @param {Array} events - Pole událostí k analýze
 * @returns {Array} - Pole nalezených vzorců
 */
function analyzeEventTypePatterns(events) {
    if (events.length < 3) {
        return [];
    }

    const patterns = [];

    // Počítání výskytů typů událostí
    const typeCounts = {};
    events.forEach(event => {
        if (!typeCounts[event.type]) {
            typeCounts[event.type] = 0;
        }
        typeCounts[event.type]++;
    });

    // Nalezení nejčastějších typů
    const sortedTypes = Object.entries(typeCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 2);

    sortedTypes.forEach(([type, count]) => {
        if (count >= 3) {
            const percentage = Math.round(count / events.length * 100);

            patterns.push({
                title: `Častý výskyt typu události: ${getTypeName(type)}`,
                description: `${count} událostí (${percentage}%) je typu ${getTypeName(type)}.`
            });
        }
    });

    return patterns;
}

/**
 * Formátování časového intervalu
 * @param {number} hours - Počet hodin
 * @returns {string} - Formátovaný interval
 */
function formatInterval(hours) {
    if (hours < 1) {
        return `${Math.round(hours * 60)} minut`;
    } else if (hours < 24) {
        return `${Math.round(hours)} hodin`;
    } else if (hours === 24) {
        return '1 den';
    } else if (hours < 48) {
        return `${Math.round(hours)} hodin`;
    } else if (hours % 24 === 0) {
        return `${Math.round(hours / 24)} dní`;
    } else if (hours < 168) {
        const days = Math.floor(hours / 24);
        const remainingHours = Math.round(hours % 24);
        return `${days} ${days === 1 ? 'den' : 'dny'} a ${remainingHours} hodin`;
    } else if (hours < 720) {
        return `${Math.round(hours / 24)} dní`;
    } else {
        return `${Math.round(hours / 720)} měsíců`;
    }
}

/**
 * Zobrazení dialogu pro přidání predikované události
 * @param {string} title - Název události
 * @param {string} date - Datum události
 */
function showAddPredictedEventDialog(title, date) {
    console.log('Zobrazení dialogu pro přidání predikované události:', title, date);

    // Vytvoření dialogu
    const dialog = document.createElement('div');
    dialog.className = 'add-event-dialog';

    // Formátování data pro input
    const datetime = new Date(date);
    const formattedDatetime = formatDatetimeForInput(datetime);

    // Vytvoření obsahu dialogu
    dialog.innerHTML = `
        <div class="add-event-dialog-content">
            <div class="add-event-dialog-header">
                <h3>Přidat predikovanou událost</h3>
                <button type="button" class="add-event-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="add-event-dialog-body">
                <div class="form-group">
                    <label>Název události *</label>
                    <input type="text" class="form-control" id="event-title" value="${title}" placeholder="Zadejte název události">
                </div>
                <div class="form-group">
                    <label>Popis události</label>
                    <textarea class="form-control" id="event-description" rows="3" placeholder="Zadejte popis události">Predikovaná událost na základě analýzy historických dat.</textarea>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Datum a čas *</label>
                        <input type="datetime-local" class="form-control" id="event-datetime" value="${formattedDatetime}">
                    </div>
                    <div class="form-group">
                        <label>Typ události</label>
                        <select class="form-control" id="event-type">
                            <option value="communication">Komunikace</option>
                            <option value="movement" selected>Pohyb</option>
                            <option value="financial">Finanční transakce</option>
                            <option value="social">Sociální aktivita</option>
                            <option value="legal">Právní událost</option>
                            <option value="other">Ostatní</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Zdroj události</label>
                        <select class="form-control" id="event-source">
                            <option value="manual" selected>Manuálně přidáno</option>
                            <option value="social">Sociální sítě</option>
                            <option value="financial">Finanční monitoring</option>
                            <option value="communication">Komunikační platformy</option>
                            <option value="location">Lokační data</option>
                            <option value="other">Ostatní</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Důležitost</label>
                        <select class="form-control" id="event-importance">
                            <option value="low">Nízká</option>
                            <option value="medium" selected>Střední</option>
                            <option value="high">Vysoká</option>
                            <option value="critical">Kritická</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label>Lokace</label>
                    <input type="text" class="form-control" id="event-location" placeholder="Zadejte lokaci události">
                </div>
                <div class="form-group">
                    <label>Související osoby</label>
                    <input type="text" class="form-control" id="event-related-persons" placeholder="Zadejte související osoby oddělené čárkou">
                </div>
                <div class="form-group">
                    <label>Tagy</label>
                    <input type="text" class="form-control" id="event-tags" value="predikce, analýza" placeholder="Zadejte tagy oddělené čárkou">
                </div>
                <div class="form-group">
                    <label>Poznámky</label>
                    <textarea class="form-control" id="event-notes" rows="2" placeholder="Zadejte poznámky k události">Tato událost byla automaticky predikována na základě analýzy historických dat.</textarea>
                </div>
            </div>
            <div class="add-event-dialog-footer">
                <button type="button" class="btn-secondary add-event-dialog-cancel">Zrušit</button>
                <button type="button" class="btn-primary add-event-dialog-add">Přidat</button>
            </div>
        </div>
    `;

    // Přidání dialogu do stránky
    document.body.appendChild(dialog);

    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.add-event-dialog-close').addEventListener('click', () => dialog.remove());
    dialog.querySelector('.add-event-dialog-cancel').addEventListener('click', () => dialog.remove());

    dialog.querySelector('.add-event-dialog-add').addEventListener('click', function() {
        // Získání hodnot z formuláře
        const title = dialog.querySelector('#event-title').value.trim();
        const description = dialog.querySelector('#event-description').value.trim();
        const datetime = dialog.querySelector('#event-datetime').value;
        const type = dialog.querySelector('#event-type').value;
        const source = dialog.querySelector('#event-source').value;
        const importance = dialog.querySelector('#event-importance').value;
        const location = dialog.querySelector('#event-location').value.trim();
        const relatedPersons = dialog.querySelector('#event-related-persons').value.trim();
        const tags = dialog.querySelector('#event-tags').value.trim();
        const notes = dialog.querySelector('#event-notes').value.trim();

        // Validace povinných polí
        if (!title) {
            alert('Zadejte název události.');
            return;
        }

        if (!datetime) {
            alert('Zadejte datum a čas události.');
            return;
        }

        // Vytvoření nové události
        const newEvent = {
            id: generateEventId(),
            title: title,
            description: description,
            datetime: datetime,
            type: type,
            source: source,
            importance: importance,
            location: location,
            relatedPersons: relatedPersons ? relatedPersons.split(',').map(p => p.trim()) : [],
            tags: tags ? tags.split(',').map(t => t.trim()) : [],
            notes: notes,
            createdAt: new Date().toISOString(),
            media: [],
            predicted: true
        };

        // Přidání události do pole událostí
        addEvent(newEvent);

        // Zavření dialogu
        dialog.remove();
    });

    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}
