/**
 * Korelačn<PERSON> nástroj - hlavní soubor
 *
 * Tento modul implementuje korelační nástroj pro OSINT systém, který propojuje
 * informace z různých modulů a identifikuje vztahy, vzorce a souvislosti.
 */

// Globální proměnné
let correlationData = {
    entities: [],
    relationships: [],
    events: [],
    sources: []
};

// Konfigurace
const correlationConfig = {
    visualizationMode: 'network', // 'network', 'timeline', 'map', 'matrix'
    filterSettings: {
        entityTypes: [],
        relationshipTypes: [],
        dateRange: {
            start: null,
            end: null
        },
        sources: [],
        minConfidence: 0
    },
    displaySettings: {
        showLabels: true,
        showDetails: true,
        colorScheme: 'default',
        nodeSize: 'medium',
        edgeWidth: 'medium'
    }
};

/**
 * Inicializace korelačního nástroje
 */
function initCorrelationTool() {
    console.log('Inicializace korelačního nástroje');

    // Inicializace globálních dat korelačního nástroje
    initGlobalCorrelationData();

    // Najít všechny instance korelačního nástroje
    const correlationModules = document.querySelectorAll('.module[id^="module-korelacni-nastroj"]');
    const correlationContainers = document.querySelectorAll('.correlation-tool-container');

    if (correlationModules.length === 0 && correlationContainers.length === 0) {
        console.log('Nenalezeny žádné moduly korelačního nástroje');
        return;
    }

    console.log(`Nalezeno ${correlationModules.length} modulů korelačního nástroje a ${correlationContainers.length} kontejnerů`);

    // Inicializace každého modulu
    correlationModules.forEach(module => {
        console.log('Inicializace modulu:', module.id);

        // Načtení šablony
        const container = module.querySelector('.correlation-tool-container');
        if (container) {
            try {
                loadCorrelationTemplate(container);

                // Načtení dat z ostatních modulů
                loadDataFromOtherModules(module);
            } catch (error) {
                console.error('Chyba při inicializaci modulu:', error);
            }
        } else {
            try {
                // Inicializace ovládacích prvků
                initCorrelationControls(module);

                // Inicializace vizualizace
                initCorrelationVisualization(module);

                // Inicializace panelu detailů
                initCorrelationDetailsPanel(module);

                // Inicializace analytických nástrojů
                initCorrelationAnalytics(module);

                // Inicializace fotogalerie
                initCorrelationPhotoGallery(module);

                // Inicializace notifikací
                initCorrelationNotifications(module);

                // Načtení dat z ostatních modulů
                loadDataFromOtherModules(module);
            } catch (error) {
                console.error('Chyba při inicializaci modulu:', error);
            }
        }
    });

    // Inicializace samostatných kontejnerů
    correlationContainers.forEach(container => {
        console.log('Inicializace kontejneru:', container.id);

        try {
            // Načtení šablony
            loadCorrelationTemplate(container);

            // Načtení dat z ostatních modulů
            const parentModule = container.closest('.module');
            if (parentModule) {
                loadDataFromOtherModules(parentModule);
            }
        } catch (error) {
            console.error('Chyba při inicializaci kontejneru:', error);
        }
    });
}

/**
 * Inicializace globálních dat korelačního nástroje
 */
function initGlobalCorrelationData() {
    console.log('Inicializace globálních dat korelačního nástroje');

    // Kontrola, zda již existují globální data
    if (typeof correlationData !== 'undefined') {
        console.log('Globální data korelačního nástroje již existují');
        return;
    }

    // Pokus o načtení dat z lokálního úložiště
    try {
        const savedData = localStorage.getItem('correlation-data-global');

        if (savedData) {
            // Parsování dat
            correlationData = JSON.parse(savedData);
            console.log('Globální data korelačního nástroje byla úspěšně načtena z lokálního úložiště');
            return;
        }
    } catch (error) {
        console.error('Chyba při načítání globálních dat korelačního nástroje:', error);
    }

    // Vytvoření nových globálních dat
    correlationData = {
        entities: [],
        relationships: [],
        events: [],
        lastUpdated: new Date().toISOString()
    };

    console.log('Vytvořena nová globální data korelačního nástroje');
}

/**
 * Inicializace ovládacích prvků korelačního nástroje
 * @param {HTMLElement} module - Element modulu korelačního nástroje
 */
function initCorrelationControls(module) {
    console.log('Inicializace ovládacích prvků korelačního nástroje');

    // Přepínání režimů vizualizace
    const viewModeButtons = module.querySelectorAll('.view-mode');
    viewModeButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Odstranění aktivní třídy ze všech tlačítek
            viewModeButtons.forEach(btn => btn.classList.remove('active'));

            // Přidání aktivní třídy na kliknuté tlačítko
            this.classList.add('active');

            // Nastavení režimu vizualizace
            correlationConfig.visualizationMode = this.getAttribute('data-view');

            // Překreslení vizualizace
            updateCorrelationVisualization(module);
        });
    });

    // Tlačítko pro přidání entity
    const addEntityButton = module.querySelector('.add-entity');
    if (addEntityButton) {
        addEntityButton.addEventListener('click', function() {
            showAddEntityDialog(module);
        });
    }

    // Tlačítko pro přidání vztahu
    const addRelationshipButton = module.querySelector('.add-relationship');
    if (addRelationshipButton) {
        addRelationshipButton.addEventListener('click', function() {
            showAddRelationshipDialog(module);
        });
    }

    // Tlačítko pro import dat
    const importDataButton = module.querySelector('.import-data');
    if (importDataButton) {
        importDataButton.addEventListener('click', function() {
            showImportDataDialog(module);
        });
    }

    // Tlačítko pro export dat
    const exportDataButton = module.querySelector('.export-data');
    if (exportDataButton) {
        exportDataButton.addEventListener('click', function() {
            exportCorrelationData(module);
        });
    }

    // Tlačítko pro spuštění analýzy
    const runAnalysisButton = module.querySelector('.run-analysis');
    if (runAnalysisButton) {
        runAnalysisButton.addEventListener('click', function() {
            runCorrelationAnalysis(module);
        });
    }

    // Inicializace filtrů
    initCorrelationFilters(module);
}

/**
 * Inicializace filtrů korelačního nástroje
 * @param {HTMLElement} module - Element modulu korelačního nástroje
 */
function initCorrelationFilters(module) {
    console.log('Inicializace filtrů korelačního nástroje');

    // Filtr typů entit
    const entityTypeFilter = module.querySelector('.filter-entity-type');
    if (entityTypeFilter) {
        entityTypeFilter.addEventListener('change', function() {
            const selectedOptions = Array.from(this.selectedOptions).map(option => option.value);
            correlationConfig.filterSettings.entityTypes = selectedOptions;
            updateCorrelationVisualization(module);
        });
    }

    // Filtr typů vztahů
    const relationshipTypeFilter = module.querySelector('.filter-relationship-type');
    if (relationshipTypeFilter) {
        relationshipTypeFilter.addEventListener('change', function() {
            const selectedOptions = Array.from(this.selectedOptions).map(option => option.value);
            correlationConfig.filterSettings.relationshipTypes = selectedOptions;
            updateCorrelationVisualization(module);
        });
    }

    // Filtr časového období
    const dateFromFilter = module.querySelector('.filter-date-from');
    const dateToFilter = module.querySelector('.filter-date-to');

    if (dateFromFilter) {
        dateFromFilter.addEventListener('change', function() {
            correlationConfig.filterSettings.dateRange.start = this.value ? new Date(this.value) : null;
            updateCorrelationVisualization(module);
        });
    }

    if (dateToFilter) {
        dateToFilter.addEventListener('change', function() {
            correlationConfig.filterSettings.dateRange.end = this.value ? new Date(this.value) : null;
            updateCorrelationVisualization(module);
        });
    }

    // Tlačítko pro aplikaci filtrů
    const applyFiltersButton = module.querySelector('.apply-filters');
    if (applyFiltersButton) {
        applyFiltersButton.addEventListener('click', function() {
            updateCorrelationVisualization(module);
        });
    }

    // Tlačítko pro reset filtrů
    const resetFiltersButton = module.querySelector('.reset-filters');
    if (resetFiltersButton) {
        resetFiltersButton.addEventListener('click', function() {
            resetCorrelationFilters(module);
        });
    }
}

/**
 * Reset filtrů korelačního nástroje
 * @param {HTMLElement} module - Element modulu korelačního nástroje
 */
function resetCorrelationFilters(module) {
    console.log('Reset filtrů korelačního nástroje');

    // Reset hodnot filtrů v UI
    const entityTypeFilter = module.querySelector('.filter-entity-type');
    if (entityTypeFilter) {
        Array.from(entityTypeFilter.options).forEach(option => option.selected = false);
    }

    const relationshipTypeFilter = module.querySelector('.filter-relationship-type');
    if (relationshipTypeFilter) {
        Array.from(relationshipTypeFilter.options).forEach(option => option.selected = false);
    }

    const dateFromFilter = module.querySelector('.filter-date-from');
    if (dateFromFilter) {
        dateFromFilter.value = '';
    }

    const dateToFilter = module.querySelector('.filter-date-to');
    if (dateToFilter) {
        dateToFilter.value = '';
    }

    // Reset hodnot v konfiguraci
    correlationConfig.filterSettings = {
        entityTypes: [],
        relationshipTypes: [],
        dateRange: {
            start: null,
            end: null
        },
        sources: [],
        minConfidence: 0
    };

    // Aktualizace vizualizace
    updateCorrelationVisualization(module);
}

/**
 * Inicializace vizualizace korelačního nástroje
 * @param {HTMLElement} module - Element modulu korelačního nástroje
 */
function initCorrelationVisualization(module) {
    console.log('Inicializace vizualizace korelačního nástroje');

    // Najít kontejner pro vizualizaci
    const visualizationContainer = module.querySelector('.correlation-visualization');
    if (!visualizationContainer) {
        console.error('Nenalezen kontejner pro vizualizaci');
        return;
    }

    // Vytvořit kontejnery pro různé typy vizualizací
    const networkContainer = document.createElement('div');
    networkContainer.className = 'correlation-network';
    networkContainer.style.display = correlationConfig.visualizationMode === 'network' ? 'block' : 'none';

    const timelineContainer = document.createElement('div');
    timelineContainer.className = 'correlation-timeline';
    timelineContainer.style.display = correlationConfig.visualizationMode === 'timeline' ? 'block' : 'none';

    const mapContainer = document.createElement('div');
    mapContainer.className = 'correlation-map';
    mapContainer.style.display = correlationConfig.visualizationMode === 'map' ? 'block' : 'none';

    const matrixContainer = document.createElement('div');
    matrixContainer.className = 'correlation-matrix';
    matrixContainer.style.display = correlationConfig.visualizationMode === 'matrix' ? 'block' : 'none';

    // Přidat kontejnery do vizualizace
    visualizationContainer.appendChild(networkContainer);
    visualizationContainer.appendChild(timelineContainer);
    visualizationContainer.appendChild(mapContainer);
    visualizationContainer.appendChild(matrixContainer);

    // Vytvořit legendu
    createCorrelationLegend(visualizationContainer);

    // Inicializovat vizualizaci podle aktuálního režimu
    updateCorrelationVisualization(module);
}

/**
 * Vytvoření legendy pro korelační nástroj
 * @param {HTMLElement} container - Kontejner pro legendu
 */
function createCorrelationLegend(container) {
    console.log('Vytvoření legendy pro korelační nástroj');

    // Vytvořit kontejner pro legendu
    const legend = document.createElement('div');
    legend.className = 'correlation-legend';

    // Přidat položky legendy pro entity
    legend.innerHTML = `
        <div class="legend-section">
            <h5>Typy entit</h5>
            <div class="legend-item">
                <div class="legend-color entity-person"></div>
                <div class="legend-label">Osoba</div>
            </div>
            <div class="legend-item">
                <div class="legend-color entity-organization"></div>
                <div class="legend-label">Organizace</div>
            </div>
            <div class="legend-item">
                <div class="legend-color entity-location"></div>
                <div class="legend-label">Lokace</div>
            </div>
            <div class="legend-item">
                <div class="legend-color entity-event"></div>
                <div class="legend-label">Událost</div>
            </div>
            <div class="legend-item">
                <div class="legend-color entity-asset"></div>
                <div class="legend-label">Majetek</div>
            </div>
            <div class="legend-item">
                <div class="legend-color entity-digital"></div>
                <div class="legend-label">Digitální entita</div>
            </div>
        </div>
        <div class="legend-section">
            <h5>Typy vztahů</h5>
            <div class="legend-item">
                <div class="legend-color relationship-family"></div>
                <div class="legend-label">Rodinný</div>
            </div>
            <div class="legend-item">
                <div class="legend-color relationship-business"></div>
                <div class="legend-label">Obchodní</div>
            </div>
            <div class="legend-item">
                <div class="legend-color relationship-social"></div>
                <div class="legend-label">Sociální</div>
            </div>
            <div class="legend-item">
                <div class="legend-color relationship-ownership"></div>
                <div class="legend-label">Vlastnictví</div>
            </div>
            <div class="legend-item">
                <div class="legend-color relationship-communication"></div>
                <div class="legend-label">Komunikace</div>
            </div>
            <div class="legend-item">
                <div class="legend-color relationship-transaction"></div>
                <div class="legend-label">Transakce</div>
            </div>
        </div>
    `;

    // Přidat legendu do kontejneru
    container.appendChild(legend);
}

/**
 * Aktualizace vizualizace korelačního nástroje
 * @param {HTMLElement} module - Element modulu korelačního nástroje
 */
function updateCorrelationVisualization(module) {
    console.log('Aktualizace vizualizace korelačního nástroje');

    // Skrýt všechny kontejnery vizualizací
    const networkContainer = module.querySelector('.correlation-network');
    const timelineContainer = module.querySelector('.correlation-timeline');
    const mapContainer = module.querySelector('.correlation-map');
    const matrixContainer = module.querySelector('.correlation-matrix');

    if (networkContainer) networkContainer.style.display = 'none';
    if (timelineContainer) timelineContainer.style.display = 'none';
    if (mapContainer) mapContainer.style.display = 'none';
    if (matrixContainer) matrixContainer.style.display = 'none';

    // Zobrazit kontejner podle aktuálního režimu
    switch (correlationConfig.visualizationMode) {
        case 'network':
            if (networkContainer) {
                networkContainer.style.display = 'block';
                renderNetworkVisualization(networkContainer);
            }
            break;
        case 'timeline':
            if (timelineContainer) {
                timelineContainer.style.display = 'block';
                renderTimelineVisualization(timelineContainer);
            }
            break;
        case 'map':
            if (mapContainer) {
                mapContainer.style.display = 'block';
                renderMapVisualization(mapContainer);
            }
            break;
        case 'matrix':
            if (matrixContainer) {
                matrixContainer.style.display = 'block';
                renderMatrixVisualization(matrixContainer);
            }
            break;
    }
}

/**
 * Vykreslení síťové vizualizace
 * @param {HTMLElement} container - Kontejner pro vizualizaci
 */
function renderNetworkVisualization(container) {
    console.log('Vykreslení síťové vizualizace');

    // Kontrola, zda máme data k vizualizaci
    if (correlationData.entities.length === 0) {
        container.innerHTML = `
            <div class="visualization-empty-state">
                <i class="fas fa-project-diagram"></i>
                <p>Nejsou k dispozici žádná data pro vizualizaci. Přidejte entity a vztahy nebo importujte data z jiných modulů.</p>
            </div>
        `;
        return;
    }

    // Zde by byla implementace síťové vizualizace pomocí knihovny jako je vis.js nebo cytoscape.js
    // Pro účely demonstrace pouze zobrazíme zástupný obsah
    container.innerHTML = `
        <div class="visualization-placeholder">
            <i class="fas fa-project-diagram"></i>
            <p>Síťová vizualizace bude implementována pomocí knihovny vis.js nebo cytoscape.js.</p>
            <p>Počet entit: ${correlationData.entities.length}</p>
            <p>Počet vztahů: ${correlationData.relationships.length}</p>
        </div>
    `;
}

/**
 * Vykreslení časové vizualizace
 * @param {HTMLElement} container - Kontejner pro vizualizaci
 */
function renderTimelineVisualization(container) {
    console.log('Vykreslení časové vizualizace');

    // Kontrola, zda máme data k vizualizaci
    if (correlationData.events.length === 0) {
        container.innerHTML = `
            <div class="visualization-empty-state">
                <i class="fas fa-clock"></i>
                <p>Nejsou k dispozici žádná data pro časovou vizualizaci. Přidejte události nebo importujte data z jiných modulů.</p>
            </div>
        `;
        return;
    }

    // Zde by byla implementace časové vizualizace pomocí knihovny jako je vis-timeline
    // Pro účely demonstrace pouze zobrazíme zástupný obsah
    container.innerHTML = `
        <div class="visualization-placeholder">
            <i class="fas fa-clock"></i>
            <p>Časová vizualizace bude implementována pomocí knihovny vis-timeline.</p>
            <p>Počet událostí: ${correlationData.events.length}</p>
        </div>
    `;
}

/**
 * Vykreslení mapové vizualizace
 * @param {HTMLElement} container - Kontejner pro vizualizaci
 */
function renderMapVisualization(container) {
    console.log('Vykreslení mapové vizualizace');

    // Kontrola, zda máme data k vizualizaci
    const entitiesWithLocation = correlationData.entities.filter(entity => entity.location);

    if (entitiesWithLocation.length === 0) {
        container.innerHTML = `
            <div class="visualization-empty-state">
                <i class="fas fa-map-marked-alt"></i>
                <p>Nejsou k dispozici žádná data pro mapovou vizualizaci. Přidejte entity s lokacemi nebo importujte data z jiných modulů.</p>
            </div>
        `;
        return;
    }

    // Zde by byla implementace mapové vizualizace pomocí knihovny jako je Leaflet nebo Mapbox
    // Pro účely demonstrace pouze zobrazíme zástupný obsah
    container.innerHTML = `
        <div class="visualization-placeholder">
            <i class="fas fa-map-marked-alt"></i>
            <p>Mapová vizualizace bude implementována pomocí knihovny Leaflet nebo Mapbox.</p>
            <p>Počet entit s lokací: ${entitiesWithLocation.length}</p>
        </div>
    `;
}

/**
 * Vykreslení maticové vizualizace
 * @param {HTMLElement} container - Kontejner pro vizualizaci
 */
function renderMatrixVisualization(container) {
    console.log('Vykreslení maticové vizualizace');

    // Kontrola, zda máme data k vizualizaci
    if (correlationData.entities.length === 0 || correlationData.relationships.length === 0) {
        container.innerHTML = `
            <div class="visualization-empty-state">
                <i class="fas fa-table"></i>
                <p>Nejsou k dispozici žádná data pro maticovou vizualizaci. Přidejte entity a vztahy nebo importujte data z jiných modulů.</p>
            </div>
        `;
        return;
    }

    // Zde by byla implementace maticové vizualizace
    // Pro účely demonstrace pouze zobrazíme zástupný obsah
    container.innerHTML = `
        <div class="visualization-placeholder">
            <i class="fas fa-table"></i>
            <p>Maticová vizualizace bude implementována pomocí HTML tabulky nebo knihovny D3.js.</p>
            <p>Počet entit: ${correlationData.entities.length}</p>
            <p>Počet vztahů: ${correlationData.relationships.length}</p>
        </div>
    `;
}

/**
 * Inicializace panelu detailů korelačního nástroje
 * @param {HTMLElement} module - Element modulu korelačního nástroje
 */
function initCorrelationDetailsPanel(module) {
    console.log('Inicializace panelu detailů korelačního nástroje');

    // Najít nebo vytvořit panel detailů
    let detailsPanel = module.querySelector('.correlation-details-panel');

    if (!detailsPanel) {
        // Vytvořit panel detailů
        detailsPanel = document.createElement('div');
        detailsPanel.className = 'correlation-details-panel';

        // Přidat obsah panelu
        detailsPanel.innerHTML = `
            <div class="correlation-details-header">
                <h4>Detail entity</h4>
                <button type="button" class="correlation-details-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="correlation-details-content">
                <!-- Obsah detailu bude generován dynamicky -->
                <div class="details-placeholder">
                    <i class="fas fa-info-circle"></i>
                    <p>Vyberte entitu nebo vztah pro zobrazení detailů.</p>
                </div>
            </div>
        `;

        // Přidat panel do modulu
        const visualizationContainer = module.querySelector('.correlation-visualization');
        if (visualizationContainer) {
            visualizationContainer.appendChild(detailsPanel);
        } else {
            module.appendChild(detailsPanel);
        }
    }

    // Přidat event listener pro zavření panelu
    const closeButton = detailsPanel.querySelector('.correlation-details-close');
    if (closeButton) {
        closeButton.addEventListener('click', function() {
            detailsPanel.classList.remove('active');
        });
    }
}

/**
 * Zobrazení detailu entity v panelu detailů
 * @param {HTMLElement} module - Element modulu korelačního nástroje
 * @param {Object} entity - Entita k zobrazení
 */
function showEntityDetails(module, entity) {
    console.log('Zobrazení detailu entity:', entity);

    // Najít panel detailů
    const detailsPanel = module.querySelector('.correlation-details-panel');
    if (!detailsPanel) return;

    // Najít obsah panelu
    const detailsContent = detailsPanel.querySelector('.correlation-details-content');
    if (!detailsContent) return;

    // Aktualizovat nadpis
    const detailsHeader = detailsPanel.querySelector('.correlation-details-header h4');
    if (detailsHeader) {
        detailsHeader.textContent = `Detail entity: ${entity.name || 'Bez názvu'}`;
    }

    // Vytvořit obsah detailu
    let detailsHtml = `
        <div class="entity-details">
            <div class="entity-icon ${entity.type ? 'entity-' + entity.type : ''}">
                <i class="${getEntityIcon(entity.type)}"></i>
            </div>
            <div class="entity-basic-info">
                <h5>${entity.name || 'Bez názvu'}</h5>
                <div class="entity-type-badge">${getEntityTypeName(entity.type)}</div>
            </div>

            <div class="entity-properties">
                <table class="details-table">
                    <tbody>
    `;

    // Přidat vlastnosti entity
    for (const [key, value] of Object.entries(entity)) {
        // Přeskočit interní vlastnosti
        if (['id', 'name', 'type'].includes(key)) continue;

        // Formátovat hodnotu
        let formattedValue = value;
        if (typeof value === 'object' && value !== null) {
            formattedValue = JSON.stringify(value);
        } else if (key === 'createdAt' || key === 'updatedAt' || key.toLowerCase().includes('date')) {
            formattedValue = formatDate(value);
        }

        detailsHtml += `
            <tr>
                <td>${formatPropertyName(key)}</td>
                <td>${formattedValue || '-'}</td>
            </tr>
        `;
    }

    detailsHtml += `
                    </tbody>
                </table>
            </div>

            <div class="entity-relationships">
                <h5>Související entity</h5>
                <div class="related-entities-list">
    `;

    // Přidat související entity
    const relatedEntities = getRelatedEntities(entity.id);
    if (relatedEntities.length > 0) {
        relatedEntities.forEach(relation => {
            const relatedEntity = relation.entity;
            detailsHtml += `
                <div class="related-entity-item" data-entity-id="${relatedEntity.id}">
                    <div class="related-entity-icon ${relatedEntity.type ? 'entity-' + relatedEntity.type : ''}">
                        <i class="${getEntityIcon(relatedEntity.type)}"></i>
                    </div>
                    <div class="related-entity-info">
                        <div class="related-entity-name">${relatedEntity.name || 'Bez názvu'}</div>
                        <div class="relationship-type">${getRelationshipTypeName(relation.type)}</div>
                    </div>
                    <button type="button" class="btn-inline show-entity-details" data-entity-id="${relatedEntity.id}">
                        <i class="fas fa-info-circle"></i>
                    </button>
                </div>
            `;
        });
    } else {
        detailsHtml += `
            <div class="no-related-entities">
                <p>Tato entita nemá žádné související entity.</p>
            </div>
        `;
    }

    detailsHtml += `
                </div>
            </div>

            <div class="entity-actions">
                <button type="button" class="btn-inline edit-entity" data-entity-id="${entity.id}">
                    <i class="fas fa-edit"></i> Upravit
                </button>
                <button type="button" class="btn-inline delete-entity" data-entity-id="${entity.id}">
                    <i class="fas fa-trash"></i> Odstranit
                </button>
                <button type="button" class="btn-inline add-relationship" data-entity-id="${entity.id}">
                    <i class="fas fa-link"></i> Přidat vztah
                </button>
            </div>
        </div>
    `;

    // Aktualizovat obsah panelu
    detailsContent.innerHTML = detailsHtml;

    // Přidat event listenery pro tlačítka
    const editButton = detailsContent.querySelector('.edit-entity');
    if (editButton) {
        editButton.addEventListener('click', function() {
            const entityId = this.getAttribute('data-entity-id');
            showEditEntityDialog(module, entityId);
        });
    }

    const deleteButton = detailsContent.querySelector('.delete-entity');
    if (deleteButton) {
        deleteButton.addEventListener('click', function() {
            const entityId = this.getAttribute('data-entity-id');
            deleteEntity(module, entityId);
        });
    }

    const addRelationshipButton = detailsContent.querySelector('.add-relationship');
    if (addRelationshipButton) {
        addRelationshipButton.addEventListener('click', function() {
            const entityId = this.getAttribute('data-entity-id');
            showAddRelationshipDialog(module, entityId);
        });
    }

    // Přidat event listenery pro zobrazení detailů souvisejících entit
    const showEntityButtons = detailsContent.querySelectorAll('.show-entity-details');
    showEntityButtons.forEach(button => {
        button.addEventListener('click', function() {
            const entityId = this.getAttribute('data-entity-id');
            const entity = correlationData.entities.find(e => e.id === entityId);
            if (entity) {
                showEntityDetails(module, entity);
            }
        });
    });

    // Zobrazit panel
    detailsPanel.classList.add('active');
}

/**
 * Získání ikon pro typy entit
 * @param {string} entityType - Typ entity
 * @returns {string} - Třída ikony
 */
function getEntityIcon(entityType) {
    switch (entityType) {
        case 'person':
            return 'fas fa-user';
        case 'organization':
            return 'fas fa-building';
        case 'location':
            return 'fas fa-map-marker-alt';
        case 'event':
            return 'fas fa-calendar-alt';
        case 'asset':
            return 'fas fa-money-bill-wave';
        case 'digital':
            return 'fas fa-globe';
        default:
            return 'fas fa-question-circle';
    }
}

/**
 * Získání názvů typů entit
 * @param {string} entityType - Typ entity
 * @returns {string} - Název typu entity
 */
function getEntityTypeName(entityType) {
    switch (entityType) {
        case 'person':
            return 'Osoba';
        case 'organization':
            return 'Organizace';
        case 'location':
            return 'Lokace';
        case 'event':
            return 'Událost';
        case 'asset':
            return 'Majetek';
        case 'digital':
            return 'Digitální entita';
        default:
            return 'Neznámý typ';
    }
}

/**
 * Získání názvů typů vztahů
 * @param {string} relationshipType - Typ vztahu
 * @returns {string} - Název typu vztahu
 */
function getRelationshipTypeName(relationshipType) {
    switch (relationshipType) {
        case 'family':
            return 'Rodinný vztah';
        case 'business':
            return 'Obchodní vztah';
        case 'social':
            return 'Sociální vztah';
        case 'ownership':
            return 'Vlastnictví';
        case 'communication':
            return 'Komunikace';
        case 'transaction':
            return 'Transakce';
        default:
            return 'Neznámý vztah';
    }
}

/**
 * Formátování názvu vlastnosti
 * @param {string} propertyName - Název vlastnosti
 * @returns {string} - Formátovaný název vlastnosti
 */
function formatPropertyName(propertyName) {
    // Převést camelCase na slova oddělená mezerami
    const formatted = propertyName.replace(/([A-Z])/g, ' $1').toLowerCase();
    // Převést první písmeno na velké
    return formatted.charAt(0).toUpperCase() + formatted.slice(1);
}

/**
 * Formátování data
 * @param {string|Date} date - Datum k formátování
 * @returns {string} - Formátované datum
 */
function formatDate(date) {
    if (!date) return '';

    try {
        const dateObj = new Date(date);
        if (isNaN(dateObj.getTime())) return date;

        return dateObj.toLocaleDateString('cs-CZ', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        console.error('Chyba při formátování data:', error);
        return date;
    }
}

/**
 * Získání souvisejících entit
 * @param {string} entityId - ID entity
 * @returns {Array} - Pole souvisejících entit
 */
function getRelatedEntities(entityId) {
    const relatedEntities = [];

    // Najít všechny vztahy, kde je entita zdrojem nebo cílem
    correlationData.relationships.forEach(relationship => {
        if (relationship.sourceId === entityId) {
            const targetEntity = correlationData.entities.find(e => e.id === relationship.targetId);
            if (targetEntity) {
                relatedEntities.push({
                    entity: targetEntity,
                    type: relationship.type,
                    direction: 'outgoing'
                });
            }
        } else if (relationship.targetId === entityId) {
            const sourceEntity = correlationData.entities.find(e => e.id === relationship.sourceId);
            if (sourceEntity) {
                relatedEntities.push({
                    entity: sourceEntity,
                    type: relationship.type,
                    direction: 'incoming'
                });
            }
        }
    });

    return relatedEntities;
}

/**
 * Inicializace analytických nástrojů korelačního nástroje
 * @param {HTMLElement} module - Element modulu korelačního nástroje
 */
function initCorrelationAnalytics(module) {
    console.log('Inicializace analytických nástrojů korelačního nástroje');

    // Najít panel analýzy
    const analysisPanel = module.querySelector('.correlation-analysis-panel');
    if (!analysisPanel) return;

    // Přidat event listenery pro záložky
    const analysisTabs = analysisPanel.querySelectorAll('.analysis-tab');
    analysisTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Odstranit aktivní třídu ze všech záložek
            analysisTabs.forEach(t => t.classList.remove('active'));

            // Přidat aktivní třídu na kliknutou záložku
            this.classList.add('active');

            // Skrýt všechny obsahy záložek
            const tabContents = analysisPanel.querySelectorAll('.analysis-tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Zobrazit obsah vybrané záložky
            const tabId = this.getAttribute('data-tab');
            const tabContent = analysisPanel.querySelector(`#${tabId}-tab`);
            if (tabContent) {
                tabContent.classList.add('active');
            }
        });
    });

    // Přidat event listener pro tlačítko spuštění analýzy
    const runAnalysisButton = analysisPanel.querySelector('.run-analysis');
    if (runAnalysisButton) {
        runAnalysisButton.addEventListener('click', function() {
            runCorrelationAnalysis(module);
        });
    }

    // Přidat event listener pro tlačítko zobrazení/skrytí panelu
    const togglePanelButton = analysisPanel.querySelector('.toggle-analysis-panel');
    if (togglePanelButton) {
        togglePanelButton.addEventListener('click', function() {
            const analysisContent = analysisPanel.querySelector('.analysis-content');
            if (analysisContent) {
                if (analysisContent.style.display === 'none') {
                    analysisContent.style.display = 'block';
                    this.innerHTML = '<i class="fas fa-chevron-up"></i>';
                } else {
                    analysisContent.style.display = 'none';
                    this.innerHTML = '<i class="fas fa-chevron-down"></i>';
                }
            }
        });
    }
}

/**
 * Spuštění analýzy korelačního nástroje
 * @param {HTMLElement} module - Element modulu korelačního nástroje
 */
function runCorrelationAnalysis(module) {
    console.log('Spuštění analýzy korelačního nástroje');

    // Kontrola, zda máme data k analýze
    if (correlationData.entities.length === 0) {
        showNotification('Nejsou k dispozici žádná data pro analýzu. Přidejte entity a vztahy nebo importujte data z jiných modulů.', 'warning');
        return;
    }

    // Zobrazení notifikace o zahájení analýzy
    showNotification('Probíhá analýza dat...', 'info');

    // Simulace analýzy
    setTimeout(() => {
        // Analýza centrality
        analyzeCentrality(module);

        // Analýza komunit
        analyzeCommunities(module);

        // Analýza anomálií
        analyzeAnomalies(module);

        // Analýza predikcí
        analyzePredictions(module);

        // Zobrazení notifikace o dokončení analýzy
        showNotification('Analýza byla úspěšně dokončena.', 'success');
    }, 1500);
}

/**
 * Analýza centrality entit
 * @param {HTMLElement} module - Element modulu korelačního nástroje
 */
function analyzeCentrality(module) {
    console.log('Analýza centrality entit');

    // Najít záložku centrality
    const centralityTab = module.querySelector('#centrality-tab');
    if (!centralityTab) return;

    // Výpočet centrality pro každou entitu
    const centralityScores = {};

    correlationData.entities.forEach(entity => {
        // Počet vztahů, ve kterých je entita zapojena
        const relationships = correlationData.relationships.filter(
            rel => rel.sourceId === entity.id || rel.targetId === entity.id
        );

        // Výpočet skóre centrality
        centralityScores[entity.id] = {
            entity: entity,
            degree: relationships.length,
            betweenness: Math.random() * 100, // Simulace betweenness centrality
            closeness: Math.random() * 100, // Simulace closeness centrality
            eigenvector: Math.random() * 100 // Simulace eigenvector centrality
        };
    });

    // Seřazení entit podle degree centrality
    const sortedEntities = Object.values(centralityScores).sort((a, b) => b.degree - a.degree);

    // Vytvoření HTML pro výsledky analýzy
    let centralityHTML = `
        <div class="analysis-results">
            <div class="analysis-section">
                <h5>Klíčové entity podle centrality</h5>
                <div class="analysis-results-list">
    `;

    // Přidání top 5 entit
    const topEntities = sortedEntities.slice(0, 5);
    if (topEntities.length > 0) {
        topEntities.forEach(item => {
            centralityHTML += `
                <div class="analysis-result-item">
                    <div class="analysis-result-icon ${item.entity.type ? 'entity-' + item.entity.type : ''}">
                        <i class="${getEntityIcon(item.entity.type)}"></i>
                    </div>
                    <div class="analysis-result-content">
                        <div class="analysis-result-title">${item.entity.name || 'Bez názvu'}</div>
                        <div class="analysis-result-description">
                            <span class="analysis-metric">Degree: ${item.degree}</span>
                            <span class="analysis-metric">Betweenness: ${item.betweenness.toFixed(2)}</span>
                            <span class="analysis-metric">Closeness: ${item.closeness.toFixed(2)}</span>
                        </div>
                        <div class="analysis-result-actions">
                            <button type="button" class="btn-inline btn-sm show-entity-details" data-entity-id="${item.entity.id}">
                                <i class="fas fa-info-circle"></i> Detail
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
    } else {
        centralityHTML += `
            <div class="analysis-empty-result">
                <p>Nebyly nalezeny žádné entity pro analýzu centrality.</p>
            </div>
        `;
    }

    centralityHTML += `
                </div>
            </div>

            <div class="analysis-section">
                <h5>Interpretace výsledků</h5>
                <div class="analysis-interpretation">
                    <p>Entity s vysokou centralitou představují klíčové uzly v síti vztahů. Tyto entity mají významný vliv na tok informací a zdrojů v síti.</p>
                    <ul>
                        <li><strong>Degree centrality</strong> - počet přímých spojení entity</li>
                        <li><strong>Betweenness centrality</strong> - míra, do jaké entity slouží jako most mezi jinými entitami</li>
                        <li><strong>Closeness centrality</strong> - míra blízkosti entity ke všem ostatním entitám v síti</li>
                    </ul>
                </div>
            </div>
        </div>
    `;

    // Aktualizace obsahu záložky
    centralityTab.innerHTML = centralityHTML;

    // Přidat event listenery pro tlačítka zobrazení detailů
    const showEntityButtons = centralityTab.querySelectorAll('.show-entity-details');
    showEntityButtons.forEach(button => {
        button.addEventListener('click', function() {
            const entityId = this.getAttribute('data-entity-id');
            const entity = correlationData.entities.find(e => e.id === entityId);
            if (entity) {
                showEntityDetails(module, entity);
            }
        });
    });
}

/**
 * Inicializace fotogalerie korelačního nástroje
 * @param {HTMLElement} module - Element modulu korelačního nástroje
 */
function initCorrelationPhotoGallery(module) {
    console.log('Inicializace fotogalerie korelačního nástroje');

    try {
        // Najít kontejner pro fotogalerii
        const galleryContainer = module.querySelector('.correlation-gallery-container');
        if (!galleryContainer) {
            console.log('Nenalezen kontejner pro fotogalerii');
            return;
        }

        // Najít galerii
        const photoGallery = galleryContainer.querySelector('.photo-gallery');
        if (!photoGallery) {
            console.log('Nenalezena fotogalerie');
            return;
        }

        // Najít tlačítko pro přidání fotografie
        const addButton = photoGallery.querySelector('.photo-gallery-add');
        if (!addButton) {
            console.log('Nenalezeno tlačítko pro přidání fotografie');
            return;
        }

        // Inicializovat galerii
        if (typeof initPhotoGallery === 'function') {
            try {
                initPhotoGallery(galleryContainer, photoGallery, addButton);
                console.log('Fotogalerie korelačního nástroje byla úspěšně inicializována');
            } catch (error) {
                console.error('Chyba při inicializaci fotogalerie:', error);

                // Přidat vlastní event listener pro případ chyby
                addButton.addEventListener('click', function() {
                    alert('Došlo k chybě při inicializaci fotogalerie. Zkuste stránku obnovit.');
                });
            }
        } else {
            console.log('Funkce initPhotoGallery není dostupná, používám vlastní implementaci');

            // Pokud není dostupná funkce initPhotoGallery, přidáme vlastní event listener
            addButton.addEventListener('click', function() {
                // Vytvoření jednoduchého dialogu pro nahrání fotografie
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = 'image/*';
                input.multiple = true;

                input.addEventListener('change', function(event) {
                    const files = event.target.files;
                    if (files && files.length > 0) {
                        for (let i = 0; i < files.length; i++) {
                            const file = files[i];
                            if (file.type.startsWith('image/')) {
                                // Vytvoření náhledu
                                const reader = new FileReader();
                                reader.onload = function(e) {
                                    const photoItem = document.createElement('div');
                                    photoItem.className = 'photo-item';
                                    photoItem.innerHTML = `
                                        <div class="photo-preview">
                                            <img src="${e.target.result}" alt="${file.name}">
                                        </div>
                                        <div class="photo-info">
                                            <div class="photo-name">${file.name}</div>
                                            <div class="photo-size">${formatFileSize(file.size)}</div>
                                        </div>
                                        <div class="photo-actions">
                                            <button type="button" class="photo-action-btn delete-photo">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    `;

                                    // Přidat náhled do galerie
                                    const photoGrid = photoGallery.querySelector('.photo-gallery-grid');
                                    if (photoGrid) {
                                        photoGrid.appendChild(photoItem);

                                        // Skrýt prázdný stav
                                        const emptyState = photoGallery.querySelector('.photo-gallery-empty');
                                        if (emptyState) {
                                            emptyState.style.display = 'none';
                                        }

                                        // Přidat event listener pro smazání
                                        const deleteButton = photoItem.querySelector('.delete-photo');
                                        if (deleteButton) {
                                            deleteButton.addEventListener('click', function() {
                                                photoItem.remove();

                                                // Pokud není žádná fotografie, zobrazit prázdný stav
                                                if (photoGrid.children.length === 0 && emptyState) {
                                                    emptyState.style.display = 'block';
                                                }
                                            });
                                        }
                                    }
                                };
                                reader.readAsDataURL(file);
                            }
                        }
                    }
                });

                // Simulace kliknutí na input
                input.click();
            });

            // Přidat podporu pro drag & drop
            photoGallery.addEventListener('dragover', function(event) {
                event.preventDefault();
                event.stopPropagation();
                photoGallery.classList.add('drag-over');
            });

            photoGallery.addEventListener('dragleave', function(event) {
                event.preventDefault();
                event.stopPropagation();
                photoGallery.classList.remove('drag-over');
            });

            photoGallery.addEventListener('drop', function(event) {
                event.preventDefault();
                event.stopPropagation();
                photoGallery.classList.remove('drag-over');

                const files = event.dataTransfer.files;
                if (files && files.length > 0) {
                    for (let i = 0; i < files.length; i++) {
                        const file = files[i];
                        if (file.type.startsWith('image/')) {
                            // Vytvoření náhledu
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                const photoItem = document.createElement('div');
                                photoItem.className = 'photo-item';
                                photoItem.innerHTML = `
                                    <div class="photo-preview">
                                        <img src="${e.target.result}" alt="${file.name}">
                                    </div>
                                    <div class="photo-info">
                                        <div class="photo-name">${file.name}</div>
                                        <div class="photo-size">${formatFileSize(file.size)}</div>
                                    </div>
                                    <div class="photo-actions">
                                        <button type="button" class="photo-action-btn delete-photo">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                `;

                                // Přidat náhled do galerie
                                const photoGrid = photoGallery.querySelector('.photo-gallery-grid');
                                if (photoGrid) {
                                    photoGrid.appendChild(photoItem);

                                    // Skrýt prázdný stav
                                    const emptyState = photoGallery.querySelector('.photo-gallery-empty');
                                    if (emptyState) {
                                        emptyState.style.display = 'none';
                                    }

                                    // Přidat event listener pro smazání
                                    const deleteButton = photoItem.querySelector('.delete-photo');
                                    if (deleteButton) {
                                        deleteButton.addEventListener('click', function() {
                                            photoItem.remove();

                                            // Pokud není žádná fotografie, zobrazit prázdný stav
                                            if (photoGrid.children.length === 0 && emptyState) {
                                                emptyState.style.display = 'block';
                                            }
                                        });
                                    }
                                }
                            };
                            reader.readAsDataURL(file);
                        }
                    }
                }
            });
        }
    } catch (error) {
        console.error('Chyba při inicializaci fotogalerie korelačního nástroje:', error);
    }
}

/**
 * Formátování velikosti souboru
 * @param {number} bytes - Velikost souboru v bajtech
 * @returns {string} - Formátovaná velikost souboru
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Inicializace notifikací korelačního nástroje
 * @param {HTMLElement} module - Element modulu korelačního nástroje
 */
function initCorrelationNotifications(module) {
    console.log('Inicializace notifikací korelačního nástroje');

    // Najít kontejner pro notifikace
    const notificationsContainer = module.querySelector('.correlation-notifications');
    if (!notificationsContainer) return;

    // Najít formulář pro nastavení notifikací
    const emailInput = notificationsContainer.querySelector('#correlation-alert-email');
    const frequencySelect = notificationsContainer.querySelector('#correlation-alert-frequency');
    const saveButton = notificationsContainer.querySelector('.save-notification-settings');
    const testButton = notificationsContainer.querySelector('.test-email-alert');

    // Načíst uložené nastavení
    const savedSettings = localStorage.getItem('correlationNotificationSettings');
    if (savedSettings) {
        try {
            const settings = JSON.parse(savedSettings);
            if (emailInput && settings.email) emailInput.value = settings.email;
            if (frequencySelect && settings.frequency) frequencySelect.value = settings.frequency;
        } catch (error) {
            console.error('Chyba při načítání nastavení notifikací:', error);
        }
    }

    // Přidat event listener pro tlačítko uložení
    if (saveButton) {
        saveButton.addEventListener('click', function() {
            saveCorrelationNotificationSettings(module);
        });
    }

    // Přidat event listener pro tlačítko testu
    if (testButton) {
        testButton.addEventListener('click', function() {
            testCorrelationEmailAlert(module);
        });
    }
}

/**
 * Uložení nastavení notifikací korelačního nástroje
 * @param {HTMLElement} module - Element modulu korelačního nástroje
 */
function saveCorrelationNotificationSettings(module) {
    console.log('Ukládání nastavení notifikací korelačního nástroje');

    // Najít kontejner pro notifikace
    const notificationsContainer = module.querySelector('.correlation-notifications');
    if (!notificationsContainer) return;

    // Najít formulář pro nastavení notifikací
    const emailInput = notificationsContainer.querySelector('#correlation-alert-email');
    const frequencySelect = notificationsContainer.querySelector('#correlation-alert-frequency');

    // Validace e-mailu
    if (emailInput && !validateEmail(emailInput.value)) {
        showNotification('Zadejte platnou e-mailovou adresu.', 'error');
        return;
    }

    // Uložit nastavení
    const settings = {
        email: emailInput ? emailInput.value : '',
        frequency: frequencySelect ? frequencySelect.value : 'daily'
    };

    localStorage.setItem('correlationNotificationSettings', JSON.stringify(settings));

    // Zobrazit notifikaci
    showNotification('Nastavení notifikací bylo úspěšně uloženo.', 'success');
}

/**
 * Test e-mailové notifikace korelačního nástroje
 * @param {HTMLElement} module - Element modulu korelačního nástroje
 */
function testCorrelationEmailAlert(module) {
    console.log('Test e-mailové notifikace korelačního nástroje');

    // Najít kontejner pro notifikace
    const notificationsContainer = module.querySelector('.correlation-notifications');
    if (!notificationsContainer) return;

    // Najít formulář pro nastavení notifikací
    const emailInput = notificationsContainer.querySelector('#correlation-alert-email');

    // Validace e-mailu
    if (emailInput && !validateEmail(emailInput.value)) {
        showNotification('Zadejte platnou e-mailovou adresu pro test notifikace.', 'error');
        return;
    }

    // Simulace odeslání e-mailu
    showNotification(`Testovací e-mail byl odeslán na adresu ${emailInput ? emailInput.value : 'neznámá adresa'}.`, 'success');
}

/**
 * Validace e-mailové adresy
 * @param {string} email - E-mailová adresa k validaci
 * @returns {boolean} - True, pokud je e-mail platný, jinak false
 */
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(String(email).toLowerCase());
}

/**
 * Zobrazení notifikace
 * @param {string} message - Zpráva notifikace
 * @param {string} type - Typ notifikace (success, error, warning, info)
 */
function showNotification(message, type = 'info') {
    console.log(`Zobrazení notifikace: ${message} ${type}`);

    try {
        // Kontrola, zda existuje funkce pro zobrazení notifikace
        if (typeof showToast === 'function') {
            showToast(message, type);
            return;
        }

        // Vytvoření vlastní notifikace, pokud není dostupná funkce showToast
        const notification = document.createElement('div');
        notification.className = `correlation-notification ${type}`;
        notification.style.position = 'fixed';
        notification.style.bottom = '20px';
        notification.style.right = '20px';
        notification.style.backgroundColor = getNotificationColor(type);
        notification.style.color = '#fff';
        notification.style.padding = '15px';
        notification.style.borderRadius = '5px';
        notification.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.2)';
        notification.style.zIndex = '9999';
        notification.style.display = 'flex';
        notification.style.alignItems = 'center';
        notification.style.minWidth = '300px';
        notification.style.maxWidth = '400px';

        notification.innerHTML = `
            <div class="notification-icon" style="margin-right: 10px;">
                <i class="fas ${getNotificationIcon(type)}"></i>
            </div>
            <div class="notification-content" style="flex: 1;">
                <div class="notification-message">${message}</div>
            </div>
            <button type="button" class="notification-close" style="background: none; border: none; color: #fff; cursor: pointer; margin-left: 10px;">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Přidat notifikaci do dokumentu
        document.body.appendChild(notification);

        // Přidat event listener pro zavření notifikace
        const closeButton = notification.querySelector('.notification-close');
        if (closeButton) {
            closeButton.addEventListener('click', function() {
                notification.remove();
            });
        }

        // Automatické zavření notifikace po 5 sekundách
        setTimeout(() => {
            if (document.body.contains(notification)) {
                notification.remove();
            }
        }, 5000);
    } catch (error) {
        console.error('Chyba při zobrazení notifikace:', error);

        // Záložní řešení - použití alert
        if (type === 'error') {
            alert(`Chyba: ${message}`);
        } else if (type === 'warning') {
            alert(`Upozornění: ${message}`);
        } else if (type === 'success') {
            alert(`Úspěch: ${message}`);
        } else {
            alert(`Informace: ${message}`);
        }
    }
}

/**
 * Získání barvy pro typ notifikace
 * @param {string} type - Typ notifikace
 * @returns {string} - Barva notifikace
 */
function getNotificationColor(type) {
    switch (type) {
        case 'success':
            return '#27ae60';
        case 'error':
            return '#e74c3c';
        case 'warning':
            return '#f39c12';
        case 'info':
        default:
            return '#3498db';
    }
}

/**
 * Funkce pro zobrazení dialogu pro přidání entity
 * @param {HTMLElement} module - Modul korelačního nástroje
 */
function showAddEntityDialog(module) {
    console.log('Zobrazení dialogu pro přidání entity');

    // Vytvoření dialogu
    const dialog = document.createElement('div');
    dialog.className = 'correlation-dialog';
    dialog.innerHTML = `
        <div class="correlation-dialog-content">
            <div class="correlation-dialog-header">
                <h4>Přidat entitu</h4>
                <button type="button" class="correlation-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="correlation-dialog-body">
                <form id="add-entity-form">
                    <div class="form-group">
                        <label for="entity-name">Název entity</label>
                        <input type="text" id="entity-name" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="entity-type">Typ entity</label>
                        <select id="entity-type" class="form-control" required>
                            <option value="">-- Vyberte typ entity --</option>
                            <option value="person">Osoba</option>
                            <option value="organization">Organizace</option>
                            <option value="location">Lokace</option>
                            <option value="event">Událost</option>
                            <option value="asset">Majetek</option>
                            <option value="digital">Digitální entita</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="entity-description">Popis entity</label>
                        <textarea id="entity-description" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="entity-attributes">Atributy entity (ve formátu klíč:hodnota, jeden na řádek)</label>
                        <textarea id="entity-attributes" class="form-control" rows="3" placeholder="např. datum_narození:1.1.1990"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="entity-confidence">Úroveň důvěryhodnosti</label>
                        <select id="entity-confidence" class="form-control">
                            <option value="high">Vysoká</option>
                            <option value="medium" selected>Střední</option>
                            <option value="low">Nízká</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="correlation-dialog-footer">
                <button type="button" class="btn-secondary cancel-entity">Zrušit</button>
                <button type="button" class="btn-primary save-entity">Uložit</button>
            </div>
        </div>
    `;

    // Přidání dialogu do dokumentu
    document.body.appendChild(dialog);

    // Získání reference na formulář a tlačítka
    const form = dialog.querySelector('#add-entity-form');
    const cancelButton = dialog.querySelector('.cancel-entity');
    const saveButton = dialog.querySelector('.save-entity');
    const closeButton = dialog.querySelector('.correlation-dialog-close');

    // Přidání event listenerů
    cancelButton.addEventListener('click', function() {
        dialog.remove();
    });

    closeButton.addEventListener('click', function() {
        dialog.remove();
    });

    saveButton.addEventListener('click', function() {
        // Validace formuláře
        const nameInput = form.querySelector('#entity-name');
        const typeInput = form.querySelector('#entity-type');

        if (!nameInput.value.trim()) {
            showNotification('Zadejte název entity.', 'error');
            nameInput.focus();
            return;
        }

        if (!typeInput.value) {
            showNotification('Vyberte typ entity.', 'error');
            typeInput.focus();
            return;
        }

        // Získání hodnot z formuláře
        const name = nameInput.value.trim();
        const type = typeInput.value;
        const description = form.querySelector('#entity-description').value.trim();
        const attributesText = form.querySelector('#entity-attributes').value.trim();
        const confidence = form.querySelector('#entity-confidence').value;

        // Zpracování atributů
        const attributes = {};
        if (attributesText) {
            attributesText.split('\n').forEach(line => {
                const [key, value] = line.split(':');
                if (key && value) {
                    attributes[key.trim()] = value.trim();
                }
            });
        }

        // Vytvoření entity
        const entity = {
            id: 'entity_' + Date.now(),
            name: name,
            type: type,
            description: description,
            attributes: attributes,
            confidence: confidence,
            created: new Date().toISOString(),
            updated: new Date().toISOString()
        };

        // Přidání entity do seznamu entit
        addEntityToCorrelationData(module, entity);

        // Zavření dialogu
        dialog.remove();

        // Zobrazení notifikace
        showNotification(`Entita "${name}" byla úspěšně přidána.`, 'success');

        // Aktualizace vizualizace
        updateCorrelationVisualization(module);
    });
}

/**
 * Funkce pro zobrazení dialogu pro přidání vztahu
 * @param {HTMLElement} module - Modul korelačního nástroje
 */
function showAddRelationshipDialog(module) {
    console.log('Zobrazení dialogu pro přidání vztahu');

    // Kontrola, zda existují entity pro vytvoření vztahu
    if (!correlationData.entities || correlationData.entities.length < 2) {
        showNotification('Pro vytvoření vztahu potřebujete alespoň dvě entity. Nejprve přidejte entity.', 'warning');
        return;
    }

    // Vytvoření dialogu
    const dialog = document.createElement('div');
    dialog.className = 'correlation-dialog';
    dialog.innerHTML = `
        <div class="correlation-dialog-content">
            <div class="correlation-dialog-header">
                <h4>Přidat vztah</h4>
                <button type="button" class="correlation-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="correlation-dialog-body">
                <form id="add-relationship-form">
                    <div class="form-group">
                        <label for="source-entity">Zdrojová entita</label>
                        <select id="source-entity" class="form-control" required>
                            <option value="">-- Vyberte zdrojovou entitu --</option>
                            ${correlationData.entities.map(entity => `
                                <option value="${entity.id}">${entity.name} (${getEntityTypeName(entity.type)})</option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="relationship-type">Typ vztahu</label>
                        <select id="relationship-type" class="form-control" required>
                            <option value="">-- Vyberte typ vztahu --</option>
                            <option value="family">Rodinný</option>
                            <option value="business">Obchodní</option>
                            <option value="social">Sociální</option>
                            <option value="ownership">Vlastnictví</option>
                            <option value="communication">Komunikace</option>
                            <option value="transaction">Transakce</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="target-entity">Cílová entita</label>
                        <select id="target-entity" class="form-control" required>
                            <option value="">-- Vyberte cílovou entitu --</option>
                            ${correlationData.entities.map(entity => `
                                <option value="${entity.id}">${entity.name} (${getEntityTypeName(entity.type)})</option>
                            `).join('')}
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="relationship-description">Popis vztahu</label>
                        <textarea id="relationship-description" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="relationship-attributes">Atributy vztahu (ve formátu klíč:hodnota, jeden na řádek)</label>
                        <textarea id="relationship-attributes" class="form-control" rows="3" placeholder="např. datum_vzniku:1.1.2020"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="relationship-confidence">Úroveň důvěryhodnosti</label>
                        <select id="relationship-confidence" class="form-control">
                            <option value="high">Vysoká</option>
                            <option value="medium" selected>Střední</option>
                            <option value="low">Nízká</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="correlation-dialog-footer">
                <button type="button" class="btn-secondary cancel-relationship">Zrušit</button>
                <button type="button" class="btn-primary save-relationship">Uložit</button>
            </div>
        </div>
    `;

    // Přidání dialogu do dokumentu
    document.body.appendChild(dialog);

    // Získání reference na formulář a tlačítka
    const form = dialog.querySelector('#add-relationship-form');
    const cancelButton = dialog.querySelector('.cancel-relationship');
    const saveButton = dialog.querySelector('.save-relationship');
    const closeButton = dialog.querySelector('.correlation-dialog-close');

    // Přidání event listenerů
    cancelButton.addEventListener('click', function() {
        dialog.remove();
    });

    closeButton.addEventListener('click', function() {
        dialog.remove();
    });

    saveButton.addEventListener('click', function() {
        // Validace formuláře
        const sourceEntityInput = form.querySelector('#source-entity');
        const targetEntityInput = form.querySelector('#target-entity');
        const relationshipTypeInput = form.querySelector('#relationship-type');

        if (!sourceEntityInput.value) {
            showNotification('Vyberte zdrojovou entitu.', 'error');
            sourceEntityInput.focus();
            return;
        }

        if (!relationshipTypeInput.value) {
            showNotification('Vyberte typ vztahu.', 'error');
            relationshipTypeInput.focus();
            return;
        }

        if (!targetEntityInput.value) {
            showNotification('Vyberte cílovou entitu.', 'error');
            targetEntityInput.focus();
            return;
        }

        if (sourceEntityInput.value === targetEntityInput.value) {
            showNotification('Zdrojová a cílová entita nemohou být stejné.', 'error');
            targetEntityInput.focus();
            return;
        }

        // Získání hodnot z formuláře
        const sourceId = sourceEntityInput.value;
        const targetId = targetEntityInput.value;
        const type = relationshipTypeInput.value;
        const description = form.querySelector('#relationship-description').value.trim();
        const attributesText = form.querySelector('#relationship-attributes').value.trim();
        const confidence = form.querySelector('#relationship-confidence').value;

        // Zpracování atributů
        const attributes = {};
        if (attributesText) {
            attributesText.split('\n').forEach(line => {
                const [key, value] = line.split(':');
                if (key && value) {
                    attributes[key.trim()] = value.trim();
                }
            });
        }

        // Vytvoření vztahu
        const relationship = {
            id: 'relationship_' + Date.now(),
            sourceId: sourceId,
            targetId: targetId,
            type: type,
            description: description,
            attributes: attributes,
            confidence: confidence,
            created: new Date().toISOString(),
            updated: new Date().toISOString()
        };

        // Přidání vztahu do seznamu vztahů
        addRelationshipToCorrelationData(module, relationship);

        // Zavření dialogu
        dialog.remove();

        // Zobrazení notifikace
        showNotification(`Vztah byl úspěšně přidán.`, 'success');

        // Aktualizace vizualizace
        updateCorrelationVisualization(module);
    });
}

/**
 * Získání názvu typu entity
 * @param {string} type - Typ entity
 * @returns {string} - Název typu entity
 */
function getEntityTypeName(type) {
    switch (type) {
        case 'person':
            return 'Osoba';
        case 'organization':
            return 'Organizace';
        case 'location':
            return 'Lokace';
        case 'event':
            return 'Událost';
        case 'asset':
            return 'Majetek';
        case 'digital':
            return 'Digitální entita';
        default:
            return type;
    }
}

/**
 * Funkce pro zobrazení dialogu pro import dat
 * @param {HTMLElement} module - Modul korelačního nástroje
 */
function showImportDataDialog(module) {
    console.log('Zobrazení dialogu pro import dat');

    // Vytvoření dialogu
    const dialog = document.createElement('div');
    dialog.className = 'correlation-dialog';
    dialog.innerHTML = `
        <div class="correlation-dialog-content">
            <div class="correlation-dialog-header">
                <h4>Importovat data</h4>
                <button type="button" class="correlation-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="correlation-dialog-body">
                <div class="import-options">
                    <h5>Vyberte zdroj dat</h5>
                    <div class="import-option">
                        <input type="radio" id="import-from-modules" name="import-source" value="modules" checked>
                        <label for="import-from-modules">Importovat z ostatních modulů</label>
                    </div>
                    <div class="import-option">
                        <input type="radio" id="import-from-file" name="import-source" value="file">
                        <label for="import-from-file">Importovat ze souboru</label>
                    </div>
                    <div class="import-option">
                        <input type="radio" id="import-from-text" name="import-source" value="text">
                        <label for="import-from-text">Importovat z textu</label>
                    </div>
                </div>

                <div id="import-modules-options" class="import-source-options">
                    <h5>Vyberte moduly pro import</h5>
                    <div class="form-group">
                        <div class="checkbox-group">
                            <div class="checkbox-item">
                                <input type="checkbox" id="import-people" name="import-modules" value="people" checked>
                                <label for="import-people">Evidence obyvatel</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="import-companies" name="import-modules" value="companies" checked>
                                <label for="import-companies">Firmy</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="import-locations" name="import-modules" value="locations" checked>
                                <label for="import-locations">Lokace</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="import-timeline" name="import-modules" value="timeline" checked>
                                <label for="import-timeline">Časová osa</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="import-financial" name="import-modules" value="financial" checked>
                                <label for="import-financial">Finanční monitoring</label>
                            </div>
                            <div class="checkbox-item">
                                <input type="checkbox" id="import-messaging" name="import-modules" value="messaging" checked>
                                <label for="import-messaging">Komunikační platformy</label>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="import-file-options" class="import-source-options" style="display: none;">
                    <h5>Vyberte soubor pro import</h5>
                    <div class="form-group">
                        <input type="file" id="import-file" class="form-control" accept=".json,.csv">
                        <small class="form-text text-muted">Podporované formáty: JSON, CSV</small>
                    </div>
                </div>

                <div id="import-text-options" class="import-source-options" style="display: none;">
                    <h5>Vložte data pro import</h5>
                    <div class="form-group">
                        <textarea id="import-text" class="form-control" rows="10" placeholder="Vložte data ve formátu JSON nebo CSV"></textarea>
                        <small class="form-text text-muted">Podporované formáty: JSON, CSV</small>
                    </div>
                </div>
            </div>
            <div class="correlation-dialog-footer">
                <button type="button" class="btn-secondary cancel-import">Zrušit</button>
                <button type="button" class="btn-primary start-import">Importovat</button>
            </div>
        </div>
    `;

    // Přidání dialogu do dokumentu
    document.body.appendChild(dialog);

    // Získání reference na formulář a tlačítka
    const cancelButton = dialog.querySelector('.cancel-import');
    const importButton = dialog.querySelector('.start-import');
    const closeButton = dialog.querySelector('.correlation-dialog-close');

    // Přepínání mezi zdroji importu
    const importSourceRadios = dialog.querySelectorAll('input[name="import-source"]');
    const importModulesOptions = dialog.querySelector('#import-modules-options');
    const importFileOptions = dialog.querySelector('#import-file-options');
    const importTextOptions = dialog.querySelector('#import-text-options');

    importSourceRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            importModulesOptions.style.display = 'none';
            importFileOptions.style.display = 'none';
            importTextOptions.style.display = 'none';

            switch (this.value) {
                case 'modules':
                    importModulesOptions.style.display = 'block';
                    break;
                case 'file':
                    importFileOptions.style.display = 'block';
                    break;
                case 'text':
                    importTextOptions.style.display = 'block';
                    break;
            }
        });
    });

    // Přidání event listenerů
    cancelButton.addEventListener('click', function() {
        dialog.remove();
    });

    closeButton.addEventListener('click', function() {
        dialog.remove();
    });

    importButton.addEventListener('click', function() {
        // Získání vybraného zdroje importu
        const selectedSource = dialog.querySelector('input[name="import-source"]:checked').value;

        switch (selectedSource) {
            case 'modules':
                // Získání vybraných modulů
                const selectedModules = Array.from(dialog.querySelectorAll('input[name="import-modules"]:checked')).map(checkbox => checkbox.value);

                // Import dat z vybraných modulů
                importDataFromModules(module, selectedModules);
                break;

            case 'file':
                // Získání vybraného souboru
                const fileInput = dialog.querySelector('#import-file');
                if (!fileInput.files || fileInput.files.length === 0) {
                    showNotification('Vyberte soubor pro import.', 'error');
                    return;
                }

                // Import dat ze souboru
                importDataFromFile(module, fileInput.files[0]);
                break;

            case 'text':
                // Získání vloženého textu
                const textInput = dialog.querySelector('#import-text');
                if (!textInput.value.trim()) {
                    showNotification('Vložte data pro import.', 'error');
                    return;
                }

                // Import dat z textu
                importDataFromText(module, textInput.value.trim());
                break;
        }

        // Zavření dialogu
        dialog.remove();
    });
}

/**
 * Import dat z vybraných modulů
 * @param {HTMLElement} module - Modul korelačního nástroje
 * @param {Array} selectedModules - Vybrané moduly pro import
 */
function importDataFromModules(module, selectedModules) {
    console.log('Import dat z vybraných modulů:', selectedModules);

    // Zobrazení notifikace o zahájení importu
    showNotification('Probíhá import dat z vybraných modulů...', 'info');

    // Import dat z vybraných modulů
    let importedEntities = 0;
    let importedRelationships = 0;

    // Evidence obyvatel
    if (selectedModules.includes('people')) {
        try {
            importDataFromPeopleModule();
            importedEntities += correlationData.entities.filter(e => e.source === 'people-module').length;
            importedRelationships += correlationData.relationships.filter(r => r.source === 'people-module').length;
        } catch (error) {
            console.error('Chyba při importu dat z evidence obyvatel:', error);
        }
    }

    // Firmy
    if (selectedModules.includes('companies')) {
        try {
            importDataFromCompaniesModule();
            importedEntities += correlationData.entities.filter(e => e.source === 'companies-module').length;
            importedRelationships += correlationData.relationships.filter(r => r.source === 'companies-module').length;
        } catch (error) {
            console.error('Chyba při importu dat z firem:', error);
        }
    }

    // Lokace
    if (selectedModules.includes('locations')) {
        try {
            importDataFromLocationsModule();
            importedEntities += correlationData.entities.filter(e => e.source === 'locations-module').length;
            importedRelationships += correlationData.relationships.filter(r => r.source === 'locations-module').length;
        } catch (error) {
            console.error('Chyba při importu dat z lokací:', error);
        }
    }

    // Časová osa
    if (selectedModules.includes('timeline')) {
        try {
            importDataFromTimelineModule();
            importedEntities += correlationData.entities.filter(e => e.source === 'timeline-module').length;
            importedRelationships += correlationData.relationships.filter(r => r.source === 'timeline-module').length;
        } catch (error) {
            console.error('Chyba při importu dat z časové osy:', error);
        }
    }

    // Finanční monitoring
    if (selectedModules.includes('financial')) {
        try {
            importDataFromFinancialModule();
            importedEntities += correlationData.entities.filter(e => e.source === 'financial-module').length;
            importedRelationships += correlationData.relationships.filter(r => r.source === 'financial-module').length;
        } catch (error) {
            console.error('Chyba při importu dat z finančního monitoringu:', error);
        }
    }

    // Komunikační platformy
    if (selectedModules.includes('messaging')) {
        try {
            importDataFromMessagingModule();
            importedEntities += correlationData.entities.filter(e => e.source === 'messaging-module').length;
            importedRelationships += correlationData.relationships.filter(r => r.source === 'messaging-module').length;
        } catch (error) {
            console.error('Chyba při importu dat z komunikačních platforem:', error);
        }
    }

    // Uložení dat
    saveCorrelationData();

    // Aktualizace vizualizace
    updateCorrelationVisualization(module);

    // Zobrazení notifikace o dokončení importu
    showNotification(`Import dat byl dokončen. Importováno ${importedEntities} entit a ${importedRelationships} vztahů.`, 'success');
}

/**
 * Import dat z modulu evidence obyvatel
 */
function importDataFromPeopleModule() {
    console.log('Import dat z modulu evidence obyvatel');

    // Najít všechny moduly evidence obyvatel
    const peopleModules = document.querySelectorAll('.module[id^="module-evidence-obyvatel"]');
    if (peopleModules.length === 0) {
        console.log('Nenalezeny žádné moduly evidence obyvatel');
        return;
    }

    // Procházení všech modulů evidence obyvatel
    peopleModules.forEach(module => {
        // Najít všechny osoby v modulu
        const personElements = module.querySelectorAll('.person-card');

        personElements.forEach(personElement => {
            // Získání dat o osobě
            const name = personElement.querySelector('.person-name')?.textContent || '';
            const birthDate = personElement.querySelector('.person-birth-date')?.textContent || '';
            const address = personElement.querySelector('.person-address')?.textContent || '';
            const idNumber = personElement.querySelector('.person-id-number')?.textContent || '';

            // Vytvoření entity osoby
            const personEntity = {
                id: 'entity_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                name: name,
                type: 'person',
                birthDate: birthDate,
                address: address,
                idNumber: idNumber,
                source: 'people-module',
                created: new Date().toISOString(),
                updated: new Date().toISOString()
            };

            // Přidání entity do dat
            addEntityIfNotExists(personEntity);

            // Najít související osoby (např. rodinné vztahy)
            const relatedPersonElements = personElement.querySelectorAll('.related-person');
            relatedPersonElements.forEach(relatedPersonElement => {
                const relatedName = relatedPersonElement.querySelector('.related-person-name')?.textContent || '';
                const relationType = relatedPersonElement.querySelector('.relation-type')?.textContent || '';

                if (relatedName && relationType) {
                    // Vytvoření entity související osoby
                    const relatedPersonEntity = {
                        id: 'entity_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                        name: relatedName,
                        type: 'person',
                        source: 'people-module',
                        created: new Date().toISOString(),
                        updated: new Date().toISOString()
                    };

                    // Přidání entity do dat
                    addEntityIfNotExists(relatedPersonEntity);

                    // Vytvoření vztahu mezi osobami
                    const relationship = {
                        id: 'relationship_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                        sourceId: personEntity.id,
                        targetId: relatedPersonEntity.id,
                        type: mapRelationshipType(relationType),
                        description: relationType,
                        source: 'people-module',
                        created: new Date().toISOString(),
                        updated: new Date().toISOString()
                    };

                    // Přidání vztahu do dat
                    addRelationshipIfNotExists(relationship);
                }
            });
        });
    });
}

/**
 * Import dat z modulu firem
 */
function importDataFromCompaniesModule() {
    console.log('Import dat z modulu firem');

    // Najít všechny moduly firem
    const companyModules = document.querySelectorAll('.module[id^="module-firmy"]');
    if (companyModules.length === 0) {
        console.log('Nenalezeny žádné moduly firem');
        return;
    }

    // Procházení všech modulů firem
    companyModules.forEach(module => {
        // Najít všechny firmy v modulu
        const companyElements = module.querySelectorAll('.company-card');

        companyElements.forEach(companyElement => {
            // Získání dat o firmě
            const name = companyElement.querySelector('.company-name')?.textContent || '';
            const id = companyElement.querySelector('.company-id')?.textContent || '';
            const address = companyElement.querySelector('.company-address')?.textContent || '';
            const industry = companyElement.querySelector('.company-industry')?.textContent || '';

            // Vytvoření entity firmy
            const companyEntity = {
                id: 'entity_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                name: name,
                type: 'organization',
                companyId: id,
                address: address,
                industry: industry,
                source: 'companies-module',
                created: new Date().toISOString(),
                updated: new Date().toISOString()
            };

            // Přidání entity do dat
            addEntityIfNotExists(companyEntity);

            // Najít osoby spojené s firmou
            const personElements = companyElement.querySelectorAll('.company-person');
            personElements.forEach(personElement => {
                const personName = personElement.querySelector('.person-name')?.textContent || '';
                const personRole = personElement.querySelector('.person-role')?.textContent || '';

                if (personName) {
                    // Vytvoření entity osoby
                    const personEntity = {
                        id: 'entity_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                        name: personName,
                        type: 'person',
                        source: 'companies-module',
                        created: new Date().toISOString(),
                        updated: new Date().toISOString()
                    };

                    // Přidání entity do dat
                    addEntityIfNotExists(personEntity);

                    // Vytvoření vztahu mezi osobou a firmou
                    const relationship = {
                        id: 'relationship_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                        sourceId: personEntity.id,
                        targetId: companyEntity.id,
                        type: 'business',
                        description: personRole || 'Zaměstnanec',
                        source: 'companies-module',
                        created: new Date().toISOString(),
                        updated: new Date().toISOString()
                    };

                    // Přidání vztahu do dat
                    addRelationshipIfNotExists(relationship);
                }
            });
        });
    });
}

/**
 * Import dat z modulu lokací
 */
function importDataFromLocationsModule() {
    console.log('Import dat z modulu lokací');

    // Najít všechny moduly lokací
    const locationModules = document.querySelectorAll('.module[id^="module-lokace"]');
    if (locationModules.length === 0) {
        console.log('Nenalezeny žádné moduly lokací');
        return;
    }

    // Procházení všech modulů lokací
    locationModules.forEach(module => {
        // Najít všechny lokace v modulu
        const locationElements = module.querySelectorAll('.location-card');

        locationElements.forEach(locationElement => {
            // Získání dat o lokaci
            const name = locationElement.querySelector('.location-name')?.textContent || '';
            const address = locationElement.querySelector('.location-address')?.textContent || '';
            const coordinates = locationElement.querySelector('.location-coordinates')?.textContent || '';
            const type = locationElement.querySelector('.location-type')?.textContent || '';

            // Vytvoření entity lokace
            const locationEntity = {
                id: 'entity_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                name: name,
                type: 'location',
                address: address,
                coordinates: coordinates,
                locationType: type,
                source: 'locations-module',
                created: new Date().toISOString(),
                updated: new Date().toISOString()
            };

            // Přidání entity do dat
            addEntityIfNotExists(locationEntity);

            // Najít osoby spojené s lokací
            const personElements = locationElement.querySelectorAll('.location-person');
            personElements.forEach(personElement => {
                const personName = personElement.querySelector('.person-name')?.textContent || '';
                const personRole = personElement.querySelector('.person-role')?.textContent || '';

                if (personName) {
                    // Vytvoření entity osoby
                    const personEntity = {
                        id: 'entity_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                        name: personName,
                        type: 'person',
                        source: 'locations-module',
                        created: new Date().toISOString(),
                        updated: new Date().toISOString()
                    };

                    // Přidání entity do dat
                    addEntityIfNotExists(personEntity);

                    // Vytvoření vztahu mezi osobou a lokací
                    const relationship = {
                        id: 'relationship_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                        sourceId: personEntity.id,
                        targetId: locationEntity.id,
                        type: personRole?.toLowerCase().includes('vlastn') ? 'ownership' : 'social',
                        description: personRole || 'Spojeno s lokací',
                        source: 'locations-module',
                        created: new Date().toISOString(),
                        updated: new Date().toISOString()
                    };

                    // Přidání vztahu do dat
                    addRelationshipIfNotExists(relationship);
                }
            });
        });
    });
}

/**
 * Mapování typu vztahu na standardní typy
 * @param {string} relationType - Typ vztahu z modulu
 * @returns {string} - Standardní typ vztahu
 */
function mapRelationshipType(relationType) {
    const type = relationType.toLowerCase();

    if (type.includes('rodič') || type.includes('dítě') || type.includes('manžel') || type.includes('manželka') || type.includes('sourozenec')) {
        return 'family';
    } else if (type.includes('zaměstnanec') || type.includes('zaměstnavatel') || type.includes('kolega') || type.includes('obchodní')) {
        return 'business';
    } else if (type.includes('přítel') || type.includes('známý') || type.includes('soused')) {
        return 'social';
    } else if (type.includes('vlastník') || type.includes('majitel')) {
        return 'ownership';
    } else if (type.includes('komunikace') || type.includes('kontakt')) {
        return 'communication';
    } else if (type.includes('transakce') || type.includes('platba')) {
        return 'transaction';
    } else {
        return 'social'; // Výchozí typ
    }
}

/**
 * Přidání entity do dat, pokud ještě neexistuje
 * @param {Object} entity - Entita k přidání
 */
function addEntityIfNotExists(entity) {
    // Kontrola, zda entita již existuje
    const existingEntity = correlationData.entities.find(e =>
        e.name === entity.name && e.type === entity.type
    );

    if (!existingEntity) {
        correlationData.entities.push(entity);
    }
}

/**
 * Přidání vztahu do dat, pokud ještě neexistuje
 * @param {Object} relationship - Vztah k přidání
 */
function addRelationshipIfNotExists(relationship) {
    // Kontrola, zda vztah již existuje
    const existingRelationship = correlationData.relationships.find(r =>
        r.sourceId === relationship.sourceId &&
        r.targetId === relationship.targetId &&
        r.type === relationship.type
    );

    if (!existingRelationship) {
        correlationData.relationships.push(relationship);
    }
}

/**
 * Import dat z modulu časové osy
 */
function importDataFromTimelineModule() {
    console.log('Import dat z modulu časové osy');

    // Najít všechny moduly časové osy
    const timelineModules = document.querySelectorAll('.module[id^="module-casova-osa"]');
    if (timelineModules.length === 0) {
        console.log('Nenalezeny žádné moduly časové osy');
        return;
    }

    // Procházení všech modulů časové osy
    timelineModules.forEach(module => {
        // Najít všechny události v modulu
        const eventElements = module.querySelectorAll('.timeline-event');

        eventElements.forEach(eventElement => {
            // Získání dat o události
            const name = eventElement.querySelector('.event-name')?.textContent || '';
            const date = eventElement.querySelector('.event-date')?.textContent || '';
            const description = eventElement.querySelector('.event-description')?.textContent || '';
            const type = eventElement.querySelector('.event-type')?.textContent || '';

            // Vytvoření entity události
            const eventEntity = {
                id: 'entity_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                name: name,
                type: 'event',
                date: date,
                description: description,
                eventType: type,
                source: 'timeline-module',
                created: new Date().toISOString(),
                updated: new Date().toISOString()
            };

            // Přidání entity do dat
            addEntityIfNotExists(eventEntity);

            // Najít osoby spojené s událostí
            const personElements = eventElement.querySelectorAll('.event-person');
            personElements.forEach(personElement => {
                const personName = personElement.querySelector('.person-name')?.textContent || '';
                const personRole = personElement.querySelector('.person-role')?.textContent || '';

                if (personName) {
                    // Vytvoření entity osoby
                    const personEntity = {
                        id: 'entity_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                        name: personName,
                        type: 'person',
                        source: 'timeline-module',
                        created: new Date().toISOString(),
                        updated: new Date().toISOString()
                    };

                    // Přidání entity do dat
                    addEntityIfNotExists(personEntity);

                    // Vytvoření vztahu mezi osobou a událostí
                    const relationship = {
                        id: 'relationship_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                        sourceId: personEntity.id,
                        targetId: eventEntity.id,
                        type: 'social',
                        description: personRole || 'Účastník události',
                        source: 'timeline-module',
                        created: new Date().toISOString(),
                        updated: new Date().toISOString()
                    };

                    // Přidání vztahu do dat
                    addRelationshipIfNotExists(relationship);
                }
            });

            // Najít lokace spojené s událostí
            const locationElements = eventElement.querySelectorAll('.event-location');
            locationElements.forEach(locationElement => {
                const locationName = locationElement.querySelector('.location-name')?.textContent || '';

                if (locationName) {
                    // Vytvoření entity lokace
                    const locationEntity = {
                        id: 'entity_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                        name: locationName,
                        type: 'location',
                        source: 'timeline-module',
                        created: new Date().toISOString(),
                        updated: new Date().toISOString()
                    };

                    // Přidání entity do dat
                    addEntityIfNotExists(locationEntity);

                    // Vytvoření vztahu mezi lokací a událostí
                    const relationship = {
                        id: 'relationship_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                        sourceId: eventEntity.id,
                        targetId: locationEntity.id,
                        type: 'social',
                        description: 'Místo události',
                        source: 'timeline-module',
                        created: new Date().toISOString(),
                        updated: new Date().toISOString()
                    };

                    // Přidání vztahu do dat
                    addRelationshipIfNotExists(relationship);
                }
            });
        });
    });
}

/**
 * Import dat z modulu finančního monitoringu
 */
function importDataFromFinancialModule() {
    console.log('Import dat z modulu finančního monitoringu');

    // Najít všechny moduly finančního monitoringu
    const financialModules = document.querySelectorAll('.module[id^="module-financni-monitoring"]');
    if (financialModules.length === 0) {
        console.log('Nenalezeny žádné moduly finančního monitoringu');
        return;
    }

    // Procházení všech modulů finančního monitoringu
    financialModules.forEach(module => {
        // Najít všechny transakce v modulu
        const transactionElements = module.querySelectorAll('.transaction-item');

        transactionElements.forEach(transactionElement => {
            // Získání dat o transakci
            const description = transactionElement.querySelector('.transaction-description')?.textContent || '';
            const amount = transactionElement.querySelector('.transaction-amount')?.textContent || '';
            const date = transactionElement.querySelector('.transaction-date')?.textContent || '';
            const sourceAccount = transactionElement.querySelector('.transaction-source')?.textContent || '';
            const targetAccount = transactionElement.querySelector('.transaction-target')?.textContent || '';

            // Vytvoření entity transakce
            const transactionEntity = {
                id: 'entity_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                name: `Transakce: ${description}`,
                type: 'event',
                description: description,
                amount: amount,
                date: date,
                sourceAccount: sourceAccount,
                targetAccount: targetAccount,
                source: 'financial-module',
                created: new Date().toISOString(),
                updated: new Date().toISOString()
            };

            // Přidání entity do dat
            addEntityIfNotExists(transactionEntity);

            // Vytvoření entit pro zdrojový a cílový účet
            if (sourceAccount) {
                const sourceAccountEntity = {
                    id: 'entity_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                    name: `Účet: ${sourceAccount}`,
                    type: 'asset',
                    accountNumber: sourceAccount,
                    source: 'financial-module',
                    created: new Date().toISOString(),
                    updated: new Date().toISOString()
                };

                // Přidání entity do dat
                addEntityIfNotExists(sourceAccountEntity);

                // Vytvoření vztahu mezi zdrojovým účtem a transakcí
                const relationship = {
                    id: 'relationship_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                    sourceId: sourceAccountEntity.id,
                    targetId: transactionEntity.id,
                    type: 'transaction',
                    description: 'Zdrojový účet',
                    source: 'financial-module',
                    created: new Date().toISOString(),
                    updated: new Date().toISOString()
                };

                // Přidání vztahu do dat
                addRelationshipIfNotExists(relationship);
            }

            if (targetAccount) {
                const targetAccountEntity = {
                    id: 'entity_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                    name: `Účet: ${targetAccount}`,
                    type: 'asset',
                    accountNumber: targetAccount,
                    source: 'financial-module',
                    created: new Date().toISOString(),
                    updated: new Date().toISOString()
                };

                // Přidání entity do dat
                addEntityIfNotExists(targetAccountEntity);

                // Vytvoření vztahu mezi cílovým účtem a transakcí
                const relationship = {
                    id: 'relationship_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                    sourceId: transactionEntity.id,
                    targetId: targetAccountEntity.id,
                    type: 'transaction',
                    description: 'Cílový účet',
                    source: 'financial-module',
                    created: new Date().toISOString(),
                    updated: new Date().toISOString()
                };

                // Přidání vztahu do dat
                addRelationshipIfNotExists(relationship);
            }
        });
    });
}

/**
 * Import dat z modulu komunikačních platforem
 */
function importDataFromMessagingModule() {
    console.log('Import dat z modulu komunikačních platforem');

    // Najít všechny moduly komunikačních platforem
    const messagingModules = document.querySelectorAll('.module[id^="module-komunikacni-platformy"]');
    if (messagingModules.length === 0) {
        console.log('Nenalezeny žádné moduly komunikačních platforem');
        return;
    }

    // Procházení všech modulů komunikačních platforem
    messagingModules.forEach(module => {
        // Najít všechny konverzace v modulu
        const conversationElements = module.querySelectorAll('.conversation-item');

        conversationElements.forEach(conversationElement => {
            // Získání dat o konverzaci
            const platform = conversationElement.querySelector('.conversation-platform')?.textContent || '';
            const date = conversationElement.querySelector('.conversation-date')?.textContent || '';

            // Najít účastníky konverzace
            const participantElements = conversationElement.querySelectorAll('.conversation-participant');
            const participants = [];

            participantElements.forEach(participantElement => {
                const participantName = participantElement.querySelector('.participant-name')?.textContent || '';
                const participantId = participantElement.querySelector('.participant-id')?.textContent || '';

                if (participantName) {
                    // Vytvoření entity účastníka
                    const participantEntity = {
                        id: 'entity_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                        name: participantName,
                        type: 'person',
                        platformId: participantId,
                        platform: platform,
                        source: 'messaging-module',
                        created: new Date().toISOString(),
                        updated: new Date().toISOString()
                    };

                    // Přidání entity do dat
                    addEntityIfNotExists(participantEntity);

                    participants.push(participantEntity);
                }
            });

            // Vytvoření vztahů mezi účastníky
            for (let i = 0; i < participants.length; i++) {
                for (let j = i + 1; j < participants.length; j++) {
                    const relationship = {
                        id: 'relationship_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9),
                        sourceId: participants[i].id,
                        targetId: participants[j].id,
                        type: 'communication',
                        description: `Komunikace na platformě ${platform}`,
                        date: date,
                        platform: platform,
                        source: 'messaging-module',
                        created: new Date().toISOString(),
                        updated: new Date().toISOString()
                    };

                    // Přidání vztahu do dat
                    addRelationshipIfNotExists(relationship);
                }
            }
        });
    });
}

/**
 * Import dat ze souboru
 * @param {HTMLElement} module - Modul korelačního nástroje
 * @param {File} file - Soubor s daty
 */
function importDataFromFile(module, file) {
    console.log('Import dat ze souboru:', file.name);

    // Zobrazení notifikace o zahájení importu
    showNotification('Probíhá import dat ze souboru...', 'info');

    // Kontrola typu souboru
    const fileExtension = file.name.split('.').pop().toLowerCase();

    if (fileExtension === 'json') {
        // Import dat z JSON souboru
        const reader = new FileReader();
        reader.onload = function(event) {
            try {
                const data = JSON.parse(event.target.result);
                importDataFromJSON(module, data);
            } catch (error) {
                console.error('Chyba při importu dat z JSON souboru:', error);
                showNotification('Chyba při importu dat z JSON souboru. Zkontrolujte formát souboru.', 'error');
            }
        };
        reader.readAsText(file);
    } else if (fileExtension === 'csv') {
        // Import dat z CSV souboru
        const reader = new FileReader();
        reader.onload = function(event) {
            try {
                importDataFromCSV(module, event.target.result);
            } catch (error) {
                console.error('Chyba při importu dat z CSV souboru:', error);
                showNotification('Chyba při importu dat z CSV souboru. Zkontrolujte formát souboru.', 'error');
            }
        };
        reader.readAsText(file);
    } else {
        showNotification('Nepodporovaný formát souboru. Podporované formáty jsou JSON a CSV.', 'error');
    }
}

/**
 * Import dat z textu
 * @param {HTMLElement} module - Modul korelačního nástroje
 * @param {string} text - Text s daty
 */
function importDataFromText(module, text) {
    console.log('Import dat z textu');

    // Zobrazení notifikace o zahájení importu
    showNotification('Probíhá import dat z textu...', 'info');

    // Kontrola formátu textu
    if (text.trim().startsWith('{') || text.trim().startsWith('[')) {
        // Import dat z JSON textu
        try {
            const data = JSON.parse(text);
            importDataFromJSON(module, data);
        } catch (error) {
            console.error('Chyba při importu dat z JSON textu:', error);
            showNotification('Chyba při importu dat z JSON textu. Zkontrolujte formát textu.', 'error');
        }
    } else {
        // Import dat z CSV textu
        try {
            importDataFromCSV(module, text);
        } catch (error) {
            console.error('Chyba při importu dat z CSV textu:', error);
            showNotification('Chyba při importu dat z CSV textu. Zkontrolujte formát textu.', 'error');
        }
    }
}

/**
 * Import dat z JSON
 * @param {HTMLElement} module - Modul korelačního nástroje
 * @param {Object} data - Data k importu
 */
function importDataFromJSON(module, data) {
    console.log('Import dat z JSON');

    let importedEntities = 0;
    let importedRelationships = 0;

    // Import entit
    if (data.entities && Array.isArray(data.entities)) {
        data.entities.forEach(entity => {
            // Kontrola, zda entita má všechny povinné atributy
            if (entity.id && entity.name && entity.type) {
                // Přidání entity do dat
                addEntityIfNotExists(entity);
                importedEntities++;
            }
        });
    }

    // Import vztahů
    if (data.relationships && Array.isArray(data.relationships)) {
        data.relationships.forEach(relationship => {
            // Kontrola, zda vztah má všechny povinné atributy
            if (relationship.id && relationship.sourceId && relationship.targetId && relationship.type) {
                // Přidání vztahu do dat
                addRelationshipIfNotExists(relationship);
                importedRelationships++;
            }
        });
    }

    // Uložení dat
    saveCorrelationData(module);

    // Aktualizace vizualizace
    updateCorrelationVisualization(module);

    // Zobrazení notifikace o dokončení importu
    showNotification(`Import dat byl dokončen. Importováno ${importedEntities} entit a ${importedRelationships} vztahů.`, 'success');
}

/**
 * Import dat z CSV
 * @param {HTMLElement} module - Modul korelačního nástroje
 * @param {string} csv - CSV data
 */
function importDataFromCSV(module, csv) {
    console.log('Import dat z CSV');

    // Rozdělení CSV na řádky
    const lines = csv.split('\n');
    if (lines.length < 2) {
        showNotification('CSV soubor neobsahuje dostatek dat.', 'error');
        return;
    }

    // Získání hlavičky
    const header = lines[0].split(',').map(h => h.trim());

    // Kontrola, zda CSV obsahuje data o entitách nebo vztazích
    if (header.includes('type') && header.includes('name')) {
        // Import entit
        importEntitiesFromCSV(module, lines, header);
    } else if (header.includes('sourceId') && header.includes('targetId') && header.includes('type')) {
        // Import vztahů
        importRelationshipsFromCSV(module, lines, header);
    } else {
        showNotification('Nepodporovaný formát CSV. CSV musí obsahovat data o entitách nebo vztazích.', 'error');
    }
}

/**
 * Import entit z CSV
 * @param {HTMLElement} module - Modul korelačního nástroje
 * @param {Array} lines - Řádky CSV
 * @param {Array} header - Hlavička CSV
 */
function importEntitiesFromCSV(module, lines, header) {
    console.log('Import entit z CSV');

    let importedEntities = 0;

    // Indexy sloupců
    const nameIndex = header.indexOf('name');
    const typeIndex = header.indexOf('type');
    const idIndex = header.indexOf('id');
    const descriptionIndex = header.indexOf('description');

    // Import entit
    for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        const values = line.split(',').map(v => v.trim());

        // Kontrola, zda řádek obsahuje dostatek hodnot
        if (values.length < Math.max(nameIndex, typeIndex) + 1) continue;

        // Vytvoření entity
        const entity = {
            id: idIndex >= 0 && values[idIndex] ? values[idIndex] : generateEntityId(),
            name: values[nameIndex],
            type: values[typeIndex],
            description: descriptionIndex >= 0 && values[descriptionIndex] ? values[descriptionIndex] : '',
            source: 'csv-import',
            created: new Date().toISOString(),
            updated: new Date().toISOString()
        };

        // Přidání dalších atributů
        for (let j = 0; j < header.length; j++) {
            if (j !== nameIndex && j !== typeIndex && j !== idIndex && j !== descriptionIndex && header[j] && values[j]) {
                entity[header[j]] = values[j];
            }
        }

        // Přidání entity do dat
        addEntityIfNotExists(entity);
        importedEntities++;
    }

    // Uložení dat
    saveCorrelationData(module);

    // Aktualizace vizualizace
    updateCorrelationVisualization(module);

    // Zobrazení notifikace o dokončení importu
    showNotification(`Import entit byl dokončen. Importováno ${importedEntities} entit.`, 'success');
}

/**
 * Import vztahů z CSV
 * @param {HTMLElement} module - Modul korelačního nástroje
 * @param {Array} lines - Řádky CSV
 * @param {Array} header - Hlavička CSV
 */
function importRelationshipsFromCSV(module, lines, header) {
    console.log('Import vztahů z CSV');

    let importedRelationships = 0;

    // Indexy sloupců
    const sourceIdIndex = header.indexOf('sourceId');
    const targetIdIndex = header.indexOf('targetId');
    const typeIndex = header.indexOf('type');
    const idIndex = header.indexOf('id');
    const descriptionIndex = header.indexOf('description');

    // Import vztahů
    for (let i = 1; i < lines.length; i++) {
        const line = lines[i].trim();
        if (!line) continue;

        const values = line.split(',').map(v => v.trim());

        // Kontrola, zda řádek obsahuje dostatek hodnot
        if (values.length < Math.max(sourceIdIndex, targetIdIndex, typeIndex) + 1) continue;

        // Vytvoření vztahu
        const relationship = {
            id: idIndex >= 0 && values[idIndex] ? values[idIndex] : generateRelationshipId(),
            sourceId: values[sourceIdIndex],
            targetId: values[targetIdIndex],
            type: values[typeIndex],
            description: descriptionIndex >= 0 && values[descriptionIndex] ? values[descriptionIndex] : '',
            source: 'csv-import',
            created: new Date().toISOString(),
            updated: new Date().toISOString()
        };

        // Přidání dalších atributů
        for (let j = 0; j < header.length; j++) {
            if (j !== sourceIdIndex && j !== targetIdIndex && j !== typeIndex && j !== idIndex && j !== descriptionIndex && header[j] && values[j]) {
                relationship[header[j]] = values[j];
            }
        }

        // Přidání vztahu do dat
        addRelationshipIfNotExists(relationship);
        importedRelationships++;
    }

    // Uložení dat
    saveCorrelationData(module);

    // Aktualizace vizualizace
    updateCorrelationVisualization(module);

    // Zobrazení notifikace o dokončení importu
    showNotification(`Import vztahů byl dokončen. Importováno ${importedRelationships} vztahů.`, 'success');
}

/**
 * Funkce pro export dat korelačního nástroje
 * @param {HTMLElement} module - Modul korelačního nástroje
 */
function exportCorrelationData(module) {
    console.log('Export dat korelačního nástroje');

    // Vytvoření dialogu
    const dialog = document.createElement('div');
    dialog.className = 'correlation-dialog';
    dialog.innerHTML = `
        <div class="correlation-dialog-content">
            <div class="correlation-dialog-header">
                <h4>Exportovat data</h4>
                <button type="button" class="correlation-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="correlation-dialog-body">
                <div class="export-options">
                    <h5>Vyberte formát exportu</h5>
                    <div class="export-option">
                        <input type="radio" id="export-json" name="export-format" value="json" checked>
                        <label for="export-json">JSON</label>
                    </div>
                    <div class="export-option">
                        <input type="radio" id="export-csv" name="export-format" value="csv">
                        <label for="export-csv">CSV</label>
                    </div>
                </div>

                <div class="export-options">
                    <h5>Vyberte obsah exportu</h5>
                    <div class="export-option">
                        <input type="checkbox" id="export-entities" name="export-content" value="entities" checked>
                        <label for="export-entities">Entity</label>
                    </div>
                    <div class="export-option">
                        <input type="checkbox" id="export-relationships" name="export-content" value="relationships" checked>
                        <label for="export-relationships">Vztahy</label>
                    </div>
                </div>

                <div id="export-csv-options" class="export-format-options" style="display: none;">
                    <h5>Možnosti CSV exportu</h5>
                    <div class="form-group">
                        <label for="csv-separator">Oddělovač</label>
                        <select id="csv-separator" class="form-control">
                            <option value="," selected>Čárka (,)</option>
                            <option value=";">Středník (;)</option>
                            <option value="\\t">Tabulátor (\\t)</option>
                        </select>
                    </div>
                </div>

                <div id="export-preview" class="export-preview">
                    <h5>Náhled exportu</h5>
                    <div class="export-preview-content">
                        <pre id="export-preview-text" class="export-preview-text">Vyberte formát a obsah exportu pro zobrazení náhledu.</pre>
                    </div>
                </div>
            </div>
            <div class="correlation-dialog-footer">
                <button type="button" class="btn-secondary cancel-export">Zrušit</button>
                <button type="button" class="btn-primary download-export">Stáhnout</button>
            </div>
        </div>
    `;

    // Přidání dialogu do dokumentu
    document.body.appendChild(dialog);

    // Získání reference na formulář a tlačítka
    const cancelButton = dialog.querySelector('.cancel-export');
    const downloadButton = dialog.querySelector('.download-export');
    const closeButton = dialog.querySelector('.correlation-dialog-close');

    // Přepínání mezi formáty exportu
    const exportFormatRadios = dialog.querySelectorAll('input[name="export-format"]');
    const exportCsvOptions = dialog.querySelector('#export-csv-options');

    exportFormatRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            exportCsvOptions.style.display = this.value === 'csv' ? 'block' : 'none';
            updateExportPreview();
        });
    });

    // Aktualizace náhledu exportu při změně obsahu
    const exportContentCheckboxes = dialog.querySelectorAll('input[name="export-content"]');
    exportContentCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateExportPreview);
    });

    // Aktualizace náhledu exportu při změně oddělovače CSV
    const csvSeparatorSelect = dialog.querySelector('#csv-separator');
    if (csvSeparatorSelect) {
        csvSeparatorSelect.addEventListener('change', updateExportPreview);
    }

    // Funkce pro aktualizaci náhledu exportu
    function updateExportPreview() {
        const exportFormat = dialog.querySelector('input[name="export-format"]:checked').value;
        const exportEntities = dialog.querySelector('#export-entities').checked;
        const exportRelationships = dialog.querySelector('#export-relationships').checked;

        // Získání dat pro export
        const exportData = getExportData(exportEntities, exportRelationships);

        // Náhled exportu
        const previewText = dialog.querySelector('#export-preview-text');

        if (exportFormat === 'json') {
            // Náhled JSON exportu
            previewText.textContent = JSON.stringify(exportData, null, 2).substring(0, 1000) + (JSON.stringify(exportData, null, 2).length > 1000 ? '...' : '');
        } else if (exportFormat === 'csv') {
            // Náhled CSV exportu
            const separator = csvSeparatorSelect.value;
            let csvText = '';

            if (exportEntities) {
                csvText += 'id,name,type,description\n';
                for (let i = 0; i < Math.min(5, exportData.entities.length); i++) {
                    const entity = exportData.entities[i];
                    csvText += `${entity.id}${separator}${entity.name}${separator}${entity.type}${separator}${entity.description || ''}\n`;
                }
                if (exportData.entities.length > 5) {
                    csvText += '...\n';
                }
            }

            if (exportRelationships) {
                if (exportEntities) {
                    csvText += '\n';
                }
                csvText += 'id,sourceId,targetId,type,description\n';
                for (let i = 0; i < Math.min(5, exportData.relationships.length); i++) {
                    const relationship = exportData.relationships[i];
                    csvText += `${relationship.id}${separator}${relationship.sourceId}${separator}${relationship.targetId}${separator}${relationship.type}${separator}${relationship.description || ''}\n`;
                }
                if (exportData.relationships.length > 5) {
                    csvText += '...\n';
                }
            }

            previewText.textContent = csvText;
        }
    }

    // Funkce pro získání dat pro export
    function getExportData(exportEntities, exportRelationships) {
        const exportData = {};

        if (exportEntities) {
            exportData.entities = correlationData.entities;
        }

        if (exportRelationships) {
            exportData.relationships = correlationData.relationships;
        }

        return exportData;
    }

    // Inicializace náhledu exportu
    updateExportPreview();

    // Přidání event listenerů
    cancelButton.addEventListener('click', function() {
        dialog.remove();
    });

    closeButton.addEventListener('click', function() {
        dialog.remove();
    });

    downloadButton.addEventListener('click', function() {
        // Získání formátu a obsahu exportu
        const exportFormat = dialog.querySelector('input[name="export-format"]:checked').value;
        const exportEntities = dialog.querySelector('#export-entities').checked;
        const exportRelationships = dialog.querySelector('#export-relationships').checked;

        // Kontrola, zda je vybrán alespoň jeden typ obsahu
        if (!exportEntities && !exportRelationships) {
            showNotification('Vyberte alespoň jeden typ obsahu pro export.', 'error');
            return;
        }

        // Získání dat pro export
        const exportData = getExportData(exportEntities, exportRelationships);

        // Vytvoření souboru pro stažení
        let fileContent = '';
        let fileName = `correlation-data-${new Date().toISOString().slice(0, 10)}`;
        let mimeType = '';

        if (exportFormat === 'json') {
            // Export do JSON
            fileContent = JSON.stringify(exportData, null, 2);
            fileName += '.json';
            mimeType = 'application/json';
        } else if (exportFormat === 'csv') {
            // Export do CSV
            const separator = csvSeparatorSelect.value;

            if (exportEntities) {
                fileContent += 'id,name,type,description\n';
                exportData.entities.forEach(entity => {
                    fileContent += `${entity.id}${separator}${entity.name}${separator}${entity.type}${separator}${entity.description || ''}\n`;
                });
            }

            if (exportRelationships) {
                if (exportEntities) {
                    fileContent += '\n';
                }
                fileContent += 'id,sourceId,targetId,type,description\n';
                exportData.relationships.forEach(relationship => {
                    fileContent += `${relationship.id}${separator}${relationship.sourceId}${separator}${relationship.targetId}${separator}${relationship.type}${separator}${relationship.description || ''}\n`;
                });
            }

            fileName += '.csv';
            mimeType = 'text/csv';
        }

        // Vytvoření a stažení souboru
        const blob = new Blob([fileContent], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        a.click();
        URL.revokeObjectURL(url);

        // Zobrazení notifikace
        showNotification(`Data byla úspěšně exportována do souboru ${fileName}.`, 'success');

        // Zavření dialogu
        dialog.remove();
    });
}

/**
 * Přidání vztahu do dat korelačního nástroje
 * @param {HTMLElement} module - Modul korelačního nástroje
 * @param {Object} relationship - Vztah k přidání
 */
function addRelationshipToCorrelationData(module, relationship) {
    console.log('Přidání vztahu do dat korelačního nástroje:', relationship);

    // Kontrola, zda existují data korelačního nástroje
    if (!module.correlationData) {
        initCorrelationData(module);
    }

    // Přidání vztahu do seznamu vztahů
    module.correlationData.relationships.push(relationship);

    // Aktualizace času poslední změny
    module.correlationData.lastUpdated = new Date().toISOString();

    // Uložení dat do lokálního úložiště
    saveCorrelationData(module);
}

/**
 * Získání ikony pro typ notifikace
 * @param {string} type - Typ notifikace
 * @returns {string} - Třída ikony
 */
function getNotificationIcon(type) {
    switch (type) {
        case 'success':
            return 'fa-check-circle';
        case 'error':
            return 'fa-exclamation-circle';
        case 'warning':
            return 'fa-exclamation-triangle';
        case 'info':
        default:
            return 'fa-info-circle';
    }
}

/**
 * Přidání entity do dat korelačního nástroje
 * @param {HTMLElement} module - Modul korelačního nástroje
 * @param {Object} entity - Entita k přidání
 */
function addEntityToCorrelationData(module, entity) {
    console.log('Přidání entity do dat korelačního nástroje:', entity);

    // Přidání entity do globálních dat
    if (!correlationData.entities) {
        correlationData.entities = [];
    }

    // Přidání entity do seznamu entit
    correlationData.entities.push(entity);

    // Aktualizace času poslední změny
    correlationData.lastUpdated = new Date().toISOString();

    // Uložení dat do lokálního úložiště
    saveCorrelationData();

    // Aktualizace vizualizace
    updateCorrelationVisualization(module);
}

/**
 * Přidání vztahu do dat korelačního nástroje
 * @param {HTMLElement} module - Modul korelačního nástroje
 * @param {Object} relationship - Vztah k přidání
 */
function addRelationshipToCorrelationData(module, relationship) {
    console.log('Přidání vztahu do dat korelačního nástroje:', relationship);

    // Přidání vztahu do globálních dat
    if (!correlationData.relationships) {
        correlationData.relationships = [];
    }

    // Přidání vztahu do seznamu vztahů
    correlationData.relationships.push(relationship);

    // Aktualizace času poslední změny
    correlationData.lastUpdated = new Date().toISOString();

    // Uložení dat do lokálního úložiště
    saveCorrelationData();

    // Aktualizace vizualizace
    updateCorrelationVisualization(module);
}

/**
 * Uložení dat korelačního nástroje do lokálního úložiště
 */
function saveCorrelationData() {
    console.log('Ukládání dat korelačního nástroje');

    try {
        // Uložení dat do lokálního úložiště
        localStorage.setItem('correlation-data-global', JSON.stringify(correlationData));

        console.log('Data korelačního nástroje byla úspěšně uložena');
        return true;
    } catch (error) {
        console.error('Chyba při ukládání dat korelačního nástroje:', error);
        showNotification('Nepodařilo se uložit data korelačního nástroje.', 'error');
        return false;
    }
}

/**
 * Načtení dat z ostatních modulů
 * @param {HTMLElement} module - Element modulu korelačního nástroje
 */
function loadDataFromOtherModules(module) {
    console.log('Načítání dat z ostatních modulů');

    // Zobrazení notifikace o zahájení načítání
    showNotification('Probíhá načítání dat z ostatních modulů...', 'info');

    // Simulace načítání dat
    setTimeout(() => {
        // Načtení dat z modulu evidence obyvatel
        loadDataFromPersonModule();

        // Načtení dat z modulu firmy
        loadDataFromCompanyModule();

        // Načtení dat z modulu lokace
        loadDataFromLocationModule();

        // Načtení dat z modulu časové osy
        loadDataFromTimelineModule();

        // Načtení dat z modulu finančního monitoringu
        loadDataFromFinancialModule();

        // Načtení dat z modulu komunikačních platforem
        loadDataFromMessagingModule();

        // Aktualizace vizualizace
        updateCorrelationVisualization(module);

        // Zobrazení notifikace o dokončení načítání
        showNotification('Data z ostatních modulů byla úspěšně načtena.', 'success');
    }, 2000);
}

/**
 * Načtení dat z modulu evidence obyvatel
 */
function loadDataFromPersonModule() {
    console.log('Načítání dat z modulu evidence obyvatel');

    // Najít všechny moduly evidence obyvatel
    const personModules = document.querySelectorAll('.module[id^="module-evidence-obyvatel"]');
    if (personModules.length === 0) {
        console.log('Nenalezeny žádné moduly evidence obyvatel');
        return;
    }

    // Procházení všech modulů evidence obyvatel
    personModules.forEach(module => {
        // Najít všechny osoby v modulu
        const personElements = module.querySelectorAll('.person-card');

        personElements.forEach(personElement => {
            // Získání dat o osobě
            const name = personElement.querySelector('.person-name')?.textContent || '';
            const birthDate = personElement.querySelector('.person-birth-date')?.textContent || '';
            const address = personElement.querySelector('.person-address')?.textContent || '';
            const idNumber = personElement.querySelector('.person-id-number')?.textContent || '';

            // Vytvoření entity osoby
            const personEntity = {
                id: generateEntityId(),
                name: name,
                type: 'person',
                birthDate: birthDate,
                address: address,
                idNumber: idNumber,
                source: 'person-module',
                createdAt: new Date().toISOString()
            };

            // Přidání entity do dat
            addEntityIfNotExists(personEntity);

            // Najít rodinné příslušníky
            const relativeElements = personElement.querySelectorAll('.person-relative');
            relativeElements.forEach(relativeElement => {
                const relativeName = relativeElement.querySelector('.relative-name')?.textContent || '';
                const relationType = relativeElement.querySelector('.relative-type')?.textContent || '';

                // Vytvoření entity rodinného příslušníka
                const relativeEntity = {
                    id: generateEntityId(),
                    name: relativeName,
                    type: 'person',
                    source: 'person-module',
                    createdAt: new Date().toISOString()
                };

                // Přidání entity do dat
                addEntityIfNotExists(relativeEntity);

                // Vytvoření vztahu mezi osobou a rodinným příslušníkem
                const relationship = {
                    id: generateRelationshipId(),
                    sourceId: personEntity.id,
                    targetId: relativeEntity.id,
                    type: 'family',
                    description: relationType,
                    source: 'person-module',
                    createdAt: new Date().toISOString()
                };

                // Přidání vztahu do dat
                addRelationshipIfNotExists(relationship);
            });
        });
    });
}

/**
 * Načtení dat z modulu firmy
 */
function loadDataFromCompanyModule() {
    console.log('Načítání dat z modulu firmy');

    // Najít všechny moduly firem
    const companyModules = document.querySelectorAll('.module[id^="module-firmy"]');
    if (companyModules.length === 0) {
        console.log('Nenalezeny žádné moduly firem');
        return;
    }

    // Procházení všech modulů firem
    companyModules.forEach(module => {
        // Najít všechny firmy v modulu
        const companyElements = module.querySelectorAll('.company-card');

        companyElements.forEach(companyElement => {
            // Získání dat o firmě
            const name = companyElement.querySelector('.company-name')?.textContent || '';
            const ico = companyElement.querySelector('.company-ico')?.textContent || '';
            const address = companyElement.querySelector('.company-address')?.textContent || '';
            const legalForm = companyElement.querySelector('.company-legal-form')?.textContent || '';

            // Vytvoření entity firmy
            const companyEntity = {
                id: generateEntityId(),
                name: name,
                type: 'organization',
                ico: ico,
                address: address,
                legalForm: legalForm,
                source: 'company-module',
                createdAt: new Date().toISOString()
            };

            // Přidání entity do dat
            addEntityIfNotExists(companyEntity);

            // Najít statutární orgány
            const statutoryElements = companyElement.querySelectorAll('.company-statutory');
            statutoryElements.forEach(statutoryElement => {
                const personName = statutoryElement.querySelector('.statutory-name')?.textContent || '';
                const role = statutoryElement.querySelector('.statutory-role')?.textContent || '';

                // Vytvoření entity osoby
                const personEntity = {
                    id: generateEntityId(),
                    name: personName,
                    type: 'person',
                    source: 'company-module',
                    createdAt: new Date().toISOString()
                };

                // Přidání entity do dat
                addEntityIfNotExists(personEntity);

                // Vytvoření vztahu mezi firmou a osobou
                const relationship = {
                    id: generateRelationshipId(),
                    sourceId: personEntity.id,
                    targetId: companyEntity.id,
                    type: 'business',
                    description: role,
                    source: 'company-module',
                    createdAt: new Date().toISOString()
                };

                // Přidání vztahu do dat
                addRelationshipIfNotExists(relationship);
            });
        });
    });
}

/**
 * Načtení dat z modulu lokace
 */
function loadDataFromLocationModule() {
    console.log('Načítání dat z modulu lokace');

    // Najít všechny moduly lokací
    const locationModules = document.querySelectorAll('.module[id^="module-mapa"], .module[id^="module-lokace"]');
    if (locationModules.length === 0) {
        console.log('Nenalezeny žádné moduly lokací');
        return;
    }

    // Procházení všech modulů lokací
    locationModules.forEach(module => {
        // Najít všechny lokace v modulu
        const locationElements = module.querySelectorAll('.location-marker, .map-marker');

        locationElements.forEach(locationElement => {
            // Získání dat o lokaci
            const name = locationElement.querySelector('.marker-title')?.textContent || '';
            const address = locationElement.querySelector('.marker-address')?.textContent || '';
            const description = locationElement.querySelector('.marker-description')?.textContent || '';
            const latitude = locationElement.getAttribute('data-lat') || '';
            const longitude = locationElement.getAttribute('data-lng') || '';

            // Vytvoření entity lokace
            const locationEntity = {
                id: generateEntityId(),
                name: name,
                type: 'location',
                address: address,
                description: description,
                latitude: latitude,
                longitude: longitude,
                source: 'location-module',
                createdAt: new Date().toISOString()
            };

            // Přidání entity do dat
            addEntityIfNotExists(locationEntity);
        });
    });
}

/**
 * Načtení dat z modulu časové osy
 */
function loadDataFromTimelineModule() {
    console.log('Načítání dat z modulu časové osy');

    // Najít všechny moduly časové osy
    const timelineModules = document.querySelectorAll('.module[id^="module-casova-osa"]');
    if (timelineModules.length === 0) {
        console.log('Nenalezeny žádné moduly časové osy');
        return;
    }

    // Procházení všech modulů časové osy
    timelineModules.forEach(module => {
        // Najít všechny události v modulu
        const eventElements = module.querySelectorAll('.timeline-event');

        eventElements.forEach(eventElement => {
            // Získání dat o události
            const title = eventElement.querySelector('.event-title')?.textContent || '';
            const description = eventElement.querySelector('.event-description')?.textContent || '';
            const dateStr = eventElement.querySelector('.event-date')?.textContent || '';
            const type = eventElement.querySelector('.event-type')?.textContent || '';
            const location = eventElement.querySelector('.event-location')?.textContent || '';

            // Vytvoření entity události
            const eventEntity = {
                id: generateEntityId(),
                name: title,
                type: 'event',
                description: description,
                date: dateStr,
                eventType: type,
                location: location,
                source: 'timeline-module',
                createdAt: new Date().toISOString()
            };

            // Přidání entity do dat
            addEntityIfNotExists(eventEntity);

            // Přidání události do pole událostí
            correlationData.events.push({
                id: eventEntity.id,
                title: title,
                description: description,
                date: dateStr,
                type: type,
                location: location
            });

            // Najít související osoby
            const personElements = eventElement.querySelectorAll('.event-person');
            personElements.forEach(personElement => {
                const personName = personElement.textContent || '';

                // Vytvoření entity osoby
                const personEntity = {
                    id: generateEntityId(),
                    name: personName,
                    type: 'person',
                    source: 'timeline-module',
                    createdAt: new Date().toISOString()
                };

                // Přidání entity do dat
                addEntityIfNotExists(personEntity);

                // Vytvoření vztahu mezi událostí a osobou
                const relationship = {
                    id: generateRelationshipId(),
                    sourceId: personEntity.id,
                    targetId: eventEntity.id,
                    type: 'participation',
                    description: 'Účastník události',
                    source: 'timeline-module',
                    createdAt: new Date().toISOString()
                };

                // Přidání vztahu do dat
                addRelationshipIfNotExists(relationship);
            });
        });
    });
}

/**
 * Načtení dat z modulu finančního monitoringu
 */
function loadDataFromFinancialModule() {
    console.log('Načítání dat z modulu finančního monitoringu');

    // Najít všechny moduly finančního monitoringu
    const financialModules = document.querySelectorAll('.module[id^="module-financni"]');
    if (financialModules.length === 0) {
        console.log('Nenalezeny žádné moduly finančního monitoringu');
        return;
    }

    // Procházení všech modulů finančního monitoringu
    financialModules.forEach(module => {
        // Najít všechny transakce v modulu
        const transactionElements = module.querySelectorAll('.financial-transaction');

        transactionElements.forEach(transactionElement => {
            // Získání dat o transakci
            const title = transactionElement.querySelector('.transaction-title')?.textContent || '';
            const amount = transactionElement.querySelector('.transaction-amount')?.textContent || '';
            const dateStr = transactionElement.querySelector('.transaction-date')?.textContent || '';
            const type = transactionElement.querySelector('.transaction-type')?.textContent || '';
            const source = transactionElement.querySelector('.transaction-source')?.textContent || '';
            const target = transactionElement.querySelector('.transaction-target')?.textContent || '';

            // Vytvoření entity transakce
            const transactionEntity = {
                id: generateEntityId(),
                name: title,
                type: 'asset',
                amount: amount,
                date: dateStr,
                transactionType: type,
                source: 'financial-module',
                createdAt: new Date().toISOString()
            };

            // Přidání entity do dat
            addEntityIfNotExists(transactionEntity);

            // Vytvoření entit zdroje a cíle transakce
            if (source) {
                const sourceEntity = {
                    id: generateEntityId(),
                    name: source,
                    type: determineEntityType(source),
                    source: 'financial-module',
                    createdAt: new Date().toISOString()
                };

                // Přidání entity do dat
                addEntityIfNotExists(sourceEntity);

                // Vytvoření vztahu mezi zdrojem a transakcí
                const relationship = {
                    id: generateRelationshipId(),
                    sourceId: sourceEntity.id,
                    targetId: transactionEntity.id,
                    type: 'transaction',
                    description: 'Zdroj transakce',
                    source: 'financial-module',
                    createdAt: new Date().toISOString()
                };

                // Přidání vztahu do dat
                addRelationshipIfNotExists(relationship);
            }

            if (target) {
                const targetEntity = {
                    id: generateEntityId(),
                    name: target,
                    type: determineEntityType(target),
                    source: 'financial-module',
                    createdAt: new Date().toISOString()
                };

                // Přidání entity do dat
                addEntityIfNotExists(targetEntity);

                // Vytvoření vztahu mezi transakcí a cílem
                const relationship = {
                    id: generateRelationshipId(),
                    sourceId: transactionEntity.id,
                    targetId: targetEntity.id,
                    type: 'transaction',
                    description: 'Cíl transakce',
                    source: 'financial-module',
                    createdAt: new Date().toISOString()
                };

                // Přidání vztahu do dat
                addRelationshipIfNotExists(relationship);
            }
        });
    });
}

/**
 * Načtení dat z modulu komunikačních platforem
 */
function loadDataFromMessagingModule() {
    console.log('Načítání dat z modulu komunikačních platforem');

    // Najít všechny moduly komunikačních platforem
    const messagingModules = document.querySelectorAll('.module[id^="module-komunikace"], .module[id^="module-messaging"]');
    if (messagingModules.length === 0) {
        console.log('Nenalezeny žádné moduly komunikačních platforem');
        return;
    }

    // Procházení všech modulů komunikačních platforem
    messagingModules.forEach(module => {
        // Najít všechny zprávy v modulu
        const messageElements = module.querySelectorAll('.message-item');

        messageElements.forEach(messageElement => {
            // Získání dat o zprávě
            const content = messageElement.querySelector('.message-content')?.textContent || '';
            const dateStr = messageElement.querySelector('.message-date')?.textContent || '';
            const sender = messageElement.querySelector('.message-sender')?.textContent || '';
            const receiver = messageElement.querySelector('.message-receiver')?.textContent || '';
            const platform = messageElement.querySelector('.message-platform')?.textContent || '';

            // Vytvoření entity zprávy
            const messageEntity = {
                id: generateEntityId(),
                name: `Zpráva: ${content.substring(0, 30)}${content.length > 30 ? '...' : ''}`,
                type: 'digital',
                content: content,
                date: dateStr,
                platform: platform,
                source: 'messaging-module',
                createdAt: new Date().toISOString()
            };

            // Přidání entity do dat
            addEntityIfNotExists(messageEntity);

            // Vytvoření entit odesílatele a příjemce
            if (sender) {
                const senderEntity = {
                    id: generateEntityId(),
                    name: sender,
                    type: 'person',
                    source: 'messaging-module',
                    createdAt: new Date().toISOString()
                };

                // Přidání entity do dat
                addEntityIfNotExists(senderEntity);

                // Vytvoření vztahu mezi odesílatelem a zprávou
                const relationship = {
                    id: generateRelationshipId(),
                    sourceId: senderEntity.id,
                    targetId: messageEntity.id,
                    type: 'communication',
                    description: 'Odesílatel',
                    source: 'messaging-module',
                    createdAt: new Date().toISOString()
                };

                // Přidání vztahu do dat
                addRelationshipIfNotExists(relationship);
            }

            if (receiver) {
                const receiverEntity = {
                    id: generateEntityId(),
                    name: receiver,
                    type: 'person',
                    source: 'messaging-module',
                    createdAt: new Date().toISOString()
                };

                // Přidání entity do dat
                addEntityIfNotExists(receiverEntity);

                // Vytvoření vztahu mezi zprávou a příjemcem
                const relationship = {
                    id: generateRelationshipId(),
                    sourceId: messageEntity.id,
                    targetId: receiverEntity.id,
                    type: 'communication',
                    description: 'Příjemce',
                    source: 'messaging-module',
                    createdAt: new Date().toISOString()
                };

                // Přidání vztahu do dat
                addRelationshipIfNotExists(relationship);
            }
        });
    });
}

/**
 * Určení typu entity podle názvu
 * @param {string} name - Název entity
 * @returns {string} - Typ entity
 */
function determineEntityType(name) {
    // Pokud název obsahuje s.r.o., a.s., apod., je to pravděpodobně organizace
    if (/s\.r\.o\.|a\.s\.|k\.s\.|v\.o\.s\.|spol\.|společnost|firma|podnik/i.test(name)) {
        return 'organization';
    }

    // Pokud název obsahuje ulici, město, apod., je to pravděpodobně lokace
    if (/ulice|náměstí|třída|město|obec|čp\.|č\.|PSČ/i.test(name)) {
        return 'location';
    }

    // Pokud název obsahuje datum, je to pravděpodobně událost
    if (/\d{1,2}\.\d{1,2}\.\d{4}|\d{4}-\d{2}-\d{2}/.test(name)) {
        return 'event';
    }

    // Pokud název obsahuje Kč, EUR, USD, apod., je to pravděpodobně majetek
    if (/Kč|EUR|USD|€|\$/.test(name)) {
        return 'asset';
    }

    // Pokud název obsahuje @, .com, .cz, apod., je to pravděpodobně digitální entita
    if (/@|\.com|\.cz|\.org|\.net|\.eu|www\.|http/.test(name)) {
        return 'digital';
    }

    // Výchozí typ je osoba
    return 'person';
}

/**
 * Generování ID entity
 * @returns {string} - Vygenerované ID
 */
function generateEntityId() {
    return 'entity-' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

/**
 * Generování ID vztahu
 * @returns {string} - Vygenerované ID
 */
function generateRelationshipId() {
    return 'rel-' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

/**
 * Přidání entity do dat, pokud ještě neexistuje
 * @param {Object} entity - Entita k přidání
 */
function addEntityIfNotExists(entity) {
    // Kontrola, zda entita již existuje
    const existingEntity = correlationData.entities.find(e =>
        e.name === entity.name && e.type === entity.type
    );

    if (!existingEntity) {
        correlationData.entities.push(entity);
    }
}

/**
 * Přidání vztahu do dat, pokud ještě neexistuje
 * @param {Object} relationship - Vztah k přidání
 */
function addRelationshipIfNotExists(relationship) {
    // Kontrola, zda vztah již existuje
    const existingRelationship = correlationData.relationships.find(r =>
        r.sourceId === relationship.sourceId &&
        r.targetId === relationship.targetId &&
        r.type === relationship.type
    );

    if (!existingRelationship) {
        correlationData.relationships.push(relationship);
    }
}

/**
 * Načtení šablony korelačního nástroje
 * @param {HTMLElement} container - Kontejner pro načtení šablony
 */
function loadCorrelationTemplate(container) {
    console.log('Načítání šablony korelačního nástroje');

    // Vložení šablony přímo do kontejneru (místo načítání pomocí fetch)
    const templateHTML = `
<!-- Šablona pro modul korelačního nástroje -->
<div class="correlation-container">
    <!-- Ovládací prvky -->
    <div class="correlation-controls">
        <div class="correlation-controls-left">
            <button type="button" class="btn-primary add-entity">
                <i class="fas fa-plus"></i> Přidat entitu
            </button>
            <button type="button" class="btn-primary add-relationship">
                <i class="fas fa-link"></i> Přidat vztah
            </button>
            <button type="button" class="btn-primary import-data">
                <i class="fas fa-file-import"></i> Importovat data
            </button>
        </div>
        <div class="correlation-controls-right">
            <div class="view-mode-selector">
                <button type="button" class="view-mode active" data-view="network">
                    <i class="fas fa-project-diagram"></i> Síť
                </button>
                <button type="button" class="view-mode" data-view="timeline">
                    <i class="fas fa-clock"></i> Časová osa
                </button>
                <button type="button" class="view-mode" data-view="map">
                    <i class="fas fa-map-marked-alt"></i> Mapa
                </button>
                <button type="button" class="view-mode" data-view="matrix">
                    <i class="fas fa-table"></i> Matice
                </button>
            </div>
            <button type="button" class="btn-primary export-data">
                <i class="fas fa-file-export"></i> Exportovat
            </button>
        </div>
    </div>

    <!-- Filtry -->
    <div class="correlation-filters">
        <div class="correlation-filter">
            <label for="filter-entity-type">Typy entit</label>
            <select id="filter-entity-type" class="filter-entity-type" multiple>
                <option value="person">Osoby</option>
                <option value="organization">Organizace</option>
                <option value="location">Lokace</option>
                <option value="event">Události</option>
                <option value="asset">Majetek</option>
                <option value="digital">Digitální entity</option>
            </select>
        </div>
        <div class="correlation-filter">
            <label for="filter-relationship-type">Typy vztahů</label>
            <select id="filter-relationship-type" class="filter-relationship-type" multiple>
                <option value="family">Rodinné</option>
                <option value="business">Obchodní</option>
                <option value="social">Sociální</option>
                <option value="ownership">Vlastnictví</option>
                <option value="communication">Komunikace</option>
                <option value="transaction">Transakce</option>
            </select>
        </div>
        <div class="correlation-filter">
            <label>Časové období</label>
            <div class="date-range-picker">
                <input type="date" class="filter-date-from" placeholder="Od">
                <span>-</span>
                <input type="date" class="filter-date-to" placeholder="Do">
            </div>
        </div>
        <div class="correlation-filter">
            <label for="filter-search">Vyhledávání</label>
            <input type="text" id="filter-search" class="filter-search" placeholder="Hledat...">
        </div>
        <div class="correlation-filter-buttons">
            <button type="button" class="btn-primary apply-filters">
                <i class="fas fa-filter"></i> Aplikovat filtry
            </button>
            <button type="button" class="btn-secondary reset-filters">
                <i class="fas fa-undo"></i> Reset
            </button>
        </div>
    </div>

    <!-- Vizualizace -->
    <div class="correlation-visualization">
        <!-- Obsah bude generován dynamicky -->
        <div class="visualization-empty-state">
            <i class="fas fa-project-diagram"></i>
            <p>Nejsou k dispozici žádná data pro vizualizaci. Přidejte entity a vztahy nebo importujte data z jiných modulů.</p>
            <button type="button" class="btn-primary import-data">
                <i class="fas fa-file-import"></i> Importovat data
            </button>
        </div>
    </div>

    <!-- Panel analýzy -->
    <div class="correlation-analysis-panel">
        <div class="analysis-header">
            <h4>Analýza dat</h4>
            <div class="analysis-header-controls">
                <button type="button" class="btn-primary run-analysis">
                    <i class="fas fa-play"></i> Spustit analýzu
                </button>
                <button type="button" class="toggle-analysis-panel">
                    <i class="fas fa-chevron-up"></i>
                </button>
            </div>
        </div>
        <div class="analysis-content">
            <div class="analysis-tabs">
                <button type="button" class="analysis-tab active" data-tab="centrality">Centralita</button>
                <button type="button" class="analysis-tab" data-tab="communities">Komunity</button>
                <button type="button" class="analysis-tab" data-tab="anomalies">Anomálie</button>
                <button type="button" class="analysis-tab" data-tab="predictions">Predikce</button>
            </div>
            <div class="analysis-tab-content active" id="centrality-tab">
                <div class="analysis-placeholder">
                    <i class="fas fa-info-circle"></i>
                    <p>Spusťte analýzu pro zobrazení výsledků centrality.</p>
                </div>
            </div>
            <div class="analysis-tab-content" id="communities-tab">
                <div class="analysis-placeholder">
                    <i class="fas fa-info-circle"></i>
                    <p>Spusťte analýzu pro zobrazení výsledků komunit.</p>
                </div>
            </div>
            <div class="analysis-tab-content" id="anomalies-tab">
                <div class="analysis-placeholder">
                    <i class="fas fa-info-circle"></i>
                    <p>Spusťte analýzu pro zobrazení výsledků anomálií.</p>
                </div>
            </div>
            <div class="analysis-tab-content" id="predictions-tab">
                <div class="analysis-placeholder">
                    <i class="fas fa-info-circle"></i>
                    <p>Spusťte analýzu pro zobrazení predikcí.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Fotogalerie -->
    <div class="correlation-gallery-container">
        <h4>Fotodokumentace</h4>
        <div class="photo-gallery">
            <div class="photo-gallery-header">
                <div class="photo-gallery-title">Fotografie</div>
                <button type="button" class="photo-gallery-add">
                    <i class="fas fa-plus"></i> Přidat fotografii
                </button>
            </div>
            <div class="photo-gallery-content">
                <div class="photo-gallery-empty">
                    <i class="fas fa-images"></i>
                    <p>Žádné fotografie. Přidejte fotografie pomocí tlačítka výše nebo přetažením souborů do této oblasti.</p>
                </div>
                <div class="photo-gallery-grid">
                    <!-- Fotografie budou přidány dynamicky -->
                </div>
            </div>
        </div>
    </div>

    <!-- Notifikace -->
    <div class="correlation-notifications">
        <h4>Nastavení notifikací</h4>
        <div class="notification-settings">
            <div class="form-group">
                <label for="correlation-alert-email">E-mailová adresa pro notifikace</label>
                <input type="email" id="correlation-alert-email" class="form-control" placeholder="<EMAIL>">
            </div>
            <div class="form-group">
                <label for="correlation-alert-frequency">Frekvence notifikací</label>
                <select id="correlation-alert-frequency" class="form-control">
                    <option value="realtime">V reálném čase</option>
                    <option value="daily">Denně</option>
                    <option value="weekly">Týdně</option>
                </select>
            </div>
            <div class="form-group">
                <label>Typy notifikací</label>
                <div class="notification-checkboxes">
                    <label><input type="checkbox" value="new-entity" checked> Nové entity</label>
                    <label><input type="checkbox" value="new-relationship" checked> Nové vztahy</label>
                    <label><input type="checkbox" value="anomalies" checked> Anomálie</label>
                    <label><input type="checkbox" value="predictions" checked> Predikce</label>
                </div>
            </div>
            <div class="notification-buttons">
                <button type="button" class="btn-primary save-notification-settings">
                    <i class="fas fa-save"></i> Uložit nastavení
                </button>
                <button type="button" class="btn-secondary test-email-alert">
                    <i class="fas fa-paper-plane"></i> Test
                </button>
            </div>
        </div>
    </div>
</div>`;

    // Vložení šablony do kontejneru
    container.innerHTML = templateHTML;

    try {
        // Inicializace ovládacích prvků
        initCorrelationControls(container);

        // Inicializace vizualizace
        initCorrelationVisualization(container);

        // Inicializace panelu detailů
        initCorrelationDetailsPanel(container);

        // Inicializace analytických nástrojů
        initCorrelationAnalytics(container);

        // Inicializace fotogalerie
        initCorrelationPhotoGallery(container);

        // Inicializace notifikací
        initCorrelationNotifications(container);

        console.log('Šablona korelačního nástroje byla úspěšně načtena');
    } catch (error) {
        console.error('Chyba při inicializaci korelačního nástroje:', error);

        // Zobrazení chybové zprávy
        container.innerHTML = `
            <div class="correlation-error">
                <i class="fas fa-exclamation-triangle"></i>
                <p>Nepodařilo se inicializovat korelační nástroj. Zkuste stránku obnovit.</p>
                <p>Chyba: ${error.message}</p>
            </div>
        `;
    }
}

// Inicializace při načtení stránky
document.addEventListener('DOMContentLoaded', function() {
    console.log('Inicializace korelačního nástroje');

    // Inicializace korelačního nástroje
    initCorrelationTool();
});
