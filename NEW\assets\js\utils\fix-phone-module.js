/**
 * Oprava modulu telefonní analýzy - přid<PERSON>í ikony koše
 */

// P<PERSON><PERSON>mé přidání event listenerů na dokument
document.addEventListener('click', function(event) {
    // Naj<PERSON>t tla<PERSON>, na které bylo kliknuto
    const button = event.target.closest('.module-action-btn');
    if (!button) return;

    // Najít modul, ke kterému tlačítko patří
    const moduleElement = button.closest('.module');
    if (!moduleElement) return;

    // Kontrola, zda jde o modul telefonní analýzy
    const header = moduleElement.querySelector('.module-header');
    if (!header) return;

    const moduleIcon = header.querySelector('.module-icon i');
    const moduleTitle = header.querySelector('h3');

    if (!moduleIcon || !moduleTitle ||
        !moduleIcon.className.includes('fa-phone') ||
        !moduleTitle.textContent.includes('Telefonní čísla')) {
        return;
    }

    // Zastavení propagace události
    event.stopPropagation();
    event.preventDefault();

    // Zpracování kliknutí podle typu tlačítka
    if (button.classList.contains('delete')) {
        console.log('Kliknuto na tlačítko smazání v modulu telefonní analýzy');
        if (confirm('Opravdu chcete odstranit tento modul?')) {
            moduleElement.remove();
            if (typeof updateTableOfContents === 'function') {
                updateTableOfContents();
            }
        }
    } else if (button.classList.contains('move-up')) {
        console.log('Kliknuto na tlačítko posun nahoru v modulu telefonní analýzy');
        const prevModule = moduleElement.previousElementSibling;
        if (prevModule && !prevModule.classList.contains('add-module-container')) {
            moduleElement.parentNode.insertBefore(moduleElement, prevModule);
            if (typeof updateTableOfContents === 'function') {
                updateTableOfContents();
            }
        }
    } else if (button.classList.contains('move-down')) {
        console.log('Kliknuto na tlačítko posun dolů v modulu telefonní analýzy');
        const nextModule = moduleElement.nextElementSibling;
        if (nextModule) {
            moduleElement.parentNode.insertBefore(nextModule, moduleElement);
            if (typeof updateTableOfContents === 'function') {
                updateTableOfContents();
            }
        }
    }
}, true); // Použití capture fáze, aby se event listener spustil před ostatními

// Funkce pro opravu modulů telefonní analýzy
function fixPhoneModules() {
    console.log('Spouštím opravu modulů telefonní analýzy');

    // Najít všechny moduly telefonní analýzy
    const phoneModules = Array.from(document.querySelectorAll('.module')).filter(module => {
        const header = module.querySelector('.module-header');
        if (!header) return false;

        const moduleIcon = header.querySelector('.module-icon i');
        const moduleTitle = header.querySelector('h3');

        return moduleIcon && moduleTitle &&
               moduleIcon.className.includes('fa-phone') &&
               moduleTitle.textContent.includes('Telefonní čísla');
    });

    console.log(`Nalezeno ${phoneModules.length} modulů telefonní analýzy`);

    // Opravit každý modul
    phoneModules.forEach((module, index) => {
        console.log(`Opravuji modul telefonní analýzy #${index + 1}`);

        // KOMPLETNÍ PŘESTAVBA MODULU
        // Vytvoření nové hlavičky
        const oldHeader = module.querySelector('.module-header');
        if (!oldHeader) return;

        // Získat titulek modulu
        const moduleTitle = oldHeader.querySelector('h3')?.textContent || 'Telefonní čísla';

        // Vytvořit novou hlavičku
        const newHeader = document.createElement('div');
        newHeader.className = 'module-header';
        newHeader.style.display = 'flex';
        newHeader.style.alignItems = 'center';
        newHeader.style.padding = '10px 15px';
        newHeader.style.backgroundColor = '#f8f9fa';
        newHeader.style.borderBottom = '1px solid #e9ecef';
        newHeader.style.position = 'relative';

        // Přidat ikonu modulu
        const iconDiv = document.createElement('div');
        iconDiv.className = 'module-icon';
        iconDiv.innerHTML = '<i class="fas fa-phone"></i>';
        iconDiv.style.marginRight = '10px';
        newHeader.appendChild(iconDiv);

        // Přidat titulek modulu
        const titleElement = document.createElement('h3');
        titleElement.textContent = moduleTitle;
        titleElement.style.margin = '0';
        titleElement.style.fontSize = '16px';
        titleElement.style.fontWeight = '600';
        newHeader.appendChild(titleElement);

        // Přidat kontejner pro akce
        const actionsContainer = document.createElement('div');
        actionsContainer.className = 'module-actions';
        actionsContainer.style.display = 'flex';
        actionsContainer.style.marginLeft = 'auto';
        actionsContainer.style.zIndex = '1000';

        // Přidat tlačítka pro posun nahoru/dolů a smazání
        // Tlačítko pro posun nahoru
        const moveUpButton = document.createElement('button');
        moveUpButton.type = 'button';
        moveUpButton.className = 'module-action-btn move-up';
        moveUpButton.title = 'Posunout nahoru';
        moveUpButton.innerHTML = '<i class="fas fa-arrow-up"></i>';
        moveUpButton.style.background = 'none';
        moveUpButton.style.border = 'none';
        moveUpButton.style.color = '#6c757d';
        moveUpButton.style.fontSize = '14px';
        moveUpButton.style.cursor = 'pointer';
        moveUpButton.style.padding = '5px';
        moveUpButton.style.marginLeft = '5px';
        moveUpButton.style.borderRadius = '3px';
        moveUpButton.style.zIndex = '1001';
        actionsContainer.appendChild(moveUpButton);

        // Tlačítko pro posun dolů
        const moveDownButton = document.createElement('button');
        moveDownButton.type = 'button';
        moveDownButton.className = 'module-action-btn move-down';
        moveDownButton.title = 'Posunout dolů';
        moveDownButton.innerHTML = '<i class="fas fa-arrow-down"></i>';
        moveDownButton.style.background = 'none';
        moveDownButton.style.border = 'none';
        moveDownButton.style.color = '#6c757d';
        moveDownButton.style.fontSize = '14px';
        moveDownButton.style.cursor = 'pointer';
        moveDownButton.style.padding = '5px';
        moveDownButton.style.marginLeft = '5px';
        moveDownButton.style.borderRadius = '3px';
        moveDownButton.style.zIndex = '1001';
        actionsContainer.appendChild(moveDownButton);

        // Tlačítko pro smazání
        const deleteButton = document.createElement('button');
        deleteButton.type = 'button';
        deleteButton.className = 'module-action-btn delete';
        deleteButton.title = 'Odstranit modul';
        deleteButton.innerHTML = '<i class="fas fa-trash"></i>';
        deleteButton.style.background = 'none';
        deleteButton.style.border = 'none';
        deleteButton.style.color = '#dc3545';
        deleteButton.style.fontSize = '14px';
        deleteButton.style.cursor = 'pointer';
        deleteButton.style.padding = '5px';
        deleteButton.style.marginLeft = '5px';
        deleteButton.style.borderRadius = '3px';
        deleteButton.style.zIndex = '1001';
        actionsContainer.appendChild(deleteButton);

        newHeader.appendChild(actionsContainer);

        // Nahradit starou hlavičku novou
        oldHeader.parentNode.replaceChild(newHeader, oldHeader);

        // Přidat event listenery pro tlačítka
        moveUpButton.onclick = function(event) {
            event.stopPropagation();
            event.preventDefault();
            console.log('Kliknuto na tlačítko posun nahoru');
            const prevModule = module.previousElementSibling;
            if (prevModule && !prevModule.classList.contains('add-module-container')) {
                module.parentNode.insertBefore(module, prevModule);
                if (typeof updateTableOfContents === 'function') {
                    updateTableOfContents();
                }
            }
            return false;
        };

        moveDownButton.onclick = function(event) {
            event.stopPropagation();
            event.preventDefault();
            console.log('Kliknuto na tlačítko posun dolů');
            const nextModule = module.nextElementSibling;
            if (nextModule) {
                module.parentNode.insertBefore(nextModule, module);
                if (typeof updateTableOfContents === 'function') {
                    updateTableOfContents();
                }
            }
            return false;
        };

        deleteButton.onclick = function(event) {
            event.stopPropagation();
            event.preventDefault();
            console.log('Kliknuto na tlačítko smazání');
            if (confirm('Opravdu chcete odstranit tento modul?')) {
                module.remove();
                if (typeof updateTableOfContents === 'function') {
                    updateTableOfContents();
                }
            }
            return false;
        };
    });
}

// Spustit opravu po načtení stránky
document.addEventListener('DOMContentLoaded', function() {
    // Počkat na načtení všech ostatních skriptů
    setTimeout(fixPhoneModules, 1000);

    // Pro jistotu spustit opravu ještě jednou po delší době
    setTimeout(fixPhoneModules, 3000);
});

// Přidat opravu i při kliknutí na stránku (pro případ, že se moduly přidají dynamicky)
document.addEventListener('click', function() {
    setTimeout(fixPhoneModules, 500);
});
