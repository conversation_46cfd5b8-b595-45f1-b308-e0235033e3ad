/**
 * <PERSON><PERSON><PERSON> osa - pomo<PERSON><PERSON><PERSON>
 */

/**
 * <PERSON><PERSON>id<PERSON>í události do časové osy
 * @param {Object} event - Událost k přidání
 */
function addEvent(event) {
    console.log('Přidání události do časové osy:', event);
    
    // Přidání události do pole událostí
    timelineEvents.push(event);
    
    // Seřazení událostí podle data
    timelineEvents.sort((a, b) => new Date(a.datetime) - new Date(b.datetime));
    
    // Uložení dat
    saveTimelineData();
    
    // Překreslení časové osy
    renderTimelineEvents();
    
    // Zobrazení notifikace
    showNotification('Událost byla úspěšně přidána.', 'success');
}

/**
 * Úprava události v časové ose
 * @param {string} eventId - ID události
 * @param {Object} updatedEvent - Aktualizovaná událost
 */
function updateEvent(eventId, updatedEvent) {
    console.log('Úprava události v časové ose:', eventId, updatedEvent);
    
    // Najít index události podle ID
    const eventIndex = timelineEvents.findIndex(e => e.id === eventId);
    if (eventIndex === -1) {
        console.error('Událost nebyla nalezena:', eventId);
        return;
    }
    
    // Aktualizace události
    timelineEvents[eventIndex] = {
        ...timelineEvents[eventIndex],
        ...updatedEvent
    };
    
    // Seřazení událostí podle data
    timelineEvents.sort((a, b) => new Date(a.datetime) - new Date(b.datetime));
    
    // Uložení dat
    saveTimelineData();
    
    // Překreslení časové osy
    renderTimelineEvents();
    
    // Zobrazení notifikace
    showNotification('Událost byla úspěšně upravena.', 'success');
}

/**
 * Odstranění události z časové osy
 * @param {string} eventId - ID události
 */
function deleteEvent(eventId) {
    console.log('Odstranění události z časové osy:', eventId);
    
    // Potvrzení odstranění
    if (!confirm('Opravdu chcete odstranit tuto událost?')) {
        return;
    }
    
    // Najít index události podle ID
    const eventIndex = timelineEvents.findIndex(e => e.id === eventId);
    if (eventIndex === -1) {
        console.error('Událost nebyla nalezena:', eventId);
        return;
    }
    
    // Odstranění události
    timelineEvents.splice(eventIndex, 1);
    
    // Uložení dat
    saveTimelineData();
    
    // Překreslení časové osy
    renderTimelineEvents();
    
    // Zobrazení notifikace
    showNotification('Událost byla úspěšně odstraněna.', 'success');
}

/**
 * Zobrazení dialogu pro úpravu události
 * @param {string} eventId - ID události
 */
function showEditEventDialog(eventId) {
    console.log('Zobrazení dialogu pro úpravu události:', eventId);
    
    // Najít událost podle ID
    const event = timelineEvents.find(e => e.id === eventId);
    if (!event) {
        console.error('Událost nebyla nalezena:', eventId);
        return;
    }
    
    // Vytvoření dialogu
    const dialog = document.createElement('div');
    dialog.className = 'edit-event-dialog';
    
    // Formátování data a času pro input
    const datetime = new Date(event.datetime);
    const formattedDatetime = formatDatetimeForInput(datetime);
    
    // Vytvoření obsahu dialogu
    dialog.innerHTML = `
        <div class="edit-event-dialog-content">
            <div class="edit-event-dialog-header">
                <h3>Upravit událost</h3>
                <button type="button" class="edit-event-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="edit-event-dialog-body">
                <div class="form-group">
                    <label>Název události *</label>
                    <input type="text" class="form-control" id="edit-event-title" value="${event.title}" placeholder="Zadejte název události">
                </div>
                <div class="form-group">
                    <label>Popis události</label>
                    <textarea class="form-control" id="edit-event-description" rows="3" placeholder="Zadejte popis události">${event.description || ''}</textarea>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Datum a čas *</label>
                        <input type="datetime-local" class="form-control" id="edit-event-datetime" value="${formattedDatetime}">
                    </div>
                    <div class="form-group">
                        <label>Typ události</label>
                        <select class="form-control" id="edit-event-type">
                            <option value="communication" ${event.type === 'communication' ? 'selected' : ''}>Komunikace</option>
                            <option value="movement" ${event.type === 'movement' ? 'selected' : ''}>Pohyb</option>
                            <option value="financial" ${event.type === 'financial' ? 'selected' : ''}>Finanční transakce</option>
                            <option value="social" ${event.type === 'social' ? 'selected' : ''}>Sociální aktivita</option>
                            <option value="legal" ${event.type === 'legal' ? 'selected' : ''}>Právní událost</option>
                            <option value="other" ${event.type === 'other' ? 'selected' : ''}>Ostatní</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Zdroj události</label>
                        <select class="form-control" id="edit-event-source">
                            <option value="manual" ${event.source === 'manual' ? 'selected' : ''}>Manuálně přidáno</option>
                            <option value="social" ${event.source === 'social' ? 'selected' : ''}>Sociální sítě</option>
                            <option value="financial" ${event.source === 'financial' ? 'selected' : ''}>Finanční monitoring</option>
                            <option value="communication" ${event.source === 'communication' ? 'selected' : ''}>Komunikační platformy</option>
                            <option value="location" ${event.source === 'location' ? 'selected' : ''}>Lokační data</option>
                            <option value="other" ${event.source === 'other' ? 'selected' : ''}>Ostatní</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Důležitost</label>
                        <select class="form-control" id="edit-event-importance">
                            <option value="low" ${event.importance === 'low' ? 'selected' : ''}>Nízká</option>
                            <option value="medium" ${event.importance === 'medium' ? 'selected' : ''}>Střední</option>
                            <option value="high" ${event.importance === 'high' ? 'selected' : ''}>Vysoká</option>
                            <option value="critical" ${event.importance === 'critical' ? 'selected' : ''}>Kritická</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label>Lokace</label>
                    <input type="text" class="form-control" id="edit-event-location" value="${event.location || ''}" placeholder="Zadejte lokaci události">
                </div>
                <div class="form-group">
                    <label>Související osoby</label>
                    <input type="text" class="form-control" id="edit-event-related-persons" value="${event.relatedPersons ? event.relatedPersons.join(', ') : ''}" placeholder="Zadejte související osoby oddělené čárkou">
                </div>
                <div class="form-group">
                    <label>Tagy</label>
                    <input type="text" class="form-control" id="edit-event-tags" value="${event.tags ? event.tags.join(', ') : ''}" placeholder="Zadejte tagy oddělené čárkou">
                </div>
                <div class="form-group">
                    <label>Poznámky</label>
                    <textarea class="form-control" id="edit-event-notes" rows="2" placeholder="Zadejte poznámky k události">${event.notes || ''}</textarea>
                </div>
            </div>
            <div class="edit-event-dialog-footer">
                <button type="button" class="btn-secondary edit-event-dialog-cancel">Zrušit</button>
                <button type="button" class="btn-primary edit-event-dialog-save" data-event-id="${event.id}">Uložit</button>
            </div>
        </div>
    `;
    
    // Přidání dialogu do stránky
    document.body.appendChild(dialog);
    
    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.edit-event-dialog-close').addEventListener('click', () => dialog.remove());
    dialog.querySelector('.edit-event-dialog-cancel').addEventListener('click', () => dialog.remove());
    
    dialog.querySelector('.edit-event-dialog-save').addEventListener('click', function() {
        // Získání hodnot z formuláře
        const title = dialog.querySelector('#edit-event-title').value.trim();
        const description = dialog.querySelector('#edit-event-description').value.trim();
        const datetime = dialog.querySelector('#edit-event-datetime').value;
        const type = dialog.querySelector('#edit-event-type').value;
        const source = dialog.querySelector('#edit-event-source').value;
        const importance = dialog.querySelector('#edit-event-importance').value;
        const location = dialog.querySelector('#edit-event-location').value.trim();
        const relatedPersons = dialog.querySelector('#edit-event-related-persons').value.trim();
        const tags = dialog.querySelector('#edit-event-tags').value.trim();
        const notes = dialog.querySelector('#edit-event-notes').value.trim();
        
        // Validace povinných polí
        if (!title) {
            alert('Zadejte název události.');
            return;
        }
        
        if (!datetime) {
            alert('Zadejte datum a čas události.');
            return;
        }
        
        // Vytvoření aktualizované události
        const updatedEvent = {
            title: title,
            description: description,
            datetime: datetime,
            type: type,
            source: source,
            importance: importance,
            location: location,
            relatedPersons: relatedPersons ? relatedPersons.split(',').map(p => p.trim()) : [],
            tags: tags ? tags.split(',').map(t => t.trim()) : [],
            notes: notes
        };
        
        // Aktualizace události
        const eventId = this.getAttribute('data-event-id');
        updateEvent(eventId, updatedEvent);
        
        // Zavření dialogu
        dialog.remove();
    });
    
    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}

/**
 * Přidání události do galerie
 * @param {string} eventId - ID události
 */
function addEventToGallery(eventId) {
    console.log('Přidání události do galerie:', eventId);
    
    // Najít událost podle ID
    const event = timelineEvents.find(e => e.id === eventId);
    if (!event) {
        console.error('Událost nebyla nalezena:', eventId);
        return;
    }
    
    // Najít galerii
    const timelineModule = document.querySelector('.module[id^="module-casova-osa"]');
    if (!timelineModule) return;
    
    const gallery = timelineModule.querySelector('.timeline-photo-gallery');
    if (!gallery) return;
    
    // Vytvoření názvu pro fotografii
    const photoTitle = `${event.title} - ${formatDate(new Date(event.datetime))}`;
    
    // Zobrazení dialogu pro výběr obrázku
    const dialog = document.createElement('div');
    dialog.className = 'add-to-gallery-dialog';
    
    // Vytvoření obsahu dialogu
    dialog.innerHTML = `
        <div class="add-to-gallery-dialog-content">
            <div class="add-to-gallery-dialog-header">
                <h3>Přidat do galerie</h3>
                <button type="button" class="add-to-gallery-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="add-to-gallery-dialog-body">
                <p>Vyberte způsob přidání obrázku:</p>
                <div class="add-to-gallery-options">
                    <div class="add-to-gallery-option" data-option="url">
                        <i class="fas fa-link"></i>
                        <span>URL adresa</span>
                    </div>
                    <div class="add-to-gallery-option" data-option="upload">
                        <i class="fas fa-upload"></i>
                        <span>Nahrát soubor</span>
                    </div>
                    <div class="add-to-gallery-option" data-option="paste">
                        <i class="fas fa-paste"></i>
                        <span>Vložit ze schránky</span>
                    </div>
                </div>
                <div class="add-to-gallery-details" style="display: none;">
                    <!-- Zde bude zobrazen obsah podle vybrané možnosti -->
                </div>
            </div>
            <div class="add-to-gallery-dialog-footer">
                <button type="button" class="btn-secondary add-to-gallery-dialog-cancel">Zrušit</button>
                <button type="button" class="btn-primary add-to-gallery-dialog-add" disabled>Přidat</button>
            </div>
        </div>
    `;
    
    // Přidání dialogu do stránky
    document.body.appendChild(dialog);
    
    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.add-to-gallery-dialog-close').addEventListener('click', () => dialog.remove());
    dialog.querySelector('.add-to-gallery-dialog-cancel').addEventListener('click', () => dialog.remove());
    
    // Přidání event listenerů pro možnosti
    const options = dialog.querySelectorAll('.add-to-gallery-option');
    options.forEach(option => {
        option.addEventListener('click', function() {
            // Odstranění aktivní třídy ze všech možností
            options.forEach(opt => opt.classList.remove('active'));
            
            // Přidání aktivní třídy na kliknutou možnost
            this.classList.add('active');
            
            // Zobrazení detailů
            const optionType = this.getAttribute('data-option');
            showAddToGalleryDetails(dialog, optionType, photoTitle);
            
            // Povolení tlačítka pro přidání
            dialog.querySelector('.add-to-gallery-dialog-add').disabled = false;
        });
    });
    
    // Přidání event listeneru pro tlačítko přidání
    dialog.querySelector('.add-to-gallery-dialog-add').addEventListener('click', function() {
        const activeOption = dialog.querySelector('.add-to-gallery-option.active');
        if (activeOption) {
            const optionType = activeOption.getAttribute('data-option');
            addToGalleryFromDialog(dialog, optionType, gallery, photoTitle, event);
        }
    });
    
    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}

/**
 * Zobrazení detailů pro přidání do galerie
 * @param {HTMLElement} dialog - Element dialogu
 * @param {string} optionType - Typ možnosti (url, upload, paste)
 * @param {string} photoTitle - Název fotografie
 */
function showAddToGalleryDetails(dialog, optionType, photoTitle) {
    const detailsContainer = dialog.querySelector('.add-to-gallery-details');
    if (!detailsContainer) return;
    
    // Zobrazení detailů
    detailsContainer.style.display = 'block';
    
    // Obsah podle typu možnosti
    let detailsContent = '';
    
    switch (optionType) {
        case 'url':
            detailsContent = `
                <div class="form-group">
                    <label>URL adresa obrázku</label>
                    <input type="text" class="form-control" id="gallery-image-url" placeholder="Zadejte URL adresu obrázku">
                </div>
                <div class="form-group">
                    <label>Název obrázku</label>
                    <input type="text" class="form-control" id="gallery-image-title" value="${photoTitle}" placeholder="Zadejte název obrázku">
                </div>
            `;
            break;
        case 'upload':
            detailsContent = `
                <div class="form-group">
                    <label>Vyberte soubor</label>
                    <input type="file" class="form-control" id="gallery-image-file" accept="image/*">
                </div>
                <div class="form-group">
                    <label>Název obrázku</label>
                    <input type="text" class="form-control" id="gallery-image-title" value="${photoTitle}" placeholder="Zadejte název obrázku">
                </div>
            `;
            break;
        case 'paste':
            detailsContent = `
                <div class="form-group">
                    <label>Vložte obrázek ze schránky (Ctrl+V)</label>
                    <div class="paste-area" id="gallery-paste-area">
                        <i class="fas fa-paste"></i>
                        <p>Klikněte sem a stiskněte Ctrl+V pro vložení obrázku</p>
                    </div>
                </div>
                <div class="form-group">
                    <label>Název obrázku</label>
                    <input type="text" class="form-control" id="gallery-image-title" value="${photoTitle}" placeholder="Zadejte název obrázku">
                </div>
            `;
            break;
    }
    
    detailsContainer.innerHTML = detailsContent;
    
    // Přidání event listenerů pro vložení ze schránky
    if (optionType === 'paste') {
        const pasteArea = detailsContainer.querySelector('#gallery-paste-area');
        if (pasteArea) {
            pasteArea.addEventListener('click', function() {
                this.focus();
            });
            
            pasteArea.addEventListener('paste', function(event) {
                handlePasteInDialog(event, pasteArea);
            });
        }
    }
}

/**
 * Přidání obrázku do galerie z dialogu
 * @param {HTMLElement} dialog - Element dialogu
 * @param {string} optionType - Typ možnosti (url, upload, paste)
 * @param {HTMLElement} gallery - Element galerie
 * @param {string} photoTitle - Název fotografie
 * @param {Object} event - Událost
 */
function addToGalleryFromDialog(dialog, optionType, gallery, photoTitle, event) {
    // Získání názvu obrázku
    const titleInput = dialog.querySelector('#gallery-image-title');
    const title = titleInput ? titleInput.value.trim() : photoTitle;
    
    // Přidání obrázku podle typu možnosti
    switch (optionType) {
        case 'url':
            const urlInput = dialog.querySelector('#gallery-image-url');
            if (urlInput && urlInput.value.trim()) {
                const url = urlInput.value.trim();
                addPhotoToGallery(gallery, url, title);
                
                // Přidání média k události
                addMediaToEvent(event.id, url, title);
                
                // Zavření dialogu
                dialog.remove();
            } else {
                alert('Zadejte URL adresu obrázku.');
            }
            break;
        case 'upload':
            const fileInput = dialog.querySelector('#gallery-image-file');
            if (fileInput && fileInput.files.length > 0) {
                const file = fileInput.files[0];
                processPhotoForGallery(file, gallery, title);
                
                // Zavření dialogu
                dialog.remove();
            } else {
                alert('Vyberte soubor.');
            }
            break;
        case 'paste':
            const pasteArea = dialog.querySelector('#gallery-paste-area');
            if (pasteArea && pasteArea.querySelector('img')) {
                const img = pasteArea.querySelector('img');
                const url = img.src;
                addPhotoToGallery(gallery, url, title);
                
                // Přidání média k události
                addMediaToEvent(event.id, url, title);
                
                // Zavření dialogu
                dialog.remove();
            } else {
                alert('Vložte obrázek ze schránky.');
            }
            break;
    }
}
