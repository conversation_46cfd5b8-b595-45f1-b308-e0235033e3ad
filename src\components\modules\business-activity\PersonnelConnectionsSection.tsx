"use client";

import { useState } from "react";
import { useFieldArray, Control } from "react-hook-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Network, PlusCircle, Trash2, Users, Building, Star } from "lucide-react";
import { FormItemRHF } from "./FormComponents";
import { PersonnelConnection } from "./schemas";
import { cn } from "@/lib/utils";
import type { BusinessActivityFormValues } from "./BusinessActivityForm";

interface PersonnelConnectionsSectionProps {
  control: Control<BusinessActivityFormValues>;
}

export function PersonnelConnectionsSection({ control }: PersonnelConnectionsSectionProps) {
  const [expanded, setExpanded] = useState(false);

  const { fields, append, remove } = useFieldArray({
    control,
    name: "personnelConnectionRecords",
  });

  const handleAddConnection = () => {
    append({
      id: crypto.randomUUID(),
      personName: "",
      relationship: "",
      isSignificant: false,
    });
  };

  return (
    <Card className={cn(expanded ? "" : "hover:border-primary/50 cursor-pointer transition-all")}>
      <CardHeader 
        className="flex flex-row items-center justify-between"
        onClick={() => setExpanded(!expanded)}
      >
        <CardTitle className="text-lg flex items-center">
          <Network className="mr-2 h-5 w-5" />
          Personální propojení
        </CardTitle>
        <Badge variant={fields.length > 0 ? "default" : "outline"}>
          {fields.length} propojení
        </Badge>
      </CardHeader>
      {expanded && (
        <CardContent className="space-y-6 pt-0">
          <FormItemRHF
            label="Obecné informace o personálních propojeních"
            name="personnelConnections"
            control={control}
            as="textarea"
            rows={3}
            placeholder="Obecné informace o personálních propojeních subjektu..."
          />

          {fields.map((item, index) => (
            <Card key={item.id} className="p-4 shadow-sm bg-card-foreground/5 relative">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => remove(index)}
                className="absolute top-2 right-2 text-destructive hover:bg-destructive/10"
              >
                <Trash2 className="h-5 w-5" />
              </Button>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormItemRHF
                  label="Jméno osoby"
                  name={`personnelConnectionRecords.${index}.personName`}
                  control={control}
                  placeholder="Jméno propojené osoby"
                />
                <FormItemRHF
                  label="Vztah"
                  name={`personnelConnectionRecords.${index}.relationship`}
                  control={control}
                  placeholder="Např. obchodní partner, spolupracovník..."
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormItemRHF
                  label="Společnost"
                  name={`personnelConnectionRecords.${index}.company`}
                  control={control}
                  placeholder="Společnost, ve které působí"
                />
                <FormItemRHF
                  label="Pozice"
                  name={`personnelConnectionRecords.${index}.position`}
                  control={control}
                  placeholder="Pozice v dané společnosti"
                />
              </div>

              <div className="flex items-center mb-4">
                <FormItemRHF
                  label="Významné propojení"
                  name={`personnelConnectionRecords.${index}.isSignificant`}
                  control={control}
                  as="switch"
                />
                <div className="ml-2 text-sm text-muted-foreground">
                  Označte, pokud jde o významné propojení s vysokou důležitostí
                </div>
              </div>

              <FormItemRHF
                label="Popis propojení"
                name={`personnelConnectionRecords.${index}.description`}
                control={control}
                as="textarea"
                rows={3}
                placeholder="Detailní popis personálního propojení..."
              />

              <FormItemRHF
                label="Poznámky"
                name={`personnelConnectionRecords.${index}.notes`}
                control={control}
                as="textarea"
                rows={2}
                placeholder="Další poznámky k personálnímu propojení..."
              />
            </Card>
          ))}

          <Button
            type="button"
            variant="outline"
            onClick={handleAddConnection}
            className="w-full"
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Přidat personální propojení
          </Button>
        </CardContent>
      )}
    </Card>
  );
}
