/**
 * Monitoring komunikačních platforem - pomocn<PERSON>
 */

/**
 * Formátování data
 * @param {string} dateString - Datum ve formátu ISO
 * @returns {string} - Formátované datum
 */
function formatDate(dateString) {
    if (!dateString) return 'Neznámé datum';
    
    const date = new Date(dateString);
    
    // Kontrola, zda je datum platné
    if (isNaN(date.getTime())) return 'Neplatné datum';
    
    // Formátování data
    return date.toLocaleString('cs-CZ', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * Získání názvu kategorie
 * @param {string} category - Kód kategorie
 * @returns {string} - Název kategorie
 */
function getCategoryName(category) {
    const categories = {
        'news': 'Zpravodajství',
        'politics': 'Politika',
        'technology': 'Technologie',
        'finance': 'Finance',
        'entertainment': 'Zábava',
        'sports': 'Sport',
        'education': 'Vzdělávání',
        'other': 'Jiné'
    };
    
    return categories[category] || 'Neznámá kategorie';
}

/**
 * Získání HTML pro badge stavu
 * @param {string} status - Stav (pending, active, error)
 * @returns {string} - HTML pro badge stavu
 */
function getStatusBadge(status) {
    let badgeClass, badgeText;
    
    switch (status) {
        case 'pending':
            badgeClass = 'badge-warning';
            badgeText = 'Čeká na zpracování';
            break;
        case 'active':
            badgeClass = 'badge-success';
            badgeText = 'Aktivní';
            break;
        case 'error':
            badgeClass = 'badge-danger';
            badgeText = 'Chyba';
            break;
        default:
            badgeClass = 'badge-secondary';
            badgeText = 'Neznámý stav';
    }
    
    return `<span class="badge ${badgeClass}">${badgeText}</span>`;
}

/**
 * Získání názvu typu položky podle platformy
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 * @returns {string} - Název typu položky
 */
function getItemTypeName(platform) {
    switch (platform) {
        case 'telegram':
            return 'Telegram kanál';
        case 'discord':
            return 'Discord server';
        case 'whatsapp':
            return 'WhatsApp skupina';
        default:
            return 'Položka';
    }
}

/**
 * Získání HTML obsahu pro záložku zpráv
 * @param {string} itemId - ID kanálu/serveru/skupiny
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 * @returns {string} - HTML obsah pro záložku zpráv
 */
function getMessagesContent(itemId, platform) {
    // Získání zpráv podle platformy
    let messages;
    let users;
    
    switch (platform) {
        case 'telegram':
            messages = messagingPlatformsData.telegram.messages[itemId] || [];
            users = messagingPlatformsData.telegram.users;
            break;
        case 'discord':
            messages = messagingPlatformsData.discord.messages[itemId] || [];
            users = messagingPlatformsData.discord.users;
            break;
        case 'whatsapp':
            messages = messagingPlatformsData.whatsapp.messages[itemId] || [];
            users = messagingPlatformsData.whatsapp.users;
            break;
        default:
            messages = [];
            users = {};
    }
    
    // Kontrola, zda existují zprávy
    if (messages.length === 0) {
        return `
            <div class="no-messages">
                <i class="fas fa-comment-slash"></i>
                <p>Zatím nebyly nalezeny žádné zprávy.</p>
            </div>
        `;
    }
    
    // Vytvoření HTML pro zprávy
    let messagesHtml = '<div class="message-list">';
    
    messages.forEach(message => {
        const user = users[message.userId] || { displayName: 'Neznámý uživatel', avatarUrl: '' };
        
        // Vytvoření HTML pro přílohy
        let attachmentsHtml = '';
        if (message.attachments && message.attachments.length > 0) {
            attachmentsHtml = '<div class="message-attachments">';
            
            message.attachments.forEach(attachment => {
                if (attachment.type === 'image') {
                    attachmentsHtml += `
                        <div class="message-attachment">
                            <img src="${attachment.url}" alt="Příloha">
                            <div class="message-attachment-overlay">
                                <div class="message-attachment-actions">
                                    <button type="button" class="message-attachment-action view-attachment" data-url="${attachment.url}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="message-attachment-action add-to-gallery" data-url="${attachment.url}" data-platform="${platform}" data-item-id="${itemId}" data-message-id="${message.id}">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    attachmentsHtml += `
                        <div class="message-attachment document">
                            <div class="document-icon">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <div class="document-info">
                                <div class="document-name">${attachment.name}</div>
                                <div class="document-size">${formatFileSize(attachment.size)}</div>
                            </div>
                            <div class="message-attachment-overlay">
                                <div class="message-attachment-actions">
                                    <button type="button" class="message-attachment-action download-attachment" data-url="${attachment.url}">
                                        <i class="fas fa-download"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                }
            });
            
            attachmentsHtml += '</div>';
        }
        
        messagesHtml += `
            <div class="message-item" data-message-id="${message.id}">
                <div class="message-header">
                    <div class="message-sender">
                        <div class="message-sender-avatar">
                            ${user.avatarUrl ? `<img src="${user.avatarUrl}" alt="${user.displayName}">` : user.displayName.charAt(0)}
                        </div>
                        ${user.displayName}
                    </div>
                    <div class="message-time">
                        ${formatDate(message.timestamp)}
                    </div>
                </div>
                <div class="message-content">
                    ${message.content}
                </div>
                ${attachmentsHtml}
                <div class="message-actions">
                    <button type="button" class="btn-inline btn-sm add-to-monitoring" data-message-id="${message.id}" data-platform="${platform}" data-item-id="${itemId}">
                        <i class="fas fa-eye"></i> Přidat do monitoringu
                    </button>
                    <button type="button" class="btn-inline btn-sm export-message" data-message-id="${message.id}" data-platform="${platform}" data-item-id="${itemId}">
                        <i class="fas fa-file-export"></i> Exportovat
                    </button>
                </div>
            </div>
        `;
    });
    
    messagesHtml += '</div>';
    
    return messagesHtml;
}

/**
 * Získání HTML obsahu pro záložku uživatelů
 * @param {string} itemId - ID kanálu/serveru/skupiny
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 * @returns {string} - HTML obsah pro záložku uživatelů
 */
function getUsersContent(itemId, platform) {
    // Získání zpráv podle platformy pro identifikaci uživatelů
    let messages;
    let users;
    
    switch (platform) {
        case 'telegram':
            messages = messagingPlatformsData.telegram.messages[itemId] || [];
            users = messagingPlatformsData.telegram.users;
            break;
        case 'discord':
            messages = messagingPlatformsData.discord.messages[itemId] || [];
            users = messagingPlatformsData.discord.users;
            break;
        case 'whatsapp':
            messages = messagingPlatformsData.whatsapp.messages[itemId] || [];
            users = messagingPlatformsData.whatsapp.users;
            break;
        default:
            messages = [];
            users = {};
    }
    
    // Získání unikátních ID uživatelů z zpráv
    const userIds = [...new Set(messages.map(message => message.userId))];
    
    // Kontrola, zda existují uživatelé
    if (userIds.length === 0) {
        return `
            <div class="no-users">
                <i class="fas fa-users-slash"></i>
                <p>Zatím nebyli nalezeni žádní uživatelé.</p>
            </div>
        `;
    }
    
    // Vytvoření HTML pro uživatele
    let usersHtml = '<div class="user-list">';
    
    userIds.forEach(userId => {
        const user = users[userId] || { id: userId, displayName: 'Neznámý uživatel', username: 'unknown', avatarUrl: '' };
        
        // Počet zpráv od uživatele
        const messageCount = messages.filter(message => message.userId === userId).length;
        
        usersHtml += `
            <div class="user-item" data-user-id="${userId}">
                <div class="user-avatar">
                    ${user.avatarUrl ? `<img src="${user.avatarUrl}" alt="${user.displayName}">` : user.displayName.charAt(0)}
                </div>
                <div class="user-info">
                    <div class="user-name">${user.displayName}</div>
                    <div class="user-username">@${user.username}</div>
                    <div class="user-stats">
                        <div class="user-message-count">
                            <i class="fas fa-comment"></i> ${messageCount} zpráv
                        </div>
                    </div>
                </div>
                <div class="user-actions">
                    <button type="button" class="btn-inline btn-sm add-user-to-monitoring" data-user-id="${userId}" data-platform="${platform}">
                        <i class="fas fa-eye"></i> Sledovat
                    </button>
                    <button type="button" class="btn-inline btn-sm view-user-messages" data-user-id="${userId}" data-platform="${platform}" data-item-id="${itemId}">
                        <i class="fas fa-comments"></i> Zprávy
                    </button>
                </div>
            </div>
        `;
    });
    
    usersHtml += '</div>';
    
    return usersHtml;
}

/**
 * Získání HTML obsahu pro záložku médií
 * @param {string} itemId - ID kanálu/serveru/skupiny
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 * @returns {string} - HTML obsah pro záložku médií
 */
function getMediaContent(itemId, platform) {
    // Získání zpráv podle platformy
    let messages;
    
    switch (platform) {
        case 'telegram':
            messages = messagingPlatformsData.telegram.messages[itemId] || [];
            break;
        case 'discord':
            messages = messagingPlatformsData.discord.messages[itemId] || [];
            break;
        case 'whatsapp':
            messages = messagingPlatformsData.whatsapp.messages[itemId] || [];
            break;
        default:
            messages = [];
    }
    
    // Získání všech příloh typu obrázek
    const images = [];
    const documents = [];
    
    messages.forEach(message => {
        if (message.attachments && message.attachments.length > 0) {
            message.attachments.forEach(attachment => {
                if (attachment.type === 'image') {
                    images.push({
                        ...attachment,
                        messageId: message.id,
                        timestamp: message.timestamp
                    });
                } else {
                    documents.push({
                        ...attachment,
                        messageId: message.id,
                        timestamp: message.timestamp
                    });
                }
            });
        }
    });
    
    // Kontrola, zda existují média
    if (images.length === 0 && documents.length === 0) {
        return `
            <div class="no-media">
                <i class="fas fa-photo-video"></i>
                <p>Zatím nebyla nalezena žádná média.</p>
            </div>
        `;
    }
    
    // Vytvoření HTML pro média
    let mediaHtml = '';
    
    // Přidání obrázků
    if (images.length > 0) {
        mediaHtml += `
            <div class="media-section">
                <h5>Obrázky (${images.length})</h5>
                <div class="media-grid">
        `;
        
        images.forEach(image => {
            mediaHtml += `
                <div class="media-item" data-media-id="${image.messageId}-${images.indexOf(image)}">
                    <div class="media-preview">
                        <img src="${image.url}" alt="Obrázek">
                        <div class="media-overlay">
                            <div class="media-actions">
                                <button type="button" class="media-action-btn view-media" data-url="${image.url}">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="media-action-btn add-to-gallery" data-url="${image.url}" data-platform="${platform}" data-item-id="${itemId}" data-message-id="${image.messageId}">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="media-info">
                        <div class="media-date">${formatDate(image.timestamp)}</div>
                    </div>
                </div>
            `;
        });
        
        mediaHtml += `
                </div>
            </div>
        `;
    }
    
    // Přidání dokumentů
    if (documents.length > 0) {
        mediaHtml += `
            <div class="media-section">
                <h5>Dokumenty (${documents.length})</h5>
                <div class="document-list">
        `;
        
        documents.forEach(document => {
            mediaHtml += `
                <div class="document-item" data-document-id="${document.messageId}-${documents.indexOf(document)}">
                    <div class="document-icon">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="document-info">
                        <div class="document-name">${document.name}</div>
                        <div class="document-meta">
                            <span class="document-size">${formatFileSize(document.size)}</span>
                            <span class="document-date">${formatDate(document.timestamp)}</span>
                        </div>
                    </div>
                    <div class="document-actions">
                        <button type="button" class="document-action-btn download-document" data-url="${document.url}">
                            <i class="fas fa-download"></i>
                        </button>
                    </div>
                </div>
            `;
        });
        
        mediaHtml += `
                </div>
            </div>
        `;
    }
    
    return mediaHtml;
}

/**
 * Získání HTML obsahu pro záložku analýzy
 * @param {string} itemId - ID kanálu/serveru/skupiny
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 * @returns {string} - HTML obsah pro záložku analýzy
 */
function getAnalysisContent(itemId, platform) {
    // Získání zpráv podle platformy
    let messages;
    let users;
    
    switch (platform) {
        case 'telegram':
            messages = messagingPlatformsData.telegram.messages[itemId] || [];
            users = messagingPlatformsData.telegram.users;
            break;
        case 'discord':
            messages = messagingPlatformsData.discord.messages[itemId] || [];
            users = messagingPlatformsData.discord.users;
            break;
        case 'whatsapp':
            messages = messagingPlatformsData.whatsapp.messages[itemId] || [];
            users = messagingPlatformsData.whatsapp.users;
            break;
        default:
            messages = [];
            users = {};
    }
    
    // Kontrola, zda existují zprávy pro analýzu
    if (messages.length === 0) {
        return `
            <div class="no-analysis">
                <i class="fas fa-chart-bar"></i>
                <p>Pro analýzu nejsou k dispozici žádná data.</p>
            </div>
        `;
    }
    
    // Vytvoření HTML pro analýzu
    let analysisHtml = `
        <div class="analysis-container">
            <div class="analysis-section">
                <h5>Aktivita v čase</h5>
                <div class="analysis-chart" id="activity-chart">
                    <div class="chart-placeholder">
                        <i class="fas fa-chart-line"></i>
                        <p>Graf aktivity v čase</p>
                    </div>
                </div>
            </div>
            
            <div class="analysis-section">
                <h5>Nejaktivnější uživatelé</h5>
                <div class="analysis-chart" id="users-chart">
                    <div class="chart-placeholder">
                        <i class="fas fa-chart-pie"></i>
                        <p>Graf aktivity uživatelů</p>
                    </div>
                </div>
            </div>
            
            <div class="analysis-section">
                <h5>Klíčová slova</h5>
                <div class="analysis-chart" id="keywords-chart">
                    <div class="chart-placeholder">
                        <i class="fas fa-cloud"></i>
                        <p>Word cloud klíčových slov</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    return analysisHtml;
}

/**
 * Formátování velikosti souboru
 * @param {number} bytes - Velikost v bajtech
 * @returns {string} - Formátovaná velikost
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Přidání event listenerů pro tlačítka v záložkách
 * @param {HTMLElement} container - Kontejner s tlačítky
 * @param {string} itemId - ID kanálu/serveru/skupiny
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 */
function addTabEventListeners(container, itemId, platform) {
    // Event listenery pro tlačítka v záložce zpráv
    container.querySelectorAll('.add-to-monitoring').forEach(button => {
        button.addEventListener('click', function() {
            const messageId = this.getAttribute('data-message-id');
            addMessageToMonitoring(messageId, itemId, platform);
        });
    });
    
    container.querySelectorAll('.export-message').forEach(button => {
        button.addEventListener('click', function() {
            const messageId = this.getAttribute('data-message-id');
            exportMessage(messageId, itemId, platform);
        });
    });
    
    // Event listenery pro tlačítka v záložce uživatelů
    container.querySelectorAll('.add-user-to-monitoring').forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-user-id');
            addUserToMonitoring(userId, platform);
        });
    });
    
    container.querySelectorAll('.view-user-messages').forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-user-id');
            viewUserMessages(userId, itemId, platform);
        });
    });
    
    // Event listenery pro tlačítka v záložce médií
    container.querySelectorAll('.view-media').forEach(button => {
        button.addEventListener('click', function() {
            const url = this.getAttribute('data-url');
            viewMedia(url);
        });
    });
    
    container.querySelectorAll('.add-to-gallery').forEach(button => {
        button.addEventListener('click', function() {
            const url = this.getAttribute('data-url');
            const messageId = this.getAttribute('data-message-id');
            addMediaToGallery(url, messageId, itemId, platform);
        });
    });
    
    container.querySelectorAll('.download-document').forEach(button => {
        button.addEventListener('click', function() {
            const url = this.getAttribute('data-url');
            downloadDocument(url);
        });
    });
}
