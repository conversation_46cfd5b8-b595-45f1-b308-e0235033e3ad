
"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, PlusCircle, Loader2 } from "lucide-react";

interface CaseListControlsProps {
  onSearchChange: (term: string) => void;
  onStatusFilterChange: (status: string) => void;
  onPriorityFilterChange: (priority: string) => void;
  onNewCaseClick: () => void;
  isCreatingCase?: boolean;
}

export function CaseListControls({
  onSearchChange,
  onStatusFilterChange,
  onPriorityFilterChange,
  onNewCaseClick,
  isCreatingCase = false,
}: CaseListControlsProps) {

  return (
    <div className="mb-6 rounded-xl bg-card p-4 md:p-6 shadow-md">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="relative flex-grow md:max-w-md">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Vyhledat případ (název, ID, popis...)"
            className="pl-10 w-full"
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <Select onValueChange={onStatusFilterChange} defaultValue="all">
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Všechny statusy" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Všechny statusy</SelectItem>
              <SelectItem value="active">Aktivní</SelectItem>
              <SelectItem value="pending">Čekající</SelectItem>
              <SelectItem value="completed">Dokončené</SelectItem>
              <SelectItem value="archived">Archivované</SelectItem>
            </SelectContent>
          </Select>
          <Select onValueChange={onPriorityFilterChange} defaultValue="all">
            <SelectTrigger className="w-full sm:w-[180px]">
              <SelectValue placeholder="Všechny priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Všechny priority</SelectItem>
              <SelectItem value="high">Vysoká</SelectItem>
              <SelectItem value="medium">Střední</SelectItem>
              <SelectItem value="low">Nízká</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={onNewCaseClick} className="w-full sm:w-auto" disabled={isCreatingCase}>
            {isCreatingCase ? (
              <Loader2 className="mr-2 h-5 w-5 animate-spin" />
            ) : (
              <PlusCircle className="mr-2 h-5 w-5" />
            )}
            {isCreatingCase ? "Vytváření..." : "Nový případ"}
          </Button>
        </div>
      </div>
    </div>
  );
}
