
"use client";

import { useEffect, useState } from "react";
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label"; // Keep if used outside FormField
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Loader2, Search } from "lucide-react";
import { db } from "@/lib/firebase";
import { doc, updateDoc, serverTimestamp } from "firebase/firestore";
import type { Subject, PhysicalPersonSubject, LegalEntitySubject } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { lookupCompanyInAresDirectly } from "@/ai/flows/ares-direct-lookup-flow";

// Schema je stejné jako pro AddSubjectDialog, typ subjektu se po vytvoření nemění.
const formSchema = z.object({
    subjectType: z.enum(['physical', 'legal']), // Typ se nebude měnit
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    dateOfBirth: z.string().optional(),
    nationality: z.string().optional(),
    companyId: z.string().optional(),
    name: z.string().optional(),
    address: z.string().optional(),
    notes: z.string().optional(),
  }).superRefine((data, ctx) => {
    if (data.subjectType === 'physical') {
      if (!data.firstName?.trim()) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Jméno je povinné.", path: ['firstName'] });
      }
      if (!data.lastName?.trim()) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Příjmení je povinné.", path: ['lastName'] });
      }
    } else if (data.subjectType === 'legal') {
      if (!data.companyId?.trim()) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, message: "IČO je povinné.", path: ['companyId'] });
      }
      if (!data.name?.trim()) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Název společnosti je povinný.", path: ['name'] });
      }
    }
  });

type FormValues = z.infer<typeof formSchema>;

interface EditSubjectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  caseId: string;
  subject: Subject; // Data subjektu k úpravě
  onSubjectUpdated: () => void;
}

export function EditSubjectDialog({ open, onOpenChange, caseId, subject, onSubjectUpdated }: EditSubjectDialogProps) {
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [isAresLoading, setIsAresLoading] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {}, // Bude nastaveno v useEffect
  });

  useEffect(() => {
    if (open && subject) {
      form.reset({
        subjectType: subject.type,
        firstName: subject.type === 'physical' ? (subject as PhysicalPersonSubject).firstName : "",
        lastName: subject.type === 'physical' ? (subject as PhysicalPersonSubject).lastName : "",
        dateOfBirth: subject.type === 'physical' ? (subject as PhysicalPersonSubject).dateOfBirth || "" : "",
        nationality: subject.type === 'physical' ? (subject as PhysicalPersonSubject).nationality || "" : "",
        companyId: subject.type === 'legal' ? (subject as LegalEntitySubject).companyId : "",
        name: subject.type === 'legal' ? (subject as LegalEntitySubject).name : "",
        address: subject.type === 'legal' ? (subject as LegalEntitySubject).address || "" : "",
        notes: subject.type === 'legal' ? (subject as LegalEntitySubject).notes || "" : "",
      });
      setIsSaving(false);
      setIsAresLoading(false);
    }
  }, [open, subject, form]);

  const handleAresLookup = async () => {
    const companyId = form.getValues("companyId");
    if (!companyId || companyId.trim().length < 8) {
      toast({ title: "Chybné IČO", description: "Zadejte platné IČO.", variant: "destructive" });
      form.setError("companyId", { type: "manual", message: "Zadejte platné IČO." });
      return;
    }
    setIsAresLoading(true);
    try {
      const aresData = await lookupCompanyInAresDirectly({ companyId });
      form.setValue("name", aresData.name, { shouldValidate: true });
      form.setValue("address", aresData.address, { shouldValidate: true });
      
      let notesContent = "";
      if (aresData.legalForm) notesContent += `Právní forma: ${aresData.legalForm}\n`;
      if (aresData.establishmentDate) notesContent += `Datum založení: ${aresData.establishmentDate}`;

      // Append ARES data to existing notes or set if notes are empty
      const currentNotes = form.getValues("notes") || "";
      const newNotes = (currentNotes ? currentNotes + "\n\n" : "") + "Data z ARES:\n" + notesContent.trim();
      form.setValue("notes", newNotes.trim());

      toast({ title: "Data z ARES načtena", description: `Nalezen subjekt: ${aresData.name}` });
    } catch (error: any) {
      console.error("ARES lookup error in EditSubjectDialog:", error);
      toast({ title: "Chyba při načítání z ARES", description: error.message, variant: "destructive" });
    } finally {
      setIsAresLoading(false);
    }
  };

  const onSubmit: SubmitHandler<FormValues> = async (data) => {
    setIsSaving(true);
    try {
      const subjectDocRef = doc(db, "cases", caseId, "subjects", subject.id);
      let updatedData: Partial<Subject> & { updatedAt: any } = { updatedAt: serverTimestamp() }; // Ensure updatedAt is always included

      if (subject.type === 'physical') {
        updatedData = {
          ...updatedData,
          type: 'physical', // Ensure type is part of the update if needed by rules, though usually not changed
          firstName: data.firstName!,
          lastName: data.lastName!,
          dateOfBirth: data.dateOfBirth || '',
          nationality: data.nationality || '',
        };
      } else if (subject.type === 'legal') {
        updatedData = {
          ...updatedData,
          type: 'legal', // Ensure type
          companyId: data.companyId!,
          name: data.name!,
          address: data.address || '',
          notes: data.notes || '',
        };
      }
      
      await updateDoc(subjectDocRef, updatedData);
      toast({ title: "Subjekt aktualizován", description: "Změny byly úspěšně uloženy." });
      onSubjectUpdated();
      onOpenChange(false);
    } catch (error: any) {
      console.error("Error updating subject:", error);
      toast({ title: "Chyba při aktualizaci subjektu", description: error.message, variant: "destructive" });
    } finally {
      setIsSaving(false);
    }
  };
  
  const watchedSubjectType = form.watch("subjectType"); // This will be subject.type and won't change

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto p-6">
        <DialogHeader>
          <DialogTitle>Upravit subjekt</DialogTitle>
          <DialogDescription>
            Změňte informace o subjektu. Typ subjektu nelze měnit.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 mt-4">
            <FormItem>
              <FormLabel>Typ subjektu</FormLabel>
              <Input value={subject.type === 'physical' ? 'Fyzická osoba' : 'Právnická osoba'} readOnly className="bg-muted/50 cursor-not-allowed" />
            </FormItem>

            {/* Render fields based on the subject.type passed in props, not watchedSubjectType from form state if type is immutable */}
            {subject.type === 'physical' && (
              <div className="space-y-4 pt-4 border-t">
                 <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={form.control} name="firstName" render={({ field }) => (
                        <FormItem>
                          <FormLabel>Jméno <span className="text-destructive">*</span></FormLabel>
                          <FormControl><Input placeholder="Jan" {...field} /></FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField control={form.control} name="lastName" render={({ field }) => (
                        <FormItem>
                          <FormLabel>Příjmení <span className="text-destructive">*</span></FormLabel>
                          <FormControl><Input placeholder="Novák" {...field} /></FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField control={form.control} name="dateOfBirth" render={({ field }) => (
                        <FormItem>
                          <FormLabel>Datum narození</FormLabel>
                          <FormControl><Input type="date" {...field} /></FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField control={form.control} name="nationality" render={({ field }) => (
                        <FormItem>
                          <FormLabel>Národnost</FormLabel>
                          <FormControl><Input placeholder="Česká republika" {...field} /></FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
              </div>
            )}

            {subject.type === 'legal' && (
              <div className="space-y-4 pt-4 border-t">
                <FormField control={form.control} name="companyId" render={({ field }) => (
                    <FormItem>
                      <FormLabel>IČO <span className="text-destructive">*</span></FormLabel>
                      <FormControl><Input placeholder="12345678" {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField control={form.control} name="name" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Název společnosti <span className="text-destructive">*</span></FormLabel>
                      <FormControl><Input placeholder="Název firmy s.r.o." {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                 <Button type="button" variant="outline" onClick={handleAresLookup} disabled={isAresLoading || !form.getValues("companyId")?.trim()} className="w-full sm:w-auto">
                  {isAresLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Search className="mr-2 h-4 w-4" />}
                  Načíst z ARES
                </Button>
                <FormField control={form.control} name="address" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Adresa sídla</FormLabel>
                      <FormControl><Textarea placeholder="Ulice 123, Město, PSČ" {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField control={form.control} name="notes" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Poznámky</FormLabel>
                      <FormControl><Textarea placeholder="Další informace o společnosti..." {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}
            
            <DialogFooter className="pt-6 border-t">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={isSaving || isAresLoading}>
                Zrušit
              </Button>
              <Button type="submit" disabled={isSaving || isAresLoading}>
                {(isSaving || isAresLoading) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isSaving ? "Ukládání..." : (isAresLoading ? "Načítání ARES..." : "Uložit změny")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
