/**
 * Monitoring komunikačn<PERSON>ch platforem - funkce pro práci s UI
 */

/**
 * Zobrazení detailu Telegram kanálu
 * @param {string} channelId - ID kanálu
 * @param {string} platform - Název platformy (telegram)
 */
function showChannelDetail(channelId, platform) {
    console.log('Zobrazení detailu Telegram kanálu:', channelId);
    
    // Najít kanál v datech
    const channel = messagingPlatformsData.telegram.channels.find(c => c.id === channelId);
    if (!channel) {
        console.error('Kanál nebyl nalezen:', channelId);
        return;
    }
    
    // Najít kontejner pro detail kanálu
    const detailContainer = document.getElementById('telegram-channel-detail');
    if (!detailContainer) {
        console.error('Kontejner pro detail kanálu nebyl nalezen');
        return;
    }
    
    // Vytvoření HTML pro detail kanálu
    let detailHtml = `
        <div class="channel-detail-header">
            <div class="channel-detail-title">
                <i class="fab fa-telegram"></i> ${channel.name}
            </div>
            <div class="channel-detail-actions">
                <button type="button" class="btn-inline refresh-channel" data-channel-id="${channelId}" data-platform="${platform}">
                    <i class="fas fa-sync-alt"></i> Aktualizovat
                </button>
                <button type="button" class="btn-inline export-channel" data-channel-id="${channelId}" data-platform="${platform}">
                    <i class="fas fa-file-export"></i> Exportovat
                </button>
                <button type="button" class="btn-inline remove-channel" data-channel-id="${channelId}" data-platform="${platform}">
                    <i class="fas fa-trash"></i> Odstranit
                </button>
            </div>
        </div>
        
        <div class="channel-detail-info">
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Identifikátor</div>
                <div class="channel-detail-info-value">${channel.identifier}</div>
            </div>
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Typ</div>
                <div class="channel-detail-info-value">${channel.type || 'Neznámý'}</div>
            </div>
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Počet členů</div>
                <div class="channel-detail-info-value">${channel.memberCount}</div>
            </div>
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Poslední aktualizace</div>
                <div class="channel-detail-info-value">${formatDate(channel.lastUpdate)}</div>
            </div>
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Kategorie</div>
                <div class="channel-detail-info-value">${getCategoryName(channel.category)}</div>
            </div>
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Stav</div>
                <div class="channel-detail-info-value">${getStatusBadge(channel.status)}</div>
            </div>
        </div>
        
        <div class="channel-detail-description">
            ${channel.description ? channel.description : '<em>Bez popisu</em>'}
        </div>
        
        <div class="channel-detail-tabs">
            <button class="channel-detail-tab active" data-tab="messages">Zprávy</button>
            <button class="channel-detail-tab" data-tab="users">Uživatelé</button>
            <button class="channel-detail-tab" data-tab="media">Média</button>
            <button class="channel-detail-tab" data-tab="analysis">Analýza</button>
        </div>
        
        <div class="channel-detail-content active" id="messages-tab">
            ${getMessagesContent(channelId, platform)}
        </div>
        
        <div class="channel-detail-content" id="users-tab">
            ${getUsersContent(channelId, platform)}
        </div>
        
        <div class="channel-detail-content" id="media-tab">
            ${getMediaContent(channelId, platform)}
        </div>
        
        <div class="channel-detail-content" id="analysis-tab">
            ${getAnalysisContent(channelId, platform)}
        </div>
    `;
    
    detailContainer.innerHTML = detailHtml;
    
    // Přidání event listenerů pro záložky
    detailContainer.querySelectorAll('.channel-detail-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            // Odstranění aktivní třídy ze všech záložek a obsahů
            detailContainer.querySelectorAll('.channel-detail-tab').forEach(t => t.classList.remove('active'));
            detailContainer.querySelectorAll('.channel-detail-content').forEach(c => c.classList.remove('active'));
            
            // Přidání aktivní třídy na kliknutou záložku
            this.classList.add('active');
            
            // Zobrazení odpovídajícího obsahu
            const tabName = this.getAttribute('data-tab');
            const content = detailContainer.querySelector(`#${tabName}-tab`);
            if (content) {
                content.classList.add('active');
            }
        });
    });
    
    // Přidání event listenerů pro tlačítka
    detailContainer.querySelector('.refresh-channel').addEventListener('click', function() {
        const channelId = this.getAttribute('data-channel-id');
        const platform = this.getAttribute('data-platform');
        refreshChannelData(channelId, platform);
    });
    
    detailContainer.querySelector('.export-channel').addEventListener('click', function() {
        const channelId = this.getAttribute('data-channel-id');
        const platform = this.getAttribute('data-platform');
        exportChannelData(channelId, platform);
    });
    
    detailContainer.querySelector('.remove-channel').addEventListener('click', function() {
        const channelId = this.getAttribute('data-channel-id');
        const platform = this.getAttribute('data-platform');
        removeChannel(channelId, platform);
    });
    
    // Přidání event listenerů pro tlačítka v záložkách
    addTabEventListeners(detailContainer, channelId, platform);
}

/**
 * Zobrazení detailu Discord serveru
 * @param {string} serverId - ID serveru
 * @param {string} platform - Název platformy (discord)
 */
function showServerDetail(serverId, platform) {
    console.log('Zobrazení detailu Discord serveru:', serverId);
    
    // Najít server v datech
    const server = messagingPlatformsData.discord.servers.find(s => s.id === serverId);
    if (!server) {
        console.error('Server nebyl nalezen:', serverId);
        return;
    }
    
    // Najít kontejner pro detail serveru
    const detailContainer = document.getElementById('discord-channel-detail');
    if (!detailContainer) {
        console.error('Kontejner pro detail serveru nebyl nalezen');
        return;
    }
    
    // Vytvoření HTML pro detail serveru (podobné jako u Telegram kanálu)
    let detailHtml = `
        <div class="channel-detail-header">
            <div class="channel-detail-title">
                <i class="fab fa-discord"></i> ${server.name}
            </div>
            <div class="channel-detail-actions">
                <button type="button" class="btn-inline refresh-channel" data-server-id="${serverId}" data-platform="${platform}">
                    <i class="fas fa-sync-alt"></i> Aktualizovat
                </button>
                <button type="button" class="btn-inline export-channel" data-server-id="${serverId}" data-platform="${platform}">
                    <i class="fas fa-file-export"></i> Exportovat
                </button>
                <button type="button" class="btn-inline remove-channel" data-server-id="${serverId}" data-platform="${platform}">
                    <i class="fas fa-trash"></i> Odstranit
                </button>
            </div>
        </div>
        
        <div class="channel-detail-info">
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Identifikátor</div>
                <div class="channel-detail-info-value">${server.identifier}</div>
            </div>
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Typ</div>
                <div class="channel-detail-info-value">Server</div>
            </div>
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Počet členů</div>
                <div class="channel-detail-info-value">${server.memberCount}</div>
            </div>
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Poslední aktualizace</div>
                <div class="channel-detail-info-value">${formatDate(server.lastUpdate)}</div>
            </div>
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Kategorie</div>
                <div class="channel-detail-info-value">${getCategoryName(server.category)}</div>
            </div>
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Stav</div>
                <div class="channel-detail-info-value">${getStatusBadge(server.status)}</div>
            </div>
        </div>
        
        <div class="channel-detail-description">
            ${server.description ? server.description : '<em>Bez popisu</em>'}
        </div>
        
        <div class="channel-detail-tabs">
            <button class="channel-detail-tab active" data-tab="messages">Zprávy</button>
            <button class="channel-detail-tab" data-tab="users">Uživatelé</button>
            <button class="channel-detail-tab" data-tab="media">Média</button>
            <button class="channel-detail-tab" data-tab="analysis">Analýza</button>
        </div>
        
        <div class="channel-detail-content active" id="messages-tab">
            ${getMessagesContent(serverId, platform)}
        </div>
        
        <div class="channel-detail-content" id="users-tab">
            ${getUsersContent(serverId, platform)}
        </div>
        
        <div class="channel-detail-content" id="media-tab">
            ${getMediaContent(serverId, platform)}
        </div>
        
        <div class="channel-detail-content" id="analysis-tab">
            ${getAnalysisContent(serverId, platform)}
        </div>
    `;
    
    detailContainer.innerHTML = detailHtml;
    
    // Přidání event listenerů (podobné jako u Telegram kanálu)
    detailContainer.querySelectorAll('.channel-detail-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            detailContainer.querySelectorAll('.channel-detail-tab').forEach(t => t.classList.remove('active'));
            detailContainer.querySelectorAll('.channel-detail-content').forEach(c => c.classList.remove('active'));
            
            this.classList.add('active');
            
            const tabName = this.getAttribute('data-tab');
            const content = detailContainer.querySelector(`#${tabName}-tab`);
            if (content) {
                content.classList.add('active');
            }
        });
    });
    
    // Přidání event listenerů pro tlačítka
    detailContainer.querySelector('.refresh-channel').addEventListener('click', function() {
        const serverId = this.getAttribute('data-server-id');
        const platform = this.getAttribute('data-platform');
        refreshChannelData(serverId, platform);
    });
    
    detailContainer.querySelector('.export-channel').addEventListener('click', function() {
        const serverId = this.getAttribute('data-server-id');
        const platform = this.getAttribute('data-platform');
        exportChannelData(serverId, platform);
    });
    
    detailContainer.querySelector('.remove-channel').addEventListener('click', function() {
        const serverId = this.getAttribute('data-server-id');
        const platform = this.getAttribute('data-platform');
        removeChannel(serverId, platform);
    });
    
    // Přidání event listenerů pro tlačítka v záložkách
    addTabEventListeners(detailContainer, serverId, platform);
}

/**
 * Zobrazení detailu WhatsApp skupiny
 * @param {string} groupId - ID skupiny
 * @param {string} platform - Název platformy (whatsapp)
 */
function showGroupDetail(groupId, platform) {
    console.log('Zobrazení detailu WhatsApp skupiny:', groupId);
    
    // Najít skupinu v datech
    const group = messagingPlatformsData.whatsapp.groups.find(g => g.id === groupId);
    if (!group) {
        console.error('Skupina nebyla nalezena:', groupId);
        return;
    }
    
    // Najít kontejner pro detail skupiny
    const detailContainer = document.getElementById('whatsapp-channel-detail');
    if (!detailContainer) {
        console.error('Kontejner pro detail skupiny nebyl nalezen');
        return;
    }
    
    // Vytvoření HTML pro detail skupiny (podobné jako u Telegram kanálu a Discord serveru)
    let detailHtml = `
        <div class="channel-detail-header">
            <div class="channel-detail-title">
                <i class="fab fa-whatsapp"></i> ${group.name}
            </div>
            <div class="channel-detail-actions">
                <button type="button" class="btn-inline refresh-channel" data-group-id="${groupId}" data-platform="${platform}">
                    <i class="fas fa-sync-alt"></i> Aktualizovat
                </button>
                <button type="button" class="btn-inline export-channel" data-group-id="${groupId}" data-platform="${platform}">
                    <i class="fas fa-file-export"></i> Exportovat
                </button>
                <button type="button" class="btn-inline remove-channel" data-group-id="${groupId}" data-platform="${platform}">
                    <i class="fas fa-trash"></i> Odstranit
                </button>
            </div>
        </div>
        
        <div class="channel-detail-info">
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Identifikátor</div>
                <div class="channel-detail-info-value">${group.identifier}</div>
            </div>
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Typ</div>
                <div class="channel-detail-info-value">Skupina</div>
            </div>
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Počet členů</div>
                <div class="channel-detail-info-value">${group.memberCount}</div>
            </div>
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Poslední aktualizace</div>
                <div class="channel-detail-info-value">${formatDate(group.lastUpdate)}</div>
            </div>
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Kategorie</div>
                <div class="channel-detail-info-value">${getCategoryName(group.category)}</div>
            </div>
            <div class="channel-detail-info-item">
                <div class="channel-detail-info-label">Stav</div>
                <div class="channel-detail-info-value">${getStatusBadge(group.status)}</div>
            </div>
        </div>
        
        <div class="channel-detail-description">
            ${group.description ? group.description : '<em>Bez popisu</em>'}
        </div>
        
        <div class="channel-detail-tabs">
            <button class="channel-detail-tab active" data-tab="messages">Zprávy</button>
            <button class="channel-detail-tab" data-tab="users">Uživatelé</button>
            <button class="channel-detail-tab" data-tab="media">Média</button>
            <button class="channel-detail-tab" data-tab="analysis">Analýza</button>
        </div>
        
        <div class="channel-detail-content active" id="messages-tab">
            ${getMessagesContent(groupId, platform)}
        </div>
        
        <div class="channel-detail-content" id="users-tab">
            ${getUsersContent(groupId, platform)}
        </div>
        
        <div class="channel-detail-content" id="media-tab">
            ${getMediaContent(groupId, platform)}
        </div>
        
        <div class="channel-detail-content" id="analysis-tab">
            ${getAnalysisContent(groupId, platform)}
        </div>
    `;
    
    detailContainer.innerHTML = detailHtml;
    
    // Přidání event listenerů (podobné jako u Telegram kanálu a Discord serveru)
    detailContainer.querySelectorAll('.channel-detail-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            detailContainer.querySelectorAll('.channel-detail-tab').forEach(t => t.classList.remove('active'));
            detailContainer.querySelectorAll('.channel-detail-content').forEach(c => c.classList.remove('active'));
            
            this.classList.add('active');
            
            const tabName = this.getAttribute('data-tab');
            const content = detailContainer.querySelector(`#${tabName}-tab`);
            if (content) {
                content.classList.add('active');
            }
        });
    });
    
    // Přidání event listenerů pro tlačítka
    detailContainer.querySelector('.refresh-channel').addEventListener('click', function() {
        const groupId = this.getAttribute('data-group-id');
        const platform = this.getAttribute('data-platform');
        refreshChannelData(groupId, platform);
    });
    
    detailContainer.querySelector('.export-channel').addEventListener('click', function() {
        const groupId = this.getAttribute('data-group-id');
        const platform = this.getAttribute('data-platform');
        exportChannelData(groupId, platform);
    });
    
    detailContainer.querySelector('.remove-channel').addEventListener('click', function() {
        const groupId = this.getAttribute('data-group-id');
        const platform = this.getAttribute('data-platform');
        removeChannel(groupId, platform);
    });
    
    // Přidání event listenerů pro tlačítka v záložkách
    addTabEventListeners(detailContainer, groupId, platform);
}
