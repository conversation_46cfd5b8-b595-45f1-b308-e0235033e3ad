"use client";

import type { Sub<PERSON><PERSON><PERSON><PERSON>, UseFormReturn } from 'react-hook-form';
import { useForm, useFieldArray, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import type { 
  LocationsModuleData, LocationRecord, PhysicalPersonSubject, LegalEntitySubject, PhotoMetadata, 
  LocationType, LocationFrequency, LocationSource 
} from '@/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PlusCircle, Trash2, ImageUp, MapPin, Globe, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { db } from '@/lib/firebase';
import { doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { cn } from '@/lib/utils';
import { useState, useEffect } from 'react';
import {
  Form,
  FormField,
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from "@/components/ui/form";
import dynamic from 'next/dynamic'; // For Leaflet map
import { PhotoDocumentationSection } from './PhotoDocumentationSection';
import { FormItemRHF, FormItemSelectRHF, type LocationsFormValues, locationsModuleSchema } from './FormComponents';

// Leaflet CSS - must be imported for maps to render correctly
import 'leaflet/dist/leaflet.css';

// Dynamically import Leaflet map components to avoid SSR issues
const MapContainer = dynamic(() => import('react-leaflet').then(mod => mod.MapContainer), { ssr: false });
const TileLayer = dynamic(() => import('react-leaflet').then(mod => mod.TileLayer), { ssr: false });
const Marker = dynamic(() => import('react-leaflet').then(mod => mod.Marker), { ssr: false });
const Popup = dynamic(() => import('react-leaflet').then(mod => mod.Popup), { ssr: false });

interface LocationsFormProps {
  caseId: string;
  subject: PhysicalPersonSubject | LegalEntitySubject;
  existingData: LocationsModuleData | null;
  onSave: (moduleId: string, wasNew: boolean) => void;
}

const locationTypeOptions: { value: LocationType; label: string }[] = [
  { value: "home", label: "Bydliště" }, { value: "work", label: "Pracoviště" },
  { value: "family", label: "Rodinné místo" }, { value: "leisure", label: "Volnočasová aktivita" },
  { value: "travel", label: "Cestování" }, { value: "hideout", label: "Úkryt" },
  { value: "meeting", label: "Místo setkávání" }, { value: "other", label: "Jiné" },
];
const locationFrequencyOptions: { value: LocationFrequency; label: string }[] = [
  { value: "frequent", label: "Častý výskyt" }, { value: "occasional", label: "Občasný výskyt" },
  { value: "rare", label: "Ojedinělý výskyt" }, { value: "one-time", label: "Jednorázový výskyt" },
];
const locationSourceOptions: { value: LocationSource; label: string }[] = [
  { value: "social-media", label: "Sociální sítě" }, { value: "surveillance", label: "Sledování" },
  { value: "witness", label: "Svědectví" }, { value: "documents", label: "Dokumenty" },
  { value: "database", label: "Databáze" }, { value: "other", label: "Jiný" },
];

export function LocationsForm({ caseId, subject, existingData, onSave }: LocationsFormProps) {
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const moduleId = "locations";
  const [mapApiReady, setMapApiReady] = useState(false);

  useEffect(() => {
    // Check if window is defined (client-side) before trying to use Leaflet
    if (typeof window !== 'undefined') {
      setMapApiReady(true);
    }
  }, []);

  const form = useForm<LocationsFormValues>({
    resolver: zodResolver(locationsModuleSchema),
    defaultValues: {
      locations: existingData?.locations?.map(loc => ({
        ...loc,
        id: loc.id || crypto.randomUUID(),
        locationType: loc.locationType || undefined,
        frequency: loc.frequency || undefined,
        source: loc.source || undefined,
        photos: loc.photos?.map(p => ({...p, id: p.id || crypto.randomUUID()})) || [],
      })) || [],
    },
  });

  const { fields, append, remove } = useFieldArray({ control: form.control, name: "locations" });

  const addNewLocation = () => {
    append({
      id: crypto.randomUUID(),
      name: "", locationType: undefined, otherLocationTypeDetail: "", address: "",
      gpsCoordinates: "", frequency: undefined, firstSeen: "", lastSeen: "",
      source: undefined, otherSourceDetail: "", descriptionNotes: "", photos: [],
    });
  };

  const onSubmitHandler: SubmitHandler<LocationsFormValues> = async (data) => {
    setIsSaving(true);
    try {
      const moduleDocRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);

      // Funkce pro odstranění undefined hodnot z objektu (rekurzivně)
      const removeUndefinedValues = (obj: any): any => {
        if (obj === null || obj === undefined) {
          return null;
        }
        
        if (Array.isArray(obj)) {
          return obj.map(removeUndefinedValues).filter(item => item !== undefined);
        }
        
        if (typeof obj === 'object') {
          const cleaned: any = {};
          for (const [key, value] of Object.entries(obj)) {
            if (value !== undefined) {
              const cleanedValue = removeUndefinedValues(value);
              if (cleanedValue !== undefined) {
                cleaned[key] = cleanedValue;
              }
            }
          }
          return cleaned;
        }
        
        return obj;
      };

      // Odstranit undefined hodnoty před uložením
      const cleanedData = removeUndefinedValues(data);

      const dataToSave: LocationsModuleData = {
        ...cleanedData, 
        subjectId: subject.id,
        lastUpdatedAt: serverTimestamp(), 
        createdAt: existingData?.createdAt || serverTimestamp(),
      };
      await setDoc(moduleDocRef, dataToSave, { merge: true });
      toast({ title: "Data modulu Lokace uložena." });
      const wasNew = !existingData || !existingData.createdAt;
      onSave(moduleId, wasNew);
    } catch (error: any) {
      toast({ title: "Chyba ukládání dat modulu Lokace", description: error.message, variant: "destructive" });
    } finally {
      setIsSaving(false);
    }
  };
  
  const parseGps = (gpsString?: string): [number, number] | null => {
    if (!gpsString) return null;
    const parts = gpsString.split(',').map(p => parseFloat(p.trim()));
    if (parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1])) {
      return [parts[0], parts[1]];
    }
    return null;
  };


  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmitHandler)} className="space-y-8">
        <Card className="shadow-lg border border-primary/30">
          <CardHeader>
            <CardTitle className="text-xl flex items-center"><MapPin className="mr-3 h-6 w-6 text-primary"/>Lokace subjektu: {subject.type === 'physical' ? `${subject.firstName} ${subject.lastName}` : subject.name}</CardTitle>
            <CardDescription>Zadejte informace o relevantních lokacích spojených se subjektem.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6 pt-4">
            {fields.map((field, index) => {
              const watchedLocationType = form.watch(`locations.${index}.locationType`);
              const watchedSource = form.watch(`locations.${index}.source`);
              const gpsCoordsString = form.watch(`locations.${index}.gpsCoordinates`);
              const parsedCoords = parseGps(gpsCoordsString);

              return (
                <Card key={field.id} className="p-4 shadow-md bg-card-foreground/5 relative">
                  <Button type="button" variant="ghost" size="icon" onClick={() => remove(index)} className="absolute top-3 right-3 text-destructive hover:bg-destructive/10"><Trash2 className="h-5 w-5"/></Button>
                  <CardHeader className="px-0 pt-0 pb-4"><CardTitle className="text-lg">Lokace {index + 1}</CardTitle></CardHeader>
                  <CardContent className="px-0 pb-0 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormItemRHF label="Název lokace" name={`locations.${index}.name`} control={form.control} placeholder="Např. Domov, Práce, Chata u lesa"/>
                      <FormItemSelectRHF label="Typ lokace" name={`locations.${index}.locationType`} control={form.control} options={locationTypeOptions} placeholder="-- Vyberte typ --"/>
                    </div>
                    {watchedLocationType === 'other' && (
                      <FormItemRHF label="Upřesnění typu lokace" name={`locations.${index}.otherLocationTypeDetail`} control={form.control} placeholder="Popište vlastní typ"/>
                    )}
                    <FormItemRHF label="Adresa" name={`locations.${index}.address`} control={form.control} placeholder="Přesná adresa místa"/>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormItemRHF label="GPS souřadnice (lat, lon)" name={`locations.${index}.gpsCoordinates`} control={form.control} placeholder="např. 50.0872, 14.4212"/>
                      <FormItemSelectRHF label="Četnost výskytu" name={`locations.${index}.frequency`} control={form.control} options={locationFrequencyOptions} placeholder="-- Vyberte --"/>
                    </div>

                    {mapApiReady && parsedCoords && (
                      <Card className="my-4">
                        <CardHeader className="pb-2 pt-3"><CardTitle className="text-md flex items-center"><Globe className="mr-2 h-5 w-5 text-primary/80"/>Mapa</CardTitle></CardHeader>
                        <CardContent>
                          <div style={{ height: '250px', width: '100%' }}>
                            <MapContainer center={parsedCoords} zoom={13} scrollWheelZoom={false} style={{ height: "100%", width: "100%" }}>
                              <TileLayer
                                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                              />
                              <Marker position={parsedCoords}>
                                <Popup>{form.getValues(`locations.${index}.name`) || form.getValues(`locations.${index}.address`) || 'Zadaná lokace'}</Popup>
                              </Marker>
                            </MapContainer>
                          </div>
                           <FormDescription className="text-xs mt-2">Základní zobrazení mapy. Možnost označit oblasti a body zájmu bude přidána později.</FormDescription>
                        </CardContent>
                      </Card>
                    )}
                     {!mapApiReady && form.getValues(`locations.${index}.gpsCoordinates`) && (
                        <p className="text-sm text-muted-foreground">Mapa se načítá...</p>
                    )}


                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormItemRHF label="První zjištění" name={`locations.${index}.firstSeen`} control={form.control} type="date"/>
                      <FormItemRHF label="Poslední zjištění" name={`locations.${index}.lastSeen`} control={form.control} type="date"/>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormItemSelectRHF label="Zdroj informace" name={`locations.${index}.source`} control={form.control} options={locationSourceOptions} placeholder="-- Vyberte zdroj --"/>
                      {watchedSource === 'other' && (
                        <FormItemRHF label="Upřesnění zdroje" name={`locations.${index}.otherSourceDetail`} control={form.control} placeholder="Detaily o zdroji"/>
                      )}
                    </div>
                    <FormItemRHF label="Popis a poznámky k lokaci" name={`locations.${index}.descriptionNotes`} control={form.control} as="textarea" rows={3} placeholder="Důležité informace, pozorování, souvislosti..."/>
                    
                    <PhotoDocumentationSection 
                      form={form} 
                      title="Fotografie lokace"
                      description="Fotografie tohoto konkrétního místa"
                      namePrefix={`locations.${index}.photos`} 
                      photoHint="location place building"
                      caseId={caseId}
                      subjectId={subject.id}
                      moduleId={moduleId}
                    />
                  </CardContent>
                </Card>
              );
            })}
            <Button type="button" variant="outline" onClick={addNewLocation} className="w-full md:w-auto">
              <PlusCircle className="mr-2 h-4 w-4"/> Přidat další lokaci
            </Button>
          </CardContent>
        </Card>

        <div className="flex justify-end pt-8 mt-8 border-t border-border">
          <Button type="submit" disabled={isSaving} className="w-full md:w-auto text-lg py-3 px-6 shadow-md">
            {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}Uložit data modulu Lokace
          </Button>
        </div>
      </form>
    </Form>
  );
}
