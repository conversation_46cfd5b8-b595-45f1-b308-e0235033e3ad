import type { FieldValue, Timestamp } from "firebase/firestore";
import { LinkedInModuleData } from './linkedin'; // Import LinkedIn types
import { TwitterModuleData } from './twitter'; // Import Twitter types
import { PhoneAnalysisModuleData } from './phoneAnalysis'; // Import Phone Analysis types

export type CaseStatus = 'active' | 'pending' | 'completed' | 'archived';
export type CasePriority = 'high' | 'medium' | 'low';

export interface Case {
  id: string;
  title: string;
  status: CaseStatus;
  priority: CasePriority;
  subjectsCount?: number;
  creationDate: string;
  investigatorId?: string;
  investigatorName?: string;
  deadlineInfo: string;
  description: string;
  progress: number;
  modulesCompleted: number;
  modulesTotal: number;
  referenceNumber?: string;
  internalId?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface UserProfile {
  uid: string;
  email: string;
  firstName: string;
  lastName:string;
  rank?: string;
  title?: string;
  oec?: string;
  department?: string;
  createdAt: Timestamp | FieldValue;
}

export type SubjectType = 'physical' | 'legal';

export interface SubjectBase {
  id: string;
  caseId: string;
  type: SubjectType;
  addedAt: Timestamp | FieldValue;
  updatedAt?: Timestamp | FieldValue;
}

export interface PhysicalPersonSubject extends SubjectBase {
  type: 'physical';
  firstName: string;
  lastName: string;
  dateOfBirth?: string;
  nationality?: string;
}

export interface LegalEntitySubject extends SubjectBase {
  type: 'legal';
  companyId: string;
  name: string;
  address?: string;
  notes?: string;
  legalForm?: string;
  establishmentDate?: string;
}

export type Subject = PhysicalPersonSubject | LegalEntitySubject;

// ----- Typy pro Modul Evidence Obyvatel -----

export interface AddressRecord {
  id?: string;
  address: string;
  type?: string; // typ adresy (trvalá, přechodná, korespondenční)
  dateFrom?: string; // od kdy na adrese
  dateTo?: string; // do kdy na adrese
  verified?: boolean; // ověřená adresa
  source?: string; // zdroj informace
}

export interface DocumentRecord {
  id?: string;
  documentType: string;
  documentNumber: string;
  issuedBy?: string;
  validity?: string;
  issuedDate?: string; // datum vydání
  status?: string; // platný/neplatný/ukradený/ztracený
  notes?: string; // poznámky k dokladu
}

export interface ContactRecord {
  id?: string;
  value: string;
  subType?: string;
  verified?: boolean; // ověřený kontakt
  active?: boolean; // aktivní kontakt
  lastSeen?: string; // kdy naposledy viděn/použit
  notes?: string; // poznámky
}

export interface PhotoMetadata {
  id?: string;
  fileName?: string;
  storagePath?: string;
  downloadURL?: string;
  description?: string;
  dateTaken?: string;
  sourceURL?: string;
  location?: string; // místo pořízení fotografie
  photographerInfo?: string; // kdo fotografii pořídil
  tags?: string; // tagy pro vyhledávání
  verified?: boolean; // ověřená fotografie
}

// Nová interface pro fyzické znaky
export interface PhysicalCharacteristics {
  id?: string;
  height?: string; // výška
  weight?: string; // váha
  eyeColor?: string; // barva očí
  hairColor?: string; // barva vlasů
  build?: string; // postava
  scarsMarks?: string; // jizvy a znamínka
  tattoos?: string; // tetování
  otherDistinguishing?: string; // jiné rozpoznávací znaky
}

export interface EvidenceObyvatelData {
  id?: string;
  subjectId: string;

  // Základní údaje
  fullName?: string;
  useOriginalScriptName: boolean;
  fullNameOriginalScript: string;
  dateOfBirth?: string;
  placeOfBirth?: string;
  personalIdNumber?: string;
  nationality?: string;

  // Bydliště
  permanentAddress?: string;
  temporaryAddresses?: AddressRecord[];

  // Doklady
  idCardNumber?: string;
  idCardIssuedBy?: string;
  idCardValidity?: string;
  otherDocuments?: DocumentRecord[];

  // Kontakty (základní)
  phoneNumbers?: ContactRecord[];
  emails?: ContactRecord[];
  socialProfiles?: ContactRecord[];

  // Fotografie
  photos?: PhotoMetadata[];

  // Fyzické charakteristiky
  physicalCharacteristics?: PhysicalCharacteristics;

  // Základní osobní údaje
  aliases?: string; // přezdívky, aliasy
  criminalRecord?: string; // trestní minulost
  languages?: string; // jazykové znalosti
  interests?: string; // zájmy, koníčky
  politicalAffiliation?: string; // politická příslušnost
  religiousAffiliation?: string; // náboženská příslušnost
  travelHistory?: string; // cestovní historie
  medicalInfo?: string; // základní zdravotní informace
  emergencyContact?: string; // nouzový kontakt

  // Vyšetřovací poznámky
  investigationNotes?: string; // poznámky vyšetřovatele
  threatLevel?: string; // úroveň hrozby (low/medium/high/critical)
  surveillanceNotes?: string; // poznámky ze sledování
  lastUpdatedBy?: string; // kdo naposledy aktualizoval

  lastUpdatedAt?: Timestamp | FieldValue;
  createdAt?: Timestamp | FieldValue;
}

// ----- Typy pro Modul Rodinní příslušníci -----
export type RelationshipType =
  | "mother" | "father" | "sibling" | "spouse" | "girlfriend" | "boyfriend"
  | "child" | "child-in-law" | "parent-in-law" | "grandparent" | "grandchild"
  | "other";

export type RelationshipStatus =
  | "active" | "inactive" | "strained" | "hostile" | "unknown";

export type RelationshipIntensity =
  | "very_close" | "close" | "regular" | "occasional" | "minimal" | "unknown";

export type InformationReliability =
  | "verified" | "probable" | "possible" | "unconfirmed" | "unknown";

export type InformationSource =
  | "social_media" | "surveillance" | "witness" | "documents" 
  | "phone_analysis" | "investigation" | "open_source" | "other";

export type ThreatLevel =
  | "none" | "low" | "medium" | "high" | "critical" | "unknown";

export interface ContactInfo {
  id?: string;
  type: "phone" | "email" | "social" | "address";
  value: string;
  platform?: string; // Pro sociální sítě
  verified?: boolean;
  active?: boolean;
  lastContact?: string;
  notes?: string;
}

export interface PhysicalDescription {
  id?: string;
  height?: string;
  weight?: string;
  hairColor?: string;
  eyeColor?: string;
  build?: string;
  distinguishingMarks?: string; // jizvy, tetování, atd.
  otherFeatures?: string;
}

export interface ProfessionalInfo {
  id?: string;
  currentJob?: string;
  employer?: string;
  workAddress?: string;
  previousJobs?: string;
  education?: string;
  skills?: string;
  income?: string;
}

export interface SecurityAssessment {
  id?: string;
  weaponAccess?: boolean;
  weaponTraining?: boolean;
  criminalHistory?: string;
  threatLevel?: ThreatLevel;
  threatAssessment?: string;
  surveillanceNotes?: string;
}

export interface FamilyMemberRecord {
  id: string;
  
  // Základní údaje
  fullName: string;
  originalScriptName?: string; // Pro jména v jiných písmech
  aliases?: string; // přezdívky, jiná jména
  dateOfBirth?: string;
  placeOfBirth?: string;
  personalIdNumber?: string;
  nationality?: string;
  
  // Vztah k hlavnímu subjektu
  relationshipType?: RelationshipType;
  relationshipOtherDetail?: string;
  relationshipStatus?: RelationshipStatus;
  relationshipIntensity?: RelationshipIntensity;
  relationshipStartDate?: string;
  relationshipEndDate?: string; // pokud neaktivní
  lastContact?: string; // datum posledního kontaktu
  contactFrequency?: string; // jak často komunikují
  
  // Bydliště a kontakty
  permanentAddress?: string;
  currentAddress?: string; // pokud jiné než trvalé
  contactInfo?: ContactInfo[];
  
  // Fyzické charakteristiky
  physicalDescription?: PhysicalDescription;
  
  // Profesní informace
  professionalInfo?: ProfessionalInfo;
  
  // Úmrtí (pokud aplikovatelné)
  isDeceased: boolean;
  dateOfDeath?: string;
  placeOfDeath?: string;
  causeOfDeath?: string;
  
  // OSINT informace
  informationSource?: InformationSource;
  sourceDetail?: string; // odkud konkrétně pochází info
  informationReliability?: InformationReliability;
  verificationDate?: string;
  lastSeen?: string; // kdy byl naposledy pozorován
  
  // Bezpečnostní hodnocení
  securityAssessment?: SecurityAssessment;
  
  // Poznámky a fotografie
  investigationNotes?: string;
  behaviorNotes?: string; // pozorování chování
  notes?: string;
  photos?: PhotoMetadata[];
}

export interface FamilyMembersModuleData {
  id?: string;
  subjectId: string;
  familyMembers: FamilyMemberRecord[];
  // Přehledové poznámky
  familyOverview?: string; // celkový přehled rodiny
  familyDynamics?: string; // rodinná dynamika
  keyRelationships?: string; // klíčové vztahy
  securityConcerns?: string; // bezpečnostní obavy
  investigationPriorities?: string; // priority vyšetřování
  generalNotes?: string;
  lastUpdatedAt?: Timestamp | FieldValue;
  createdAt?: Timestamp | FieldValue;
}

// ----- Typy pro Modul Řidičské průkazy -----
export interface TrafficOffense {
  id: string;
  date?: string;
  description?: string;
  location?: string;
  exactAddress?: string; // Přesná adresa přestupku
  gpsCoordinates?: string; // GPS souřadnice místa
  penaltyAmount?: string;
  pointsDeducted?: number | null;
  
  // Detaily o přestupku
  offenseType?: string; // Typ přestupku (rychlost, alkohol, atd.)
  officerName?: string; // Jméno policisty
  officerBadgeNumber?: string; // Číslo odznaku
  caseNumber?: string; // Číslo případu/protokolu
  vehicleUsed?: string; // Použité vozidlo (SPZ)
  
  // Právní následky
  legalStatus?: 'pending' | 'resolved' | 'appealed' | 'dismissed'; // Stav řízení
  courtDate?: string; // Datum soudu (pokud aplikovatelné)
  appealStatus?: string; // Stav odvolání
  
  // OSINT informace
  source?: InformationSource; // Zdroj informace
  sourceDetail?: string;
  verified?: boolean; // Ověřeno
  verificationDate?: string;
  
  // Doplňující dokumenty
  photos?: PhotoMetadata[]; // Fotografie (protokol, pokuta, atd.)
  
  notes?: string;
}

export interface DriverLicenseModuleData {
  id?: string;
  subjectId: string;
  
  // Základní údaje o řidičském průkazu
  licenseNumber?: string;
  validity?: string;
  validFrom?: string; // Datum vydání
  validTo?: string; // Platnost do
  issuedBy?: string; // Kdo vydal (která úřad)
  placeOfIssue?: string; // Místo vydání
  categories?: string;
  restrictions?: string;
  
  // Zdravotní způsobilost
  medicalFitness?: 'yes' | 'no' | 'limited' | 'unknown';
  medicalRestrictions?: string; // Popis zdravotních omezení
  medicalCertificateDate?: string; // Datum lékařského vyšetření
  medicalCertificateValidUntil?: string;
  
  // Historie řidičských průkazů
  previousLicenses?: string; // Historie předchozích ŘP
  licenseHistory?: string; // Detailní historie (výměny, duplikáty, atd.)
  
  // Evidenční karta řidiče
  pointsBalance?: number | null;
  currentBanStatus?: 'yes' | 'no';
  currentBanPeriod?: string;
  currentBanReason?: string;
  banHistory?: string;
  
  // Přestupky v dopravě
  offenses?: TrafficOffense[];
  
  // OSINT informace
  informationSource?: InformationSource;
  sourceDetail?: string;
  informationReliability?: InformationReliability;
  verificationDate?: string;
  lastVerified?: string;
  
  // Bezpečnostní hodnocení
  drivingRisk?: ThreatLevel; // Riziko v dopravě
  riskAssessment?: string; // Hodnocení rizika
  drivingBehaviorNotes?: string; // Poznámky k chování při řízení
  
  // Poznámky a dokumenty
  investigationNotes?: string;
  notes?: string;
  photos?: PhotoMetadata[]; // Změna z string[] na PhotoMetadata[]
  
  lastUpdatedAt?: Timestamp | FieldValue;
  createdAt?: Timestamp | FieldValue;
}

// ----- Typy pro Modul Vozidla -----
export type VehicleRelationshipType = "owner" | "user" | "former" | "other";

export interface VehicleRecord {
  id: string;
  relationshipType?: VehicleRelationshipType;
  otherRelationshipDetail?: string;
  make?: string;
  model?: string;
  licensePlate?: string;
  vin?: string;
  color?: string;
  yearManufactured?: number | null;
  stkValidUntil?: string;
  firstRegistered?: string;
  notes?: string;
  photos?: PhotoMetadata[];
}

export interface VehiclesModuleData {
  id?: string;
  subjectId: string;
  vehicles: VehicleRecord[];
  lastUpdatedAt?: Timestamp | FieldValue;
  createdAt?: Timestamp | FieldValue;
}

// ----- Typy pro Modul Zbrojní průkaz -----
export type GunLicenseOwnershipStatus = 'yes' | 'no' | 'expired' | 'in-process' | 'unknown';
export type GunLicenseGroup = 'A' | 'B' | 'C' | 'D' | 'E' | 'F';
export type HealthFitnessStatus = 'yes' | 'limited' | 'no' | 'unknown';
export type SecurityClearanceStatus = 'yes' | 'no' | 'unknown';
export type WeaponCategory = 'A' | 'A-I' | 'B' | 'C' | 'C-I' | 'D' | 'other';

// Nové typy pro OSINT analýzu zbrojních průkazů
export type WeaponLegalStatus = 
  | 'legal_registered' | 'legal_unregistered' | 'illegal_unregistered' 
  | 'stolen' | 'converted' | 'deactivated' | 'unknown';

export type WeaponCondition = 
  | 'excellent' | 'good' | 'fair' | 'poor' | 'damaged' | 'non_functional' | 'unknown';

export type WeaponAcquisitionMethod = 
  | 'purchase_legal' | 'purchase_illegal' | 'inheritance' | 'gift' 
  | 'theft' | 'found' | 'manufactured' | 'converted' | 'other' | 'unknown';

export type WeaponUsageContext = 
  | 'sport' | 'hunting' | 'collection' | 'self_defense' | 'professional' 
  | 'criminal' | 'threatening' | 'display' | 'unknown';

export type WeaponStorageMethod = 
  | 'gun_safe' | 'locked_cabinet' | 'trigger_lock' | 'ammunition_separate' 
  | 'carried' | 'hidden' | 'unsecured' | 'unknown';

export interface WeaponAccessory {
  id: string;
  type: string; // silencer, scope, laser_sight, etc.
  brand?: string;
  model?: string;
  description?: string;
  isLegal?: boolean;
  photos?: PhotoMetadata[];
}

export interface WeaponIncident {
  id: string;
  date?: string;
  incidentType: 'misuse' | 'accident' | 'threat' | 'display' | 'theft' | 'other';
  description?: string;
  location?: string;
  witnesses?: string[];
  policeInvolved?: boolean;
  caseNumber?: string;
  outcome?: string;
  photos?: PhotoMetadata[];
  notes?: string;
}

export interface RegisteredWeapon {
  id: string;
  
  // Základní údaje o zbrani
  weaponType?: string;
  brand?: string;
  model?: string;
  serialNumber?: string;
  category?: WeaponCategory;
  caliber?: string;
  yearManufactured?: number;
  countryOfOrigin?: string;
  
  // Právní stav
  legalStatus?: WeaponLegalStatus;
  registrationNumber?: string;
  registrationDate?: string;
  registrationAuthority?: string;
  permitNumber?: string;
  permitValidUntil?: string;
  
  // Fyzický stav a popis
  condition?: WeaponCondition;
  conditionDescription?: string;
  modifications?: string; // modifikace na zbrani
  distinguishingMarks?: string; // charakteristické znaky
  
  // Nabývání a vlastnictví
  acquisitionMethod?: WeaponAcquisitionMethod;
  acquisitionDate?: string;
  acquisitionSource?: string; // od koho/kde získána
  purchasePrice?: number;
  currentLocation?: string;
  
  // Použití a zacházení
  usageContext?: WeaponUsageContext;
  usageFrequency?: string;
  lastUsed?: string;
  storageMethod?: WeaponStorageMethod;
  storageLocation?: string;
  
  // Příslušenství
  accessories?: WeaponAccessory[];
  ammunitionType?: string;
  ammunitionQuantity?: number;
  
  // Incidenty a bezpečnost
  incidents?: WeaponIncident[];
  securityConcerns?: string;
  misusageReports?: string;
  
  // OSINT informace
  informationSource?: InformationSource;
  sourceDetail?: string;
  informationReliability?: InformationReliability;
  verificationDate?: string;
  lastVerified?: string;
  
  // Fotografie a dokumentace
  photos?: PhotoMetadata[];
  notes?: string;
}

export interface GunLicenseModuleData {
  id?: string;
  subjectId: string;

  // Základní informace o zbrojním průkazu
  ownershipStatus?: GunLicenseOwnershipStatus;
  licenseNumber?: string;
  issueDate?: string;
  validUntil?: string;
  issuedBy?: string;
  placeOfIssue?: string;
  groups?: GunLicenseGroup[];

  // Zdravotní a bezpečnostní způsobilost
  healthFitness?: HealthFitnessStatus;
  healthRestrictions?: string;
  medicalCertificateDate?: string;
  medicalCertificateValidUntil?: string;
  medicalExaminer?: string;
  
  securityClearance?: SecurityClearanceStatus;
  securityClearanceDate?: string;
  securityClearanceAuthority?: string;
  securityClearanceRestrictions?: string;
  
  // Historie a právní status
  history?: string;
  previousLicenses?: string;
  licenseViolations?: string;
  disciplinaryActions?: string;
  courtProceedings?: string;
  
  // Zbrojní licence (pro firmy)
  hasGunLicensePermit?: boolean;
  gunLicensePermitNumber?: string;
  gunLicensePermitGroups?: string;
  gunLicensePermitValidUntil?: string;
  businessLicenseInfo?: string;

  // Registrované zbraně
  registeredWeapons?: RegisteredWeapon[];
  
  // Střelecká činnost a výcvik
  shootingClubMembership?: string;
  shootingCompetitions?: string;
  weaponTraining?: string;
  instructorCertifications?: string;
  rangeActivity?: string;
  
  // Sociální a osobní kontext
  notesOnSurroundingPersons?: string;
  familyWeaponAccess?: string;
  weaponHandlingInfo?: string;
  weaponAccessNotes?: string;
  weaponStorageInfo?: string;
  securityMeasures?: string;
  
  // Absence průkazu nebo problémy
  reasonForNoLicense?: string;
  illegalPossessionInfo?: string;
  interestInWeapons?: string;
  weaponRelatedCriminalActivity?: string;
  weaponThreats?: string;
  
  // OSINT a bezpečnostní hodnocení
  informationSource?: InformationSource;
  sourceDetail?: string;
  informationReliability?: InformationReliability;
  verificationDate?: string;
  lastVerified?: string;
  
  weaponRiskAssessment?: ThreatLevel;
  riskAssessmentNotes?: string;
  surveillanceNotes?: string;
  investigationPriorities?: string;
  
  // Poznámky a dokumentace
  investigationNotes?: string;
  generalNotes?: string;
  photos?: PhotoMetadata[]; // Obecné fotografie modulu
  
  lastUpdatedAt?: Timestamp | FieldValue;
  createdAt?: Timestamp | FieldValue;
}

// ----- Typy pro Modul Lokace -----
export type LocationType =
  | "home" | "work" | "family" | "leisure" | "travel"
  | "hideout" | "meeting" | "other";

export type LocationFrequency = "frequent" | "occasional" | "rare" | "one-time";
export type LocationSource =
  | "social-media" | "surveillance" | "witness"
  | "documents" | "database" | "other";

export interface LocationRecord {
  id: string;
  name?: string;
  locationType?: LocationType;
  otherLocationTypeDetail?: string;
  address?: string;
  gpsCoordinates?: string; // např. "50.0872, 14.4212"
  frequency?: LocationFrequency;
  firstSeen?: string; // datum
  lastSeen?: string; // datum
  source?: LocationSource;
  otherSourceDetail?: string;
  descriptionNotes?: string;
  photos?: PhotoMetadata[];
}

export interface LocationsModuleData {
  id?: string;
  subjectId: string;
  locations: LocationRecord[];
  lastUpdatedAt?: Timestamp | FieldValue;
  createdAt?: Timestamp | FieldValue;
}

// ----- Typy pro Modul Podnikatelské aktivity (Firma) -----
export type BusinessActivityStatus = "active" | "passive" | "former" | "no" | "unknown";
export type MainBusinessType = "osvc" | "sro" | "as" | "ks" | "vos" | "druzstvo" | "spolecenstvi" | "neziskova" | "zahranicni" | "other";
export type YesNoUnknown = "yes" | "no" | "unknown";

export interface OsvcInfo {
  ico?: string;
  dic?: string;
  startDate?: string;
  endDate?: string;
  fieldsOfActivity?: string;
  actualBusinessScope?: string;
  businessSeat?: string;
  establishmentAddress?: string;
  estimatedAnnualTurnover?: number | null;
  estimatedCollaboratorsCount?: number | null;
}

export interface CompanyInvolvementRecord {
  id: string;
  name?: string;
  ico?: string;
  address?: string;
  legalForm?: string;
  establishmentDate?: string;
  role?: string;
  ownership?: string; // Percentage or description of ownership
  dic?: string;
  businessScope?: string;
  status?: string;
  statutoryRepresentatives?: string[];
  notes?: string;
}

export interface ForeignBusinessInvolvementRecord {
  id: string;
  name?: string;
  country?: string;
  description?: string;
}

export interface BusinessBankAccountRecord {
  id: string;
  accountNumber?: string;
  bankName?: string;
  notes?: string;
}

export interface BusinessActivityModuleData {
  id?: string;
  subjectId: string;

  activityStatus?: BusinessActivityStatus;
  mainBusinessType?: MainBusinessType;
  otherBusinessTypeDetail?: string;

  osvcInfo?: OsvcInfo;

  companyInvolvements?: CompanyInvolvementRecord[];

  foreignBusinessSummary?: string;
  foreignInvolvements?: ForeignBusinessInvolvementRecord[];

  personnelConnections?: string;
  // networkMapPhotos?: PhotoMetadata[]; // Bude přidáno později

  insolvencyStatus?: YesNoUnknown;
  insolvencyDetails?: string;

  taxIssuesStatus?: YesNoUnknown;
  taxIssuesDetails?: string;

  publicContractsGrantsInfo?: string;
  bankAccounts?: BusinessBankAccountRecord[];
  // generalBusinessPhotos?: PhotoMetadata[]; // Bude přidáno později

  generalNotes?: string;

  lastUpdatedAt?: Timestamp | FieldValue;
  createdAt?: Timestamp | FieldValue;
}

// ----- Typy pro Modul Katastr nemovitostí -----
export type PropertyType =
  | "residential_house" | "residential_apartment" | "commercial"
  | "industrial" | "agricultural" | "land" | "forest" | "other";

export type PropertyOwnershipType =
  | "full_ownership" | "co_ownership" | "shared_ownership"
  | "cooperative" | "leased" | "other";

export type PropertyAcquisitionMethod =
  | "purchase" | "inheritance" | "gift" | "construction"
  | "exchange" | "privatization" | "restitution" | "other";

export type PropertyUsageType =
  | "personal_use" | "rental" | "business" | "vacant"
  | "agricultural" | "recreational" | "other";

export type PropertySource =
  | "cadastre" | "land_registry" | "public_records"
  | "witness" | "surveillance" | "social_media" | "other";

export interface PropertyRecord {
  id: string;

  // Basic information
  name?: string;
  propertyType?: PropertyType;
  otherPropertyTypeDetail?: string;

  // Location information
  address?: string;
  gpsCoordinates?: string; // "latitude,longitude" format
  cadastralArea?: string;
  cadastralNumber?: string;
  landRegistryNumber?: string;
  parcelNumbers?: string[];

  // Ownership information
  ownershipType?: PropertyOwnershipType;
  otherOwnershipTypeDetail?: string;
  ownershipPercentage?: number;
  coOwners?: string[];
  acquisitionMethod?: PropertyAcquisitionMethod;
  otherAcquisitionMethodDetail?: string;
  acquisitionDate?: string;

  // Financial information
  purchasePrice?: number;
  purchaseCurrency?: string;
  currentEstimatedValue?: number;
  valueCurrency?: string;
  mortgageInfo?: string;
  otherEncumbrances?: string;

  // Usage information
  usageType?: PropertyUsageType;
  otherUsageTypeDetail?: string;
  rentalIncome?: number;
  rentalCurrency?: string;
  tenants?: string;

  // Physical characteristics
  totalArea?: number; // in square meters
  buildingArea?: number; // in square meters
  landArea?: number; // in square meters
  numberOfRooms?: number;
  numberOfFloors?: number;
  constructionYear?: number;
  reconstructionYear?: number;

  // Source information
  source?: PropertySource;
  otherSourceDetail?: string;
  verificationStatus?: YesNoUnknown;
  verificationNotes?: string;

  // Additional information
  description?: string;
  notes?: string;
  photos?: PhotoMetadata[];

  // Related documents
  documentReferences?: string;
}

export interface CadastreModuleData {
  id?: string;
  subjectId: string;
  properties: PropertyRecord[];
  generalNotes?: string;
  lastUpdatedAt?: Timestamp | FieldValue;
  createdAt?: Timestamp | FieldValue;
}

// ----- Typy pro Modul Školení a výcvik -----
export type TrainingType =
  | "professional" | "military" | "security" | "weapons"
  | "martial_arts" | "driving" | "aviation" | "diving"
  | "survival" | "medical" | "technical" | "language"
  | "police" | "intelligence" | "special_forces" | "combat"
  | "tactical" | "explosives" | "sniper" | "reconnaissance"
  | "other";

export type TrainingStatus =
  | "completed" | "ongoing" | "planned" | "abandoned" | "failed";

export type CertificateValidity =
  | "valid" | "expired" | "revoked" | "unknown";

export type TrainingSource =
  | "self_reported" | "verified" | "social_media" | "witness"
  | "intelligence" | "public_records" | "other";

export type MartialArtsBeltColor =
  | "white" | "yellow" | "orange" | "green" | "blue"
  | "purple" | "brown" | "black" | "red" | "other";

export type MilitaryRank =
  | "private" | "corporal" | "sergeant" | "lieutenant"
  | "captain" | "major" | "colonel" | "general" | "other";

export type PoliceRank =
  | "rotny" | "strazmistr" | "nadstrazmistr" | "podpraporcik"
  | "praporcik" | "nadpraporcik" | "podporucik" | "porucik"
  | "nadporucik" | "kapitan" | "major" | "podplukovnik"
  | "plukovnik" | "general" | "other";

export type DangerAssessment =
  | "none" | "low" | "medium" | "high" | "extreme" | "unknown";

export interface TrainingRecord {
  id: string;
  name: string;
  trainingType: TrainingType;
  otherTrainingTypeDetail?: string;
  institution?: string;
  location?: string;
  startDate?: string;
  endDate?: string;
  status: TrainingStatus;
  description?: string;
  skills?: string[];

  // Certifikace
  certificateObtained: boolean;
  certificateName?: string;
  certificateId?: string;
  certificateValidity?: CertificateValidity;
  certificateValidUntil?: string;

  // Informace o zdroji
  source?: TrainingSource;
  otherSourceDetail?: string;
  verificationStatus?: YesNoUnknown;
  verificationNotes?: string;

  // Rozšířené informace o bojových uměních
  martialArtStyle?: string;
  beltColor?: MartialArtsBeltColor;
  beltDegree?: number; // Dan/Kyu stupeň
  competitionAchievements?: string;

  // Vojenské informace
  militaryBranch?: string; // Složka (pozemní síly, letectvo, námořnictvo, atd.)
  militarySpecialization?: string; // Specializace (pěchota, dělostřelectvo, atd.)
  militaryRank?: MilitaryRank;
  militaryUnit?: string;
  conscriptionService?: boolean; // Povinná vojenská služba
  conscriptionYears?: string; // Období povinné vojenské služby

  // Policejní informace
  policeDepartment?: string;
  policeSpecialization?: string;
  policeRank?: PoliceRank;
  policeUnit?: string;

  // Informace o zbraních
  weaponTypes?: string[]; // Typy zbraní, se kterými umí zacházet
  weaponLicenses?: string[]; // Licence na zbraně
  explosivesTraining?: boolean; // Výcvik s výbušninami

  // Hodnocení nebezpečnosti
  dangerAssessment?: DangerAssessment;
  dangerAssessmentReason?: string;

  // Obecné poznámky a dokumentace
  notes?: string;
  photos?: PhotoMetadata[];
  documentReferences?: string;
}

export interface TrainingModuleData {
  id?: string;
  subjectId: string;
  trainings: TrainingRecord[];
  generalNotes?: string;
  lastUpdatedAt?: Timestamp | FieldValue;
  createdAt?: Timestamp | FieldValue;
}

// ----- Typy pro Modul Emailové analýzy -----
export type EmailSource =
  | "osint" | "investigation" | "public_data" | "social_media"
  | "data_breach" | "direct_communication" | "other";

export type EmailVerificationStatus =
  | "verified" | "unverified" | "invalid" | "disposable" | "unknown";

export type DataBreachSeverity =
  | "none" | "low" | "medium" | "high" | "critical" | "unknown";

export interface DataBreachRecord {
  id: string;
  source: string;
  date: string;
  recordsCount: string;
  compromisedData: string[];
  description?: string;
}

export interface ConnectedProfile {
  id: string;
  platformType: string;
  platformName: string;
  username: string;
  profileUrl?: string;
  notes?: string;
  verificationStatus?: YesNoUnknown;
}

export interface EmailHeadersAnalysis {
  senderIp?: string;
  ipGeolocation?: string;
  emailClient?: string;
  authResults?: string;
  emailRoute?: string[];
  rawHeaders?: string;
  analysisDate?: string;
}

export interface EmailRecord {
  id: string;
  emailAddress: string;
  source: EmailSource;
  otherSourceDetail?: string;
  discoveryDate?: string;
  verificationStatus: EmailVerificationStatus;
  verificationDate?: string;
  probableName?: string;
  associatedOrganization?: string;
  dataBreaches?: DataBreachRecord[];
  connectedProfiles?: ConnectedProfile[];
  headersAnalysis?: EmailHeadersAnalysis;
  notes?: string;
  photos?: PhotoMetadata[];
}

export interface EmailAnalysisModuleData {
  id?: string;
  subjectId: string;
  emails: EmailRecord[];
  generalNotes?: string;
  lastUpdatedAt?: Timestamp | FieldValue;
  createdAt?: Timestamp | FieldValue;
}

// ----- Typy pro Modul Telefonní čísla -----
export type PhoneSource =
  | "osint" | "investigation" | "public_data" | "social_media"
  | "direct_communication" | "official_document" | "other";

export type PhoneVerificationStatus =
  | "verified" | "unverified" | "invalid" | "disconnected" | "unknown";

export type PhoneType =
  | "mobile" | "landline" | "business" | "fax" | "voip" | "other";

export type PhoneCarrier =
  | "o2" | "t-mobile" | "vodafone" | "other" | "unknown";

export type PhoneServiceRegistration =
  | "whatsapp" | "signal" | "telegram" | "viber" | "facebook" | "other";

export interface PhoneContact {
  id: string;
  name: string;
  phoneNumber: string;
  relationship?: string;
  notes?: string;
}

export interface PhoneActivity {
  id: string;
  date: string;
  activityType: string;
  description: string;
  location?: string;
  duration?: string;
  notes?: string;
}

export interface PhoneValidationResult {
  isValid: boolean;
  internationalFormat?: string;
  countryCode?: string;
  countryName?: string;
  carrier?: string;
  phoneType?: PhoneType;
  validationDate?: string;
}

export interface PhoneRecord {
  id: string;
  phoneNumber: string;
  source: PhoneSource;
  otherSourceDetail?: string;
  discoveryDate?: string;
  verificationStatus: PhoneVerificationStatus;
  verificationDate?: string;
  phoneType?: PhoneType;
  carrier?: PhoneCarrier;
  owner?: string;
  associatedOrganization?: string;
  registeredServices?: PhoneServiceRegistration[];
  contacts?: PhoneContact[];
  activities?: PhoneActivity[];
  validationResult?: PhoneValidationResult;
  notes?: string;
  photos?: PhotoMetadata[];
}

export interface PhoneNumbersModuleData {
  id?: string;
  subjectId: string;
  phones: PhoneRecord[];
  generalNotes?: string;
  lastUpdatedAt?: Timestamp | FieldValue;
  createdAt?: Timestamp | FieldValue;
}

// ----- Typy pro Modul IP adresy a síťová analýza -----
export type IpAddressType = "ipv4" | "ipv6";
export type IpAddressSource =
  | "osint" | "investigation" | "public_data" | "social_media"
  | "direct_communication" | "server_logs" | "email_headers"
  | "network_scan" | "other";

export type IpAddressUsage =
  | "residential" | "business" | "hosting" | "vpn" | "proxy"
  | "tor_exit_node" | "mobile" | "datacenter" | "other";

export type IpAddressVerificationStatus =
  | "verified" | "unverified" | "invalid" | "suspicious" | "unknown";

export type NetworkProtocol =
  | "http" | "https" | "ftp" | "ssh" | "telnet" | "smtp"
  | "pop3" | "imap" | "dns" | "rdp" | "vnc" | "other";

export type NetworkDeviceType =
  | "router" | "switch" | "firewall" | "server" | "desktop"
  | "laptop" | "mobile" | "iot" | "other";

export type NetworkConnectionType =
  | "direct" | "vpn" | "proxy" | "tor" | "other";

export interface GeoLocation {
  country?: string;
  region?: string;
  city?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  timezone?: string;
  isp?: string;
  organization?: string;
  asn?: string;
  asnOrganization?: string;
}

export interface DomainRecord {
  id: string;
  domainName: string;
  registrationDate?: string;
  expirationDate?: string;
  registrar?: string;
  registrantName?: string;
  registrantOrganization?: string;
  registrantEmail?: string;
  registrantPhone?: string;
  nameservers?: string[];
  ipAddresses?: string[];
  notes?: string;
  photos?: PhotoMetadata[];
}

export interface PortScanResult {
  id: string;
  port: number;
  protocol: NetworkProtocol;
  service?: string;
  version?: string;
  status: "open" | "closed" | "filtered";
  banner?: string;
  scanDate: string;
  notes?: string;
}

export interface NetworkActivity {
  id: string;
  date: string;
  activityType: string;
  description: string;
  sourceIp?: string;
  destinationIp?: string;
  protocol?: NetworkProtocol;
  port?: number;
  userAgent?: string;
  notes?: string;
}

export interface NetworkDevice {
  id: string;
  name?: string;
  deviceType: NetworkDeviceType;
  otherDeviceTypeDetail?: string;
  macAddress?: string;
  ipAddress?: string;
  manufacturer?: string;
  model?: string;
  operatingSystem?: string;
  firmwareVersion?: string;
  discoveryDate?: string;
  lastSeenDate?: string;
  vulnerabilities?: string[];
  notes?: string;
  photos?: PhotoMetadata[];
}

export interface MalwareIndicator {
  id: string;
  indicatorType: "hash" | "filename" | "url" | "ip" | "domain" | "other";
  value: string;
  malwareFamily?: string;
  detectionDate?: string;
  source?: string;
  severity?: "low" | "medium" | "high" | "critical";
  notes?: string;
}

export interface IpAddressRecord {
  id: string;
  ipAddress: string;
  ipType: IpAddressType;
  source: IpAddressSource;
  otherSourceDetail?: string;
  discoveryDate?: string;
  verificationStatus: IpAddressVerificationStatus;
  verificationDate?: string;
  usage?: IpAddressUsage;
  otherUsageDetail?: string;
  hostname?: string;
  macAddress?: string;
  connectionType?: NetworkConnectionType;
  otherConnectionTypeDetail?: string;
  owner?: string;
  associatedOrganization?: string;
  geoLocation?: GeoLocation;
  portScanResults?: PortScanResult[];
  activities?: NetworkActivity[];
  malwareIndicators?: MalwareIndicator[];
  relatedDomains?: string[]; // IDs of related domains
  relatedDevices?: string[]; // IDs of related devices
  notes?: string;
  photos?: PhotoMetadata[];
}

export interface NetworkAnalysisModuleData {
  id?: string;
  subjectId: string;
  ipAddresses: IpAddressRecord[];
  domains: DomainRecord[];
  devices: NetworkDevice[];
  networkMap?: string; // URL to network map visualization
  generalNotes?: string;
  lastUpdatedAt?: Timestamp | FieldValue;
  createdAt?: Timestamp | FieldValue;
}

// ----- Typy pro Modul Mapové overlapy -----
export type MapBasemapType = "streets" | "satellite" | "terrain" | "dark" | "hybrid" | "topo";
export type MapPointType = "general" | "crime" | "person" | "vehicle" | "building" | "surveillance" | "meeting" | "evidence" | "other";
export type MapAreaType = "general" | "crime_area" | "surveillance" | "restricted" | "search" | "operation" | "other";
export type MapRouteType = "general" | "vehicle_route" | "person_route" | "escape_route" | "patrol" | "surveillance" | "other";
export type MapLayerType = "points" | "areas" | "routes" | "heatmap" | "clusters" | "external";

export interface MapPoint {
  id: string;
  name: string;
  description?: string;
  pointType: MapPointType;
  otherPointTypeDetail?: string;
  latitude: number;
  longitude: number;
  address?: string;
  date?: string;
  category?: string;
  icon?: string;
  color?: string;
  photos?: PhotoMetadata[];
  relatedSubjects?: string[];
  relatedPoints?: string[];
  notes?: string;
}

export interface MapArea {
  id: string;
  name: string;
  description?: string;
  areaType: MapAreaType;
  otherAreaTypeDetail?: string;
  coordinates: Array<{lat: number, lng: number}>;
  address?: string;
  date?: string;
  category?: string;
  color?: string;
  photos?: PhotoMetadata[];
  relatedSubjects?: string[];
  relatedAreas?: string[];
  notes?: string;
  areaSqKm?: number;
}

export interface MapRoute {
  id: string;
  name: string;
  description?: string;
  routeType: MapRouteType;
  otherRouteTypeDetail?: string;
  coordinates: Array<{lat: number, lng: number}>;
  startAddress?: string;
  endAddress?: string;
  startDate?: string;
  endDate?: string;
  category?: string;
  color?: string;
  photos?: PhotoMetadata[];
  relatedSubjects?: string[];
  relatedRoutes?: string[];
  notes?: string;
  distanceKm?: number;
}

export interface MapExternalLayer {
  id: string;
  name: string;
  type: string;
  url: string;
  apiKey?: string;
  isVisible: boolean;
  opacity: number;
  zIndex: number;
  attribution?: string;
  notes?: string;
}

export interface MapAnalysisResult {
  id: string;
  name: string;
  description?: string;
  analysisType: string;
  date: string;
  targetArea?: string;
  targetRoute?: string;
  parameters?: Record<string, any>;
  results?: Record<string, any>;
  visualizationSettings?: Record<string, any>;
  notes?: string;
}

export interface MapOverlaysModuleData {
  id?: string;
  subjectId: string;
  points: MapPoint[];
  areas: MapArea[];
  routes: MapRoute[];
  externalLayers: MapExternalLayer[];
  analysisResults: MapAnalysisResult[];
  defaultCenter?: {lat: number, lng: number};
  defaultZoom?: number;
  defaultBasemap?: MapBasemapType;
  visibleLayers?: MapLayerType[];
  mapSettings?: Record<string, any>;
  generalNotes?: string;
  lastUpdatedAt?: Timestamp | FieldValue;
  createdAt?: Timestamp | FieldValue;
}

export type { TwitterModuleData }; // Export new types

// Export phone analysis types
export type { PhoneAnalysisModuleData } from './phoneAnalysis';

// ----- Typy pro Modul Finanční monitoring -----

export type FinancialRegistryType =
  | "business_registry" | "ares" | "insolvency" | "execution" | "tax_debtors"
  | "vat_registry" | "cedr_subsidies" | "public_contracts" | "court_proceedings"
  | "property_registry" | "land_registry" | "patents" | "trademarks" | "databox"
  | "other";

export type FinancialInformationReliability =
  | "verified" | "probable" | "possible" | "unconfirmed" | "unknown";

export type FinancialInformationSource =
  | "official_registry" | "court_records" | "tax_authority" | "social_media"
  | "surveillance" | "witness" | "documents" | "investigation" | "open_source" | "other";

export type FinancialStatus =
  | "active" | "inactive" | "suspended" | "liquidated" | "bankrupt" | "unknown";

export type ExecutionStatus =
  | "active" | "completed" | "stopped" | "unsuccessful" | "unknown";

export type InsolvencyStatus =
  | "no_proceedings" | "petition_filed" | "proceedings_started" | "reorganization"
  | "bankruptcy" | "discharge" | "proceedings_stopped" | "unknown";

export type TaxDebtorStatus =
  | "no_debt" | "has_debt" | "cleared" | "enforcement" | "unknown";

export type PropertyOwnershipStatus =
  | "sole_owner" | "co_owner" | "former_owner" | "lessee" | "user" | "unknown";

export interface FinancialRegistryRecord {
  id: string;
  registryType: FinancialRegistryType;
  otherRegistryTypeDetail?: string;
  
  // Identifikace subjektu
  subjectName?: string;
  ico?: string; // IČO pro firmy
  dic?: string; // DIČ
  personalIdNumber?: string; // Rodné číslo pro fyzické osoby
  birthDate?: string; // Datum narození
  
  // Registry information
  registryNumber?: string;
  registrationDate?: string;
  validFrom?: string;
  validTo?: string;
  issuingAuthority?: string;
  
  // Status informace
  currentStatus?: FinancialStatus;
  statusDate?: string;
  statusDetails?: string;
  
  // Právní informace
  legalForm?: string;
  businessScope?: string;
  registeredOffice?: string;
  
  // Insolvenční řízení
  insolvencyStatus?: InsolvencyStatus;
  insolvencyNumber?: string;
  insolvencyDate?: string;
  insolvencyAdministrator?: string;
  debtAmount?: number;
  creditorsList?: string;
  
  // Exekuce
  executionStatus?: ExecutionStatus;
  executionNumber?: string;
  executionDate?: string;
  executor?: string;
  debtorAmount?: number;
  executionReason?: string;
  
  // Daňové dluhy
  taxDebtorStatus?: TaxDebtorStatus;
  taxDebtAmount?: number;
  taxDebtDetails?: string;
  
  // Dotace a granty
  subsidyAmount?: number;
  subsidyProvider?: string;
  subsidyPurpose?: string;
  subsidyDate?: string;
  
  // Veřejné zakázky
  contractAmount?: number;
  contractDescription?: string;
  contractingAuthority?: string;
  contractDate?: string;
  
  // Majetkové záznamy
  propertyType?: string;
  propertyAddress?: string;
  propertyOwnershipStatus?: PropertyOwnershipStatus;
  propertyValue?: number;
  propertyAcquisitionDate?: string;
  
  // OSINT informace
  informationSource?: FinancialInformationSource;
  sourceDetail?: string;
  informationReliability?: FinancialInformationReliability;
  verificationDate?: string;
  lastVerified?: string;
  
  // Dokumentace
  photos?: PhotoMetadata[];
  documentReferences?: string;
  notes?: string;
}

export interface FinancialConnectionRecord {
  id: string;
  connectedEntityName?: string;
  connectedEntityICO?: string;
  connectionType?: string; // vlastník, jednatel, společník, dodavatel, zákazník
  connectionDescription?: string;
  ownershipPercentage?: number;
  startDate?: string;
  endDate?: string;
  isActive?: boolean;
  notes?: string;
}

export interface FinancialAssetRecord {
  id: string;
  assetType?: string; // nemovitost, vozidlo, účet, pohledávka, cenný papír
  assetName?: string;
  assetValue?: number;
  valueCurrency?: string;
  acquisitionDate?: string;
  acquisitionMethod?: string; // koupě, dědictví, dar, atd.
  currentLocation?: string;
  ownershipStatus?: PropertyOwnershipStatus;
  mortgageInfo?: string;
  legalRestrictions?: string;
  photos?: PhotoMetadata[];
  notes?: string;
}

export interface FinancialTransactionRecord {
  id: string;
  date?: string;
  amount?: number;
  currency?: string;
  transactionType?: string; // příjem, výdaj, převod, investice
  counterparty?: string; // druhá strana transakce
  description?: string;
  accountNumber?: string;
  bankName?: string;
  reference?: string;
  isRegular?: boolean; // pravidelná transakce
  frequency?: string; // frekvence (měsíčně, čtvrtletně, atd.)
  suspiciousActivity?: boolean;
  riskLevel?: ThreatLevel;
  source?: FinancialInformationSource;
  notes?: string;
}

export interface FinancialRiskAssessment {
  overallRisk?: ThreatLevel;
  insolvencyRisk?: ThreatLevel;
  liquidityRisk?: ThreatLevel;
  legalRisk?: ThreatLevel;
  reputationRisk?: ThreatLevel;
  
  riskFactors?: string[];
  mitigatingFactors?: string[];
  riskAssessmentDate?: string;
  assessedBy?: string;
  nextReviewDate?: string;
  
  riskNotes?: string;
}

export interface FinancialMonitoringModuleData {
  id?: string;
  subjectId: string;
  
  // Registry records
  registryRecords?: FinancialRegistryRecord[];
  
  // Connections and relationships
  businessConnections?: FinancialConnectionRecord[];
  familyConnections?: FinancialConnectionRecord[];
  
  // Assets and liabilities
  assets?: FinancialAssetRecord[];
  liabilities?: string; // obecný popis závazků
  
  // Financial transactions
  transactions?: FinancialTransactionRecord[];
  bankAccounts?: string[]; // čísla účtů
  
  // Income and expenses analysis
  estimatedIncome?: number;
  incomeCurrency?: string;
  incomeSource?: string;
  estimatedExpenses?: number;
  expensesCurrency?: string;
  lifestyleAnalysis?: string;
  
  // Tax and legal status
  taxCompliance?: YesNoUnknown;
  taxIssues?: string;
  legalProceedings?: string;
  courtCases?: string;
  
  // Business activity (pro fyzické osoby s podnikáním)
  businessActivity?: YesNoUnknown;
  businessType?: string;
  businessIncome?: number;
  businessAssets?: string;
  
  // Money laundering and suspicious activity
  suspiciousTransactions?: string;
  mlRiskFactors?: string[];
  complianceIssues?: string;
  sanctionsCheck?: YesNoUnknown;
  
  // Risk assessment
  riskAssessment?: FinancialRiskAssessment;
  
  // Investigation priorities
  investigationPriorities?: string;
  recommendedActions?: string;
  followUpRequired?: YesNoUnknown;
  nextReviewDate?: string;
  
  // OSINT sources and verification
  informationSources?: string;
  lastVerificationDate?: string;
  dataQualityAssessment?: string;
  
  // General information
  executiveSummary?: string;
  keyFindings?: string;
  redFlags?: string;
  
  // Notes and documentation
  investigationNotes?: string;
  generalNotes?: string;
  photos?: PhotoMetadata[]; // Obecné fotografie modulu
  
  lastUpdatedAt?: Timestamp | FieldValue;
  createdAt?: Timestamp | FieldValue;
}
