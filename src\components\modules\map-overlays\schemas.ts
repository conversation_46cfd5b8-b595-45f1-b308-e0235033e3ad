import { z } from "zod";
import { v4 as uuidv4 } from "uuid";

// Schémata pro validaci formulář<PERSON>

export const mapPointSchema = z.object({
  id: z.string().optional().default(() => uuidv4()),
  name: z.string().min(1, "<PERSON><PERSON><PERSON>v je povinný"),
  description: z.string().optional(),
  pointType: z.enum(["general", "crime", "person", "vehicle", "building", "surveillance", "meeting", "evidence", "other"]),
  otherPointTypeDetail: z.string().optional(),
  latitude: z.number(),
  longitude: z.number(),
  address: z.string().optional(),
  date: z.string().optional(),
  category: z.string().optional(),
  icon: z.string().optional(),
  color: z.string().optional(),
  photos: z.array(
    z.object({
      id: z.string(),
      fileName: z.string().optional(),
      storagePath: z.string().optional(),
      downloadURL: z.string().optional(),
      description: z.string().optional(),
      dateTaken: z.string().optional(),
      sourceURL: z.string().optional(),
    })
  ).optional().default([]),
  relatedSubjects: z.array(z.string()).optional().default([]),
  relatedPoints: z.array(z.string()).optional().default([]),
  notes: z.string().optional(),
});

export const mapCoordinateSchema = z.object({
  lat: z.number(),
  lng: z.number(),
});

export const mapAreaSchema = z.object({
  id: z.string().optional().default(() => uuidv4()),
  name: z.string().min(1, "Název je povinný"),
  description: z.string().optional(),
  areaType: z.enum(["general", "crime_area", "surveillance", "restricted", "search", "operation", "other"]),
  otherAreaTypeDetail: z.string().optional(),
  coordinates: z.array(mapCoordinateSchema).min(3, "Oblast musí mít alespoň 3 body"),
  address: z.string().optional(),
  date: z.string().optional(),
  category: z.string().optional(),
  color: z.string().optional(),
  photos: z.array(
    z.object({
      id: z.string(),
      fileName: z.string().optional(),
      storagePath: z.string().optional(),
      downloadURL: z.string().optional(),
      description: z.string().optional(),
      dateTaken: z.string().optional(),
      sourceURL: z.string().optional(),
    })
  ).optional().default([]),
  relatedSubjects: z.array(z.string()).optional().default([]),
  relatedAreas: z.array(z.string()).optional().default([]),
  notes: z.string().optional(),
  areaSqKm: z.number().optional(),
});

export const mapRouteSchema = z.object({
  id: z.string().optional().default(() => uuidv4()),
  name: z.string().min(1, "Název je povinný"),
  description: z.string().optional(),
  routeType: z.enum(["general", "vehicle_route", "person_route", "escape_route", "patrol", "surveillance", "other"]),
  otherRouteTypeDetail: z.string().optional(),
  coordinates: z.array(mapCoordinateSchema).min(2, "Trasa musí mít alespoň 2 body"),
  startAddress: z.string().optional(),
  endAddress: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  category: z.string().optional(),
  color: z.string().optional(),
  photos: z.array(
    z.object({
      id: z.string(),
      fileName: z.string().optional(),
      storagePath: z.string().optional(),
      downloadURL: z.string().optional(),
      description: z.string().optional(),
      dateTaken: z.string().optional(),
      sourceURL: z.string().optional(),
    })
  ).optional().default([]),
  relatedSubjects: z.array(z.string()).optional().default([]),
  relatedRoutes: z.array(z.string()).optional().default([]),
  notes: z.string().optional(),
  distanceKm: z.number().optional(),
});

export const mapExternalLayerSchema = z.object({
  id: z.string().optional().default(() => uuidv4()),
  name: z.string().min(1, "Název je povinný"),
  type: z.string().min(1, "Typ je povinný"),
  url: z.string().url("Neplatná URL"),
  apiKey: z.string().optional(),
  isVisible: z.boolean().default(true),
  opacity: z.number().min(0).max(1).default(1),
  zIndex: z.number().default(0),
  attribution: z.string().optional(),
  notes: z.string().optional(),
});

export const mapAnalysisResultSchema = z.object({
  id: z.string().optional().default(() => uuidv4()),
  name: z.string().min(1, "Název je povinný"),
  description: z.string().optional(),
  analysisType: z.string().min(1, "Typ analýzy je povinný"),
  date: z.string(),
  targetArea: z.string().optional(),
  targetRoute: z.string().optional(),
  parameters: z.record(z.any()).optional(),
  results: z.record(z.any()).optional(),
  visualizationSettings: z.record(z.any()).optional(),
  notes: z.string().optional(),
});

export const mapOverlaysModuleSchema = z.object({
  points: z.array(mapPointSchema).default([]),
  areas: z.array(mapAreaSchema).default([]),
  routes: z.array(mapRouteSchema).default([]),
  externalLayers: z.array(mapExternalLayerSchema).default([]),
  analysisResults: z.array(mapAnalysisResultSchema).default([]),
  defaultCenter: z.object({
    lat: z.number(),
    lng: z.number(),
  }).optional(),
  defaultZoom: z.number().optional(),
  defaultBasemap: z.enum(["streets", "satellite", "terrain", "dark", "hybrid", "topo"]).optional(),
  visibleLayers: z.array(z.enum(["points", "areas", "routes", "heatmap", "clusters", "external"])).optional(),
  mapSettings: z.record(z.any()).optional(),
  generalNotes: z.string().optional(),
});

export type MapPointFormValues = z.infer<typeof mapPointSchema>;
export type MapAreaFormValues = z.infer<typeof mapAreaSchema>;
export type MapRouteFormValues = z.infer<typeof mapRouteSchema>;
export type MapExternalLayerFormValues = z.infer<typeof mapExternalLayerSchema>;
export type MapAnalysisResultFormValues = z.infer<typeof mapAnalysisResultSchema>;
export type MapOverlaysModuleFormValues = z.infer<typeof mapOverlaysModuleSchema>;

// Konstanty pro výběrové seznamy
export const mapPointTypes = ["general", "crime", "person", "vehicle", "building", "surveillance", "meeting", "evidence", "other"] as const;
export const mapAreaTypes = ["general", "crime_area", "surveillance", "restricted", "search", "operation", "other"] as const;
export const mapRouteTypes = ["general", "vehicle_route", "person_route", "escape_route", "patrol", "surveillance", "other"] as const;
export const mapBasemapTypes = ["streets", "satellite", "terrain", "dark", "hybrid", "topo"] as const;
export const mapLayerTypes = ["points", "areas", "routes", "heatmap", "clusters", "external"] as const;
