
"use client";

import { useEffect, useState } from "react";
import type { SubmitHandler } from 'react-hook-form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Loader2, Search } from "lucide-react";
import { db } from "@/lib/firebase";
import { collection, addDoc, serverTimestamp } from "firebase/firestore";
import type { PhysicalPersonSubject, LegalEntitySubject } from "@/types";
import { useToast } from "@/hooks/use-toast";
import { lookupCompanyInAresDirectly } from "@/ai/flows/ares-direct-lookup-flow"; // Import ARES flow

// Schémata pro validaci
const formSchema = z.object({
    subjectType: z.enum(['physical', 'legal'], { required_error: "Musíte vybrat typ subjektu." }),
    firstName: z.string().optional(),
    lastName: z.string().optional(),
    dateOfBirth: z.string().optional(),
    nationality: z.string().optional(),
    companyId: z.string().optional(), // IČO
    name: z.string().optional(), // Název společnosti pro právnickou osobu
    address: z.string().optional(),
    notes: z.string().optional(),
  }).superRefine((data, ctx) => {
    if (data.subjectType === 'physical') {
      if (!data.firstName?.trim()) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Jméno je povinné.", path: ['firstName'] });
      }
      if (!data.lastName?.trim()) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Příjmení je povinné.", path: ['lastName'] });
      }
    } else if (data.subjectType === 'legal') {
      if (!data.companyId?.trim()) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, message: "IČO je povinné.", path: ['companyId'] });
      }
      if (!data.name?.trim()) {
        ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Název společnosti je povinný.", path: ['name'] });
      }
    }
  });

type FormValues = z.infer<typeof formSchema>;

interface AddSubjectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  caseId: string;
  onSubjectAdded: () => void;
}

export function AddSubjectDialog({ open, onOpenChange, caseId, onSubjectAdded }: AddSubjectDialogProps) {
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [isAresLoading, setIsAresLoading] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      subjectType: undefined,
      firstName: "",
      lastName: "",
      dateOfBirth: "",
      nationality: "",
      companyId: "",
      name: "",
      address: "",
      notes: "",
    },
  });

  useEffect(() => {
    if (open) {
      console.log("AddSubjectDialog rendered or props changed. Open:", open, "Case ID:", caseId);
      form.reset({
        subjectType: undefined,
        firstName: "",
        lastName: "",
        dateOfBirth: "",
        nationality: "",
        companyId: "",
        name: "",
        address: "",
        notes: "",
      });
      setIsSaving(false);
      setIsAresLoading(false);
    }
  }, [open, form, caseId]);

  const handleAresLookup = async () => {
    const companyId = form.getValues("companyId");
    if (!companyId || companyId.trim().length < 8) {
      toast({
        title: "Chybné IČO",
        description: "Zadejte platné IČO (minimálně 8 číslic) pro vyhledání v ARES.",
        variant: "destructive",
      });
      form.setError("companyId", { type: "manual", message: "Zadejte platné IČO." });
      return;
    }
    setIsAresLoading(true);
    try {
      const aresData = await lookupCompanyInAresDirectly({ companyId });
      if (aresData) {
        form.setValue("name", aresData.name, { shouldValidate: true });
        form.setValue("address", aresData.address, { shouldValidate: true });
        
        let notesContent = "";
        if (aresData.legalForm) notesContent += `Právní forma: ${aresData.legalForm}\n`;
        if (aresData.establishmentDate) notesContent += `Datum založení: ${aresData.establishmentDate}`;
        
        const currentNotes = form.getValues("notes");
        form.setValue("notes", (currentNotes ? currentNotes + "\n\n" : "") + "Data z ARES:\n" + notesContent.trim());

        toast({ title: "Data z ARES načtena", description: `Nalezen subjekt: ${aresData.name}` });
      } else {
        toast({ title: "Data z ARES", description: "Pro zadané IČO nebyla nalezena žádná data.", variant: "default" });
      }
    } catch (error: any) {
      console.error("ARES lookup error in AddSubjectDialog:", error);
      toast({
        title: "Chyba při načítání z ARES",
        description: error.message || "Nepodařilo se načíst data.",
        variant: "destructive",
      });
    } finally {
      setIsAresLoading(false);
    }
  };

  const onSubmit: SubmitHandler<FormValues> = async (data) => {
    setIsSaving(true);
    try {
      let subjectDataToSave;

      if (data.subjectType === 'physical') {
        subjectDataToSave = {
          type: 'physical' as 'physical',
          firstName: data.firstName!,
          lastName: data.lastName!,
          dateOfBirth: data.dateOfBirth || '',
          nationality: data.nationality || '',
          caseId: caseId,
          addedAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        } satisfies Omit<PhysicalPersonSubject, 'id'>;
      } else if (data.subjectType === 'legal') {
        subjectDataToSave = {
          type: 'legal' as 'legal',
          companyId: data.companyId!,
          name: data.name!,
          address: data.address || '',
          notes: data.notes || '',
          caseId: caseId,
          addedAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        } satisfies Omit<LegalEntitySubject, 'id'>;
      } else {
        throw new Error("Neznámý nebo nevybraný typ subjektu.");
      }

      const subjectsCollectionRef = collection(db, "cases", caseId, "subjects");
      await addDoc(subjectsCollectionRef, subjectDataToSave);

      onSubjectAdded(); // This should trigger re-fetch if onSnapshot is set up correctly on parent
      onOpenChange(false); // Close the dialog
    } catch (error: any) {
      console.error("Error adding subject:", error);
      toast({
        title: "Chyba při přidávání subjektu",
        description: error.message || "Nepodařilo se přidat subjekt.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const watchedSubjectType = form.watch("subjectType");

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl max-h-[90vh] overflow-y-auto p-6">
        <DialogHeader>
          <DialogTitle>Přidat nový subjekt</DialogTitle>
          <DialogDescription>
            Vyberte typ subjektu a vyplňte požadované informace.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 mt-4">
            <FormField
              control={form.control}
              name="subjectType"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>Typ subjektu <span className="text-destructive">*</span></FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={(value) => {
                        field.onChange(value);
                        form.reset({ 
                           subjectType: value as 'physical' | 'legal', 
                           firstName: "", lastName: "", dateOfBirth: "", nationality: "",
                           companyId: "", name: "", address: "", notes: ""
                        });
                      }}
                      value={field.value}
                      className="flex flex-col space-y-2 md:flex-row md:space-y-0 md:space-x-4"
                    >
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="physical" id="type-physical" />
                        </FormControl>
                        <Label htmlFor="type-physical" className="font-normal">Fyzická osoba</Label>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <RadioGroupItem value="legal" id="type-legal" />
                        </FormControl>
                        <Label htmlFor="type-legal" className="font-normal">Právnická osoba</Label>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {watchedSubjectType === 'physical' && (
              <div className="space-y-4 pt-4 border-t">
                <h3 className="text-lg font-medium">Detail fyzické osoby</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField control={form.control} name="firstName" render={({ field }) => (
                      <FormItem>
                        <FormLabel>Jméno <span className="text-destructive">*</span></FormLabel>
                        <FormControl><Input placeholder="Jan" {...field} /></FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField control={form.control} name="lastName" render={({ field }) => (
                      <FormItem>
                        <FormLabel>Příjmení <span className="text-destructive">*</span></FormLabel>
                        <FormControl><Input placeholder="Novák" {...field} /></FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField control={form.control} name="dateOfBirth" render={({ field }) => (
                      <FormItem>
                        <FormLabel>Datum narození</FormLabel>
                        <FormControl><Input type="date" {...field} /></FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField control={form.control} name="nationality" render={({ field }) => (
                      <FormItem>
                        <FormLabel>Národnost</FormLabel>
                        <FormControl><Input placeholder="Česká republika" {...field} /></FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            )}

            {watchedSubjectType === 'legal' && (
              <div className="space-y-4 pt-4 border-t">
                <h3 className="text-lg font-medium">Detail právnické osoby</h3>
                <FormField control={form.control} name="companyId" render={({ field }) => (
                    <FormItem>
                      <FormLabel>IČO <span className="text-destructive">*</span></FormLabel>
                      <FormControl><Input placeholder="12345678" {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField control={form.control} name="name" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Název společnosti <span className="text-destructive">*</span></FormLabel>
                      <FormControl><Input placeholder="Název firmy s.r.o." {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <Button type="button" variant="outline" onClick={handleAresLookup} disabled={isAresLoading || !form.getValues("companyId")?.trim()} className="w-full sm:w-auto">
                  {isAresLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Search className="mr-2 h-4 w-4" />}
                  Načíst z ARES
                </Button>
                <FormField control={form.control} name="address" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Adresa sídla</FormLabel>
                      <FormControl><Textarea placeholder="Ulice 123, Město, PSČ" {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField control={form.control} name="notes" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Poznámky</FormLabel>
                      <FormControl><Textarea placeholder="Další informace o společnosti (např. právní forma, datum založení z ARES)..." {...field} /></FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            )}
            
            <DialogFooter className="pt-6 border-t">
              <Button type="button" variant="outline" onClick={() => onOpenChange(false)} disabled={isSaving || isAresLoading}>
                Zrušit
              </Button>
              <Button type="submit" disabled={isSaving || isAresLoading || !watchedSubjectType}>
                {(isSaving || isAresLoading) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isSaving ? "Ukládání..." : (isAresLoading ? "Načítání ARES..." : "Uložit subjekt")}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
