"use client";

import { useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import type { SubmitHandler } from "react-hook-form";
import type {
  CadastreModuleData,
  Subject,
  PhysicalPersonSubject,
  LegalEntitySubject,
  PropertyType,
  PropertyOwnershipType,
  PropertyAcquisitionMethod,
  PropertyUsageType,
  PropertySource,
  YesNoUnknown,
} from "@/types";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { PlusCircle, Trash2, Building2, Loader2, Home, MapPin, FileText, Banknote, Users, Info } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { db } from "@/lib/firebase";
import { doc, setDoc, serverTimestamp } from "firebase/firestore";
import { Form } from "@/components/ui/form";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { cadastreModuleSchema, CadastreModuleFormValues } from "./schemas";
import { FormItemRHF, FormItemSelectRHF, StringArrayField } from "./FormComponents";
import { PhotoDocumentationSection } from "./PhotoDocumentationSection";
import { PropertyMapField } from "./PropertyMapField";

interface CadastreFormProps {
  caseId: string;
  subject: Subject;
  existingData: CadastreModuleData | null;
  onSave: (moduleId: string, wasNew: boolean) => void;
}

// Options for select fields
const propertyTypeOptions: { value: PropertyType; label: string }[] = [
  { value: "residential_house", label: "Rodinný dům" },
  { value: "residential_apartment", label: "Byt" },
  { value: "commercial", label: "Komerční nemovitost" },
  { value: "industrial", label: "Průmyslový objekt" },
  { value: "agricultural", label: "Zemědělská nemovitost" },
  { value: "land", label: "Pozemek" },
  { value: "forest", label: "Les" },
  { value: "other", label: "Jiný typ" },
];

const ownershipTypeOptions: { value: PropertyOwnershipType; label: string }[] = [
  { value: "full_ownership", label: "Výhradní vlastnictví" },
  { value: "co_ownership", label: "Spoluvlastnictví" },
  { value: "shared_ownership", label: "Společné jmění manželů" },
  { value: "cooperative", label: "Družstevní vlastnictví" },
  { value: "leased", label: "Pronájem" },
  { value: "other", label: "Jiný typ vlastnictví" },
];

const acquisitionMethodOptions: { value: PropertyAcquisitionMethod; label: string }[] = [
  { value: "purchase", label: "Koupě" },
  { value: "inheritance", label: "Dědictví" },
  { value: "gift", label: "Dar" },
  { value: "construction", label: "Výstavba" },
  { value: "exchange", label: "Směna" },
  { value: "privatization", label: "Privatizace" },
  { value: "restitution", label: "Restituce" },
  { value: "other", label: "Jiný způsob" },
];

const usageTypeOptions: { value: PropertyUsageType; label: string }[] = [
  { value: "personal_use", label: "Osobní užívání" },
  { value: "rental", label: "Pronájem" },
  { value: "business", label: "Podnikání" },
  { value: "vacant", label: "Neobsazeno" },
  { value: "agricultural", label: "Zemědělské využití" },
  { value: "recreational", label: "Rekreační využití" },
  { value: "other", label: "Jiné využití" },
];

const propertySourceOptions: { value: PropertySource; label: string }[] = [
  { value: "cadastre", label: "Katastr nemovitostí" },
  { value: "land_registry", label: "Pozemkové knihy" },
  { value: "public_records", label: "Veřejné záznamy" },
  { value: "witness", label: "Svědectví" },
  { value: "surveillance", label: "Sledování" },
  { value: "social_media", label: "Sociální sítě" },
  { value: "other", label: "Jiný zdroj" },
];

const verificationStatusOptions: { value: YesNoUnknown; label: string }[] = [
  { value: "yes", label: "Ověřeno" },
  { value: "no", label: "Neověřeno" },
  { value: "unknown", label: "Nezjištěno" },
];

export function CadastreForm({ caseId, subject, existingData, onSave }: CadastreFormProps) {
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const moduleId = "cadastre";

  const form = useForm<CadastreModuleFormValues>({
    resolver: zodResolver(cadastreModuleSchema),
    defaultValues: {
      properties: existingData?.properties?.map((prop) => ({
        ...prop,
        id: prop.id || crypto.randomUUID(),
        photos: prop.photos?.map((p) => ({ ...p, id: p.id || crypto.randomUUID() })) || [],
        parcelNumbers: prop.parcelNumbers || [],
        coOwners: prop.coOwners || [],
      })) || [],
      generalNotes: existingData?.generalNotes || "",
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "properties",
  });

  const addNewProperty = () => {
    append({
      id: crypto.randomUUID(),
      name: "",
      propertyType: undefined,
      address: "",
      gpsCoordinates: "",
      cadastralArea: "",
      cadastralNumber: "",
      landRegistryNumber: "",
      parcelNumbers: [],
      ownershipType: undefined,
      ownershipPercentage: null,
      coOwners: [],
      acquisitionMethod: undefined,
      acquisitionDate: "",
      purchasePrice: null,
      purchaseCurrency: "CZK",
      currentEstimatedValue: null,
      valueCurrency: "CZK",
      mortgageInfo: "",
      otherEncumbrances: "",
      usageType: undefined,
      rentalIncome: null,
      rentalCurrency: "CZK",
      tenants: "",
      totalArea: null,
      buildingArea: null,
      landArea: null,
      numberOfRooms: null,
      numberOfFloors: null,
      constructionYear: null,
      reconstructionYear: null,
      source: undefined,
      verificationStatus: undefined,
      verificationNotes: "",
      description: "",
      notes: "",
      photos: [],
      documentReferences: "",
    });
  };

  const onSubmitHandler: SubmitHandler<CadastreModuleFormValues> = async (data) => {
    setIsSaving(true);
    try {
      const moduleDocRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);
      
      // Funkce pro odstranění undefined hodnot z objektu (rekurzivně)
      const removeUndefinedValues = (obj: any): any => {
        if (obj === null || obj === undefined) {
          return null;
        }
        
        if (Array.isArray(obj)) {
          return obj.map(removeUndefinedValues).filter(item => item !== undefined);
        }
        
        if (typeof obj === 'object') {
          const cleaned: any = {};
          for (const [key, value] of Object.entries(obj)) {
            if (value !== undefined) {
              const cleanedValue = removeUndefinedValues(value);
              if (cleanedValue !== undefined) {
                cleaned[key] = cleanedValue;
              }
            }
          }
          return cleaned;
        }
        
        return obj;
      };

      // Odstranit undefined hodnoty před uložením
      const cleanedData = removeUndefinedValues(data);

      const dataToSave: CadastreModuleData = {
        ...existingData, // zachováme existující data z nezobrazených sekcí
        ...cleanedData, // přepíšeme daty z formuláře
        subjectId: subject.id,
        lastUpdatedAt: serverTimestamp(),
        createdAt: existingData?.createdAt || serverTimestamp(),
      };
      await setDoc(moduleDocRef, dataToSave, { merge: true });
      toast({ title: "Data modulu Katastr nemovitostí uložena." });
      const wasNew = !existingData || !existingData.createdAt;
      onSave(moduleId, wasNew);
    } catch (error: any) {
      toast({
        title: "Chyba ukládání dat modulu",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmitHandler)} className="space-y-8">
        <Card className="shadow-lg border border-primary/30">
          <CardHeader>
            <CardTitle className="text-xl flex items-center">
              <Building2 className="mr-3 h-6 w-6 text-primary" />
              Katastr nemovitostí: {subject.type === "physical" ? `${(subject as PhysicalPersonSubject).firstName} ${(subject as PhysicalPersonSubject).lastName}` : (subject as LegalEntitySubject).name}
            </CardTitle>
            <CardDescription>
              Zadejte informace o nemovitostech spojených se subjektem.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6 pt-4">
            {fields.map((field, index) => {
              const watchedPropertyType = form.watch(`properties.${index}.propertyType`);
              const watchedOwnershipType = form.watch(`properties.${index}.ownershipType`);
              const watchedAcquisitionMethod = form.watch(`properties.${index}.acquisitionMethod`);
              const watchedUsageType = form.watch(`properties.${index}.usageType`);
              const watchedSource = form.watch(`properties.${index}.source`);

              return (
                <Card key={field.id} className="p-4 shadow-md bg-card-foreground/5 relative">
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => remove(index)}
                    className="absolute top-3 right-3 text-destructive hover:bg-destructive/10"
                  >
                    <Trash2 className="h-5 w-5" />
                  </Button>
                  <CardHeader className="px-0 pt-0 pb-4">
                    <CardTitle className="text-lg">Nemovitost {index + 1}</CardTitle>
                  </CardHeader>
                  <CardContent className="px-0 pb-0 space-y-4">
                    <Tabs defaultValue="basic" className="w-full">
                      <TabsList className="grid grid-cols-6 mb-4">
                        <TabsTrigger value="basic" className="flex items-center">
                          <Home className="mr-2 h-4 w-4" />
                          <span className="hidden sm:inline">Základní údaje</span>
                          <span className="sm:hidden">Základní</span>
                        </TabsTrigger>
                        <TabsTrigger value="location" className="flex items-center">
                          <MapPin className="mr-2 h-4 w-4" />
                          <span className="hidden sm:inline">Umístění</span>
                          <span className="sm:hidden">Umístění</span>
                        </TabsTrigger>
                        <TabsTrigger value="ownership" className="flex items-center">
                          <FileText className="mr-2 h-4 w-4" />
                          <span className="hidden sm:inline">Vlastnictví</span>
                          <span className="sm:hidden">Vlastnictví</span>
                        </TabsTrigger>
                        <TabsTrigger value="financial" className="flex items-center">
                          <Banknote className="mr-2 h-4 w-4" />
                          <span className="hidden sm:inline">Finance</span>
                          <span className="sm:hidden">Finance</span>
                        </TabsTrigger>
                        <TabsTrigger value="usage" className="flex items-center">
                          <Users className="mr-2 h-4 w-4" />
                          <span className="hidden sm:inline">Využití</span>
                          <span className="sm:hidden">Využití</span>
                        </TabsTrigger>
                        <TabsTrigger value="additional" className="flex items-center">
                          <Info className="mr-2 h-4 w-4" />
                          <span className="hidden sm:inline">Doplňující</span>
                          <span className="sm:hidden">Doplňující</span>
                        </TabsTrigger>
                      </TabsList>

                      <TabsContent value="basic" className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormItemRHF
                            label="Název nemovitosti"
                            name={`properties.${index}.name`}
                            control={form.control}
                            placeholder="Např. Rodinný dům Praha, Chata Lipno..."
                          />
                          <FormItemSelectRHF
                            label="Typ nemovitosti"
                            name={`properties.${index}.propertyType`}
                            control={form.control}
                            options={propertyTypeOptions}
                            placeholder="-- Vyberte typ --"
                          />
                        </div>
                        {watchedPropertyType === "other" && (
                          <FormItemRHF
                            label="Upřesnění typu nemovitosti"
                            name={`properties.${index}.otherPropertyTypeDetail`}
                            control={form.control}
                            placeholder="Popište vlastní typ nemovitosti"
                          />
                        )}
                        <FormItemRHF
                          label="Popis nemovitosti"
                          name={`properties.${index}.description`}
                          control={form.control}
                          as="textarea"
                          rows={3}
                          placeholder="Detailní popis nemovitosti..."
                        />
                      </TabsContent>

                      <TabsContent value="location" className="space-y-4">
                        <FormItemRHF
                          label="Adresa"
                          name={`properties.${index}.address`}
                          control={form.control}
                          placeholder="Přesná adresa nemovitosti"
                        />
                        <FormItemRHF
                          label="GPS souřadnice (lat, lon)"
                          name={`properties.${index}.gpsCoordinates`}
                          control={form.control}
                          placeholder="např. 50.0872, 14.4212"
                        />

                        <PropertyMapField 
                          control={form.control} 
                          index={index} 
                          setValue={form.setValue}
                          getValues={form.getValues}
                        />

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <FormItemRHF
                            label="Katastrální území"
                            name={`properties.${index}.cadastralArea`}
                            control={form.control}
                            placeholder="Název katastrálního území"
                          />
                          <FormItemRHF
                            label="Katastrální číslo"
                            name={`properties.${index}.cadastralNumber`}
                            control={form.control}
                            placeholder="Číslo v katastru"
                          />
                          <FormItemRHF
                            label="Číslo listu vlastnictví"
                            name={`properties.${index}.landRegistryNumber`}
                            control={form.control}
                            placeholder="LV"
                          />
                        </div>

                        <StringArrayField
                          label="Parcelní čísla"
                          name={`properties.${index}.parcelNumbers`}
                          control={form.control}
                          placeholder="Zadejte parcelní číslo"
                          addButtonText="Přidat parcelní číslo"
                          itemLabel="Parcela"
                        />
                      </TabsContent>

                      <TabsContent value="ownership" className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormItemSelectRHF
                            label="Typ vlastnictví"
                            name={`properties.${index}.ownershipType`}
                            control={form.control}
                            options={ownershipTypeOptions}
                            placeholder="-- Vyberte typ --"
                          />
                          {watchedOwnershipType === "co_ownership" && (
                            <FormItemRHF
                              label="Podíl vlastnictví (%)"
                              name={`properties.${index}.ownershipPercentage`}
                              control={form.control}
                              type="number"
                              placeholder="Např. 50"
                            />
                          )}
                        </div>
                        {watchedOwnershipType === "other" && (
                          <FormItemRHF
                            label="Upřesnění typu vlastnictví"
                            name={`properties.${index}.otherOwnershipTypeDetail`}
                            control={form.control}
                            placeholder="Popište vlastní typ vlastnictví"
                          />
                        )}

                        {(watchedOwnershipType === "co_ownership" || watchedOwnershipType === "shared_ownership") && (
                          <StringArrayField
                            label="Spoluvlastníci"
                            name={`properties.${index}.coOwners`}
                            control={form.control}
                            placeholder="Jméno spoluvlastníka"
                            addButtonText="Přidat spoluvlastníka"
                            itemLabel="Spoluvlastník"
                          />
                        )}

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormItemSelectRHF
                            label="Způsob nabytí"
                            name={`properties.${index}.acquisitionMethod`}
                            control={form.control}
                            options={acquisitionMethodOptions}
                            placeholder="-- Vyberte způsob --"
                          />
                          <FormItemRHF
                            label="Datum nabytí"
                            name={`properties.${index}.acquisitionDate`}
                            control={form.control}
                            type="date"
                          />
                        </div>
                        {watchedAcquisitionMethod === "other" && (
                          <FormItemRHF
                            label="Upřesnění způsobu nabytí"
                            name={`properties.${index}.otherAcquisitionMethodDetail`}
                            control={form.control}
                            placeholder="Popište způsob nabytí"
                          />
                        )}
                      </TabsContent>

                      <TabsContent value="financial" className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <FormItemRHF
                            label="Kupní cena"
                            name={`properties.${index}.purchasePrice`}
                            control={form.control}
                            type="number"
                            placeholder="Částka"
                            className="md:col-span-2"
                          />
                          <FormItemRHF
                            label="Měna"
                            name={`properties.${index}.purchaseCurrency`}
                            control={form.control}
                            placeholder="CZK"
                          />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <FormItemRHF
                            label="Aktuální odhadovaná hodnota"
                            name={`properties.${index}.currentEstimatedValue`}
                            control={form.control}
                            type="number"
                            placeholder="Částka"
                            className="md:col-span-2"
                          />
                          <FormItemRHF
                            label="Měna"
                            name={`properties.${index}.valueCurrency`}
                            control={form.control}
                            placeholder="CZK"
                          />
                        </div>
                        <FormItemRHF
                          label="Informace o hypotéce"
                          name={`properties.${index}.mortgageInfo`}
                          control={form.control}
                          as="textarea"
                          rows={2}
                          placeholder="Detaily o hypotéce, výše, banka, splatnost..."
                        />
                        <FormItemRHF
                          label="Jiná zatížení nemovitosti"
                          name={`properties.${index}.otherEncumbrances`}
                          control={form.control}
                          as="textarea"
                          rows={2}
                          placeholder="Věcná břemena, zástavy, exekuce..."
                        />
                      </TabsContent>

                      <TabsContent value="usage" className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormItemSelectRHF
                            label="Způsob využití"
                            name={`properties.${index}.usageType`}
                            control={form.control}
                            options={usageTypeOptions}
                            placeholder="-- Vyberte způsob --"
                          />
                          {watchedUsageType === "other" && (
                            <FormItemRHF
                              label="Upřesnění způsobu využití"
                              name={`properties.${index}.otherUsageTypeDetail`}
                              control={form.control}
                              placeholder="Popište způsob využití"
                            />
                          )}
                        </div>
                        {watchedUsageType === "rental" && (
                          <>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                              <FormItemRHF
                                label="Příjem z pronájmu"
                                name={`properties.${index}.rentalIncome`}
                                control={form.control}
                                type="number"
                                placeholder="Částka"
                                className="md:col-span-2"
                              />
                              <FormItemRHF
                                label="Měna"
                                name={`properties.${index}.rentalCurrency`}
                                control={form.control}
                                placeholder="CZK"
                              />
                            </div>
                            <FormItemRHF
                              label="Nájemníci"
                              name={`properties.${index}.tenants`}
                              control={form.control}
                              as="textarea"
                              rows={2}
                              placeholder="Informace o nájemnících..."
                            />
                          </>
                        )}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <FormItemRHF
                            label="Celková plocha (m²)"
                            name={`properties.${index}.totalArea`}
                            control={form.control}
                            type="number"
                            placeholder="Celková plocha"
                          />
                          <FormItemRHF
                            label="Zastavěná plocha (m²)"
                            name={`properties.${index}.buildingArea`}
                            control={form.control}
                            type="number"
                            placeholder="Zastavěná plocha"
                          />
                          <FormItemRHF
                            label="Plocha pozemku (m²)"
                            name={`properties.${index}.landArea`}
                            control={form.control}
                            type="number"
                            placeholder="Plocha pozemku"
                          />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <FormItemRHF
                            label="Počet místností"
                            name={`properties.${index}.numberOfRooms`}
                            control={form.control}
                            type="number"
                            placeholder="Počet místností"
                          />
                          <FormItemRHF
                            label="Počet podlaží"
                            name={`properties.${index}.numberOfFloors`}
                            control={form.control}
                            type="number"
                            placeholder="Počet podlaží"
                          />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormItemRHF
                            label="Rok výstavby"
                            name={`properties.${index}.constructionYear`}
                            control={form.control}
                            type="number"
                            placeholder="Rok výstavby"
                          />
                          <FormItemRHF
                            label="Rok rekonstrukce"
                            name={`properties.${index}.reconstructionYear`}
                            control={form.control}
                            type="number"
                            placeholder="Rok rekonstrukce"
                          />
                        </div>
                      </TabsContent>

                      <TabsContent value="additional" className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormItemSelectRHF
                            label="Zdroj informací"
                            name={`properties.${index}.source`}
                            control={form.control}
                            options={propertySourceOptions}
                            placeholder="-- Vyberte zdroj --"
                          />
                          <FormItemSelectRHF
                            label="Stav ověření"
                            name={`properties.${index}.verificationStatus`}
                            control={form.control}
                            options={verificationStatusOptions}
                            placeholder="-- Vyberte stav --"
                          />
                        </div>
                        {watchedSource === "other" && (
                          <FormItemRHF
                            label="Upřesnění zdroje"
                            name={`properties.${index}.otherSourceDetail`}
                            control={form.control}
                            placeholder="Popište zdroj informací"
                          />
                        )}
                        <FormItemRHF
                          label="Poznámky k ověření"
                          name={`properties.${index}.verificationNotes`}
                          control={form.control}
                          as="textarea"
                          rows={2}
                          placeholder="Detaily o ověření informací..."
                        />
                        <FormItemRHF
                          label="Odkazy na dokumenty"
                          name={`properties.${index}.documentReferences`}
                          control={form.control}
                          as="textarea"
                          rows={2}
                          placeholder="Odkazy na související dokumenty, čísla spisů..."
                        />
                        <FormItemRHF
                          label="Poznámky"
                          name={`properties.${index}.notes`}
                          control={form.control}
                          as="textarea"
                          rows={3}
                          placeholder="Další poznámky k nemovitosti..."
                        />
                      </TabsContent>
                    </Tabs>

                    <PhotoDocumentationSection
                      form={form}
                      title="Fotodokumentace nemovitosti"
                      description="Fotografie související s touto nemovitostí"
                      namePrefix={`properties.${index}.photos`}
                      photoHint="building property real estate cadastre"
                      caseId={caseId}
                      subjectId={subject.id}
                      moduleId={moduleId}
                    />
                  </CardContent>
                </Card>
              );
            })}

            <Button type="button" variant="outline" onClick={addNewProperty} className="w-full md:w-auto">
              <PlusCircle className="mr-2 h-4 w-4" /> Přidat nemovitost
            </Button>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Obecné poznámky ke katastru nemovitostí</CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <FormItemRHF
                  label="Další relevantní informace"
                  name="generalNotes"
                  control={form.control}
                  as="textarea"
                  rows={4}
                  placeholder="Jakékoli další důležité informace o nemovitostech subjektu..."
                />
              </CardContent>
            </Card>
          </CardContent>
        </Card>

        <div className="flex justify-end pt-8 mt-8 border-t border-border">
          <Button
            type="submit"
            disabled={isSaving}
            className="w-full md:w-auto text-lg py-3 px-6 shadow-md"
          >
            {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Uložit data modulu
          </Button>
        </div>
      </form>
    </Form>
  );
}
