"use client";

import { useState } from "react";
import { UseFormReturn, useFieldArray } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MapOverlaysModuleFormValues } from "../schemas";
import { MapAnalysisResult } from "@/types";
import { Plus, Trash2, Search, Edit, BarChart, PieChart, LineChart, Activity, Download, Share2 } from "lucide-react";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface MapAnalysisTabProps {
  form: UseFormReturn<MapOverlaysModuleFormValues>;
}

export function MapAnalysisTab({ form }: MapAnalysisTabProps) {
  const [selectedAnalysisId, setSelectedAnalysisId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("list");

  const { control, register, watch } = form;
  const { fields, append, remove, update } = useFieldArray({
    control,
    name: "analysisResults",
  });

  const analysisResults = watch("analysisResults") || [];
  const selectedAnalysisIndex = analysisResults.findIndex(a => a.id === selectedAnalysisId);

  // Filtrování analýz podle vyhledávacího výrazu
  const filteredAnalyses = analysisResults.filter(analysis => 
    analysis.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    analysis.analysisType.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (analysis.description && analysis.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Přidání nové analýzy
  const handleAddAnalysis = () => {
    const newAnalysis: MapAnalysisResult = {
      id: uuidv4(),
      name: "Nová analýza",
      analysisType: "heatmap",
      date: new Date().toISOString().split('T')[0],
      parameters: {},
      results: {},
      visualizationSettings: {},
    };
    append(newAnalysis);
    setSelectedAnalysisId(newAnalysis.id);
    setActiveTab("detail");
  };

  // Odstranění analýzy
  const handleRemoveAnalysis = (index: number) => {
    if (window.confirm("Opravdu chcete smazat tuto analýzu?")) {
      remove(index);
      if (selectedAnalysisId === analysisResults[index].id) {
        setSelectedAnalysisId(null);
        setActiveTab("list");
      }
    }
  };

  // Výběr analýzy pro editaci
  const handleSelectAnalysis = (id: string) => {
    setSelectedAnalysisId(id);
    setActiveTab("detail");
  };

  // Spuštění analýzy
  const runAnalysis = (analysisType: string) => {
    alert(`Analýza typu "${analysisType}" bude implementována v budoucí verzi.`);
  };

  // Ikona pro typ analýzy
  const getAnalysisIcon = (analysisType: string) => {
    switch (analysisType) {
      case "heatmap":
        return <Activity className="h-4 w-4" />;
      case "cluster":
        return <PieChart className="h-4 w-4" />;
      case "timeseries":
        return <LineChart className="h-4 w-4" />;
      default:
        return <BarChart className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Analýza mapových dat</CardTitle>
              <CardDescription>
                Vytváření a správa analýz mapových dat
              </CardDescription>
            </div>
            <Button onClick={handleAddAnalysis}>
              <Plus className="mr-2 h-4 w-4" />
              Nová analýza
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="list">Seznam analýz</TabsTrigger>
              <TabsTrigger value="detail" disabled={selectedAnalysisId === null}>
                Detail analýzy
              </TabsTrigger>
            </TabsList>

            <TabsContent value="list" className="pt-4">
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Vyhledat analýzy..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                <ScrollArea className="h-[400px] rounded-md border">
                  {filteredAnalyses.length === 0 ? (
                    <div className="p-4 text-center text-muted-foreground">
                      Nebyly nalezeny žádné analýzy
                    </div>
                  ) : (
                    <div className="p-4 space-y-2">
                      {filteredAnalyses.map((analysis, index) => (
                        <div
                          key={analysis.id}
                          className="flex items-center justify-between p-2 rounded-md hover:bg-muted cursor-pointer"
                          onClick={() => handleSelectAnalysis(analysis.id)}
                        >
                          <div className="flex items-center space-x-2">
                            <div className="bg-primary/10 p-2 rounded-md">
                              {getAnalysisIcon(analysis.analysisType)}
                            </div>
                            <div>
                              <div className="font-medium">{analysis.name}</div>
                              <div className="text-xs text-muted-foreground">
                                {analysis.analysisType} • {analysis.date}
                              </div>
                            </div>
                          </div>
                          <div className="flex space-x-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleSelectAnalysis(analysis.id);
                              }}
                            >
                              <Edit className="h-4 w-4 text-muted-foreground" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRemoveAnalysis(index);
                              }}
                            >
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </div>
            </TabsContent>

            <TabsContent value="detail" className="pt-4">
              {selectedAnalysisId && selectedAnalysisIndex !== -1 && (
                <div className="space-y-4">
                  <Tabs defaultValue="basic">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="basic">Základní informace</TabsTrigger>
                      <TabsTrigger value="parameters">Parametry</TabsTrigger>
                      <TabsTrigger value="results">Výsledky</TabsTrigger>
                    </TabsList>

                    <TabsContent value="basic" className="space-y-4 pt-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`analysisResults.${selectedAnalysisIndex}.name`}>Název analýzy</Label>
                          <Input
                            id={`analysisResults.${selectedAnalysisIndex}.name`}
                            {...register(`analysisResults.${selectedAnalysisIndex}.name`)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`analysisResults.${selectedAnalysisIndex}.analysisType`}>Typ analýzy</Label>
                          <Select
                            defaultValue={analysisResults[selectedAnalysisIndex].analysisType}
                            onValueChange={(value) => {
                              const updatedAnalysis = { ...analysisResults[selectedAnalysisIndex], analysisType: value };
                              update(selectedAnalysisIndex, updatedAnalysis);
                            }}
                          >
                            <SelectTrigger id={`analysisResults.${selectedAnalysisIndex}.analysisType`}>
                              <SelectValue placeholder="Vyberte typ analýzy" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="heatmap">Teplotní mapa</SelectItem>
                              <SelectItem value="cluster">Shluková analýza</SelectItem>
                              <SelectItem value="timeseries">Časová řada</SelectItem>
                              <SelectItem value="density">Hustota</SelectItem>
                              <SelectItem value="proximity">Analýza blízkosti</SelectItem>
                              <SelectItem value="route">Analýza tras</SelectItem>
                              <SelectItem value="custom">Vlastní analýza</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`analysisResults.${selectedAnalysisIndex}.date`}>Datum analýzy</Label>
                          <Input
                            id={`analysisResults.${selectedAnalysisIndex}.date`}
                            type="date"
                            {...register(`analysisResults.${selectedAnalysisIndex}.date`)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`analysisResults.${selectedAnalysisIndex}.targetArea`}>Cílová oblast</Label>
                          <Input
                            id={`analysisResults.${selectedAnalysisIndex}.targetArea`}
                            {...register(`analysisResults.${selectedAnalysisIndex}.targetArea`)}
                            placeholder="ID oblasti nebo 'all' pro všechny"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`analysisResults.${selectedAnalysisIndex}.description`}>Popis analýzy</Label>
                        <Textarea
                          id={`analysisResults.${selectedAnalysisIndex}.description`}
                          {...register(`analysisResults.${selectedAnalysisIndex}.description`)}
                          placeholder="Zadejte popis analýzy..."
                          rows={4}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`analysisResults.${selectedAnalysisIndex}.notes`}>Poznámky</Label>
                        <Textarea
                          id={`analysisResults.${selectedAnalysisIndex}.notes`}
                          {...register(`analysisResults.${selectedAnalysisIndex}.notes`)}
                          placeholder="Zadejte poznámky k analýze..."
                          rows={4}
                        />
                      </div>

                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          onClick={() => runAnalysis(analysisResults[selectedAnalysisIndex].analysisType)}
                        >
                          <Activity className="mr-2 h-4 w-4" />
                          Spustit analýzu
                        </Button>
                      </div>
                    </TabsContent>

                    <TabsContent value="parameters" className="space-y-4 pt-4">
                      <Card>
                        <CardHeader>
                          <CardTitle>Parametry analýzy</CardTitle>
                          <CardDescription>
                            Nastavení parametrů pro analýzu typu "{analysisResults[selectedAnalysisIndex].analysisType}"
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            {analysisResults[selectedAnalysisIndex].analysisType === "heatmap" && (
                              <>
                                <div className="space-y-2">
                                  <Label htmlFor="heatmap-radius">Poloměr</Label>
                                  <Input
                                    id="heatmap-radius"
                                    type="number"
                                    min={1}
                                    max={50}
                                    defaultValue={25}
                                    placeholder="Poloměr teplotní mapy (1-50)"
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label htmlFor="heatmap-intensity">Intenzita</Label>
                                  <Input
                                    id="heatmap-intensity"
                                    type="number"
                                    min={0.1}
                                    max={1}
                                    step={0.1}
                                    defaultValue={0.5}
                                    placeholder="Intenzita teplotní mapy (0.1-1)"
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label htmlFor="heatmap-gradient">Barevný gradient</Label>
                                  <Select defaultValue="default">
                                    <SelectTrigger id="heatmap-gradient">
                                      <SelectValue placeholder="Vyberte barevný gradient" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="default">Výchozí (červená-žlutá)</SelectItem>
                                      <SelectItem value="blue">Modrá</SelectItem>
                                      <SelectItem value="green">Zelená</SelectItem>
                                      <SelectItem value="rainbow">Duha</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                              </>
                            )}

                            {analysisResults[selectedAnalysisIndex].analysisType === "cluster" && (
                              <>
                                <div className="space-y-2">
                                  <Label htmlFor="cluster-distance">Vzdálenost</Label>
                                  <Input
                                    id="cluster-distance"
                                    type="number"
                                    min={10}
                                    max={1000}
                                    defaultValue={100}
                                    placeholder="Vzdálenost pro shlukování (10-1000)"
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label htmlFor="cluster-min-points">Minimální počet bodů</Label>
                                  <Input
                                    id="cluster-min-points"
                                    type="number"
                                    min={1}
                                    max={100}
                                    defaultValue={3}
                                    placeholder="Minimální počet bodů ve shluku (1-100)"
                                  />
                                </div>
                              </>
                            )}

                            {analysisResults[selectedAnalysisIndex].analysisType === "timeseries" && (
                              <>
                                <div className="space-y-2">
                                  <Label htmlFor="timeseries-start-date">Počáteční datum</Label>
                                  <Input
                                    id="timeseries-start-date"
                                    type="date"
                                    defaultValue={new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label htmlFor="timeseries-end-date">Koncové datum</Label>
                                  <Input
                                    id="timeseries-end-date"
                                    type="date"
                                    defaultValue={new Date().toISOString().split('T')[0]}
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label htmlFor="timeseries-interval">Interval</Label>
                                  <Select defaultValue="day">
                                    <SelectTrigger id="timeseries-interval">
                                      <SelectValue placeholder="Vyberte interval" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectItem value="hour">Hodina</SelectItem>
                                      <SelectItem value="day">Den</SelectItem>
                                      <SelectItem value="week">Týden</SelectItem>
                                      <SelectItem value="month">Měsíc</SelectItem>
                                    </SelectContent>
                                  </Select>
                                </div>
                              </>
                            )}

                            {/* Pro ostatní typy analýz by zde byly další parametry */}
                            {!["heatmap", "cluster", "timeseries"].includes(analysisResults[selectedAnalysisIndex].analysisType) && (
                              <div className="p-4 text-center text-muted-foreground">
                                Parametry pro tento typ analýzy budou implementovány v budoucí verzi.
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>

                    <TabsContent value="results" className="space-y-4 pt-4">
                      <Card>
                        <CardHeader>
                          <div className="flex justify-between items-center">
                            <div>
                              <CardTitle>Výsledky analýzy</CardTitle>
                              <CardDescription>
                                Vizualizace a export výsledků analýzy
                              </CardDescription>
                            </div>
                            <div className="flex space-x-2">
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button variant="outline" size="icon">
                                      <Download className="h-4 w-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>Exportovat výsledky</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                              
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button variant="outline" size="icon">
                                      <Share2 className="h-4 w-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>Sdílet výsledky</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent>
                          <div className="h-[300px] bg-muted rounded-md flex items-center justify-center">
                            <div className="text-center">
                              <BarChart className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                              <p className="text-muted-foreground">
                                Spusťte analýzu pro zobrazení výsledků
                              </p>
                              <Button
                                variant="outline"
                                size="sm"
                                className="mt-4"
                                onClick={() => runAnalysis(analysisResults[selectedAnalysisIndex].analysisType)}
                              >
                                <Activity className="mr-2 h-4 w-4" />
                                Spustit analýzu
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </TabsContent>
                  </Tabs>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="border-t pt-4">
          <div className="flex justify-between w-full">
            <div className="text-sm text-muted-foreground">
              Celkem analýz: {analysisResults.length}
            </div>
            {selectedAnalysisId && (
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedAnalysisId(null);
                  setActiveTab("list");
                }}
              >
                Zpět na seznam
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
