/**
 * Mapové overlapy - funkce pro analýzu mapových dat
 */

/**
 * Změna mapového podkladu
 * @param {string} basemapType - Typ mapového podkladu (streets, satellite, terrain, dark)
 */
function changeBasemap(basemapType) {
    console.log('Změna mapového podkladu na:', basemapType);

    if (!map || !mapLayers || !mapLayers.basemaps) {
        console.error('Mapa nebo vrstvy nejsou inicializovány');
        return;
    }

    // Kontrola, zda požadovaný mapový podklad existuje
    if (!mapLayers.basemaps[basemapType]) {
        console.error('Požadovaný mapový podklad neexistuje:', basemapType);
        return;
    }

    // Odstranění aktuálního mapového podkladu
    map.removeLayer(mapLayers.basemaps[activeBasemap]);

    // Přidání nového mapového podkladu
    mapLayers.basemaps[basemapType].addTo(map);

    // Aktualizace aktivního mapového podkladu
    activeBasemap = basemapType;

    // Aktualizace UI
    updateBasemapUI(basemapType);
}

/**
 * Aktualizace UI pro mapové podklady
 * @param {string} activeBasemap - Aktivní mapový podklad
 */
function updateBasemapUI(activeBasemap) {
    // Najít aktuální modul mapových overlapů
    const mapModule = document.querySelector('.module[id^="module-mapove-overlapy"]');
    if (!mapModule) {
        console.error('Modul mapových overlapů nebyl nalezen');
        return;
    }

    // Najít všechny přepínače mapových podkladů
    const basemapSelectors = mapModule.querySelectorAll('.basemap-selector');

    // Odstranit aktivní třídu ze všech přepínačů
    basemapSelectors.forEach(selector => {
        selector.classList.remove('active');
    });

    // Přidat aktivní třídu k aktivnímu přepínači
    const activeSelector = mapModule.querySelector(`.basemap-selector[data-basemap="${activeBasemap}"]`);
    if (activeSelector) {
        activeSelector.classList.add('active');
    }
}

/**
 * Přepínání vrstev
 * @param {string} layerName - Název vrstvy
 * @param {boolean} visible - Viditelnost vrstvy
 */
function toggleLayer(layerName, visible) {
    console.log('Přepínání vrstvy:', layerName, visible);

    if (!map || !mapLayers || !mapLayers.dataLayers) {
        console.error('Mapa nebo vrstvy nejsou inicializovány');
        return;
    }

    // Kontrola, zda požadovaná vrstva existuje
    if (!mapLayers.dataLayers[layerName]) {
        console.error('Požadovaná vrstva neexistuje:', layerName);
        return;
    }

    // Přepnutí viditelnosti vrstvy
    if (visible) {
        mapLayers.dataLayers[layerName].addTo(map);
    } else {
        map.removeLayer(mapLayers.dataLayers[layerName]);
    }
}

/**
 * Povolení režimu kreslení
 * @param {string} mode - Režim kreslení (point, polygon, polyline)
 */
function enableDrawingMode(mode) {
    console.log('Povolení režimu kreslení:', mode);

    if (!map) {
        console.error('Mapa není inicializována');
        return;
    }

    // Aktivace režimu kreslení podle typu
    try {
        let handler;

        if (mode === 'point') {
            handler = new L.Draw.Marker(map);
        } else if (mode === 'polygon') {
            handler = new L.Draw.Polygon(map);
        } else if (mode === 'polyline') {
            handler = new L.Draw.Polyline(map);
        } else {
            console.error('Neznámý režim kreslení:', mode);
            return;
        }

        if (handler) {
            handler.enable();
        }
    } catch (error) {
        console.error('Chyba při aktivaci režimu kreslení:', error);
        alert('Pro kreslení na mapě je potřeba načíst knihovnu Leaflet.draw. Zkontrolujte, zda je knihovna správně načtena.');
    }
}

/**
 * Vyhledání adresy
 */
function searchAddress() {
    console.log('Vyhledání adresy');

    // Najít aktuální modul mapových overlapů
    const mapModule = document.querySelector('.module[id^="module-mapove-overlapy"]');
    if (!mapModule) {
        console.error('Modul mapových overlapů nebyl nalezen');
        return;
    }

    // Najít vstupní pole pro adresu
    const addressInput = mapModule.querySelector('#address-input');
    if (!addressInput) {
        console.error('Vstupní pole pro adresu nebylo nalezeno');
        return;
    }

    const address = addressInput.value.trim();
    if (!address) {
        alert('Zadejte adresu pro vyhledání.');
        return;
    }

    // Vyhledání adresy pomocí Nominatim API (OpenStreetMap)
    fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(address)}&limit=1`)
        .then(response => response.json())
        .then(data => {
            if (data && data.length > 0) {
                const result = data[0];
                const lat = parseFloat(result.lat);
                const lon = parseFloat(result.lon);

                // Přesun mapy na nalezenou adresu
                map.setView([lat, lon], 16);

                // Přidání markeru na nalezenou adresu
                const marker = L.marker([lat, lon]).addTo(mapLayers.dataLayers.markers);
                marker.bindPopup(`
                    <div class="popup-content">
                        <h4>Nalezená adresa</h4>
                        <p>${result.display_name}</p>
                        <div class="popup-actions">
                            <button type="button" class="btn-inline add-to-points">
                                <i class="fas fa-map-marker-alt"></i> Přidat mezi body zájmu
                            </button>
                        </div>
                    </div>
                `).openPopup();

                // Přidání markeru do seznamu
                markers.push(marker);
            } else {
                alert('Adresa nebyla nalezena. Zkuste zadat jinou adresu.');
            }
        })
        .catch(error => {
            console.error('Chyba při vyhledávání adresy:', error);
            alert('Nepodařilo se vyhledat adresu. Zkuste to prosím znovu.');
        });
}

/**
 * Vytvoření heat mapy
 */
function createHeatmap() {
    console.log('Vytvoření heat mapy');

    if (!map || !markers || markers.length === 0) {
        alert('Pro vytvoření heat mapy je potřeba přidat alespoň jeden bod na mapu.');
        return;
    }

    // Kontrola, zda je načtena knihovna Leaflet.heat
    if (typeof L.heatLayer === 'undefined') {
        alert('Pro vytvoření heat mapy je potřeba načíst knihovnu Leaflet.heat.');
        console.error('Knihovna Leaflet.heat není načtena');
        return;
    }

    // Získání bodů pro heat mapu
    const heatPoints = markers.map(marker => {
        const latLng = marker.getLatLng();
        return [latLng.lat, latLng.lng, 1]; // [lat, lng, intensity]
    });

    // Odstranění existující heat mapy
    if (heatmapLayer) {
        map.removeLayer(heatmapLayer);
    }

    // Vytvoření nové heat mapy
    heatmapLayer = L.heatLayer(heatPoints, {
        radius: 25,
        blur: 15,
        maxZoom: 17
    }).addTo(map);

    // Přidání heat mapy do seznamu vrstev
    mapLayers.dataLayers.heatmap = heatmapLayer;

    // Aktualizace UI
    updateLayersUI();
}

/**
 * Vytvoření clusterů
 */
function createClusters() {
    console.log('Vytvoření clusterů');

    if (!map || !markers || markers.length === 0) {
        alert('Pro vytvoření clusterů je potřeba přidat alespoň jeden bod na mapu.');
        return;
    }

    // Kontrola, zda je načtena knihovna Leaflet.markercluster
    if (typeof L.markerClusterGroup === 'undefined') {
        alert('Pro vytvoření clusterů je potřeba načíst knihovnu Leaflet.markercluster.');
        console.error('Knihovna Leaflet.markercluster není načtena');
        return;
    }

    // Odstranění existujících clusterů
    if (clusterLayer) {
        map.removeLayer(clusterLayer);
    }

    // Vytvoření nových clusterů
    clusterLayer = L.markerClusterGroup();

    // Přidání markerů do clusterů
    markers.forEach(marker => {
        // Vytvoření kopie markeru pro cluster
        const clusterMarker = L.marker(marker.getLatLng());

        // Kopírování popup obsahu
        if (marker.getPopup()) {
            clusterMarker.bindPopup(marker.getPopup().getContent());
        }

        // Přidání markeru do clusteru
        clusterLayer.addLayer(clusterMarker);
    });

    // Přidání clusterů na mapu
    map.addLayer(clusterLayer);

    // Přidání clusterů do seznamu vrstev
    mapLayers.dataLayers.clusters = clusterLayer;

    // Aktualizace UI
    updateLayersUI();
}

/**
 * Aktualizace UI pro vrstvy
 */
function updateLayersUI() {
    // Najít aktuální modul mapových overlapů
    const mapModule = document.querySelector('.module[id^="module-mapove-overlapy"]');
    if (!mapModule) {
        console.error('Modul mapových overlapů nebyl nalezen');
        return;
    }

    // Najít kontejner pro vrstvy
    const layersContainer = mapModule.querySelector('#layers-container');
    if (!layersContainer) {
        console.error('Kontejner pro vrstvy nebyl nalezen');
        return;
    }

    // Vytvoření HTML pro vrstvy
    let layersHTML = '';

    // Přidání datových vrstev
    Object.entries(mapLayers.dataLayers).forEach(([layerName, layer]) => {
        const layerLabel = getLayerLabel(layerName);
        const isVisible = map.hasLayer(layer);

        layersHTML += `
            <div class="layer-item">
                <label class="layer-toggle" data-layer="${layerName}">
                    <input type="checkbox" ${isVisible ? 'checked' : ''}>
                    <span class="layer-name">${layerLabel}</span>
                </label>
            </div>
        `;
    });

    // Aktualizace kontejneru pro vrstvy
    layersContainer.innerHTML = layersHTML;
}

/**
 * Získání popisku vrstvy
 * @param {string} layerName - Název vrstvy
 * @returns {string} - Popisek vrstvy
 */
function getLayerLabel(layerName) {
    const layerLabels = {
        markers: 'Body zájmu',
        areas: 'Oblasti',
        routes: 'Trasy',
        heatmap: 'Heat mapa',
        clusters: 'Clustery'
    };

    return layerLabels[layerName] || layerName;
}

/**
 * Výpočet plochy polygonu pomocí vzorce Shoelace
 * @param {Array} latlngs - Pole souřadnic [lat, lng]
 * @returns {number} - Plocha v metrech čtverečních
 */
function calculatePolygonArea(latlngs) {
    if (!latlngs || latlngs.length < 3) {
        return 0;
    }

    // Převod na kartézské souřadnice (přibližně)
    const earthRadius = 6371000; // poloměr Země v metrech
    const points = latlngs.map(latlng => {
        const lat = latlng.lat * Math.PI / 180;
        const lng = latlng.lng * Math.PI / 180;
        const x = earthRadius * Math.cos(lat) * Math.cos(lng);
        const y = earthRadius * Math.cos(lat) * Math.sin(lng);
        return { x, y };
    });

    // Výpočet plochy pomocí vzorce Shoelace
    let area = 0;
    for (let i = 0, j = points.length - 1; i < points.length; j = i++) {
        area += points[i].x * points[j].y - points[j].x * points[i].y;
    }

    // Absolutní hodnota a dělení 2
    return Math.abs(area) / 2;
}

/**
 * Analýza oblasti
 */
function analyzeArea() {
    console.log('Analýza oblasti');

    // Kontrola, zda existují oblasti
    if (!mapLayers.dataLayers.areas || mapLayers.dataLayers.areas.getLayers().length === 0) {
        alert('Pro analýzu je potřeba přidat alespoň jednu oblast na mapu.');
        return;
    }

    // Najít aktuální modul mapových overlapů
    const mapModule = document.querySelector('.module[id^="module-mapove-overlapy"]');
    if (!mapModule) {
        console.error('Modul mapových overlapů nebyl nalezen');
        return;
    }

    // Najít kontejner pro výsledky analýzy
    const analysisContainer = mapModule.querySelector('#analysis-results');
    if (!analysisContainer) {
        console.error('Kontejner pro výsledky analýzy nebyl nalezen');
        return;
    }

    // Zobrazení načítání
    analysisContainer.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Analýza oblasti...</span>
        </div>
    `;
    analysisContainer.style.display = 'block';

    // Simulace analýzy (v reálné implementaci by zde byla skutečná analýza)
    setTimeout(() => {
        // Získání první oblasti
        const area = mapLayers.dataLayers.areas.getLayers()[0];
        const bounds = area.getBounds();
        const center = bounds.getCenter();

        // Výpočet plochy oblasti (přibližně)
        let areaSqMeters = 0;
        try {
            // Kontrola, zda je načtena knihovna Leaflet.GeometryUtil
            if (typeof L.GeometryUtil !== 'undefined' && L.GeometryUtil.geodesicArea) {
                areaSqMeters = L.GeometryUtil.geodesicArea(area.getLatLngs()[0]);
            } else {
                // Jednoduchý výpočet plochy (méně přesný)
                const latlngs = area.getLatLngs()[0];
                areaSqMeters = calculatePolygonArea(latlngs);
            }
        } catch (error) {
            console.error('Chyba při výpočtu plochy:', error);
            areaSqMeters = 0;
        }
        const areaSqKm = (areaSqMeters / 1000000).toFixed(2);

        // Počet bodů v oblasti
        let pointsInArea = 0;
        markers.forEach(marker => {
            if (area.getBounds().contains(marker.getLatLng())) {
                pointsInArea++;
            }
        });

        // Zobrazení výsledků analýzy
        analysisContainer.innerHTML = `
            <div class="analysis-header">
                <h4>Výsledky analýzy oblasti</h4>
            </div>
            <div class="analysis-content">
                <div class="analysis-section">
                    <h5>Základní informace</h5>
                    <table class="analysis-table">
                        <tr>
                            <td>Střed oblasti:</td>
                            <td>${center.lat.toFixed(6)}, ${center.lng.toFixed(6)}</td>
                        </tr>
                        <tr>
                            <td>Plocha:</td>
                            <td>${areaSqKm} km²</td>
                        </tr>
                        <tr>
                            <td>Počet bodů v oblasti:</td>
                            <td>${pointsInArea}</td>
                        </tr>
                    </table>
                </div>

                <div class="analysis-section">
                    <h5>Demografické údaje</h5>
                    <p>Pro získání demografických údajů je potřeba propojení s externím zdrojem dat.</p>
                    <div class="external-links">
                        <a href="https://vdb.czso.cz/vdbvo2/" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> Veřejná databáze ČSÚ
                        </a>
                    </div>
                </div>

                <div class="analysis-section">
                    <h5>Kriminalita</h5>
                    <p>Pro získání údajů o kriminalitě je potřeba propojení s externím zdrojem dat.</p>
                    <div class="external-links">
                        <a href="https://www.mapakriminality.cz/" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> Mapa kriminality
                        </a>
                    </div>
                </div>
            </div>
        `;
    }, 1500);
}
