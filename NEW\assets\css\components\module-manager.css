/* Styly pro správce modulů */

/* Dialog pro přidání modulu */
.module-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.module-dialog-content {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    width: 800px;
    max-width: 90%;
    max-height: 90%;
    display: flex;
    flex-direction: column;
}

.module-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: #1a3c89;
    color: white;
    border-bottom: 1px solid #15306d;
}

.module-dialog-header h4 {
    margin: 0;
    font-size: 18px;
}

.module-dialog-close {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
}

.module-dialog-body {
    display: flex;
    flex: 1;
    overflow: hidden;
}

.module-categories {
    width: 200px;
    padding: 15px;
    background-color: #f5f5f5;
    border-right: 1px solid #ddd;
    overflow-y: auto;
}

.module-categories h5 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #333;
}

.category-list {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.category-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 10px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.category-item:hover {
    background-color: #e9ecef;
}

.category-item.active {
    background-color: #1a3c89;
    color: white;
}

.category-item i {
    width: 20px;
    text-align: center;
}

.module-list {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
}

.module-list h5 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #333;
}

.module-items {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.module-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.module-item:hover {
    border-color: #1a3c89;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.module-item.active {
    border-color: #1a3c89;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    background-color: rgba(26, 60, 137, 0.05);
}

.module-item-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #1a3c89;
    color: white;
    border-radius: 4px;
    font-size: 18px;
}

.module-item-content {
    flex: 1;
}

.module-item-content h6 {
    margin: 0 0 5px 0;
    font-size: 16px;
    color: #333;
}

.module-item-content p {
    margin: 0;
    font-size: 14px;
    color: #666;
}

.module-dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 15px;
    background-color: #f5f5f5;
    border-top: 1px solid #ddd;
}

.module-dialog-footer button {
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.module-dialog-footer button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #1a3c89;
    color: white;
    border: none;
}

.btn-primary:hover:not(:disabled) {
    background-color: #15306d;
}

.btn-secondary {
    background-color: #f8f9fa;
    color: #333;
    border: 1px solid #ccc;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #e2e6ea;
}

/* Responsivní úpravy */
@media (max-width: 768px) {
    .module-dialog-body {
        flex-direction: column;
    }
    
    .module-categories {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid #ddd;
    }
    
    .category-list {
        flex-direction: row;
        flex-wrap: wrap;
    }
    
    .category-item {
        flex: 1;
        min-width: 100px;
    }
}
