/**
 * Monitoring komunikačn<PERSON><PERSON> platforem - funkce pro práci s médii
 */

/**
 * Zobrazení média
 * @param {string} url - URL média
 */
function viewMedia(url) {
    console.log('Zobrazení média:', url);
    
    // Vytvoření dialogu pro zobrazení média
    const dialog = document.createElement('div');
    dialog.className = 'media-viewer-dialog';
    
    // Vytvoření obsahu dialogu
    dialog.innerHTML = `
        <div class="media-viewer-dialog-content">
            <div class="media-viewer-dialog-header">
                <h3>Náhled média</h3>
                <button type="button" class="media-viewer-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="media-viewer-dialog-body">
                <img src="${url}" alt="Náhled média" class="media-viewer-image">
            </div>
            <div class="media-viewer-dialog-footer">
                <button type="button" class="btn-inline add-to-gallery-btn" data-url="${url}">
                    <i class="fas fa-plus"></i> Přidat do galerie
                </button>
                <a href="${url}" class="btn-inline" download target="_blank">
                    <i class="fas fa-download"></i> Stáhnout
                </a>
                <button type="button" class="btn-primary media-viewer-dialog-close-btn">Zavřít</button>
            </div>
        </div>
    `;
    
    // Přidání dialogu do stránky
    document.body.appendChild(dialog);
    
    // Přidání event listenerů pro zavření dialogu
    dialog.querySelector('.media-viewer-dialog-close').addEventListener('click', () => dialog.remove());
    dialog.querySelector('.media-viewer-dialog-close-btn').addEventListener('click', () => dialog.remove());
    
    // Přidání event listeneru pro tlačítko přidání do galerie
    dialog.querySelector('.add-to-gallery-btn').addEventListener('click', function() {
        const url = this.getAttribute('data-url');
        addMediaToGallery(url);
        dialog.remove();
    });
    
    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}

/**
 * Přidání média do galerie
 * @param {string} url - URL média
 * @param {string} messageId - ID zprávy
 * @param {string} itemId - ID kanálu/serveru/skupiny
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 */
function addMediaToGallery(url, messageId, itemId, platform) {
    console.log('Přidání média do galerie:', url, messageId, itemId, platform);
    
    // Najít aktuální modul monitoringu komunikačních platforem
    const messagingModule = document.querySelector('.module[id^="module-monitoring-komunikacnich-platforem"]');
    if (!messagingModule) {
        console.error('Modul monitoringu komunikačních platforem nebyl nalezen');
        return;
    }
    
    // Najít galerii v modulu
    const gallery = messagingModule.querySelector('.messaging-platforms-gallery');
    if (!gallery) {
        console.error('Galerie nebyla nalezena v modulu monitoringu komunikačních platforem');
        return;
    }
    
    // Vytvoření názvu pro médium
    let title = 'Médium z platformy';
    
    if (platform && itemId && messageId) {
        // Získání informací o zdroji média
        let item, message, user;
        
        switch (platform) {
            case 'telegram':
                item = messagingPlatformsData.telegram.channels.find(c => c.id === itemId);
                message = (messagingPlatformsData.telegram.messages[itemId] || []).find(m => m.id === messageId);
                if (message) {
                    user = messagingPlatformsData.telegram.users[message.userId];
                }
                break;
            case 'discord':
                item = messagingPlatformsData.discord.servers.find(s => s.id === itemId);
                message = (messagingPlatformsData.discord.messages[itemId] || []).find(m => m.id === messageId);
                if (message) {
                    user = messagingPlatformsData.discord.users[message.userId];
                }
                break;
            case 'whatsapp':
                item = messagingPlatformsData.whatsapp.groups.find(g => g.id === itemId);
                message = (messagingPlatformsData.whatsapp.messages[itemId] || []).find(m => m.id === messageId);
                if (message) {
                    user = messagingPlatformsData.whatsapp.users[message.userId];
                }
                break;
        }
        
        if (item && message) {
            const platformName = getPlatformName(platform);
            const itemName = item.name;
            const userName = user ? user.displayName : 'Neznámý uživatel';
            const messageDate = message.timestamp ? formatDate(message.timestamp) : 'Neznámé datum';
            
            title = `${platformName} - ${itemName} - ${userName} - ${messageDate}`;
        }
    }
    
    // Přidání média do galerie
    addPhotoToGallery(gallery, url, title);
    
    // Zobrazení notifikace
    showNotification('Médium bylo úspěšně přidáno do galerie.', 'success');
}

/**
 * Stažení dokumentu
 * @param {string} url - URL dokumentu
 */
function downloadDocument(url) {
    console.log('Stažení dokumentu:', url);
    
    // Kontrola, zda URL není prázdná nebo #
    if (!url || url === '#') {
        showNotification('Dokument není k dispozici ke stažení.', 'warning');
        return;
    }
    
    // Vytvoření odkazu pro stažení
    const link = document.createElement('a');
    link.href = url;
    link.target = '_blank';
    link.download = url.split('/').pop() || 'dokument';
    
    // Přidání odkazu do stránky a kliknutí na něj
    document.body.appendChild(link);
    link.click();
    
    // Odstranění odkazu
    document.body.removeChild(link);
    
    // Zobrazení notifikace
    showNotification('Stahování dokumentu bylo zahájeno.', 'success');
}

/**
 * Zpracování vložení (Ctrl+V) pro galerii
 * @param {Event} event - Událost vložení
 * @param {HTMLElement} gallery - Element galerie
 */
function handlePaste(event, gallery) {
    console.log('Zpracování vložení pro galerii');
    
    // Kontrola, zda je galerie platná
    if (!gallery) {
        console.error('Galerie nebyla nalezena');
        return;
    }
    
    // Kontrola, zda událost obsahuje data schránky
    if (!event.clipboardData || !event.clipboardData.items) {
        console.error('Událost neobsahuje data schránky');
        return;
    }
    
    // Procházení položek ve schránce
    const items = event.clipboardData.items;
    let imageFound = false;
    
    for (let i = 0; i < items.length; i++) {
        // Kontrola, zda položka je obrázek
        if (items[i].type.indexOf('image') !== -1) {
            imageFound = true;
            
            // Získání souboru z položky
            const file = items[i].getAsFile();
            if (!file) continue;
            
            // Zpracování souboru
            processPhotoForGallery(file, gallery);
            
            // Zastavení výchozí akce
            event.preventDefault();
            break;
        }
    }
    
    if (!imageFound) {
        console.log('Ve schránce nebyl nalezen žádný obrázek');
    }
}

/**
 * Testování emailového alertu pro monitoring komunikačních platforem
 */
function testMessagingEmailAlert() {
    console.log('Testování emailového alertu pro monitoring komunikačních platforem');
    
    // Získání emailové adresy z formuláře
    const emailInput = document.getElementById('messaging-alert-email');
    if (!emailInput || !emailInput.value) {
        showNotification('Zadejte emailovou adresu v nastavení.', 'error');
        return;
    }
    
    const email = emailInput.value.trim();
    
    // Validace emailové adresy
    if (!validateEmailFormat(email)) {
        showNotification('Zadejte platnou emailovou adresu.', 'error');
        return;
    }
    
    // Zobrazení notifikace o zahájení odesílání
    showNotification(`Odesílám testovací email na adresu ${email}...`, 'info');
    
    // Najít tlačítko TEST a změnit jeho text na "Odesílám..."
    const testButton = document.querySelector('.test-email-alert');
    if (testButton) {
        const originalContent = testButton.innerHTML;
        testButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Odesílám...';
        testButton.disabled = true;
        
        // Simulace odeslání emailu (v reálné aplikaci by zde byl API požadavek na backend)
        setTimeout(() => {
            // Obnovení původního textu tlačítka
            testButton.innerHTML = originalContent;
            testButton.disabled = false;
            
            // Zobrazení potvrzení
            showNotification(`Testovací email byl úspěšně odeslán na adresu ${email}`, 'success');
            
            // Zobrazení detailního dialogu s informacemi o odeslaném emailu
            showEmailSentDialog(email);
        }, 2000);
    } else {
        // Pokud tlačítko nebylo nalezeno, použijeme pouze notifikaci
        setTimeout(() => {
            showNotification(`Testovací email byl úspěšně odeslán na adresu ${email}`, 'success');
        }, 2000);
    }
}

/**
 * Zobrazení dialogu s informacemi o odeslaném emailu
 * @param {string} email - Emailová adresa, na kterou byl email odeslán
 */
function showEmailSentDialog(email) {
    // Vytvoření dialogu
    const dialog = document.createElement('div');
    dialog.className = 'email-sent-dialog';
    dialog.innerHTML = `
        <div class="email-sent-content">
            <div class="email-sent-header">
                <h3>Email byl odeslán</h3>
                <button type="button" class="email-sent-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="email-sent-body">
                <div class="email-sent-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="email-sent-message">
                    <p>Testovací email byl úspěšně odeslán na adresu:</p>
                    <p class="email-address">${email}</p>
                </div>
                <div class="email-sent-details">
                    <h4>Detaily emailu:</h4>
                    <ul>
                        <li><strong>Předmět:</strong> [OSINT] Testovací email pro monitoring komunikačních platforem</li>
                        <li><strong>Odesláno:</strong> ${new Date().toLocaleString()}</li>
                        <li><strong>Obsah:</strong> Toto je testovací email pro ověření funkčnosti alertů v modulu monitoringu komunikačních platforem.</li>
                    </ul>
                </div>
                <div class="email-sent-note">
                    <p>Poznámka: Pokud email nedorazí do vaší schránky, zkontrolujte složku spam nebo se obraťte na správce systému.</p>
                </div>
            </div>
            <div class="email-sent-footer">
                <button type="button" class="btn-primary email-sent-ok">OK</button>
            </div>
        </div>
    `;
    
    // Přidání dialogu do stránky
    document.body.appendChild(dialog);
    
    // Event listener pro zavření dialogu
    const closeButton = dialog.querySelector('.email-sent-close');
    if (closeButton) {
        closeButton.addEventListener('click', function() {
            dialog.remove();
        });
    }
    
    // Event listener pro tlačítko OK
    const okButton = dialog.querySelector('.email-sent-ok');
    if (okButton) {
        okButton.addEventListener('click', function() {
            dialog.remove();
        });
    }
    
    // Event listener pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}

/**
 * Validace formátu emailové adresy
 * @param {string} email - Emailová adresa k validaci
 * @returns {boolean} - True pokud je formát platný, jinak false
 */
function validateEmailFormat(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Získání názvu platformy
 * @param {string} platform - Kód platformy (telegram, discord, whatsapp)
 * @returns {string} - Název platformy
 */
function getPlatformName(platform) {
    switch (platform) {
        case 'telegram':
            return 'Telegram';
        case 'discord':
            return 'Discord';
        case 'whatsapp':
            return 'WhatsApp';
        default:
            return 'Neznámá platforma';
    }
}

/**
 * Formátování data pro název souboru
 * @param {Date} date - Datum
 * @returns {string} - Formátované datum pro název souboru
 */
function formatDateForFilename(date) {
    return date.toISOString().replace(/[:.]/g, '-').replace('T', '_').split('Z')[0];
}

/**
 * Konverze dat do formátu CSV
 * @param {Object} data - Data k exportu
 * @returns {string} - CSV řetězec
 */
function convertToCSV(data) {
    // Vytvoření hlavičky CSV
    let csv = 'Data exportu z platformy ' + data.platform + '\n';
    csv += 'Datum exportu: ' + formatDate(data.exportDate) + '\n\n';
    
    // Přidání informací o kanálu/serveru/skupině
    if (data.item) {
        csv += 'Informace o zdroji:\n';
        csv += 'Název: ' + data.item.name + '\n';
        csv += 'Identifikátor: ' + data.item.identifier + '\n';
        csv += 'Typ: ' + (data.item.type || 'Neznámý') + '\n';
        csv += 'Počet členů: ' + data.item.memberCount + '\n';
        csv += 'Kategorie: ' + getCategoryName(data.item.category) + '\n';
        csv += 'Popis: ' + (data.item.description || 'Bez popisu') + '\n\n';
    }
    
    // Přidání zpráv
    if (data.messages && data.messages.length > 0) {
        csv += 'Zprávy:\n';
        csv += 'ID zprávy;Odesílatel;Datum a čas;Obsah;Přílohy\n';
        
        data.messages.forEach(message => {
            const user = data.users && data.users[message.userId] ? data.users[message.userId].displayName : 'Neznámý uživatel';
            const attachments = message.attachments && message.attachments.length > 0 ? 
                message.attachments.map(a => a.type + ': ' + (a.name || a.url)).join(', ') : '';
            
            csv += message.id + ';' + 
                  user + ';' + 
                  formatDate(message.timestamp) + ';' + 
                  '"' + message.content.replace(/"/g, '""') + '";' + 
                  '"' + attachments.replace(/"/g, '""') + '"\n';
        });
    }
    
    return csv;
}
