/**
 * <PERSON><PERSON><PERSON> osa - pomo<PERSON><PERSON><PERSON> funkce pro analýzu a generování dat
 */

/**
 * Zobrazení notifikace
 * @param {string} message - <PERSON><PERSON>rá<PERSON> k zobrazení
 * @param {string} type - Typ notifikace (success, error, warning, info)
 */
function showNotification(message, type = 'info') {
    console.log(`Zobrazení notifikace: ${message} ${type}`);

    // Kontrola, zda existuje funkce pro zobrazení notifikace v news-translation.js
    if (typeof showNewsNotification === 'function') {
        showNewsNotification(message, type);
        return;
    }

    // Pokud funkce neexistuje, vytvoříme vlastní notifikaci
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-icon">
            <i class="fas ${getNotificationIcon(type)}"></i>
        </div>
        <div class="notification-content">
            <div class="notification-message">${message}</div>
        </div>
        <button type="button" class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Přidání notifikace do stránky
    document.body.appendChild(notification);

    // Přidání event listeneru pro zavření notifikace
    notification.querySelector('.notification-close').addEventListener('click', () => {
        notification.remove();
    });

    // Automatické zavření notifikace po 5 sekundách
    setTimeout(() => {
        notification.classList.add('notification-hiding');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 5000);
}

/**
 * Získání ikony pro notifikaci podle typu
 * @param {string} type - Typ notifikace
 * @returns {string} - Třída ikony
 */
function getNotificationIcon(type) {
    switch (type) {
        case 'success':
            return 'fa-check-circle';
        case 'error':
            return 'fa-exclamation-circle';
        case 'warning':
            return 'fa-exclamation-triangle';
        case 'info':
        default:
            return 'fa-info-circle';
    }
}

/**
 * Formátování data a času pro input typu datetime-local
 * @param {Date} date - Datum k formátování
 * @returns {string} - Formátovaný řetězec pro input
 */
function formatDatetimeForInput(date) {
    if (!date || !(date instanceof Date) || isNaN(date)) {
        return '';
    }

    // Formát: YYYY-MM-DDThh:mm
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

/**
 * Formátování data pro zobrazení
 * @param {Date} date - Datum k formátování
 * @returns {string} - Formátovaný řetězec pro zobrazení
 */
function formatDate(date) {
    if (!date || !(date instanceof Date) || isNaN(date)) {
        return '';
    }

    // Formát: DD.MM.YYYY HH:MM
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${day}.${month}.${year} ${hours}:${minutes}`;
}

/**
 * Získání názvu typu události
 * @param {string} type - Typ události
 * @returns {string} - Název typu události
 */
function getTypeName(type) {
    switch (type) {
        case 'communication':
            return 'Komunikace';
        case 'movement':
            return 'Pohyb';
        case 'financial':
            return 'Finanční transakce';
        case 'social':
            return 'Sociální aktivita';
        case 'legal':
            return 'Právní událost';
        case 'other':
            return 'Ostatní';
        default:
            return type;
    }
}

/**
 * Získání názvu zdroje události
 * @param {string} source - Zdroj události
 * @returns {string} - Název zdroje události
 */
function getSourceName(source) {
    switch (source) {
        case 'manual':
            return 'Manuálně přidáno';
        case 'social':
            return 'Sociální sítě';
        case 'financial':
            return 'Finanční monitoring';
        case 'communication':
            return 'Komunikační platformy';
        case 'location':
            return 'Lokační data';
        case 'other':
            return 'Ostatní';
        default:
            return source;
    }
}

/**
 * Získání názvu důležitosti události
 * @param {string} importance - Důležitost události
 * @returns {string} - Název důležitosti události
 */
function getImportanceName(importance) {
    switch (importance) {
        case 'low':
            return 'Nízká';
        case 'medium':
            return 'Střední';
        case 'high':
            return 'Vysoká';
        case 'critical':
            return 'Kritická';
        default:
            return importance;
    }
}

/**
 * Generování ID události
 * @returns {string} - Vygenerované ID
 */
function generateEventId() {
    return 'event-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
}

/**
 * Formátování data
 * @param {Date} date - Datum
 * @returns {string} - Formátované datum
 */
function formatDate(date) {
    if (!date) return 'Neznámé datum';

    // Kontrola, zda je datum platné
    if (isNaN(date.getTime())) return 'Neplatné datum';

    // Formátování data
    return date.toLocaleString('cs-CZ', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * Formátování data pro input
 * @param {Date} date - Datum
 * @returns {string} - Formátované datum pro input
 */
function formatDatetimeForInput(date) {
    if (!date) return '';

    // Kontrola, zda je datum platné
    if (isNaN(date.getTime())) return '';

    // Formátování data pro input
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');

    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

/**
 * Formátování data pro název souboru
 * @param {Date} date - Datum
 * @returns {string} - Formátované datum pro název souboru
 */
function formatDateForFilename(date) {
    if (!date) return 'unknown-date';

    // Kontrola, zda je datum platné
    if (isNaN(date.getTime())) return 'invalid-date';

    // Formátování data pro název souboru
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}${month}${day}_${hours}${minutes}${seconds}`;
}

/**
 * Získání názvu typu události
 * @param {string} type - Typ události
 * @returns {string} - Název typu události
 */
function getTypeName(type) {
    const types = {
        'communication': 'Komunikace',
        'movement': 'Pohyb',
        'financial': 'Finanční transakce',
        'social': 'Sociální aktivita',
        'legal': 'Právní událost',
        'other': 'Ostatní'
    };

    return types[type] || 'Neznámý typ';
}

/**
 * Získání názvu zdroje události
 * @param {string} source - Zdroj události
 * @returns {string} - Název zdroje události
 */
function getSourceName(source) {
    const sources = {
        'manual': 'Manuálně přidáno',
        'social': 'Sociální sítě',
        'financial': 'Finanční monitoring',
        'communication': 'Komunikační platformy',
        'location': 'Lokační data',
        'other': 'Ostatní'
    };

    return sources[source] || 'Neznámý zdroj';
}

/**
 * Získání názvu důležitosti události
 * @param {string} importance - Důležitost události
 * @returns {string} - Název důležitosti události
 */
function getImportanceName(importance) {
    const importances = {
        'low': 'Nízká',
        'medium': 'Střední',
        'high': 'Vysoká',
        'critical': 'Kritická'
    };

    return importances[importance] || 'Neznámá důležitost';
}

/**
 * Filtrování událostí podle nastavených filtrů
 * @returns {Array} - Filtrované události
 */
function filterEvents() {
    // Kopie pole událostí
    let filteredEvents = [...timelineEvents];

    // Filtrování podle typu
    if (timelineSettings.filters.type !== 'all') {
        filteredEvents = filteredEvents.filter(event => event.type === timelineSettings.filters.type);
    }

    // Filtrování podle zdroje
    if (timelineSettings.filters.source !== 'all') {
        filteredEvents = filteredEvents.filter(event => event.source === timelineSettings.filters.source);
    }

    // Filtrování podle data od
    if (timelineSettings.filters.dateFrom) {
        const dateFrom = new Date(timelineSettings.filters.dateFrom);
        filteredEvents = filteredEvents.filter(event => new Date(event.datetime) >= dateFrom);
    }

    // Filtrování podle data do
    if (timelineSettings.filters.dateTo) {
        const dateTo = new Date(timelineSettings.filters.dateTo);
        dateTo.setHours(23, 59, 59, 999); // Nastavení na konec dne
        filteredEvents = filteredEvents.filter(event => new Date(event.datetime) <= dateTo);
    }

    // Filtrování podle klíčových slov
    if (timelineSettings.filters.keywords) {
        const keywords = timelineSettings.filters.keywords.toLowerCase().split(/\s+/);
        filteredEvents = filteredEvents.filter(event => {
            const eventText = `${event.title} ${event.description} ${event.location} ${event.notes} ${event.tags.join(' ')} ${event.relatedPersons.join(' ')}`.toLowerCase();
            return keywords.some(keyword => eventText.includes(keyword));
        });
    }

    return filteredEvents;
}

/**
 * Seřazení událostí podle data
 * @param {Array} events - Pole událostí
 * @returns {Array} - Seřazené události
 */
function sortEvents(events) {
    return [...events].sort((a, b) => new Date(a.datetime) - new Date(b.datetime));
}

/**
 * Získání časového rozsahu událostí
 * @returns {Object} - Časový rozsah (start, end)
 */
function getTimelineRange() {
    // Kontrola, zda existují události
    if (timelineEvents.length === 0) {
        return { start: null, end: null };
    }

    // Získání filtrovaných událostí
    const filteredEvents = filterEvents();

    // Kontrola, zda existují filtrované události
    if (filteredEvents.length === 0) {
        return { start: null, end: null };
    }

    // Seřazení událostí podle data
    const sortedEvents = sortEvents(filteredEvents);

    // Získání první a poslední události
    const firstEvent = sortedEvents[0];
    const lastEvent = sortedEvents[sortedEvents.length - 1];

    return {
        start: new Date(firstEvent.datetime),
        end: new Date(lastEvent.datetime)
    };
}

/**
 * Vytvoření měřítka pro roky
 * @param {Date} start - Počáteční datum
 * @param {Date} end - Koncové datum
 * @returns {string} - HTML kód měřítka
 */
function createYearScale(start, end) {
    const startYear = start.getFullYear();
    const endYear = end.getFullYear();

    let scaleHTML = '<div class="timeline-scale-items">';

    for (let year = startYear; year <= endYear; year++) {
        scaleHTML += `
            <div class="timeline-scale-item">
                <div class="timeline-scale-marker"></div>
                <div class="timeline-scale-label">${year}</div>
            </div>
        `;
    }

    scaleHTML += '</div>';

    return scaleHTML;
}

/**
 * Vytvoření měřítka pro měsíce
 * @param {Date} start - Počáteční datum
 * @param {Date} end - Koncové datum
 * @returns {string} - HTML kód měřítka
 */
function createMonthScale(start, end) {
    const startYear = start.getFullYear();
    const startMonth = start.getMonth();
    const endYear = end.getFullYear();
    const endMonth = end.getMonth();

    const monthNames = [
        'Leden', 'Únor', 'Březen', 'Duben', 'Květen', 'Červen',
        'Červenec', 'Srpen', 'Září', 'Říjen', 'Listopad', 'Prosinec'
    ];

    let scaleHTML = '<div class="timeline-scale-items">';

    for (let year = startYear; year <= endYear; year++) {
        const firstMonth = (year === startYear) ? startMonth : 0;
        const lastMonth = (year === endYear) ? endMonth : 11;

        for (let month = firstMonth; month <= lastMonth; month++) {
            scaleHTML += `
                <div class="timeline-scale-item">
                    <div class="timeline-scale-marker"></div>
                    <div class="timeline-scale-label">${monthNames[month]} ${year}</div>
                </div>
            `;
        }
    }

    scaleHTML += '</div>';

    return scaleHTML;
}

/**
 * Vytvoření měřítka pro dny
 * @param {Date} start - Počáteční datum
 * @param {Date} end - Koncové datum
 * @returns {string} - HTML kód měřítka
 */
function createDayScale(start, end) {
    // Omezení na maximálně 30 dní
    const maxDays = 30;
    const diffDays = Math.ceil((end - start) / (1000 * 60 * 60 * 24));

    if (diffDays > maxDays) {
        // Pokud je rozsah větší než 30 dní, zobrazíme pouze prvních a posledních 15 dní
        const midStart = new Date(start);
        midStart.setDate(midStart.getDate() + 15);

        const midEnd = new Date(end);
        midEnd.setDate(midEnd.getDate() - 15);

        let scaleHTML = '<div class="timeline-scale-items">';

        // Prvních 15 dní
        for (let i = 0; i < 15; i++) {
            const day = new Date(start);
            day.setDate(day.getDate() + i);

            scaleHTML += `
                <div class="timeline-scale-item">
                    <div class="timeline-scale-marker"></div>
                    <div class="timeline-scale-label">${day.getDate()}.${day.getMonth() + 1}.</div>
                </div>
            `;
        }

        // Mezera
        scaleHTML += `
            <div class="timeline-scale-item timeline-scale-gap">
                <div class="timeline-scale-marker"></div>
                <div class="timeline-scale-label">...</div>
            </div>
        `;

        // Posledních 15 dní
        for (let i = 14; i >= 0; i--) {
            const day = new Date(end);
            day.setDate(day.getDate() - i);

            scaleHTML += `
                <div class="timeline-scale-item">
                    <div class="timeline-scale-marker"></div>
                    <div class="timeline-scale-label">${day.getDate()}.${day.getMonth() + 1}.</div>
                </div>
            `;
        }

        scaleHTML += '</div>';

        return scaleHTML;
    }

    // Pokud je rozsah menší než 30 dní, zobrazíme všechny dny
    let scaleHTML = '<div class="timeline-scale-items">';

    const currentDate = new Date(start);
    while (currentDate <= end) {
        scaleHTML += `
            <div class="timeline-scale-item">
                <div class="timeline-scale-marker"></div>
                <div class="timeline-scale-label">${currentDate.getDate()}.${currentDate.getMonth() + 1}.</div>
            </div>
        `;

        currentDate.setDate(currentDate.getDate() + 1);
    }

    scaleHTML += '</div>';

    return scaleHTML;
}

/**
 * Vytvoření měřítka pro hodiny
 * @param {Date} start - Počáteční datum
 * @param {Date} end - Koncové datum
 * @returns {string} - HTML kód měřítka
 */
function createHourScale(start, end) {
    // Omezení na maximálně 24 hodin
    const maxHours = 24;
    const diffHours = Math.ceil((end - start) / (1000 * 60 * 60));

    if (diffHours > maxHours) {
        // Pokud je rozsah větší než 24 hodin, zobrazíme pouze prvních a posledních 12 hodin
        const midStart = new Date(start);
        midStart.setHours(midStart.getHours() + 12);

        const midEnd = new Date(end);
        midEnd.setHours(midEnd.getHours() - 12);

        let scaleHTML = '<div class="timeline-scale-items">';

        // Prvních 12 hodin
        for (let i = 0; i < 12; i++) {
            const hour = new Date(start);
            hour.setHours(hour.getHours() + i);

            scaleHTML += `
                <div class="timeline-scale-item">
                    <div class="timeline-scale-marker"></div>
                    <div class="timeline-scale-label">${hour.getHours()}:00</div>
                </div>
            `;
        }

        // Mezera
        scaleHTML += `
            <div class="timeline-scale-item timeline-scale-gap">
                <div class="timeline-scale-marker"></div>
                <div class="timeline-scale-label">...</div>
            </div>
        `;

        // Posledních 12 hodin
        for (let i = 11; i >= 0; i--) {
            const hour = new Date(end);
            hour.setHours(hour.getHours() - i);

            scaleHTML += `
                <div class="timeline-scale-item">
                    <div class="timeline-scale-marker"></div>
                    <div class="timeline-scale-label">${hour.getHours()}:00</div>
                </div>
            `;
        }

        scaleHTML += '</div>';

        return scaleHTML;
    }

    // Pokud je rozsah menší než 24 hodin, zobrazíme všechny hodiny
    let scaleHTML = '<div class="timeline-scale-items">';

    const currentHour = new Date(start);
    currentHour.setMinutes(0, 0, 0); // Zarovnání na celou hodinu

    while (currentHour <= end) {
        scaleHTML += `
            <div class="timeline-scale-item">
                <div class="timeline-scale-marker"></div>
                <div class="timeline-scale-label">${currentHour.getHours()}:00</div>
            </div>
        `;

        currentHour.setHours(currentHour.getHours() + 1);
    }

    scaleHTML += '</div>';

    return scaleHTML;
}

/**
 * Přidání média k události
 * @param {string} eventId - ID události
 * @param {string} url - URL média
 * @param {string} title - Název média
 */
function addMediaToEvent(eventId, url, title) {
    console.log('Přidání média k události:', eventId, url, title);

    // Najít index události podle ID
    const eventIndex = timelineEvents.findIndex(e => e.id === eventId);
    if (eventIndex === -1) {
        console.error('Událost nebyla nalezena:', eventId);
        return;
    }

    // Přidání média k události
    if (!timelineEvents[eventIndex].media) {
        timelineEvents[eventIndex].media = [];
    }

    timelineEvents[eventIndex].media.push({
        url: url,
        title: title,
        addedAt: new Date().toISOString()
    });

    // Uložení dat
    saveTimelineData();

    // Zobrazení notifikace
    showNotification('Médium bylo úspěšně přidáno k události.', 'success');
}

/**
 * Testování emailového alertu pro časovou osu
 */
function testTimelineEmailAlert() {
    console.log('Testování emailového alertu pro časovou osu');

    // Získání emailové adresy z formuláře
    const emailInput = document.getElementById('timeline-alert-email');
    if (!emailInput || !emailInput.value) {
        showNotification('Zadejte emailovou adresu v nastavení.', 'error');
        return;
    }

    const email = emailInput.value.trim();

    // Validace emailové adresy
    if (!validateEmailFormat(email)) {
        showNotification('Zadejte platnou emailovou adresu.', 'error');
        return;
    }

    // Zobrazení notifikace o zahájení odesílání
    showNotification(`Odesílám testovací email na adresu ${email}...`, 'info');

    // Najít tlačítko TEST a změnit jeho text na "Odesílám..."
    const testButton = document.querySelector('.test-email-alert');
    if (testButton) {
        const originalContent = testButton.innerHTML;
        testButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Odesílám...';
        testButton.disabled = true;

        // Simulace odeslání emailu (v reálné aplikaci by zde byl API požadavek na backend)
        setTimeout(() => {
            // Obnovení původního textu tlačítka
            testButton.innerHTML = originalContent;
            testButton.disabled = false;

            // Zobrazení potvrzení
            showNotification(`Testovací email byl úspěšně odeslán na adresu ${email}`, 'success');

            // Zobrazení detailního dialogu s informacemi o odeslaném emailu
            showEmailSentDialog(email);
        }, 2000);
    } else {
        // Pokud tlačítko nebylo nalezeno, použijeme pouze notifikaci
        setTimeout(() => {
            showNotification(`Testovací email byl úspěšně odeslán na adresu ${email}`, 'success');
        }, 2000);
    }
}

/**
 * Uložení nastavení notifikací pro časovou osu
 */
function saveTimelineNotificationSettings() {
    console.log('Ukládání nastavení notifikací pro časovou osu');

    // Získání emailové adresy z formuláře
    const emailInput = document.getElementById('timeline-alert-email');
    if (!emailInput || !emailInput.value.trim() === '') {
        showNotification('Zadejte emailovou adresu v nastavení.', 'error');
        return;
    }

    const email = emailInput.value.trim();

    // Validace emailové adresy
    if (!validateEmailFormat(email)) {
        showNotification('Zadejte platnou emailovou adresu.', 'error');
        return;
    }

    // Získání frekvence notifikací
    const frequencySelect = document.getElementById('timeline-alert-frequency');
    const frequency = frequencySelect ? frequencySelect.value : 'daily';

    // Aktualizace nastavení
    timelineSettings.notifications = {
        email: email,
        frequency: frequency
    };

    // Uložení nastavení
    saveTimelineData();

    // Zobrazení potvrzení
    showNotification('Nastavení notifikací bylo úspěšně uloženo.', 'success');
}

/**
 * Validace formátu emailové adresy
 * @param {string} email - Emailová adresa k validaci
 * @returns {boolean} - Výsledek validace
 */
function validateEmailFormat(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Zobrazení dialogu s informacemi o odeslaném emailu
 * @param {string} email - Emailová adresa, na kterou byl odeslán email
 */
function showEmailSentDialog(email) {
    // Vytvoření dialogu
    const dialog = document.createElement('div');
    dialog.className = 'email-sent-dialog';

    // Vytvoření obsahu dialogu
    dialog.innerHTML = `
        <div class="email-sent-dialog-content">
            <div class="email-sent-dialog-header">
                <h3>Email odeslán</h3>
                <button type="button" class="email-sent-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="email-sent-dialog-body">
                <div class="email-sent-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="email-sent-details">
                    <p>Testovací email byl úspěšně odeslán na adresu:</p>
                    <p class="email-address">${email}</p>
                    <p class="email-sent-note">Zkontrolujte svou emailovou schránku, včetně složky spam.</p>
                </div>
            </div>
            <div class="email-sent-dialog-footer">
                <button type="button" class="btn-primary email-sent-dialog-ok">OK</button>
            </div>
        </div>
    `;

    // Přidání dialogu do stránky
    document.body.appendChild(dialog);

    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.email-sent-dialog-close').addEventListener('click', () => dialog.remove());
    dialog.querySelector('.email-sent-dialog-ok').addEventListener('click', () => dialog.remove());

    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}
