import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/firebase';
import { collection, doc, getDoc, getDocs } from 'firebase/firestore';
import { PDFGenerator } from '@/lib/reports/generators/PDFGenerator';

interface ModuleDataMap {
  [key: string]: any;
}

export async function POST(request: NextRequest) {
  try {
    // Parsování JSON s ošetřením chyb
    let body;
    try {
      body = await request.json();
    } catch (error) {
      console.error("Chyba při parsování JSON:", error);
      return NextResponse.json(
        { error: 'Neplatný JSON formát požadavku' },
        { status: 400 }
      );
    }

    const { caseId, reportType, settings } = body;

    console.log("API Request:", {
      caseId,
      reportType,
      settings: {
        ...settings,
        selectedModules: settings?.selectedModules,
        moduleOrder: settings?.moduleOrder
      }
    });

    // Zajistíme, že selectedModules a moduleOrder jsou v<PERSON>dy pole
    if (settings) {
      if (!Array.isArray(settings.selectedModules)) {
        if (typeof settings.selectedModules === 'string') {
          // Pokud je to string, převedeme ho na pole s jedním prvkem
          settings.selectedModules = [settings.selectedModules];
        } else if (settings.selectedModules === undefined || settings.selectedModules === null) {
          // Pokud je undefined nebo null, použijeme prázdné pole
          settings.selectedModules = [];
        } else if (typeof settings.selectedModules === 'number') {
          // Pokud je to číslo (což je chyba), převedeme ho na string a pak na pole
          settings.selectedModules = [String(settings.selectedModules)];
        } else {
          // Jinak použijeme prázdné pole
          settings.selectedModules = [];
        }
        console.log("Konvertuji selectedModules na pole:", settings.selectedModules);
      }

      if (!Array.isArray(settings.moduleOrder)) {
        if (typeof settings.moduleOrder === 'string') {
          // Pokud je to string, převedeme ho na pole s jedním prvkem
          settings.moduleOrder = [settings.moduleOrder];
        } else if (settings.moduleOrder === undefined || settings.moduleOrder === null) {
          // Pokud je undefined nebo null, použijeme prázdné pole
          settings.moduleOrder = [];
        } else if (typeof settings.moduleOrder === 'number') {
          // Pokud je to číslo (což je chyba), převedeme ho na string a pak na pole
          settings.moduleOrder = [String(settings.moduleOrder)];
        } else {
          // Jinak použijeme prázdné pole
          settings.moduleOrder = [];
        }
        console.log("Konvertuji moduleOrder na pole:", settings.moduleOrder);
      }
    }

    // Validace vstupních parametrů
    if (!caseId || !reportType || !settings) {
      return NextResponse.json(
        { error: 'Chybí povinné parametry' },
        { status: 400 }
      );
    }

    // Kontrola, zda caseId je platný řetězec
    if (typeof caseId !== 'string' || caseId.trim() === '') {
      return NextResponse.json(
        { error: 'Neplatné ID případu' },
        { status: 400 }
      );
    }

    // Kontrola, zda reportType je platný
    if (reportType !== 'full' && reportType !== 'partial') {
      return NextResponse.json(
        { error: 'Neplatný typ reportu' },
        { status: 400 }
      );
    }

    // Získání dat pro report
    let moduleData: ModuleDataMap = {};

    try {
      // Načtení dat modulů
      if (reportType === 'partial' && settings.selectedModules && Array.isArray(settings.selectedModules) && settings.selectedModules.length > 0) {
        // Načtení vybraných modulů
        for (const moduleId of settings.selectedModules) {
          if (typeof moduleId !== 'string') {
            console.warn(`Přeskakuji neplatný moduleId: ${moduleId}`);
            continue;
          }

          // Rozdělení moduleId na části
          // Podporujeme různé formáty:
          // 1. {subjectId}_{moduleType} - standardní formát
          // 2. {moduleType}_{subjectId} - alternativní formát
          // 3. {moduleType} - starý formát bez ID subjektu

          let subjectId, moduleType;

          if (moduleId.includes('_')) {
            // Pokud ID obsahuje podtržítko, rozdělíme ho
            const parts = moduleId.split('_');

            if (parts.length >= 2) {
              // Pokud máme alespoň 2 části, předpokládáme formát {subjectId}_{moduleType}
              subjectId = parts[0];

              // Pokud je více částí, spojíme všechny kromě první do moduleType
              if (parts.length > 2) {
                moduleType = parts.slice(1).join('_');
              } else {
                moduleType = parts[1];
              }

              // Kontrola, zda máme platné ID subjektu a typ modulu
              if (!subjectId || !moduleType) {
                console.warn(`Neplatný formát moduleId: ${moduleId}`);
                continue;
              }
            } else {
              console.warn(`Neplatný formát moduleId: ${moduleId}`);
              continue;
            }
          } else {
            // Pokud ID neobsahuje podtržítko, předpokládáme, že je to pouze typ modulu
            // V tomto případě musíme najít subjekt, který má tento modul
            console.warn(`ModuleId bez ID subjektu: ${moduleId}. Pokusím se najít subjekt.`);

            // Toto je záložní řešení, které by nemělo být potřeba, pokud jsou ID modulů správně formátována
            moduleType = moduleId;

            // Najdeme první subjekt, který má tento modul
            try {
              const subjectsRef = collection(db, "cases", caseId, "subjects");
              const subjectsSnap = await getDocs(subjectsRef);

              let foundSubject = false;

              for (const subjectDoc of subjectsSnap.docs) {
                const tempSubjectId = subjectDoc.id;

                // Zkusíme najít modul v moduleData
                const moduleRef = doc(db, "cases", caseId, "subjects", tempSubjectId, "moduleData", moduleType);
                const moduleSnap = await getDoc(moduleRef);

                if (moduleSnap.exists()) {
                  subjectId = tempSubjectId;
                  foundSubject = true;
                  console.log(`Nalezen subjekt ${subjectId} pro modul ${moduleType}`);
                  break;
                }

                // Zkusíme najít modul v modules
                const altModuleRef = doc(db, "cases", caseId, "subjects", tempSubjectId, "modules", moduleType);
                const altModuleSnap = await getDoc(altModuleRef);

                if (altModuleSnap.exists()) {
                  subjectId = tempSubjectId;
                  foundSubject = true;
                  console.log(`Nalezen subjekt ${subjectId} pro modul ${moduleType} (alternativní cesta)`);
                  break;
                }
              }

              if (!foundSubject) {
                console.warn(`Nenalezen žádný subjekt pro modul ${moduleType}`);
                continue;
              }
            } catch (error) {
              console.error(`Chyba při hledání subjektu pro modul ${moduleType}:`, error);
              continue;
            }
          }

          try {
            console.log(`Načítám modul ${moduleType} pro subjekt ${subjectId}`);

            // Kontrola, zda máme platné ID subjektu a typ modulu
            if (!subjectId || !moduleType) {
              console.warn(`Neplatné ID subjektu nebo typ modulu: ${moduleId}`);
              continue;
            }

            // Použijeme typ modulu tak, jak je
            const normalizedModuleType = moduleType;
            console.log(`Použitý typ modulu: ${normalizedModuleType}`);

            // Zkusíme nejprve načíst z moduleData (správná cesta)
            const moduleDocRef = doc(db, "cases", caseId, "subjects", subjectId, "moduleData", normalizedModuleType);
            console.log(`Zkouším načíst z cesty: cases/${caseId}/subjects/${subjectId}/moduleData/${normalizedModuleType}`);

            try {
              const moduleDocSnap = await getDoc(moduleDocRef);

              if (moduleDocSnap.exists()) {
                // Přidáme informace o subjektu do dat modulu
                const moduleDataWithSubject = {
                  ...moduleDocSnap.data(),
                  subjectId: subjectId
                };

                // Uložíme data pod původním ID modulu
                moduleData[moduleId] = moduleDataWithSubject;
                console.log(`Modul ${normalizedModuleType} byl úspěšně načten z moduleData`);
              } else {
                console.log(`Modul ${normalizedModuleType} neexistuje v moduleData, zkouším alternativní cestu`);

                // Pokud neexistuje, zkusíme alternativní cestu (pro zpětnou kompatibilitu)
                const altModuleDocRef = doc(db, "cases", caseId, "subjects", subjectId, "modules", normalizedModuleType);
                console.log(`Zkouším načíst z cesty: cases/${caseId}/subjects/${subjectId}/modules/${normalizedModuleType}`);

                const altModuleDocSnap = await getDoc(altModuleDocRef);

                if (altModuleDocSnap.exists()) {
                  // Přidáme informace o subjektu do dat modulu
                  const moduleDataWithSubject = {
                    ...altModuleDocSnap.data(),
                    subjectId: subjectId
                  };

                  // Uložíme data pod původním ID modulu
                  moduleData[moduleId] = moduleDataWithSubject;
                  console.log(`Modul ${normalizedModuleType} byl úspěšně načten z modules (alternativní cesta)`);
                } else {
                  console.warn(`Modul ${moduleId} neexistuje v žádné z cest`);
                }
              }
            } catch (docError) {
              console.error(`Chyba při načítání dokumentu pro modul ${moduleId}:`, docError);
            }
          } catch (error) {
            console.error(`Chyba při načítání modulu ${moduleId}:`, error);
            // Pokračujeme s dalším modulem
          }
        }
      } else if (reportType === 'full') {
        // Načtení všech modulů s daty
        try {
          console.log(`Načítám všechny moduly pro případ ${caseId}`);
          const subjectsRef = collection(db, "cases", caseId, "subjects");
          const subjectsSnap = await getDocs(subjectsRef);

          console.log(`Nalezeno ${subjectsSnap.docs.length} subjektů`);

          for (const subjectDoc of subjectsSnap.docs) {
            const subjectId = subjectDoc.id;
            const subjectData = subjectDoc.data();
            console.log(`Načítám moduly pro subjekt ${subjectId} (${subjectData.name || 'Bez jména'})`);

            try {
              // Načtení modulů z hlavní cesty (moduleData)
              const modulesRef = collection(db, "cases", caseId, "subjects", subjectId, "moduleData");
              const modulesSnap = await getDocs(modulesRef);

              console.log(`Nalezeno ${modulesSnap.docs.length} modulů v moduleData pro subjekt ${subjectId}`);

              for (const moduleDoc of modulesSnap.docs) {
                const moduleType = moduleDoc.id;
                const moduleId = `${subjectId}_${moduleType}`;
                console.log(`Načítám modul ${moduleType} z moduleData`);

                // Kontrola, zda modul obsahuje data
                const moduleDocData = moduleDoc.data();
                if (Object.keys(moduleDocData).length === 0) {
                  console.log(`Modul ${moduleType} neobsahuje žádná data, přeskakuji`);
                  continue;
                }

                // Přidáme informace o subjektu do dat modulu
                moduleDocData.subjectName = subjectData.name || '';
                moduleDocData.subjectType = subjectData.type || '';
                moduleDocData.subjectId = subjectId;

                moduleData[moduleId] = moduleDocData;
                console.log(`Modul ${moduleType} byl úspěšně načten z moduleData`);
              }

              // Načtení modulů z alternativní cesty (modules) pro zpětnou kompatibilitu
              const altModulesRef = collection(db, "cases", caseId, "subjects", subjectId, "modules");
              const altModulesSnap = await getDocs(altModulesRef);

              console.log(`Nalezeno ${altModulesSnap.docs.length} modulů v modules pro subjekt ${subjectId}`);

              for (const moduleDoc of altModulesSnap.docs) {
                const moduleType = moduleDoc.id;
                const moduleId = `${subjectId}_${moduleType}`;

                // Přeskočíme, pokud už máme tento modul z hlavní cesty
                if (moduleData[moduleId]) {
                  console.log(`Modul ${moduleType} již byl načten z hlavní cesty, přeskakuji alternativní`);
                  continue;
                }

                // Kontrola, zda modul obsahuje data
                const moduleDocData = moduleDoc.data();
                if (Object.keys(moduleDocData).length === 0) {
                  console.log(`Modul ${moduleType} neobsahuje žádná data, přeskakuji`);
                  continue;
                }

                console.log(`Načítám modul ${moduleType} z modules (alternativní cesta)`);

                // Přidáme informace o subjektu do dat modulu
                moduleDocData.subjectName = subjectData.name || '';
                moduleDocData.subjectType = subjectData.type || '';
                moduleDocData.subjectId = subjectId;

                moduleData[moduleId] = moduleDocData;
                console.log(`Modul ${moduleType} byl úspěšně načten z modules (alternativní cesta)`);
              }
            } catch (error) {
              console.error(`Chyba při načítání modulů pro subjekt ${subjectId}:`, error);
              // Pokračujeme s dalším subjektem
            }
          }
        } catch (error) {
          console.error(`Chyba při načítání subjektů pro případ ${caseId}:`, error);
          // Pokračujeme s prázdnými daty
        }
      }

      // Vypíšeme všechny načtené moduly
      console.log(`Celkem načteno ${Object.keys(moduleData).length} modulů`);
      for (const moduleId in moduleData) {
        // Extrahujeme typ modulu a ID subjektu z ID modulu
        let subjectId = '', moduleType = '';
        if (moduleId.includes('_')) {
          const parts = moduleId.split('_');
          if (parts.length >= 2) {
            subjectId = parts[0];
            moduleType = parts.slice(1).join('_');
          }
        }
        console.log(`- Modul ${moduleType} pro subjekt ${subjectId}`);
      }
    } catch (error) {
      console.error("Chyba při načítání dat pro report:", error);
      // Pokračujeme s prázdnými daty
    }

    // Generování reportu podle formátu
    let reportData: string | Buffer;

    try {
      // Kontrola, zda je formát podporovaný
      if (!['html', 'word', 'pdf'].includes(settings.format)) {
        settings.format = 'html'; // Výchozí formát
      }

      // Kontrola, zda máme nějaká data
      if (Object.keys(moduleData).length === 0) {
        console.warn("Varování: Žádná data modulů nebyla načtena pro report");
      } else {
        console.log(`Generuji report ve formátu ${settings.format} s ${Object.keys(moduleData).length} moduly`);
      }

      // Kontrola nastavení reportu
      console.log("Nastavení reportu:", {
        format: settings.format,
        title: settings.title,
        subject: settings.subject,
        reportType: reportType,
        selectedModules: settings.selectedModules?.length || 0,
        moduleOrder: settings.moduleOrder?.length || 0
      });

      // Generování reportu podle formátu
      switch (settings.format) {
        case 'html':
          reportData = generateHtmlReport(settings, moduleData, caseId);
          break;
        case 'word':
          reportData = generateWordReport(settings, moduleData, caseId);
          break;
        case 'pdf':
          reportData = await generatePDFReport(settings, moduleData, caseId);
          break;
        default:
          reportData = generateHtmlReport(settings, moduleData, caseId);
      }

      // Kontrola, zda byl report vygenerován
      if (!reportData) {
        throw new Error('Nepodařilo se vygenerovat report');
      }
    } catch (error) {
      console.error("Chyba při generování reportu:", error);

      // Vytvoříme jednoduchý HTML report s chybovou zprávou
      reportData = `
        <!DOCTYPE html>
        <html lang="cs">
        <head>
          <meta charset="UTF-8">
          <title>Chyba při generování reportu</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
            h1 { color: #d32f2f; }
            .container { max-width: 800px; margin: 0 auto; }
          </style>
        </head>
        <body>
          <div class="container">
            <h1>Chyba při generování reportu</h1>
            <p>Omlouváme se, ale při generování reportu došlo k chybě.</p>
            <p>Chybová zpráva: ${error instanceof Error ? error.message : 'Neznámá chyba'}</p>
            <p>Zkuste to prosím znovu nebo kontaktujte správce systému.</p>
          </div>
        </body>
        </html>
      `;

      // Pokračujeme s chybovým reportem místo vracení JSON chyby
    }

    const headers = new Headers();

    // Nastavení správného Content-Type podle formátu
    switch (settings.format) {
      case 'html':
        headers.set('Content-Type', 'text/html; charset=utf-8');
        headers.set('Content-Disposition', `attachment; filename="osint-report-${caseId}-${Date.now()}.html"`);
        break;
      case 'word':
        // Pro Word použijeme HTML s Word-specific styly
        headers.set('Content-Type', 'application/msword');
        headers.set('Content-Disposition', `attachment; filename="osint-report-${caseId}-${Date.now()}.doc"`);
        break;
      case 'pdf':
        // Pro PDF nastavíme správný Content-Type pro PDF soubor
        headers.set('Content-Type', 'application/pdf');
        headers.set('Content-Disposition', `attachment; filename="osint-report-${caseId}-${Date.now()}.pdf"`);
        break;
      default:
        return NextResponse.json(
          { error: 'Nepodporovaný formát' },
          { status: 400 }
        );
    }

    return new NextResponse(reportData, {
      status: 200,
      headers
    });

  } catch (error) {
    console.error('Chyba při generování reportu:', error);
    return NextResponse.json(
      { error: 'Chyba při generování reportu' },
      { status: 500 }
    );
  }
}

// Funkce pro generování PDF reportu
async function generatePDFReport(settings: any, moduleData: ModuleDataMap, caseId: string): Promise<Buffer> {
  try {
    console.log('Generuji PDF report...');

    // Vygenerujeme HTML obsah podle stylu
    let htmlContent: string;

    switch (settings.style) {
      case 'modern':
        htmlContent = generateModernPDFHtml(settings, moduleData, caseId);
        break;
      case 'analytical':
        htmlContent = generateAnalyticalPDFHtml(settings, moduleData, caseId);
        break;
      case 'official':
      default:
        htmlContent = generateOfficialPDFHtml(settings, moduleData, caseId);
        break;
    }

    console.log('HTML obsah vygenerován, velikost:', htmlContent.length, 'znaků');

    // Konverze HTML na PDF pomocí Puppeteer
    const puppeteer = require('puppeteer');

    console.log('Spouštím Puppeteer pro konverzi do PDF...');
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    const page = await browser.newPage();

    // Nastavení obsahu stránky
    await page.setContent(htmlContent, {
      waitUntil: 'networkidle0',
      timeout: 30000
    });

    // Generování PDF
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: '20mm',
        right: '15mm',
        bottom: '20mm',
        left: '15mm'
      }
    });

    await browser.close();

    console.log('PDF úspěšně vygenerováno, velikost:', pdfBuffer.length, 'bytů');
    return pdfBuffer;

  } catch (error) {
    console.error('Chyba při generování PDF:', error);
    throw new Error(`Chyba při generování PDF reportu: ${error instanceof Error ? error.message : 'Neznámá chyba'}`);
  }
}

// Pomocné funkce pro PDF generování
function getModuleTitle(moduleType: string): string {
  const moduleTitles: { [key: string]: string } = {
    // Základní moduly
    'evidence_obyvatel': 'Evidence obyvatel',
    'family_members': 'Rodinní příslušníci',
    'registr_ridicske_prukazy': 'Registr řidičských průkazů',
    'gun_license': 'Zbrojní průkazy',
    'vehicles': 'Vozidla',
    'locations': 'Lokace',
    'business_activity': 'Podnikatelská činnost',
    'cadastre': 'Katastr nemovitostí',
    'training': 'Školení a výcviky',

    // Komunikační moduly
    'email_analysis': 'Analýza emailů',
    'phone_numbers': 'Telefonní čísla',
    'phone_analysis': 'Analýza telefonů',

    // Síťové moduly
    'network_analysis': 'Síťová analýza',
    'map_overlays': 'Mapové vrstvy',

    // Sociální sítě
    'facebook': 'Facebook',
    'instagram': 'Instagram',
    'twitter': 'Twitter',
    'linkedin': 'LinkedIn',

    // Finanční monitoring
    'financial_monitoring': 'Finanční monitoring',

    // Starší názvy pro kompatibilitu
    'social_media': 'Sociální média',
    'email_search': 'Vyhledávání emailů',
    'phone_search': 'Vyhledávání telefonních čísel',
    'address_search': 'Vyhledávání adres',
    'business_search': 'Vyhledávání firem',
    'court_records': 'Soudní záznamy',
    'property_records': 'Majetkové záznamy',
    'education_records': 'Vzdělávací záznamy',
    'employment_records': 'Pracovní záznamy',
    'financial_records': 'Finanční záznamy',
    'criminal_records': 'Trestní záznamy',
    'traffic_violations': 'Dopravní přestupky',
    'bankruptcy_records': 'Záznamy o úpadku',
    'tax_records': 'Daňové záznamy',
    'insurance_records': 'Pojistné záznamy',
    'medical_records': 'Zdravotní záznamy',
    'travel_records': 'Cestovní záznamy'
  };

  return moduleTitles[moduleType] || moduleType.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function getDepartmentInfoWithoutInvestigator(department: string): string {
  // Odstranění řádku s vyšetřovatelem z department info
  const lines = department.split('\n');
  const filteredLines = lines.filter(line =>
    !line.toLowerCase().includes('vyšetřovatel') &&
    !line.toLowerCase().includes('investigator') &&
    line.trim() !== ''
  );
  return filteredLines.join('\n');
}

function extractInvestigatorName(department: string): string {
  // Extrakce jména vyšetřovatele z department info
  const lines = department.split('\n');
  const investigatorLine = lines.find(line =>
    line.toLowerCase().includes('vyšetřovatel') ||
    line.toLowerCase().includes('investigator')
  );

  if (investigatorLine) {
    // Odstranění prefixu "Vyšetřovatel:" a získání jména
    let name = investigatorLine.replace(/^.*vyšetřovatel:\s*/i, '').trim();

    // Přeuspořádání hodnosti a titulu - hodnost má být první
    // Příklad: "Bc. Václav Pospíšil por." -> "por. Bc. Václav Pospíšil"
    const rankPattern = /(.*?)\s+(por\.|npor\.|ppor\.|kpt\.|mjr\.|pplk\.|plk\.|brig\.|genmjr\.|genpor\.|gen\.)$/i;
    const match = name.match(rankPattern);

    if (match) {
      const nameWithTitle = match[1].trim(); // "Bc. Václav Pospíšil"
      const rank = match[2]; // "por."
      return `${rank} ${nameWithTitle}`;
    }

    return name;
  }

  return 'Neznámý vyšetřovatel';
}

// Zjednodušená funkce pro generování obsahu modulů pro PDF
function generateSimpleModuleContent(moduleType: string, data: any): string {
  console.log(`Generuji zjednodušený obsah pro modul: ${moduleType}`);
  console.log(`Data pro modul ${moduleType}:`, JSON.stringify(data, null, 2));

  if (!data) {
    console.log(`Žádná data pro modul ${moduleType}`);
    return '<p>Žádná data nejsou k dispozici.</p>';
  }

  // Generování jednoduchého obsahu podle typu modulu
  switch (moduleType) {
    case 'evidence_obyvatel':
      return generateSimpleEvidenceContent(data);
    case 'family_members':
      return generateSimpleFamilyMembersContent(data);
    case 'registr_ridicske_prukazy':
      return generateSimpleDriverLicenseContent(data);
    case 'gun_license':
      return generateSimpleGunLicenseContent(data);
    case 'vehicles':
      return generateSimpleVehiclesContent(data);
    case 'locations':
      return generateSimpleLocationsContent(data);
    case 'business_activity':
      return generateSimpleBusinessActivityContent(data);
    case 'cadastre':
      return generateSimpleCadastreContent(data);
    case 'training':
      return generateSimpleTrainingContent(data);
    case 'email_analysis':
      return generateSimpleEmailAnalysisContent(data);
    case 'phone_numbers':
      return generateSimplePhoneNumbersContent(data);
    case 'network_analysis':
      return generateSimpleNetworkAnalysisContent(data);
    case 'map_overlays':
      return generateSimpleMapOverlaysContent(data);
    case 'facebook':
      return generateSimpleFacebookContent(data);
    case 'instagram':
      return generateSimpleInstagramContent(data);
    case 'twitter':
      return generateSimpleTwitterContent(data);
    case 'linkedin':
      return generateSimpleLinkedinContent(data);
    case 'phone_analysis':
      return generateSimplePhoneAnalysisContent(data);
    case 'financial_monitoring':
      return generateSimpleFinancialMonitoringContent(data);
    default:
      return generateSimpleGenericContent(data);
  }
}

function generateSimpleEvidenceContent(data: any): string {
  console.log('Evidence obyvatel data:', JSON.stringify(data, null, 2));

  let html = '';

  // Data jsou přímo v objektu data, ne v data.personalInfo
  if (data && (data.fullName || data.dateOfBirth || data.personalIdNumber || data.nationality)) {

    // Základní osobní údaje
    html += '<h4 style="margin-top: 20px; margin-bottom: 10px; color: #2c3e50;">Základní osobní údaje</h4>';
    html += '<table class="info-table">';

    if (data.fullName) html += `<tr><td>Jméno a příjmení</td><td>${data.fullName}</td></tr>`;
    if (data.dateOfBirth) {
      // Převedeme datum z YYYY-MM-DD na DD-MM-YYYY
      const formattedDate = data.dateOfBirth.includes('-')
        ? data.dateOfBirth.split('-').reverse().join('-')
        : data.dateOfBirth;
      html += `<tr><td>Datum narození</td><td>${formattedDate}</td></tr>`;
    }
    if (data.personalIdNumber) html += `<tr><td>Rodné číslo</td><td>${data.personalIdNumber}</td></tr>`;
    if (data.nationality) html += `<tr><td>Státní občanství</td><td>${data.nationality}</td></tr>`;
    if (data.placeOfBirth) html += `<tr><td>Místo narození</td><td>${data.placeOfBirth}</td></tr>`;

    html += '</table>';

    // Adresní údaje
    if (data.permanentAddress) {
      html += '<h4 style="margin-top: 20px; margin-bottom: 10px; color: #2c3e50;">Adresní údaje</h4>';
      html += '<table class="info-table">';
      html += `<tr><td>Trvalé bydliště</td><td>${data.permanentAddress}</td></tr>`;
      html += '</table>';
    }

    // Doklady totožnosti
    if (data.idCardNumber || data.idCardIssuedBy || data.idCardValidity) {
      html += '<h4 style="margin-top: 20px; margin-bottom: 10px; color: #2c3e50;">Občanský průkaz</h4>';
      html += '<table class="info-table">';
      if (data.idCardNumber) html += `<tr><td>Číslo OP</td><td>${data.idCardNumber}</td></tr>`;
      if (data.idCardIssuedBy) html += `<tr><td>Vydal</td><td>${data.idCardIssuedBy}</td></tr>`;
      if (data.idCardValidity) html += `<tr><td>Platnost</td><td>${data.idCardValidity}</td></tr>`;
      html += '</table>';
    }

    // Přidání fyzických charakteristik pokud existují a jsou vyplněné
    if (data.physicalCharacteristics) {
      const pc = data.physicalCharacteristics;
      let physicalHtml = '';

      if (pc.height) physicalHtml += `<tr><td>Výška</td><td>${pc.height}</td></tr>`;
      if (pc.weight) physicalHtml += `<tr><td>Váha</td><td>${pc.weight}</td></tr>`;
      if (pc.eyeColor) physicalHtml += `<tr><td>Barva očí</td><td>${pc.eyeColor}</td></tr>`;
      if (pc.hairColor) physicalHtml += `<tr><td>Barva vlasů</td><td>${pc.hairColor}</td></tr>`;
      if (pc.build) physicalHtml += `<tr><td>Postava</td><td>${pc.build}</td></tr>`;
      if (pc.scarsMarks) physicalHtml += `<tr><td>Jizvy/znamínka</td><td>${pc.scarsMarks}</td></tr>`;
      if (pc.tattoos) physicalHtml += `<tr><td>Tetování</td><td>${pc.tattoos}</td></tr>`;
      if (pc.otherDistinguishing) physicalHtml += `<tr><td>Ostatní znaky</td><td>${pc.otherDistinguishing}</td></tr>`;

      if (physicalHtml) {
        html += `
          <h4>Fyzické charakteristiky</h4>
          <table class="info-table">
            ${physicalHtml}
          </table>
        `;
      }
    }

    // Kontaktní údaje
    if ((data.phoneNumbers && data.phoneNumbers.length > 0) || (data.emails && data.emails.length > 0)) {
      html += '<h4 style="margin-top: 20px; margin-bottom: 10px; color: #2c3e50;">Kontaktní údaje</h4>';
      html += '<table class="info-table">';

      if (data.phoneNumbers && data.phoneNumbers.length > 0) {
        data.phoneNumbers.forEach((phone: any, index: number) => {
          html += `<tr><td>Telefon ${index + 1}</td><td>${phone.value || phone.number || 'Neuvedeno'}</td></tr>`;
        });
      }

      if (data.emails && data.emails.length > 0) {
        data.emails.forEach((email: any, index: number) => {
          html += `<tr><td>Email ${index + 1}</td><td>${email.value || email.address || 'Neuvedeno'}</td></tr>`;
        });
      }

      html += '</table>';
    }

    // Přidání fotodokumentace
    if (data.photos && data.photos.length > 0) {
      html += generatePhotosSection(data.photos);
    }
  }

  return html || '<p>Žádné osobní údaje nejsou k dispozici.</p>';
}

function generateSimpleVehiclesContent(data: any): string {
  console.log('Vehicles data:', JSON.stringify(data, null, 2));
  let html = '';

  if (data.vehicles && Array.isArray(data.vehicles)) {
    data.vehicles.forEach((vehicle: any, index: number) => {
      html += `<h4 style="margin-top: 20px; margin-bottom: 10px; color: #2c3e50;">Vozidlo ${index + 1}</h4>`;
      html += '<table class="info-table">';

      // Zobrazujeme pouze vyplněná pole
      if (vehicle.make || vehicle.model) {
        const makeModel = `${vehicle.make || ''} ${vehicle.model || ''}`.trim();
        if (makeModel) html += `<tr><td>Značka a model</td><td>${makeModel}</td></tr>`;
      }
      if (vehicle.licensePlate) html += `<tr><td>SPZ</td><td>${vehicle.licensePlate}</td></tr>`;
      if (vehicle.yearManufactured) html += `<tr><td>Rok výroby</td><td>${vehicle.yearManufactured}</td></tr>`;
      if (vehicle.color) html += `<tr><td>Barva</td><td>${vehicle.color}</td></tr>`;
      if (vehicle.vin) html += `<tr><td>VIN</td><td>${vehicle.vin}</td></tr>`;
      if (vehicle.firstRegistered) html += `<tr><td>První registrace</td><td>${vehicle.firstRegistered}</td></tr>`;
      if (vehicle.stkValidUntil) html += `<tr><td>STK platnost</td><td>${vehicle.stkValidUntil}</td></tr>`;
      if (vehicle.relationshipType) {
        const relationshipText = vehicle.relationshipType === 'owner' ? 'Vlastník' :
                               vehicle.relationshipType === 'user' ? 'Uživatel' :
                               vehicle.relationshipType;
        html += `<tr><td>Vztah k vozidlu</td><td>${relationshipText}</td></tr>`;
      }
      if (vehicle.notes) html += `<tr><td>Poznámky</td><td>${vehicle.notes}</td></tr>`;

      html += '</table>';

      // Přidání fotodokumentace pro každé vozidlo
      if (vehicle.photos && vehicle.photos.length > 0) {
        console.log(`Generuji fotodokumentaci pro vozidlo ${index + 1}, počet fotek:`, vehicle.photos.length);
        vehicle.photos.forEach((photo: any, photoIndex: number) => {
          console.log(`Fotka vozidla ${index + 1}-${photoIndex + 1}:`, {
            downloadURL: photo.downloadURL,
            storagePath: photo.storagePath,
            description: photo.description,
            fileName: photo.fileName
          });
        });
        html += generatePhotosSection(vehicle.photos);
      } else {
        console.log(`Vozidlo ${index + 1} nemá žádné fotky nebo je pole prázdné`);
      }
    });
  }

  return html || '<p>Žádná vozidla nejsou k dispozici.</p>';
}

function generateSimpleGunLicenseContent(data: any): string {
  console.log('Gun license data:', JSON.stringify(data, null, 2));
  let html = '';

  if (data.licenses && Array.isArray(data.licenses)) {
    html += '<table class="info-table">';
    data.licenses.forEach((license: any, index: number) => {
      html += `
        <tr><td colspan="2"><strong>Zbrojní průkaz ${index + 1}</strong></td></tr>
        <tr><td>Číslo průkazu</td><td>${license.number || 'Neuvedeno'}</td></tr>
        <tr><td>Typ</td><td>${license.type || 'Neuvedeno'}</td></tr>
        <tr><td>Platnost do</td><td>${license.validUntil || 'Neuvedeno'}</td></tr>
        <tr><td>Stav</td><td>${license.status || 'Neuvedeno'}</td></tr>
      `;
    });
    html += '</table>';
  }

  // Přidání fotodokumentace
  if (data.photos && data.photos.length > 0) {
    html += generatePhotosSection(data.photos);
  }

  return html || '<p>Žádné zbrojní průkazy nejsou k dispozici.</p>';
}

function generateSimpleFamilyMembersContent(data: any): string {
  let html = '';

  if (data.familyMembers && Array.isArray(data.familyMembers)) {
    html += '<table class="info-table">';
    data.familyMembers.forEach((member: any, index: number) => {
      html += `
        <tr><td colspan="2"><strong>Rodinný příslušník ${index + 1}</strong></td></tr>
        <tr><td>Jméno a příjmení</td><td>${member.name || 'Neuvedeno'}</td></tr>
        <tr><td>Vztah</td><td>${member.relationship || 'Neuvedeno'}</td></tr>
        <tr><td>Datum narození</td><td>${member.birthDate || 'Neuvedeno'}</td></tr>
        <tr><td>Adresa</td><td>${member.address || 'Neuvedeno'}</td></tr>
      `;
    });
    html += '</table>';
  }

  return html || '<p>Žádní rodinní příslušníci nejsou k dispozici.</p>';
}

function generateSimpleDriverLicenseContent(data: any): string {
  let html = '';

  if (data.driverLicense) {
    html += `
      <table class="info-table">
        <tr><td>Číslo průkazu</td><td>${data.driverLicense.number || 'Neuvedeno'}</td></tr>
        <tr><td>Kategorie</td><td>${data.driverLicense.categories || 'Neuvedeno'}</td></tr>
        <tr><td>Platnost do</td><td>${data.driverLicense.validUntil || 'Neuvedeno'}</td></tr>
        <tr><td>Stav</td><td>${data.driverLicense.status || 'Neuvedeno'}</td></tr>
        <tr><td>Vydáno</td><td>${data.driverLicense.issuedDate || 'Neuvedeno'}</td></tr>
      </table>
    `;
  }

  return html || '<p>Žádný řidičský průkaz není k dispozici.</p>';
}

function generateSimpleLocationsContent(data: any): string {
  let html = '';

  if (data.locations && Array.isArray(data.locations)) {
    html += '<table class="info-table">';
    data.locations.forEach((location: any, index: number) => {
      html += `
        <tr><td colspan="2"><strong>Lokace ${index + 1}</strong></td></tr>
        <tr><td>Adresa</td><td>${location.address || 'Neuvedeno'}</td></tr>
        <tr><td>Typ</td><td>${location.type || 'Neuvedeno'}</td></tr>
        <tr><td>GPS souřadnice</td><td>${location.coordinates || 'Neuvedeno'}</td></tr>
        <tr><td>Poznámka</td><td>${location.note || 'Neuvedeno'}</td></tr>
      `;
    });
    html += '</table>';
  }

  return html || '<p>Žádné lokace nejsou k dispozici.</p>';
}

function generateSimpleBusinessActivityContent(data: any): string {
  let html = '';

  if (data.business) {
    html += `
      <table class="info-table">
        <tr><td>Název firmy</td><td>${data.business.name || 'Neuvedeno'}</td></tr>
        <tr><td>IČO</td><td>${data.business.ico || 'Neuvedeno'}</td></tr>
        <tr><td>DIČ</td><td>${data.business.dic || 'Neuvedeno'}</td></tr>
        <tr><td>Právní forma</td><td>${data.business.legalForm || 'Neuvedeno'}</td></tr>
        <tr><td>Sídlo</td><td>${data.business.address || 'Neuvedeno'}</td></tr>
        <tr><td>Předmět podnikání</td><td>${data.business.activity || 'Neuvedeno'}</td></tr>
        <tr><td>Stav</td><td>${data.business.status || 'Neuvedeno'}</td></tr>
      </table>
    `;
  }

  return html || '<p>Žádná podnikatelská činnost není k dispozici.</p>';
}

function generateSimpleCadastreContent(data: any): string {
  let html = '';

  if (data.properties && Array.isArray(data.properties)) {
    html += '<table class="info-table">';
    data.properties.forEach((property: any, index: number) => {
      html += `
        <tr><td colspan="2"><strong>Nemovitost ${index + 1}</strong></td></tr>
        <tr><td>Číslo parcely</td><td>${property.parcelNumber || 'Neuvedeno'}</td></tr>
        <tr><td>Katastrální území</td><td>${property.cadastralArea || 'Neuvedeno'}</td></tr>
        <tr><td>Typ nemovitosti</td><td>${property.type || 'Neuvedeno'}</td></tr>
        <tr><td>Výměra</td><td>${property.area || 'Neuvedeno'}</td></tr>
        <tr><td>Vlastník</td><td>${property.owner || 'Neuvedeno'}</td></tr>
      `;
    });
    html += '</table>';
  }

  return html || '<p>Žádné nemovitosti nejsou k dispozici.</p>';
}

function generateSimpleGenericContent(data: any): string {
  let html = '<table class="info-table">';

  function processValue(value: any): string {
    if (value === null || value === undefined) return 'Neuvedeno';
    if (typeof value === 'boolean') return value ? 'Ano' : 'Ne';
    if (Array.isArray(value)) return value.length > 0 ? `${value.length} položek` : 'Žádné položky';
    if (typeof value === 'object') return 'Objekt s daty';
    return String(value);
  }

  function formatKey(key: string): string {
    return key
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .replace(/_/g, ' ');
  }

  for (const [key, value] of Object.entries(data)) {
    if (key !== 'id' && key !== 'createdAt' && key !== 'updatedAt') {
      html += `<tr><td>${formatKey(key)}</td><td>${processValue(value)}</td></tr>`;
    }
  }

  html += '</table>';
  return html;
}

function generateSimpleTrainingContent(data: any): string {
  let html = '';

  if (data.trainings && Array.isArray(data.trainings)) {
    html += '<table class="info-table">';
    data.trainings.forEach((training: any, index: number) => {
      html += `
        <tr><td colspan="2"><strong>Školení ${index + 1}</strong></td></tr>
        <tr><td>Název</td><td>${training.name || 'Neuvedeno'}</td></tr>
        <tr><td>Typ</td><td>${training.type || 'Neuvedeno'}</td></tr>
        <tr><td>Datum</td><td>${training.date || 'Neuvedeno'}</td></tr>
        <tr><td>Organizátor</td><td>${training.organizer || 'Neuvedeno'}</td></tr>
        <tr><td>Certifikát</td><td>${training.certificate || 'Neuvedeno'}</td></tr>
      `;
    });
    html += '</table>';
  }

  return html || '<p>Žádná školení nejsou k dispozici.</p>';
}

function generateSimpleEmailAnalysisContent(data: any): string {
  let html = '';

  if (data.emails && Array.isArray(data.emails)) {
    html += '<table class="info-table">';
    data.emails.forEach((email: any, index: number) => {
      html += `
        <tr><td colspan="2"><strong>Email ${index + 1}</strong></td></tr>
        <tr><td>Emailová adresa</td><td>${email.address || 'Neuvedeno'}</td></tr>
        <tr><td>Typ</td><td>${email.type || 'Neuvedeno'}</td></tr>
        <tr><td>Platnost</td><td>${email.valid ? 'Platný' : 'Neplatný'}</td></tr>
        <tr><td>Poskytovatel</td><td>${email.provider || 'Neuvedeno'}</td></tr>
        <tr><td>Nalezeno v únicích</td><td>${email.breaches ? email.breaches.join(', ') : 'Ne'}</td></tr>
      `;
    });
    html += '</table>';
  }

  return html || '<p>Žádné emailové adresy nejsou k dispozici.</p>';
}

function generateSimplePhoneNumbersContent(data: any): string {
  let html = '';

  if (data.phoneNumbers && Array.isArray(data.phoneNumbers)) {
    html += '<table class="info-table">';
    data.phoneNumbers.forEach((phone: any, index: number) => {
      html += `
        <tr><td colspan="2"><strong>Telefon ${index + 1}</strong></td></tr>
        <tr><td>Číslo</td><td>${phone.number || 'Neuvedeno'}</td></tr>
        <tr><td>Typ</td><td>${phone.type || 'Neuvedeno'}</td></tr>
        <tr><td>Operátor</td><td>${phone.carrier || 'Neuvedeno'}</td></tr>
        <tr><td>Země</td><td>${phone.country || 'Neuvedeno'}</td></tr>
        <tr><td>Platnost</td><td>${phone.valid ? 'Platný' : 'Neplatný'}</td></tr>
      `;
    });
    html += '</table>';
  }

  return html || '<p>Žádná telefonní čísla nejsou k dispozici.</p>';
}

function generateSimpleNetworkAnalysisContent(data: any): string {
  let html = '';

  if (data.networkData) {
    html += `
      <table class="info-table">
        <tr><td>IP adresa</td><td>${data.networkData.ipAddress || 'Neuvedeno'}</td></tr>
        <tr><td>Poskytovatel</td><td>${data.networkData.isp || 'Neuvedeno'}</td></tr>
        <tr><td>Země</td><td>${data.networkData.country || 'Neuvedeno'}</td></tr>
        <tr><td>Město</td><td>${data.networkData.city || 'Neuvedeno'}</td></tr>
        <tr><td>Organizace</td><td>${data.networkData.organization || 'Neuvedeno'}</td></tr>
        <tr><td>Typ připojení</td><td>${data.networkData.connectionType || 'Neuvedeno'}</td></tr>
      </table>
    `;
  }

  return html || '<p>Žádná síťová data nejsou k dispozici.</p>';
}

function generateSimpleMapOverlaysContent(data: any): string {
  let html = '';

  if (data.mapData && Array.isArray(data.mapData)) {
    html += '<table class="info-table">';
    data.mapData.forEach((location: any, index: number) => {
      html += `
        <tr><td colspan="2"><strong>Mapová lokace ${index + 1}</strong></td></tr>
        <tr><td>Název</td><td>${location.name || 'Neuvedeno'}</td></tr>
        <tr><td>Souřadnice</td><td>${location.coordinates || 'Neuvedeno'}</td></tr>
        <tr><td>Typ</td><td>${location.type || 'Neuvedeno'}</td></tr>
        <tr><td>Popis</td><td>${location.description || 'Neuvedeno'}</td></tr>
      `;
    });
    html += '</table>';
  }

  return html || '<p>Žádné mapové překryvy nejsou k dispozici.</p>';
}

function generateSimpleFacebookContent(data: any): string {
  let html = '';

  if (data.profile) {
    html += `
      <table class="info-table">
        <tr><td>Jméno profilu</td><td>${data.profile.name || 'Neuvedeno'}</td></tr>
        <tr><td>URL profilu</td><td>${data.profile.url || 'Neuvedeno'}</td></tr>
        <tr><td>ID profilu</td><td>${data.profile.id || 'Neuvedeno'}</td></tr>
        <tr><td>Počet přátel</td><td>${data.profile.friendsCount || 'Neuvedeno'}</td></tr>
        <tr><td>Místo bydliště</td><td>${data.profile.location || 'Neuvedeno'}</td></tr>
        <tr><td>Zaměstnání</td><td>${data.profile.work || 'Neuvedeno'}</td></tr>
        <tr><td>Vzdělání</td><td>${data.profile.education || 'Neuvedeno'}</td></tr>
        <tr><td>Stav vztahu</td><td>${data.profile.relationshipStatus || 'Neuvedeno'}</td></tr>
      </table>
    `;
  }

  return html || '<p>Žádný Facebook profil není k dispozici.</p>';
}

function generateSimpleInstagramContent(data: any): string {
  let html = '';

  if (data.profile) {
    html += `
      <table class="info-table">
        <tr><td>Uživatelské jméno</td><td>${data.profile.username || 'Neuvedeno'}</td></tr>
        <tr><td>Celé jméno</td><td>${data.profile.fullName || 'Neuvedeno'}</td></tr>
        <tr><td>Bio</td><td>${data.profile.bio || 'Neuvedeno'}</td></tr>
        <tr><td>Počet sledujících</td><td>${data.profile.followersCount || 'Neuvedeno'}</td></tr>
        <tr><td>Počet sledovaných</td><td>${data.profile.followingCount || 'Neuvedeno'}</td></tr>
        <tr><td>Počet příspěvků</td><td>${data.profile.postsCount || 'Neuvedeno'}</td></tr>
        <tr><td>Ověřený účet</td><td>${data.profile.verified ? 'Ano' : 'Ne'}</td></tr>
        <tr><td>Soukromý účet</td><td>${data.profile.private ? 'Ano' : 'Ne'}</td></tr>
      </table>
    `;
  }

  return html || '<p>Žádný Instagram profil není k dispozici.</p>';
}

function generateSimpleTwitterContent(data: any): string {
  let html = '';

  if (data.profile) {
    html += `
      <table class="info-table">
        <tr><td>Uživatelské jméno</td><td>${data.profile.username || 'Neuvedeno'}</td></tr>
        <tr><td>Zobrazované jméno</td><td>${data.profile.displayName || 'Neuvedeno'}</td></tr>
        <tr><td>Bio</td><td>${data.profile.bio || 'Neuvedeno'}</td></tr>
        <tr><td>Počet sledujících</td><td>${data.profile.followersCount || 'Neuvedeno'}</td></tr>
        <tr><td>Počet sledovaných</td><td>${data.profile.followingCount || 'Neuvedeno'}</td></tr>
        <tr><td>Počet tweetů</td><td>${data.profile.tweetsCount || 'Neuvedeno'}</td></tr>
        <tr><td>Lokace</td><td>${data.profile.location || 'Neuvedeno'}</td></tr>
        <tr><td>Ověřený účet</td><td>${data.profile.verified ? 'Ano' : 'Ne'}</td></tr>
      </table>
    `;
  }

  return html || '<p>Žádný Twitter profil není k dispozici.</p>';
}

function generateSimpleLinkedinContent(data: any): string {
  let html = '';

  if (data.profile) {
    html += `
      <table class="info-table">
        <tr><td>Jméno</td><td>${data.profile.name || 'Neuvedeno'}</td></tr>
        <tr><td>Pozice</td><td>${data.profile.position || 'Neuvedeno'}</td></tr>
        <tr><td>Společnost</td><td>${data.profile.company || 'Neuvedeno'}</td></tr>
        <tr><td>Lokace</td><td>${data.profile.location || 'Neuvedeno'}</td></tr>
        <tr><td>Počet kontaktů</td><td>${data.profile.connectionsCount || 'Neuvedeno'}</td></tr>
        <tr><td>Vzdělání</td><td>${data.profile.education || 'Neuvedeno'}</td></tr>
        <tr><td>Zkušenosti</td><td>${data.profile.experience || 'Neuvedeno'}</td></tr>
      </table>
    `;
  }

  return html || '<p>Žádný LinkedIn profil není k dispozici.</p>';
}

function generateSimplePhoneAnalysisContent(data: any): string {
  let html = '';

  if (data.analysis) {
    html += `
      <table class="info-table">
        <tr><td>Analyzované číslo</td><td>${data.analysis.number || 'Neuvedeno'}</td></tr>
        <tr><td>Formát</td><td>${data.analysis.format || 'Neuvedeno'}</td></tr>
        <tr><td>Typ čísla</td><td>${data.analysis.type || 'Neuvedeno'}</td></tr>
        <tr><td>Operátor</td><td>${data.analysis.carrier || 'Neuvedeno'}</td></tr>
        <tr><td>Země původu</td><td>${data.analysis.country || 'Neuvedeno'}</td></tr>
        <tr><td>Časové pásmo</td><td>${data.analysis.timezone || 'Neuvedeno'}</td></tr>
        <tr><td>Rizikové hodnocení</td><td>${data.analysis.riskScore || 'Neuvedeno'}</td></tr>
      </table>
    `;
  }

  return html || '<p>Žádná analýza telefonu není k dispozici.</p>';
}

function generateSimpleFinancialMonitoringContent(data: any): string {
  let html = '';

  if (data.financial) {
    html += `
      <table class="info-table">
        <tr><td>Bankovní účty</td><td>${data.financial.bankAccounts || 'Neuvedeno'}</td></tr>
        <tr><td>Kreditní historie</td><td>${data.financial.creditHistory || 'Neuvedeno'}</td></tr>
        <tr><td>Dlužné částky</td><td>${data.financial.debts || 'Neuvedeno'}</td></tr>
        <tr><td>Investice</td><td>${data.financial.investments || 'Neuvedeno'}</td></tr>
        <tr><td>Příjmy</td><td>${data.financial.income || 'Neuvedeno'}</td></tr>
        <tr><td>Výdaje</td><td>${data.financial.expenses || 'Neuvedeno'}</td></tr>
        <tr><td>Rizikové transakce</td><td>${data.financial.suspiciousTransactions || 'Neuvedeno'}</td></tr>
      </table>
    `;
  }

  return html || '<p>Žádné finanční údaje nejsou k dispozici.</p>';
}

// Funkce pro generování fotodokumentace
function generatePhotosSection(photos: any[]): string {
  if (!photos || photos.length === 0) {
    return '';
  }

  console.log('Generuji fotodokumentaci, počet fotek:', photos.length);
  photos.forEach((photo, index) => {
    console.log(`Fotka ${index + 1}:`, {
      downloadURL: photo.downloadURL,
      storagePath: photo.storagePath,
      description: photo.description,
      fileName: photo.fileName
    });
  });

  let html = `
    <h4 style="margin-top: 25px; margin-bottom: 15px; color: #2c3e50; border-bottom: 1px solid #ddd; padding-bottom: 5px; page-break-inside: avoid;">Fotodokumentace</h4>
  `;

  photos.forEach((photo: any, index: number) => {
    if (photo.downloadURL || photo.storagePath) {
      const imageUrl = photo.downloadURL || photo.storagePath;
      // Převedeme relativní cestu na absolutní URL pro PDF
      const fullImageUrl = imageUrl.startsWith('http') ? imageUrl : `http://localhost:3000${imageUrl}`;

      html += `
        <div style="page-break-inside: avoid; margin: 20px 0; text-align: center; border: 2px solid #ddd; padding: 20px; border-radius: 8px; background: #f9f9f9;">
          <img src="${fullImageUrl}"
               alt="Fotodokumentace ${index + 1}"
               style="max-width: 90%; max-height: 350px; width: auto; height: auto; object-fit: contain; margin-bottom: 15px; border: 2px solid #ccc; border-radius: 6px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);"
               onerror="this.style.display='none'; this.nextElementSibling.style.display='block';" />
          <div style="display: none; color: #999; font-style: italic; font-size: 12pt;">Obrázek se nepodařilo načíst</div>
          <div style="font-size: 12pt; color: #333; font-weight: 600; margin-bottom: 8px; line-height: 1.4;">
            ${photo.description || photo.fileName || `Fotografie ${index + 1}`}
          </div>
          ${photo.dateTaken ? `<div style="font-size: 10pt; color: #666; font-style: italic;">${photo.dateTaken}</div>` : ''}
        </div>
      `;
    }
  });
  return html;
}

// Funkce pro generování oficiálního PDF HTML
function generateOfficialPDFHtml(settings: any, moduleData: ModuleDataMap, caseId: string): string {
  const currentDate = new Date().toLocaleDateString('cs-CZ', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });

  // Generování obsahu modulů
  let modulesHtml = '';

  try {
    // Seskupení modulů podle subjektů
    const subjectModules: { [subjectId: string]: Array<{ moduleType: string; data: any; subjectId: string }> } = {};

    for (const [moduleKey, data] of Object.entries(moduleData)) {
      const parts = moduleKey.split('_');
      if (parts.length >= 2) {
        const subjectId = parts[0];
        const moduleType = parts.slice(1).join('_');

        if (!subjectModules[subjectId]) {
          subjectModules[subjectId] = [];
        }

        subjectModules[subjectId].push({
          moduleType,
          data,
          subjectId
        });
      }
    }

    // Generování obsahu pro každý subjekt s lepším page-break handling
    let moduleIndex = 0;
    for (const [subjectId, modules] of Object.entries(subjectModules)) {
      for (const { moduleType, data, subjectId: moduleSubjectId } of modules) {
        const moduleTitle = getModuleTitle(moduleType);
        const moduleContent = generateSimpleModuleContent(moduleType, data);

        // Přidáme page-break před každý modul kromě prvního
        const pageBreak = moduleIndex > 0 ? '<div style="page-break-before: always;"></div>' : '';

        modulesHtml += `
          ${pageBreak}
          <div class="module" style="page-break-inside: avoid;">
            <h3 style="page-break-after: avoid; margin-bottom: 15px;">${moduleTitle}</h3>
            <div style="page-break-inside: avoid;">
              ${moduleContent}
            </div>
          </div>
        `;
        moduleIndex++;
      }
    }

  } catch (error) {
    console.error("Chyba při generování obsahu modulů:", error);
    modulesHtml = `
      <div class="module">
        <h3>Chyba při generování obsahu</h3>
        <p class="error">Při generování obsahu modulů došlo k chybě: ${error instanceof Error ? error.message : 'Neznámá chyba'}</p>
        <p>Zkuste to prosím znovu nebo kontaktujte správce systému.</p>
      </div>
    `;
  }

  return `<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${settings.title}</title>
    <style>
        @page {
            size: A4;
            margin: 20mm;
            @top-left {
                content: counter(page) ". strana";
                font-family: 'Times New Roman', serif;
                font-size: 12pt;
                font-weight: bold;
                margin-top: -8mm;
                border-bottom: 2px solid #000000;
                padding-bottom: 8px;
                margin-bottom: 10px;
            }
        }

        @page:first {
            @top-left {
                content: "";
            }
        }

        body {
            font-family: 'Times New Roman', serif;
            font-size: 12pt;
            line-height: 1.4;
            color: #000000;
            margin: 0;
            padding: 0;
            background: white;
        }

        .document-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            min-height: 297mm;
        }

        .header {
            margin-bottom: 30px;
        }

        .police-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 20px;
            font-weight: bold;
            font-size: 11pt;
        }

        .police-left {
            text-align: left;
            line-height: 1.2;
        }

        .police-right {
            text-align: right;
            font-size: 10pt;
        }

        .department-info {
            text-align: left;
            margin: 20px 0;
            font-size: 11pt;
            line-height: 1.3;
            white-space: pre-line;
        }

        .document-info {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin: 20px 0;
            font-size: 11pt;
        }

        .document-title {
            text-align: center;
            font-size: 16pt;
            font-weight: bold;
            margin: 40px 0 30px 0;
            text-transform: uppercase;
        }

        .subject-name {
            text-align: center;
            font-size: 14pt;
            font-weight: bold;
            margin: 20px 0;
        }

        .protocol-section {
            margin: 30px 0;
        }

        .protocol-title {
            font-weight: bold;
            margin-bottom: 10px;
            font-size: 12pt;
        }

        .protocol-text {
            text-align: justify;
            line-height: 1.5;
            margin-bottom: 15px;
        }

        .warning-section {
            margin: 40px 0;
            padding: 15px;
            border: 1px solid #666666;
            background-color: #f9f9f9;
        }

        .warning-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #d63031;
        }

        .module {
            margin: 30px 0;
            page-break-inside: avoid;
        }

        .module h3 {
            color: #000000;
            font-size: 14pt;
            margin-bottom: 15px;
            font-weight: bold;
            border-bottom: 2px solid #000000;
            padding-bottom: 5px;
        }

        .info-table, .personal-info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 11pt;
        }

        .info-table td, .personal-info-table td {
            padding: 8px 12px;
            border: 1px solid #666666;
            vertical-align: top;
        }

        .info-table td:first-child, .personal-info-table td:first-child {
            font-weight: bold;
            background-color: #f5f5f5;
            width: 30%;
        }

        .signature-block {
            margin-top: 50px;
            text-align: right;
            page-break-inside: avoid;
        }

        .signature-content {
            display: inline-block;
            text-align: left;
        }

        .signature-title {
            font-size: 11pt;
            color: #000000;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .signature-name {
            font-size: 11pt;
            font-weight: normal;
            color: #000000;
        }

        .page-break {
            page-break-before: always;
        }

        ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        li {
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="document-container">
        <!-- OFICIÁLNÍ PRVNÍ STRÁNKA -->
        <div class="header">
            <div class="police-header">
                <div class="police-left">
                    POLICIE ČESKÉ REPUBLIKY<br>
                    Krajské ředitelství policie Pardubického kraje
                </div>
                <div class="police-right">
                    JID: ${settings.jid || ''}
                </div>
            </div>
            <div class="department-info">
                ${getDepartmentInfoWithoutInvestigator(settings.department)}
            </div>
            <div class="document-info">
                <div class="left-info">
                    <div><strong>Č. j. ${settings.documentNumber || ''}</strong></div>
                </div>
                <div class="right-info">
                    <div>${settings.location} ${settings.date || currentDate}</div>
                    <div style="margin-top: 5px; font-size: 10pt;">Počet stran: <span id="page-count">__</span></div>
                </div>
            </div>
        </div>

        <div class="document-title">
            ${settings.title}
        </div>

        <div class="subject-name">
            ${settings.subject || 'Jméno, příjmení, datum narození'}
        </div>

        <div class="protocol-section">
            <div class="protocol-title">Cíl protokolu:</div>
            <div class="protocol-text">
                ${settings.purpose}
            </div>
        </div>

        <!-- Důležité upozornění na konec první stránky -->
        <div style="position: absolute; bottom: 40mm; left: 0; right: 0;">
            <div class="warning-section">
                <div class="warning-title">Důležité upozornění:</div>
                <div class="protocol-text">
                    Tento OSINT průzkum využívá kombinovaný přístup zahrnující:
                    <ul>
                        <li>Systematické vyhledávání v interních informačních systémech Policie ČR</li>
                        <li>Komplexní prověření veškerých dostupných zdrojů v otevřeném prostoru Internet</li>
                    </ul>
                    Průzkum klade důraz na získání maximálního množství relevantních informací z otevřených zdrojů při současném zachování operativní bezpečnosti a eliminaci možnosti odhalení zájmu o sledovanou osobu.
                </div>
            </div>
        </div>

        <!-- MODULY -->
        ${modulesHtml}

        <!-- Podpisová doložka na konci dokumentu -->
        <div class="signature-block">
            <div class="signature-content">
                <div class="signature-title">Zpracoval:</div>
                <div class="signature-name">${extractInvestigatorName(settings.department)}</div>
            </div>
        </div>
    </div>
    <script>
        // Automatické vyplnění počtu stran
        window.addEventListener('load', function() {
            setTimeout(function() {
                const pageCountElements = document.querySelectorAll('#page-count');
                // Odhad počtu stran na základě výšky obsahu
                const contentHeight = document.body.scrollHeight;
                const pageHeight = 842; // A4 výška v pixelech při 96 DPI
                const totalPages = Math.max(1, Math.ceil(contentHeight / pageHeight));

                pageCountElements.forEach(element => {
                    element.textContent = totalPages.toString();
                });
            }, 200);
        });
    </script>
</body>
</html>`;
}

// Funkce pro generování moderního PDF HTML
function generateModernPDFHtml(settings: any, moduleData: ModuleDataMap, caseId: string): string {
  const currentDate = new Date().toLocaleDateString('cs-CZ', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });

  // Generování obsahu modulů
  let modulesHtml = '';

  try {
    // Seskupení modulů podle subjektů
    const subjectModules: { [subjectId: string]: Array<{ moduleType: string; data: any; subjectId: string }> } = {};

    for (const [moduleKey, data] of Object.entries(moduleData)) {
      const parts = moduleKey.split('_');
      if (parts.length >= 2) {
        const subjectId = parts[0];
        const moduleType = parts.slice(1).join('_');

        if (!subjectModules[subjectId]) {
          subjectModules[subjectId] = [];
        }

        subjectModules[subjectId].push({
          moduleType,
          data,
          subjectId
        });
      }
    }

    // Generování obsahu pro každý subjekt
    for (const [subjectId, modules] of Object.entries(subjectModules)) {
      for (const { moduleType, data, subjectId: moduleSubjectId } of modules) {
        const moduleTitle = getModuleTitle(moduleType);
        const moduleContent = generateSimpleModuleContent(moduleType, data);

        modulesHtml += `
          <div class="module">
            <div class="module-header">${moduleTitle}</div>
            <div class="module-content">${moduleContent}</div>
          </div>
        `;
      }
    }

  } catch (error) {
    console.error("Chyba při generování obsahu modulů:", error);
    modulesHtml = `
      <div class="module">
        <div class="module-header">Chyba při generování obsahu</div>
        <div class="module-content">
          <p class="error">Při generování obsahu modulů došlo k chybě: ${error instanceof Error ? error.message : 'Neznámá chyba'}</p>
          <p>Zkuste to prosím znovu nebo kontaktujte správce systému.</p>
        </div>
      </div>
    `;
  }

  return `<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${settings.title}</title>
    <style>
        @page {
            size: A4;
            margin: 15mm;
            @top-left {
                content: counter(page) ". strana";
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                font-size: 12pt;
                font-weight: bold;
                color: #2c3e50;
                margin-top: -8mm;
                border-bottom: 2px solid #2c3e50;
                padding-bottom: 8px;
                margin-bottom: 10px;
            }
        }

        @page:first {
            @top-left {
                content: "";
            }
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 11pt;
            line-height: 1.5;
            color: #2c3e50;
            margin: 0;
            padding: 0;
            background: white;
        }

        .document-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
        }

        .modern-header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 25px;
            margin-bottom: 30px;
            border-radius: 8px;
        }

        .modern-header h1 {
            margin: 0 0 10px 0;
            font-size: 24pt;
            font-weight: 300;
            text-align: center;
        }

        .modern-header .subtitle {
            text-align: center;
            font-size: 14pt;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .header-info {
            display: flex;
            justify-content: space-between;
            font-size: 10pt;
            opacity: 0.8;
        }

        .info-cards {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-card {
            background: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 20px;
            border-radius: 0 8px 8px 0;
        }

        .info-card h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 14pt;
            font-weight: 600;
        }

        .info-card p {
            margin: 5px 0;
            font-size: 10pt;
            line-height: 1.4;
        }

        .module {
            margin: 25px 0;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
            page-break-inside: avoid;
        }

        .module-header {
            background: linear-gradient(90deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 15px 20px;
            font-size: 14pt;
            font-weight: 600;
        }

        .module-content {
            padding: 20px;
        }

        .info-table, .personal-info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 10pt;
            background: white;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .info-table td, .personal-info-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e9ecef;
            vertical-align: top;
        }

        .info-table td:first-child, .personal-info-table td:first-child {
            font-weight: 600;
            background-color: #f8f9fa;
            color: #495057;
            width: 35%;
            border-right: 1px solid #e9ecef;
        }

        .info-table tr:last-child td, .personal-info-table tr:last-child td {
            border-bottom: none;
        }

        .signature-section {
            margin-top: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            text-align: right;
        }

        .signature-title {
            font-size: 12pt;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .signature-name {
            font-size: 11pt;
            color: #495057;
        }

        .page-break {
            page-break-before: always;
        }

        .page-header-modern {
            text-align: left;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #2c3e50;
            font-weight: bold;
            color: #2c3e50;
        }

        .module-section {
            page-break-inside: avoid;
            margin-bottom: 30px;
        }

        .module-section h3 {
            page-break-after: avoid;
            margin-bottom: 15px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8e6cf 0%, #dcedc1 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }

        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .warning-box h4 {
            color: #856404;
            margin: 0 0 10px 0;
            font-size: 12pt;
        }

        .warning-box p {
            color: #856404;
            margin: 0;
            font-size: 10pt;
        }
    </style>
</head>
<body>
    <div class="document-container">
        <!-- Hlavička s adresou policie -->
        <div style="margin-bottom: 20px; font-size: 10pt; color: #2c3e50;">
            <div style="text-align: left;">
                <div style="font-weight: bold; margin-bottom: 5px;">POLICIE ČESKÉ REPUBLIKY</div>
                <div style="font-weight: bold; margin-bottom: 10px;">Krajské ředitelství policie Pardubického kraje</div>
                <div>${getDepartmentInfoWithoutInvestigator(settings.department)}</div>
                <div style="margin-top: 10px; display: flex; justify-content: space-between;">
                    <div><strong>Č. j. ${settings.documentNumber || ''}</strong></div>
                    <div><strong>JID: ${settings.jid || ''}</strong></div>
                </div>
            </div>
        </div>

        <div class="modern-header">
            <h1>${settings.title}</h1>
            <div class="subtitle">${settings.subject || 'Analýza subjektu'}</div>
            <div class="header-info">
                <div>${settings.location} ${settings.date || currentDate}</div>
                <div style="font-size: 9pt; opacity: 0.8;">Počet stran: <span id="page-count">__</span></div>
            </div>
        </div>

        <div class="info-cards">
            <div class="info-card">
                <h3>Cíl analýzy</h3>
                <p>${settings.purpose}</p>
            </div>
            <div class="info-card">
                <h3>Identifikace</h3>
                <p><strong>JID:</strong> ${settings.jid || 'Neuvedeno'}</p>
                <p><strong>Č.j.:</strong> ${settings.documentNumber || 'Neuvedeno'}</p>
                <p><strong>Datum:</strong> ${settings.date || currentDate}</p>
                <p><strong>Místo:</strong> ${settings.location}</p>
            </div>
        </div>

        <div class="warning-box">
            <h4>Metodika OSINT analýzy</h4>
            <p>Analýza využívá kombinovaný přístup zahrnující systematické vyhledávání v interních informačních systémech a komplexní prověření dostupných zdrojů v otevřeném prostoru internetu při zachování operativní bezpečnosti.</p>
        </div>

        <!-- Moduly -->
        ${modulesHtml}

        <!-- Bez podpisu v moderním stylu -->
    </div>
    <script>
        // Automatické vyplnění počtu stran
        window.addEventListener('load', function() {
            setTimeout(function() {
                const pageCountElements = document.querySelectorAll('#page-count');
                // Odhad počtu stran na základě výšky obsahu
                const contentHeight = document.body.scrollHeight;
                const pageHeight = 842; // A4 výška v pixelech při 96 DPI
                const totalPages = Math.max(1, Math.ceil(contentHeight / pageHeight));

                pageCountElements.forEach(element => {
                    element.textContent = totalPages.toString();
                });
            }, 200);
        });
    </script>
</body>
</html>`;
}

// Funkce pro generování analytického PDF HTML
function generateAnalyticalPDFHtml(settings: any, moduleData: ModuleDataMap, caseId: string): string {
  const currentDate = new Date().toLocaleDateString('cs-CZ', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });

  // Generování obsahu modulů
  let modulesHtml = '';

  try {
    // Seskupení modulů podle subjektů
    const subjectModules: { [subjectId: string]: Array<{ moduleType: string; data: any; subjectId: string }> } = {};

    for (const [moduleKey, data] of Object.entries(moduleData)) {
      const parts = moduleKey.split('_');
      if (parts.length >= 2) {
        const subjectId = parts[0];
        const moduleType = parts.slice(1).join('_');

        if (!subjectModules[subjectId]) {
          subjectModules[subjectId] = [];
        }

        subjectModules[subjectId].push({
          moduleType,
          data,
          subjectId
        });
      }
    }

    // Generování obsahu pro každý subjekt
    for (const [subjectId, modules] of Object.entries(subjectModules)) {
      for (const { moduleType, data, subjectId: moduleSubjectId } of modules) {
        const moduleTitle = getModuleTitle(moduleType);
        const moduleContent = generateSimpleModuleContent(moduleType, data);

        modulesHtml += `
          <div class="module">
            <div class="module-header">${moduleTitle}</div>
            <div class="module-content">${moduleContent}</div>
          </div>
        `;
      }
    }

  } catch (error) {
    console.error("Chyba při generování obsahu modulů:", error);
    modulesHtml = `
      <div class="module">
        <div class="module-header">Chyba při generování obsahu</div>
        <div class="module-content">
          <p class="error">Při generování obsahu modulů došlo k chybě: ${error instanceof Error ? error.message : 'Neznámá chyba'}</p>
          <p>Zkuste to prosím znovu nebo kontaktujte správce systému.</p>
        </div>
      </div>
    `;
  }

  return `<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${settings.title}</title>
    <style>
        @page {
            size: A4;
            margin: 20mm;
        }

        body {
            font-family: 'Calibri', 'Arial', sans-serif;
            font-size: 11pt;
            line-height: 1.6;
            color: #333333;
            margin: 0;
            padding: 0;
            background: white;
        }

        .document-container {
            max-width: 210mm;
            margin: 0 auto;
            background: white;
        }

        .analytical-header {
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }

        .analytical-header h1 {
            color: #2c3e50;
            font-size: 20pt;
            font-weight: 700;
            margin: 0 0 10px 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .analytical-header .subtitle {
            color: #7f8c8d;
            font-size: 14pt;
            font-weight: 400;
            margin-bottom: 20px;
        }

        .header-meta {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            font-size: 9pt;
            color: #7f8c8d;
        }

        .meta-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .meta-label {
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }

        .meta-value {
            color: #2c3e50;
            font-weight: 400;
        }

        .executive-summary {
            background: #ecf0f1;
            border-left: 5px solid #3498db;
            padding: 20px;
            margin: 30px 0;
            border-radius: 0 8px 8px 0;
        }

        .executive-summary h2 {
            color: #2c3e50;
            font-size: 14pt;
            font-weight: 600;
            margin: 0 0 15px 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .executive-summary p {
            margin: 0;
            font-size: 11pt;
            line-height: 1.6;
            text-align: justify;
        }

        .methodology-box {
            background: #fff;
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }

        .methodology-box h3 {
            color: #e74c3c;
            font-size: 12pt;
            font-weight: 600;
            margin: 0 0 15px 0;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .methodology-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .methodology-list li {
            padding: 8px 0;
            border-bottom: 1px solid #ecf0f1;
            position: relative;
            padding-left: 20px;
        }

        .methodology-list li:before {
            content: "▶";
            color: #3498db;
            position: absolute;
            left: 0;
        }

        .methodology-list li:last-child {
            border-bottom: none;
        }

        .module {
            margin: 30px 0;
            background: white;
            border: 1px solid #bdc3c7;
            border-radius: 8px;
            overflow: hidden;
            page-break-inside: avoid;
        }

        .module-header {
            background: #34495e;
            color: white;
            padding: 15px 25px;
            font-size: 13pt;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
        }

        .module-header:after {
            content: "";
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
        }

        .module-content {
            padding: 25px;
        }

        .info-table, .personal-info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: 10pt;
            background: white;
        }

        .info-table td, .personal-info-table td {
            padding: 12px 15px;
            border: 1px solid #bdc3c7;
            vertical-align: top;
        }

        .info-table td:first-child, .personal-info-table td:first-child {
            font-weight: 600;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #2c3e50;
            width: 30%;
            text-transform: uppercase;
            font-size: 9pt;
            letter-spacing: 0.5px;
        }

        .info-table td:last-child, .personal-info-table td:last-child {
            background: #fdfdfd;
        }

        .data-highlight {
            background: linear-gradient(120deg, #f39c12 0%, #e67e22 100%);
            color: white;
            padding: 3px 8px;
            border-radius: 4px;
            font-weight: 600;
            font-size: 9pt;
        }

        .confidence-indicator {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 8pt;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .confidence-high {
            background: #27ae60;
            color: white;
        }

        .confidence-medium {
            background: #f39c12;
            color: white;
        }

        .confidence-low {
            background: #e74c3c;
            color: white;
        }

        .analytical-footer {
            margin-top: 40px;
            padding: 25px;
            background: #2c3e50;
            color: white;
            border-radius: 8px;
        }

        .footer-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
            align-items: center;
        }

        .analyst-info h4 {
            margin: 0 0 10px 0;
            font-size: 12pt;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .analyst-info p {
            margin: 5px 0;
            font-size: 10pt;
            opacity: 0.9;
        }

        .classification {
            text-align: center;
            padding: 15px;
            background: #e74c3c;
            border-radius: 8px;
        }

        .classification-label {
            font-size: 14pt;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin: 0;
        }

        .classification-note {
            font-size: 8pt;
            margin: 5px 0 0 0;
            opacity: 0.8;
        }

        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <div class="document-container">
        <div class="analytical-header">
            <h1>${settings.title}</h1>
            <div class="subtitle">Analytická zpráva - ${settings.subject || 'Subjekt analýzy'}</div>
            <div class="header-meta">
                <div class="meta-item">
                    <div class="meta-label">Identifikátor</div>
                    <div class="meta-value">JID: ${settings.jid || 'N/A'}</div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">Číslo jednací</div>
                    <div class="meta-value">${settings.documentNumber || 'N/A'}</div>
                </div>
                <div class="meta-item">
                    <div class="meta-label">Datum zpracování</div>
                    <div class="meta-value">${settings.date || currentDate}</div>
                </div>
            </div>
        </div>

        <div class="executive-summary">
            <h2>Shrnutí analýzy</h2>
            <p>${settings.purpose}</p>
        </div>

        <div class="methodology-box">
            <h3>Metodika a zdroje</h3>
            <ul class="methodology-list">
                <li>Systematické vyhledávání v interních informačních systémech Policie ČR</li>
                <li>Komplexní analýza otevřených zdrojů (OSINT)</li>
                <li>Křížová verifikace získaných informací</li>
                <li>Hodnocení spolehlivosti a relevance dat</li>
                <li>Zachování operativní bezpečnosti během celého procesu</li>
            </ul>
        </div>

        ${modulesHtml}

        <div class="analytical-footer">
            <div class="footer-grid">
                <div class="analyst-info">
                    <h4>Analytik</h4>
                    <p><strong>${extractInvestigatorName(settings.department)}</strong></p>
                    <p>${getDepartmentInfoWithoutInvestigator(settings.department)}</p>
                    <p>Místo: ${settings.location}</p>
                </div>
                <div class="classification">
                    <div class="classification-label">Důvěrné</div>
                    <div class="classification-note">Pouze pro služební potřebu</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>`;
}

function generateHtmlReport(settings: any, moduleData: ModuleDataMap, caseId: string): string {
  // Zkontrolujeme styl reportu
  const style = settings.style || 'official';

  if (style === 'modern') {
    return generateModernHtmlReport(settings, moduleData, caseId);
  } else {
    return generateOfficialHtmlReport(settings, moduleData, caseId);
  }
}

function generateOfficialHtmlReport(settings: any, moduleData: ModuleDataMap, caseId: string): string {
  const currentDate = new Date().toLocaleDateString('cs-CZ', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });

  // Generování obsahu modulů
  let modulesHtml = '';
  let pageNumber = 2;

  try {
    // Seskupení modulů podle subjektů
    const subjectModules: { [subjectId: string]: Array<{ moduleType: string; data: any; subjectId: string }> } = {};

    for (const [moduleKey, data] of Object.entries(moduleData)) {
      const parts = moduleKey.split('_');
      if (parts.length >= 2) {
        const subjectId = parts[0];
        const moduleType = parts.slice(1).join('_');

        if (!subjectModules[subjectId]) {
          subjectModules[subjectId] = [];
        }

        subjectModules[subjectId].push({
          moduleType,
          data,
          subjectId
        });
      }
    }

    console.log(`Nalezeno ${Object.keys(subjectModules).length} subjektů s moduly`);

    // Generování obsahu pro každý subjekt
    for (const [subjectId, modules] of Object.entries(subjectModules)) {
      console.log(`Generuji HTML pro subjekt ${subjectId} s ${modules.length} moduly`);

      for (const { moduleType, data, subjectId: moduleSubjectId } of modules) {
        console.log(`Generuji obsah pro modul ${moduleType}`);

        const moduleCategory = getModuleCategory(moduleType);

        // Přidání stránky pro modul
        modulesHtml += `
          <div class="page-break">
            <div class="page-header">
              <div class="page-number">${pageNumber++}. strana</div>
              <div class="page-divider"></div>
            </div>

            <div class="section">
              <h2>${moduleCategory}</h2>
              <h3>${getModuleTitle(moduleType)}</h3>
              ${generateNewModuleContent(moduleType, data, caseId, moduleSubjectId)}
            </div>
          </div>
        `;

        console.log(`Modul ${moduleType} byl úspěšně přidán do reportu`);
      }
    }

    console.log(`Celkem vygenerováno ${pageNumber - 1} stran reportu`);
  } catch (error) {
    console.error("Chyba při generování obsahu modulů:", error);
    modulesHtml = `
      <div class="page-break">
        <div class="page-header">
          <div class="page-number">${pageNumber++}. strana</div>
          <div class="page-divider"></div>
        </div>

        <div class="section">
          <h2>Chyba při generování obsahu</h2>
          <div class="module">
            <p class="error">Při generování obsahu modulů došlo k chybě: ${error instanceof Error ? error.message : 'Neznámá chyba'}</p>
            <p>Zkuste to prosím znovu nebo kontaktujte správce systému.</p>
          </div>
        </div>
      </div>
    `;
  }

  // Základní HTML struktura s CSS styly
  return `
<!DOCTYPE html>
<html lang="cs">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${settings.title || 'OSINT Report'}</title>
  <style>
    @page {
      size: A4;
      margin: 2cm;
    }

    body {
      font-family: 'Times New Roman', serif;
      font-size: 12pt;
      line-height: 1.4;
      color: #000;
      margin: 0;
      padding: 0;
    }

    .header {
      text-align: center;
      margin-bottom: 30px;
      border-bottom: 2px solid #000;
      padding-bottom: 20px;
    }

    .header h1 {
      font-size: 18pt;
      font-weight: bold;
      margin: 0 0 10px 0;
      text-transform: uppercase;
    }

    .header .subtitle {
      font-size: 14pt;
      margin: 5px 0;
    }

    .document-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30px;
      font-size: 11pt;
    }

    .left-info, .right-info {
      flex: 1;
    }

    .right-info {
      text-align: right;
    }

    .section {
      margin-bottom: 30px;
      page-break-inside: avoid;
    }

    .section h2 {
      font-size: 16pt;
      font-weight: bold;
      margin: 0 0 15px 0;
      padding: 10px 0;
      border-bottom: 1px solid #000;
      text-transform: uppercase;
    }

    .section h3 {
      font-size: 14pt;
      font-weight: bold;
      margin: 20px 0 10px 0;
    }

    .section h4 {
      font-size: 12pt;
      font-weight: bold;
      margin: 15px 0 8px 0;
    }

    .module {
      margin-bottom: 25px;
    }

    .info-table {
      width: 100%;
      border-collapse: collapse;
      margin: 15px 0;
      font-size: 11pt;
    }

    .info-table td, .info-table th {
      padding: 8px 12px;
      border: 1px solid #000;
      vertical-align: top;
    }

    .info-table th {
      background-color: #f0f0f0;
      font-weight: bold;
      text-align: left;
    }

    .info-table td:first-child {
      font-weight: bold;
      background-color: #f5f5f5;
      width: 30%;
    }

    .page-break {
      page-break-before: always;
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding-bottom: 10px;
      border-bottom: 1px solid #ccc;
      font-size: 10pt;
      color: #666;
    }

    .page-number {
      font-weight: bold;
    }

    .page-divider {
      flex: 1;
      height: 1px;
      background-color: #ccc;
      margin: 0 20px;
    }

    .photo-container {
      text-align: center;
      margin: 20px 0;
      page-break-inside: avoid;
    }

    .photo-container img {
      max-width: 100%;
      max-height: 400px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }

    .photo-details {
      margin-top: 10px;
      font-size: 10pt;
      color: #666;
    }

    .item-card {
      border: 1px solid #ddd;
      border-radius: 5px;
      padding: 15px;
      margin: 15px 0;
      background-color: #f9f9f9;
      page-break-inside: avoid;
    }

    .item-card h4 {
      margin-top: 0;
      color: #333;
      border-bottom: 1px solid #ddd;
      padding-bottom: 5px;
    }

    .error {
      color: #d32f2f;
      font-style: italic;
    }

    @media print {
      .page-break {
        page-break-before: always;
      }

      body {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }
    }
  </style>
</head>
<body>
  <!-- Titulní stránka -->
  <div class="header">
    <h1>${settings.title || 'OSINT (Open Source Intelligence)'}</h1>
    <div class="subtitle">Analýza subjektu: ${settings.subject || 'Neznámý subjekt'}</div>
  </div>

  <div class="document-info">
    <div class="left-info">
      <div><strong>JID:</strong> ${settings.jid || 'Neuvedeno'}</div>
      <div><strong>Číslo dokumentu:</strong> ${settings.documentNumber || 'Neuvedeno'}</div>
      <div><strong>Místo:</strong> ${settings.location || 'Neuvedeno'}</div>
      <div><strong>Datum:</strong> ${settings.date || currentDate}</div>
    </div>
    <div class="right-info">
      <div><strong>Odbor:</strong></div>
      <div>${(settings.department || 'Neuvedeno').replace(/\n/g, '<br>')}</div>
    </div>
  </div>

  <div class="section">
    <h2>Účel analýzy</h2>
    <p>${settings.purpose || 'Provést komplexní OSINT (Open Source Intelligence) analýzu zájmové osoby'}</p>
  </div>

  <!-- Obsah modulů -->
  ${modulesHtml}

</body>
</html>`;


// Nová funkce pro generování obsahu modulů
function generateNewModuleContent(moduleType: string, data: any, caseId: string, subjectId: string): string {
  console.log(`=== NOVÉ GENEROVÁNÍ MODULU: ${moduleType} ===`);

  try {
    // Kontrola, zda máme data
    if (!data) {
      console.log(`Žádná data pro modul ${moduleType}`);
      return `<div class="module">
        <p>Pro modul ${getModuleTitle(moduleType)} nejsou k dispozici žádná data.</p>
      </div>`;
    }

    // Normalizace typu modulu
    let normalizedModuleType = moduleType.replace(/-/g, '_');

    console.log(`Generuji obsah pro modul typu: ${normalizedModuleType}`);

    // Generování obsahu podle typu modulu
    switch (normalizedModuleType) {
      case 'evidence_obyvatel':
        return generateEvidenceObyvatelContent(data);
      case 'family_members':
        return generateFamilyMembersContent(data);
      case 'registr_ridicske_prukazy':
        return generateDriverLicenseContent(data);
      case 'gun_license':
        return generateGunLicenseContent(data);
      case 'vehicles':
        return generateVehiclesContent(data);
      case 'locations':
        return generateLocationsContent(data);
      case 'business_activity':
        return generateBusinessActivityContent(data);
      case 'cadastre':
        return generateCadastreContent(data);
      case 'training':
        return generateTrainingContent(data);
      case 'email_analysis':
        return generateEmailAnalysisContent(data);
      case 'phone_numbers':
        return generatePhoneNumbersContent(data);
      case 'network_analysis':
        return generateNetworkAnalysisContent(data);
      case 'map_overlays':
        return generateMapOverlaysContent(data);
      case 'facebook':
        return generateFacebookContent(data);
      case 'instagram':
        return generateInstagramContent(data);
      case 'twitter':
        return generateTwitterContent(data);
      case 'linkedin':
        return generateLinkedinContent(data);
      case 'phone_analysis':
        return generatePhoneAnalysisContent(data);
      case 'financial_monitoring':
        return generateFinancialMonitoringContent(data);
      default:
        console.log(`Používám generický obsah pro modul typu: ${moduleType} (normalized: ${normalizedModuleType})`);
        return generateGenericModuleContent(data);
    }
  } catch (error) {
    console.error(`Chyba při generování obsahu modulu ${moduleType}:`, error);
    return `<div class="module">
      <h3>Modul: ${getModuleTitle(moduleType)}</h3>
      <p class="error">Při generování obsahu modulu došlo k chybě: ${error instanceof Error ? error.message : 'Neznámá chyba'}</p>
      <p>Zkuste to prosím znovu nebo kontaktujte správce systému.</p>
    </div>`;
  }
}

// Funkce pro generování obsahu Evidence obyvatel
function generateEvidenceObyvatelContent(data: any): string {
  console.log("Generuji obsah pro Evidence obyvatel");

  let html = '<div class="module">';

  if (data.personalInfo) {
    html += `
      <h4>Osobní údaje</h4>
      <table class="info-table">
        <tr><td>Jméno a příjmení:</td><td>${data.personalInfo.fullName || 'Neuvedeno'}</td></tr>
        <tr><td>Datum narození:</td><td>${data.personalInfo.birthDate || 'Neuvedeno'}</td></tr>
        <tr><td>Místo narození:</td><td>${data.personalInfo.birthPlace || 'Neuvedeno'}</td></tr>
        <tr><td>Rodné číslo:</td><td>${data.personalInfo.birthNumber || 'Neuvedeno'}</td></tr>
        <tr><td>Státní občanství:</td><td>${data.personalInfo.nationality || 'Neuvedeno'}</td></tr>
        <tr><td>Rodinný stav:</td><td>${data.personalInfo.maritalStatus || 'Neuvedeno'}</td></tr>
      </table>
    `;
  }

  if (data.addresses && Array.isArray(data.addresses)) {
    html += '<h4>Adresy</h4>';
    for (let i = 0; i < data.addresses.length; i++) {
      const address = data.addresses[i];
      html += `
        <div class="item-card">
          <h5>Adresa ${i + 1}</h5>
          <table class="info-table">
            <tr><td>Adresa:</td><td>${address.fullAddress || 'Neuvedeno'}</td></tr>
            <tr><td>Typ:</td><td>${address.type || 'Neuvedeno'}</td></tr>
            <tr><td>Platnost od:</td><td>${address.validFrom || 'Neuvedeno'}</td></tr>
            <tr><td>Platnost do:</td><td>${address.validTo || 'Neuvedeno'}</td></tr>
          </table>
        </div>
      `;
    }
  }

  if (data.documents && Array.isArray(data.documents)) {
    html += '<h4>Doklady</h4>';
    for (let i = 0; i < data.documents.length; i++) {
      const doc = data.documents[i];
      html += `
        <div class="item-card">
          <h5>${doc.type || 'Doklad'}</h5>
          <table class="info-table">
            <tr><td>Číslo:</td><td>${doc.number || 'Neuvedeno'}</td></tr>
            <tr><td>Vydáno:</td><td>${doc.issuedDate || 'Neuvedeno'}</td></tr>
            <tr><td>Platnost do:</td><td>${doc.validUntil || 'Neuvedeno'}</td></tr>
            <tr><td>Vydal:</td><td>${doc.issuedBy || 'Neuvedeno'}</td></tr>
          </table>
        </div>
      `;
    }
  }

  html += '</div>';
  return html;
}

// Funkce pro generování obsahu Vozidel
function generateVehiclesContent(data: any): string {
  console.log("Generuji obsah pro Vozidla");

  let html = '<div class="module">';

  if (data.vehicles && Array.isArray(data.vehicles)) {
    for (let i = 0; i < data.vehicles.length; i++) {
      const vehicle = data.vehicles[i];
      html += `
        <div class="item-card">
          <h4>Vozidlo ${i + 1}</h4>
          <table class="info-table">
            <tr><td>Značka:</td><td>${vehicle.make || 'Neuvedeno'}</td></tr>
            <tr><td>Model:</td><td>${vehicle.model || 'Neuvedeno'}</td></tr>
            <tr><td>SPZ:</td><td>${vehicle.licensePlate || 'Neuvedeno'}</td></tr>
            <tr><td>VIN:</td><td>${vehicle.vin || 'Neuvedeno'}</td></tr>
            <tr><td>Rok výroby:</td><td>${vehicle.year || 'Neuvedeno'}</td></tr>
            <tr><td>Barva:</td><td>${vehicle.color || 'Neuvedeno'}</td></tr>
            <tr><td>Typ vztahu:</td><td>${vehicle.relationshipType || 'Neuvedeno'}</td></tr>
            <tr><td>STK platná do:</td><td>${vehicle.stkValidUntil || 'Neuvedeno'}</td></tr>
          </table>
          ${vehicle.notes ? `<div style="margin-top: 10px;"><strong>Poznámky:</strong> ${vehicle.notes}</div>` : ''}
        </div>
      `;
    }
  } else {
    html += '<p>Žádná vozidla nejsou k dispozici.</p>';
  }

  html += '</div>';
  return html;
}

// Funkce pro generování obsahu Rodinných příslušníků
function generateFamilyMembersContent(data: any): string {
  console.log("Generuji obsah pro Rodinné příslušníky");

  let html = '<div class="module">';

  if (data.familyMembers && Array.isArray(data.familyMembers)) {
    for (let i = 0; i < data.familyMembers.length; i++) {
      const member = data.familyMembers[i];
      html += `
        <div class="item-card">
          <h4>${member.fullName || `Člen rodiny ${i + 1}`}</h4>
          <table class="info-table">
            <tr><td>Jméno a příjmení:</td><td>${member.fullName || 'Neuvedeno'}</td></tr>
            <tr><td>Datum narození:</td><td>${member.birthDate || 'Neuvedeno'}</td></tr>
            <tr><td>Rodné číslo:</td><td>${member.birthNumber || 'Neuvedeno'}</td></tr>
            <tr><td>Vztah:</td><td>${member.relationship || 'Neuvedeno'}</td></tr>
            <tr><td>Adresa:</td><td>${member.address || 'Neuvedeno'}</td></tr>
          </table>
          ${member.notes ? `<div style="margin-top: 10px;"><strong>Poznámky:</strong> ${member.notes}</div>` : ''}
        </div>
      `;
    }
  } else {
    html += '<p>Žádní rodinní příslušníci nejsou k dispozici.</p>';
  }

  html += '</div>';
  return html;
}

// Funkce pro generování obsahu Řidičských průkazů
function generateDriverLicenseContent(data: any): string {
  console.log("Generuji obsah pro Řidičské průkazy");

  let html = '<div class="module">';

  if (data.licenses && Array.isArray(data.licenses)) {
    for (let i = 0; i < data.licenses.length; i++) {
      const license = data.licenses[i];
      html += `
        <div class="item-card">
          <h4>Řidičský průkaz ${i + 1}</h4>
          <table class="info-table">
            <tr><td>Číslo průkazu:</td><td>${license.number || 'Neuvedeno'}</td></tr>
            <tr><td>Kategorie:</td><td>${license.categories || 'Neuvedeno'}</td></tr>
            <tr><td>Vydáno:</td><td>${license.issuedDate || 'Neuvedeno'}</td></tr>
            <tr><td>Platnost do:</td><td>${license.validUntil || 'Neuvedeno'}</td></tr>
            <tr><td>Vydal:</td><td>${license.issuedBy || 'Neuvedeno'}</td></tr>
            <tr><td>Stav:</td><td>${license.status || 'Neuvedeno'}</td></tr>
          </table>
        </div>
      `;
    }
  } else {
    html += '<p>Žádné řidičské průkazy nejsou k dispozici.</p>';
  }

  html += '</div>';
  return html;
}

// Funkce pro generování obsahu Zbrojních průkazů
function generateGunLicenseContent(data: any): string {
  console.log("Generuji obsah pro Zbrojní průkazy");

  let html = '<div class="module">';

  if (data.licenses && Array.isArray(data.licenses)) {
    for (let i = 0; i < data.licenses.length; i++) {
      const license = data.licenses[i];
      html += `
        <div class="item-card">
          <h4>Zbrojní průkaz ${i + 1}</h4>
          <table class="info-table">
            <tr><td>Číslo průkazu:</td><td>${license.number || 'Neuvedeno'}</td></tr>
            <tr><td>Typ:</td><td>${license.type || 'Neuvedeno'}</td></tr>
            <tr><td>Vydáno:</td><td>${license.issuedDate || 'Neuvedeno'}</td></tr>
            <tr><td>Platnost do:</td><td>${license.validUntil || 'Neuvedeno'}</td></tr>
            <tr><td>Vydal:</td><td>${license.issuedBy || 'Neuvedeno'}</td></tr>
            <tr><td>Stav:</td><td>${license.status || 'Neuvedeno'}</td></tr>
          </table>
        </div>
      `;
    }
  }

  if (data.weapons && Array.isArray(data.weapons)) {
    html += '<h4>Zbraně</h4>';
    for (let i = 0; i < data.weapons.length; i++) {
      const weapon = data.weapons[i];
      html += `
        <div class="item-card">
          <h5>Zbraň ${i + 1}</h5>
          <table class="info-table">
            <tr><td>Typ:</td><td>${weapon.type || 'Neuvedeno'}</td></tr>
            <tr><td>Výrobce:</td><td>${weapon.manufacturer || 'Neuvedeno'}</td></tr>
            <tr><td>Model:</td><td>${weapon.model || 'Neuvedeno'}</td></tr>
            <tr><td>Sériové číslo:</td><td>${weapon.serialNumber || 'Neuvedeno'}</td></tr>
            <tr><td>Ráže:</td><td>${weapon.caliber || 'Neuvedeno'}</td></tr>
          </table>
        </div>
      `;
    }
  }

  if (!data.licenses && !data.weapons) {
    html += '<p>Žádné zbrojní průkazy ani zbraně nejsou k dispozici.</p>';
  }

  html += '</div>';
  return html;
}

// Funkce pro generování obsahu Lokací
function generateLocationsContent(data: any): string {
  console.log("Generuji obsah pro Lokace");

  let html = '<div class="module">';

  if (data.locations && Array.isArray(data.locations)) {
    for (let i = 0; i < data.locations.length; i++) {
      const location = data.locations[i];
      html += `
        <div class="item-card">
          <h4>${location.name || `Lokace ${i + 1}`}</h4>
          <table class="info-table">
            <tr><td>Název:</td><td>${location.name || 'Neuvedeno'}</td></tr>
            <tr><td>Adresa:</td><td>${location.address || 'Neuvedeno'}</td></tr>
            <tr><td>Typ:</td><td>${location.type || 'Neuvedeno'}</td></tr>
            <tr><td>GPS souřadnice:</td><td>${location.coordinates || 'Neuvedeno'}</td></tr>
            <tr><td>Popis:</td><td>${location.description || 'Neuvedeno'}</td></tr>
          </table>
          ${location.notes ? `<div style="margin-top: 10px;"><strong>Poznámky:</strong> ${location.notes}</div>` : ''}
        </div>
      `;
    }
  } else {
    html += '<p>Žádné lokace nejsou k dispozici.</p>';
  }

  html += '</div>';
  return html;
}

// Funkce pro generování obsahu Facebook
function generateFacebookContent(data: any): string {
  console.log("Generuji obsah pro Facebook");

  let html = '<div class="module">';

  if (data.profile) {
    html += `
      <h4>Profil</h4>
      <table class="info-table">
        <tr><td>Jméno:</td><td>${data.profile.name || 'Neuvedeno'}</td></tr>
        <tr><td>URL profilu:</td><td>${data.profile.url || 'Neuvedeno'}</td></tr>
        <tr><td>ID:</td><td>${data.profile.id || 'Neuvedeno'}</td></tr>
        <tr><td>Počet přátel:</td><td>${data.profile.friendsCount || 'Neuvedeno'}</td></tr>
        <tr><td>Místo bydliště:</td><td>${data.profile.location || 'Neuvedeno'}</td></tr>
        <tr><td>Zaměstnání:</td><td>${data.profile.work || 'Neuvedeno'}</td></tr>
        <tr><td>Vzdělání:</td><td>${data.profile.education || 'Neuvedeno'}</td></tr>
      </table>
    `;
  }

  if (data.photos && Array.isArray(data.photos)) {
    html += '<h4>Fotografie</h4>';
    for (let i = 0; i < data.photos.length; i++) {
      const photo = data.photos[i];
      html += `
        <div class="photo-container">
          ${photo.url ? `<img src="${photo.url}" alt="Facebook foto ${i + 1}" class="photo-image">` : ''}
          <div class="photo-details">
            <div><strong>Popis:</strong> ${photo.description || 'Bez popisu'}</div>
            <div><strong>Datum:</strong> ${photo.date || 'Neuvedeno'}</div>
            <div><strong>Zdroj:</strong> Facebook</div>
          </div>
        </div>
      `;
    }
  }

  html += '</div>';
  return html;
}

// Funkce pro generování obsahu Instagram
function generateInstagramContent(data: any): string {
  console.log("Generuji obsah pro Instagram");

  let html = '<div class="module">';

  if (data.profile) {
    html += `
      <h4>Profil</h4>
      <table class="info-table">
        <tr><td>Uživatelské jméno:</td><td>${data.profile.username || 'Neuvedeno'}</td></tr>
        <tr><td>Jméno:</td><td>${data.profile.name || 'Neuvedeno'}</td></tr>
        <tr><td>Bio:</td><td>${data.profile.bio || 'Neuvedeno'}</td></tr>
        <tr><td>Počet sledujících:</td><td>${data.profile.followersCount || 'Neuvedeno'}</td></tr>
        <tr><td>Počet sledovaných:</td><td>${data.profile.followingCount || 'Neuvedeno'}</td></tr>
        <tr><td>Počet příspěvků:</td><td>${data.profile.postsCount || 'Neuvedeno'}</td></tr>
      </table>
    `;
  }

  if (data.photos && Array.isArray(data.photos)) {
    html += '<h4>Fotografie</h4>';
    for (let i = 0; i < data.photos.length; i++) {
      const photo = data.photos[i];
      html += `
        <div class="photo-container">
          ${photo.url ? `<img src="${photo.url}" alt="Instagram foto ${i + 1}" class="photo-image">` : ''}
          <div class="photo-details">
            <div><strong>Popis:</strong> ${photo.caption || 'Bez popisu'}</div>
            <div><strong>Datum:</strong> ${photo.date || 'Neuvedeno'}</div>
            <div><strong>Zdroj:</strong> Instagram</div>
          </div>
        </div>
      `;
    }
  }

  html += '</div>';
  return html;
}

// Funkce pro generování obsahu Twitter
function generateTwitterContent(data: any): string {
  console.log("Generuji obsah pro Twitter");

  let html = '<div class="module">';

  if (data.profile) {
    html += `
      <h4>Profil</h4>
      <table class="info-table">
        <tr><td>Uživatelské jméno:</td><td>${data.profile.username || 'Neuvedeno'}</td></tr>
        <tr><td>Jméno:</td><td>${data.profile.name || 'Neuvedeno'}</td></tr>
        <tr><td>Bio:</td><td>${data.profile.bio || 'Neuvedeno'}</td></tr>
        <tr><td>Počet sledujících:</td><td>${data.profile.followersCount || 'Neuvedeno'}</td></tr>
        <tr><td>Počet sledovaných:</td><td>${data.profile.followingCount || 'Neuvedeno'}</td></tr>
        <tr><td>Počet tweetů:</td><td>${data.profile.tweetsCount || 'Neuvedeno'}</td></tr>
        <tr><td>Lokace:</td><td>${data.profile.location || 'Neuvedeno'}</td></tr>
      </table>
    `;
  }

  if (data.tweets && Array.isArray(data.tweets)) {
    html += '<h4>Tweety</h4>';
    for (let i = 0; i < data.tweets.length; i++) {
      const tweet = data.tweets[i];
      html += `
        <div class="item-card">
          <div><strong>Text:</strong> ${tweet.text || 'Neuvedeno'}</div>
          <div><strong>Datum:</strong> ${tweet.date || 'Neuvedeno'}</div>
          <div><strong>Retweets:</strong> ${tweet.retweetCount || '0'}</div>
          <div><strong>Likes:</strong> ${tweet.likeCount || '0'}</div>
        </div>
      `;
    }
  }

  html += '</div>';
  return html;
}

// Funkce pro generování obsahu LinkedIn
function generateLinkedinContent(data: any): string {
  console.log("Generuji obsah pro LinkedIn");

  let html = '<div class="module">';

  if (data.profile) {
    html += `
      <h4>Profil</h4>
      <table class="info-table">
        <tr><td>Jméno:</td><td>${data.profile.name || 'Neuvedeno'}</td></tr>
        <tr><td>Pozice:</td><td>${data.profile.position || 'Neuvedeno'}</td></tr>
        <tr><td>Společnost:</td><td>${data.profile.company || 'Neuvedeno'}</td></tr>
        <tr><td>Lokace:</td><td>${data.profile.location || 'Neuvedeno'}</td></tr>
        <tr><td>Počet kontaktů:</td><td>${data.profile.connectionsCount || 'Neuvedeno'}</td></tr>
      </table>
    `;
  }

  if (data.experience && Array.isArray(data.experience)) {
    html += '<h4>Pracovní zkušenosti</h4>';
    for (let i = 0; i < data.experience.length; i++) {
      const exp = data.experience[i];
      html += `
        <div class="item-card">
          <h5>${exp.position || 'Pozice neuvedena'}</h5>
          <div><strong>Společnost:</strong> ${exp.company || 'Neuvedeno'}</div>
          <div><strong>Období:</strong> ${exp.period || 'Neuvedeno'}</div>
          <div><strong>Popis:</strong> ${exp.description || 'Neuvedeno'}</div>
        </div>
      `;
    }
  }

  html += '</div>';
  return html;
}

// Funkce pro generování obsahu ostatních modulů
function generateBusinessActivityContent(data: any): string {
  console.log("Generuji obsah pro Podnikatelskou činnost");
  let html = '<div class="module">';

  if (data.businesses && Array.isArray(data.businesses)) {
    for (let i = 0; i < data.businesses.length; i++) {
      const business = data.businesses[i];
      html += `
        <div class="item-card">
          <h4>${business.name || `Podnikání ${i + 1}`}</h4>
          <table class="info-table">
            <tr><td>Název:</td><td>${business.name || 'Neuvedeno'}</td></tr>
            <tr><td>IČO:</td><td>${business.ico || 'Neuvedeno'}</td></tr>
            <tr><td>Typ:</td><td>${business.type || 'Neuvedeno'}</td></tr>
            <tr><td>Stav:</td><td>${business.status || 'Neuvedeno'}</td></tr>
            <tr><td>Datum vzniku:</td><td>${business.establishedDate || 'Neuvedeno'}</td></tr>
          </table>
        </div>
      `;
    }
  } else {
    html += '<p>Žádná podnikatelská činnost není k dispozici.</p>';
  }

  html += '</div>';
  return html;
}

function generateCadastreContent(data: any): string {
  console.log("Generuji obsah pro Katastr nemovitostí");
  let html = '<div class="module">';

  if (data.properties && Array.isArray(data.properties)) {
    for (let i = 0; i < data.properties.length; i++) {
      const property = data.properties[i];
      html += `
        <div class="item-card">
          <h4>Nemovitost ${i + 1}</h4>
          <table class="info-table">
            <tr><td>Adresa:</td><td>${property.address || 'Neuvedeno'}</td></tr>
            <tr><td>Typ:</td><td>${property.type || 'Neuvedeno'}</td></tr>
            <tr><td>Výměra:</td><td>${property.area || 'Neuvedeno'}</td></tr>
            <tr><td>Vlastník:</td><td>${property.owner || 'Neuvedeno'}</td></tr>
            <tr><td>Podíl:</td><td>${property.share || 'Neuvedeno'}</td></tr>
          </table>
        </div>
      `;
    }
  } else {
    html += '<p>Žádné nemovitosti nejsou k dispozici.</p>';
  }

  html += '</div>';
  return html;
}

function generateTrainingContent(data: any): string {
  console.log("Generuji obsah pro Školení");
  let html = '<div class="module">';

  if (data.trainings && Array.isArray(data.trainings)) {
    for (let i = 0; i < data.trainings.length; i++) {
      const training = data.trainings[i];
      html += `
        <div class="item-card">
          <h4>${training.name || `Školení ${i + 1}`}</h4>
          <table class="info-table">
            <tr><td>Název:</td><td>${training.name || 'Neuvedeno'}</td></tr>
            <tr><td>Datum:</td><td>${training.date || 'Neuvedeno'}</td></tr>
            <tr><td>Poskytovatel:</td><td>${training.provider || 'Neuvedeno'}</td></tr>
            <tr><td>Certifikát:</td><td>${training.certificate || 'Neuvedeno'}</td></tr>
          </table>
        </div>
      `;
    }
  } else {
    html += '<p>Žádná školení nejsou k dispozici.</p>';
  }

  html += '</div>';
  return html;
}

function generateEmailAnalysisContent(data: any): string {
  console.log("Generuji obsah pro Analýzu emailů");
  let html = '<div class="module">';

  if (data.emails && Array.isArray(data.emails)) {
    for (let i = 0; i < data.emails.length; i++) {
      const email = data.emails[i];
      html += `
        <div class="item-card">
          <h4>Email ${i + 1}</h4>
          <table class="info-table">
            <tr><td>Adresa:</td><td>${email.address || 'Neuvedeno'}</td></tr>
            <tr><td>Poskytovatel:</td><td>${email.provider || 'Neuvedeno'}</td></tr>
            <tr><td>Typ:</td><td>${email.type || 'Neuvedeno'}</td></tr>
            <tr><td>Stav:</td><td>${email.status || 'Neuvedeno'}</td></tr>
          </table>
        </div>
      `;
    }
  } else {
    html += '<p>Žádné emaily nejsou k dispozici.</p>';
  }

  html += '</div>';
  return html;
}

function generatePhoneNumbersContent(data: any): string {
  console.log("Generuji obsah pro Telefonní čísla");
  let html = '<div class="module">';

  if (data.phoneNumbers && Array.isArray(data.phoneNumbers)) {
    for (let i = 0; i < data.phoneNumbers.length; i++) {
      const phone = data.phoneNumbers[i];
      html += `
        <div class="item-card">
          <h4>Telefon ${i + 1}</h4>
          <table class="info-table">
            <tr><td>Číslo:</td><td>${phone.number || 'Neuvedeno'}</td></tr>
            <tr><td>Operátor:</td><td>${phone.operator || 'Neuvedeno'}</td></tr>
            <tr><td>Typ:</td><td>${phone.type || 'Neuvedeno'}</td></tr>
            <tr><td>Stav:</td><td>${phone.status || 'Neuvedeno'}</td></tr>
          </table>
        </div>
      `;
    }
  } else {
    html += '<p>Žádná telefonní čísla nejsou k dispozici.</p>';
  }

  html += '</div>';
  return html;
}

function generateNetworkAnalysisContent(data: any): string {
  console.log("Generuji obsah pro Síťovou analýzu");
  let html = '<div class="module">';
  html += '<p>Obsah síťové analýzy bude doplněn podle dostupných dat.</p>';
  html += '</div>';
  return html;
}

function generateMapOverlaysContent(data: any): string {
  console.log("Generuji obsah pro Mapové vrstvy");
  let html = '<div class="module">';
  html += '<p>Obsah mapových vrstev bude doplněn podle dostupných dat.</p>';
  html += '</div>';
  return html;
}

function generatePhoneAnalysisContent(data: any): string {
  console.log("Generuji obsah pro Analýzu telefonů");
  let html = '<div class="module">';
  html += '<p>Obsah analýzy telefonů bude doplněn podle dostupných dat.</p>';
  html += '</div>';
  return html;
}

function generateFinancialMonitoringContent(data: any): string {
  console.log("Generuji obsah pro Finanční monitoring");
  let html = '<div class="module">';
  html += '<p>Obsah finančního monitoringu bude doplněn podle dostupných dat.</p>';
  html += '</div>';
  return html;
}

// Pomocné funkce pro generování moderního HTML reportu
function generateModernHtmlReport(settings: any, moduleData: ModuleDataMap, caseId: string): string {
  // Zatím použijeme stejnou logiku jako pro oficiální report
  return generateOfficialHtmlReport(settings, moduleData, caseId);
}

// Funkce pro generování obsahu modulů pro PDF s různými styly
function generateModulesContent(moduleData: ModuleDataMap, style: string = 'official'): string {
  let modulesHtml = '';

  try {
    // Seskupení modulů podle subjektů
    const subjectModules: { [subjectId: string]: Array<{ moduleType: string; data: any; subjectId: string }> } = {};

    for (const [moduleKey, data] of Object.entries(moduleData)) {
      const parts = moduleKey.split('_');
      if (parts.length >= 2) {
        const subjectId = parts[0];
        const moduleType = parts.slice(1).join('_');

        if (!subjectModules[subjectId]) {
          subjectModules[subjectId] = [];
        }

        subjectModules[subjectId].push({
          moduleType,
          data,
          subjectId
        });
      }
    }

    console.log(`Generuji moduly ve stylu: ${style}`);

    // Generování obsahu pro každý subjekt
    for (const [subjectId, modules] of Object.entries(subjectModules)) {
      for (const { moduleType, data, subjectId: moduleSubjectId } of modules) {
        const moduleTitle = getModuleTitle(moduleType);
        const moduleContent = generateNewModuleContent(moduleType, data, '', moduleSubjectId);

        // Generování podle stylu
        switch (style) {
          case 'modern':
            modulesHtml += `
              <div class="module">
                <div class="module-header">${moduleTitle}</div>
                <div class="module-content">${moduleContent}</div>
              </div>
            `;
            break;

          case 'analytical':
            modulesHtml += `
              <div class="module">
                <div class="module-header">${moduleTitle}</div>
                <div class="module-content">${moduleContent}</div>
              </div>
            `;
            break;

          case 'official':
          default:
            modulesHtml += `
              <div class="module">
                <h3>${moduleTitle}</h3>
                ${moduleContent}
              </div>
            `;
            break;
        }
      }
    }

  } catch (error) {
    console.error("Chyba při generování obsahu modulů:", error);
    modulesHtml = `
      <div class="module">
        <h3>Chyba při generování obsahu</h3>
        <p class="error">Při generování obsahu modulů došlo k chybě: ${error instanceof Error ? error.message : 'Neznámá chyba'}</p>
        <p>Zkuste to prosím znovu nebo kontaktujte správce systému.</p>
      </div>
    `;
  }

  return modulesHtml;
}

function generateWordReport(settings: any, moduleData: ModuleDataMap, caseId: string): string {
  // Pro Word použijeme stejný HTML jako pro běžný report, ale s Word-specific styly
  let wordReport = generateHtmlReport(settings, moduleData, caseId);

  // Přidáme Word-specific styly
  wordReport = wordReport.replace(
    '<head>',
    `<head>
    <style>
      /* Word-specific styly */
      body {
        font-family: 'Times New Roman', serif;
        font-size: 12pt;
        line-height: 1.5;
        color: #000;
        margin: 0;
        padding: 0;
      }
      .page-break {
        page-break-before: always;
        margin-top: 20px;
      }
      table {
        border-collapse: collapse;
        width: 100%;
        margin: 10px 0;
      }
      th, td {
        border: 1px solid #000;
        padding: 8px;
        text-align: left;
      }
      th {
        background-color: #f2f2f2;
      }
      img {
        max-width: 100%;
        height: auto;
      }
      h1, h2, h3, h4, h5 {
        margin-top: 20px;
        margin-bottom: 10px;
      }
      .document-title {
        font-size: 18pt;
        font-weight: bold;
        text-align: center;
        margin: 30px 0;
      }
      .signature-block {
        margin-top: 50px;
        text-align: right;
      }
    </style>`
  );

  return wordReport;
}

async function convertImagesToBase64(htmlContent: string): Promise<string> {
  const fs = require('fs').promises;
  const path = require('path');

  // Najde všechny src="/uploads/..." odkazy
  const imgRegex = /src="(\/uploads\/[^"]+)"/g;
  const matches = Array.from(htmlContent.matchAll(imgRegex));

  if (matches.length > 0) {
    console.log(`🖼️ Konvertuji ${matches.length} obrázků na base64 pro PDF...`);
  }

  let modifiedHtml = htmlContent;

  for (const match of matches) {
    const relativePath = match[1]; // /uploads/evidence-obyvatel/filename.png
    const absolutePath = path.join(process.cwd(), 'public', relativePath);

    try {
      // Zkontroluj zda soubor existuje
      await fs.access(absolutePath);

      // Načti soubor jako buffer
      const imageBuffer = await fs.readFile(absolutePath);

      // Zjisti MIME typ na základě přípony
      const ext = path.extname(relativePath).toLowerCase();
      let mimeType = 'image/jpeg'; // default
      if (ext === '.png') mimeType = 'image/png';
      if (ext === '.gif') mimeType = 'image/gif';
      if (ext === '.webp') mimeType = 'image/webp';

      // Převeď na base64 data URI
      const base64 = imageBuffer.toString('base64');
      const dataUri = `data:${mimeType};base64,${base64}`;

      // Nahraď v HTML
      modifiedHtml = modifiedHtml.replace(match[0], `src="${dataUri}"`);

      console.log(`  ✓ ${path.basename(relativePath)} (${Math.round(base64.length/1024)}KB)`);

    } catch (error) {
      console.error(`  ✗ Chyba při načítání ${path.basename(relativePath)}:`, error);
      // Ponecháme původní cestu pokud se nepodaří načíst
    }
  }

  return modifiedHtml;
}

function transformModuleDataToCaseData(moduleData: ModuleDataMap, caseId: string): any {
  // Transformace ModuleDataMap na CaseData strukturu
  const caseData: any = {
    id: caseId,
    name: 'OSINT Report',
    createdAt: new Date(),
    updatedAt: new Date(),
    evidenceObyvatel: undefined,
    familyMembers: [],
    driverLicense: undefined,
    gunLicense: undefined,
    vehicles: [],
    locations: [],
    businessActivity: undefined,
    cadastre: undefined,
    training: undefined,
    emailAnalysis: [],
    phoneNumbers: [],
    networkAnalysis: undefined,
    mapOverlays: undefined,
    facebookData: [],
    instagramData: [],
    twitterData: [],
    linkedinData: [],
    phoneAnalysis: undefined,
    financialMonitoring: undefined
  };

  // Zpracování dat modulů
  for (const [moduleId, data] of Object.entries(moduleData)) {
    if (!data) continue;

    // Extrakce typu modulu z ID
    let moduleType = '';
    if (moduleId.includes('_')) {
      const parts = moduleId.split('_');
      if (parts.length >= 2) {
        moduleType = parts.slice(1).join('_');
      }
    } else {
      moduleType = moduleId;
    }

    // Mapování dat podle typu modulu
    switch (moduleType) {
      case 'evidence_obyvatel':
      case 'evidence-obyvatel':
      case 'personal-info':
        if (!caseData.evidenceObyvatel) {
          caseData.evidenceObyvatel = {
            firstName: data.firstName || data.jmeno,
            lastName: data.lastName || data.prijmeni,
            birthDate: data.birthDate || data.datumNarozeni,
            birthPlace: data.birthPlace || data.mistoNarozeni,
            nationality: data.nationality || data.statniPrislusnost,
            idNumber: data.idNumber || data.cisloOP,
            addresses: data.addresses || data.adresy || [],
            notes: data.notes || data.poznamky
          };
        }
        break;

      case 'family_members':
      case 'family-members':
        if (Array.isArray(data.familyMembers)) {
          caseData.familyMembers.push(...data.familyMembers);
        } else if (data.fullName || data.firstName) {
          caseData.familyMembers.push(data);
        }
        break;

      case 'registr_ridicske_prukazy':
      case 'driver-license':
        if (!caseData.driverLicense) {
          caseData.driverLicense = data;
        }
        break;

      case 'gun_license':
      case 'gun-license':
        if (!caseData.gunLicense) {
          caseData.gunLicense = data;
        }
        break;

      case 'vehicles':
        if (Array.isArray(data.vehicles)) {
          caseData.vehicles.push(...data.vehicles);
        } else if (data.make || data.licensePlate || data.brand) {
          // Normalizace dat vozidla
          const vehicle = {
            brand: data.make || data.brand,
            model: data.model,
            licensePlate: data.licensePlate,
            vin: data.vin,
            color: data.color,
            yearManufactured: data.yearManufactured,
            relationshipType: data.relationshipType,
            otherRelationshipDetail: data.otherRelationshipDetail,
            stkValidUntil: data.stkValidUntil,
            firstRegistered: data.firstRegistered,
            notes: data.notes,
            photos: data.photos || []
          };
          caseData.vehicles.push(vehicle);
        }
        break;

      case 'locations':
        if (Array.isArray(data.locations)) {
          caseData.locations.push(...data.locations);
        } else if (data.name || data.address) {
          caseData.locations.push(data);
        }
        break;

      case 'business_activity':
      case 'business-activity':
      case 'firma':
      case 'company':
        if (!caseData.businessActivity) {
          caseData.businessActivity = {
            companyName: data.companyName || data.name || data.nazev,
            ico: data.ico,
            dic: data.dic,
            legalForm: data.legalForm || data.pravniForma,
            status: data.status || data.stav,
            businessActivities: data.businessActivities || data.predmetyPodnikani || [],
            photos: data.photos || [],
            notes: data.notes || data.poznamky
          };
        }
        break;

      case 'cadastre':
      case 'real-estate':
        if (!caseData.cadastre) {
          caseData.cadastre = {
            properties: data.properties || data.nemovitosti || [],
            photos: data.photos || [],
            notes: data.notes || data.poznamky
          };
        }
        break;

      case 'training':
        if (!caseData.training) {
          caseData.training = data;
        }
        break;

      case 'email_analysis':
      case 'email-analysis':
        if (Array.isArray(data.emails)) {
          caseData.emailAnalysis.push(...data.emails);
        } else if (data.address || data.emailAddress) {
          caseData.emailAnalysis.push(data);
        }
        break;

      case 'phone_numbers':
      case 'phone-numbers':
        if (Array.isArray(data.phones)) {
          caseData.phoneNumbers.push(...data.phones);
        } else if (data.phoneNumber || data.number) {
          caseData.phoneNumbers.push(data);
        }
        break;

      case 'network_analysis':
      case 'network-analysis':
        if (!caseData.networkAnalysis) {
          caseData.networkAnalysis = {
            ipAddresses: data.ipAddresses || data.addresses || [],
            domains: data.domains || [],
            photos: data.photos || [],
            notes: data.notes || data.poznamky
          };
        }
        break;

      case 'map_overlays':
      case 'map-overlays':
        if (!caseData.mapOverlays) {
          caseData.mapOverlays = data;
        }
        break;

      case 'facebook':
        if (Array.isArray(data.profiles)) {
          caseData.facebookData.push(...data.profiles);
        } else if (data.username || data.displayName) {
          caseData.facebookData.push(data);
        }
        break;

      case 'instagram':
        if (Array.isArray(data.profiles)) {
          caseData.instagramData.push(...data.profiles);
        } else if (data.username || data.displayName) {
          caseData.instagramData.push(data);
        }
        break;

      case 'twitter':
        if (Array.isArray(data.profiles)) {
          caseData.twitterData.push(...data.profiles);
        } else if (data.username || data.displayName) {
          caseData.twitterData.push(data);
        }
        break;

      case 'linkedin':
        if (Array.isArray(data.profiles)) {
          caseData.linkedinData.push(...data.profiles);
        } else if (data.fullName || data.headline) {
          caseData.linkedinData.push(data);
        }
        break;

      case 'phone_analysis':
      case 'phone-analysis':
        if (!caseData.phoneAnalysis) {
          caseData.phoneAnalysis = data;
        }
        break;

      case 'financial_monitoring':
      case 'financial-monitoring':
        if (!caseData.financialMonitoring) {
          caseData.financialMonitoring = data;
        }
        break;
    }
  }

  return caseData;
}

function getDepartmentInfoWithoutInvestigator(department: string): string {
  // Odstranění řádku s vyšetřovatelem z department info
  const lines = department.split('\n');
  const filteredLines = lines.filter(line => !line.includes('Vyšetřovatel:'));
  return filteredLines.join('<br>');
}

function extractInvestigatorName(department: string): string {
  // Extrahuje jméno vyšetřovatele z department stringu
  const lines = department.split('\n');
  const investigatorLine = lines.find(line => line.includes('Vyšetřovatel:'));

  if (investigatorLine) {
    const rawName = investigatorLine.replace('Vyšetřovatel:', '').trim();
    return formatInvestigatorName(rawName);
  }

  return 'Nepřiřazeno';
}

// Pomocné funkce pro generování reportu
function getCategoryForModule(moduleType: string): string {
  // Mapování typů modulů na kategorie
  const moduleCategories: { [key: string]: string } = {
    // Sociální sítě
    'facebook': 'Sociální sítě',
    'instagram': 'Sociální sítě',
    'twitter': 'Sociální sítě',
    'linkedin': 'Sociální sítě',
    'tiktok': 'Sociální sítě',
    'youtube': 'Sociální sítě',
    'telegram': 'Sociální sítě',
    'signal': 'Sociální sítě',
    'whatsapp': 'Sociální sítě',

    // Komunikace
    'phone-numbers': 'Komunikace',
    'phone_numbers': 'Komunikace',
    'email-analysis': 'Komunikace',
    'email_analysis': 'Komunikace',

    // Analýza
    'network-analysis': 'Síťová analýza',
    'network_analysis': 'Síťová analýza',
    'map-overlays': 'Síťová analýza',
    'map_overlays': 'Síťová analýza',
    'ip-addresses': 'Síťová analýza',
    'ip_addresses': 'Síťová analýza',

    // Nemovitosti
    'cadastre': 'Nemovitosti',

    // Osobní údaje
    'training': 'Osobní údaje',
    'evidence-obyvatel': 'Osobní údaje',
    'evidence_obyvatel': 'Osobní údaje',
    'driver-license': 'Osobní údaje',
    'registr_ridicske_prukazy': 'Osobní údaje',
    'gun_license': 'Osobní údaje',
    'vehicles': 'Osobní údaje',
    'locations': 'Osobní údaje',
    'family_members': 'Osobní údaje',

    // Podnikání
    'firma': 'Podnikání',
    'business_activity': 'Podnikání',
    'ares': 'Podnikání',

    // Další moduly
    'notes': 'Ostatní',
    'documents': 'Ostatní',
    'photos': 'Ostatní',
    'connections': 'Ostatní',
    'timeline': 'Ostatní',
    'financial': 'Finanční informace',
    'bank-accounts': 'Finanční informace',
    'criminal-records': 'Trestní záznamy',
    'weapons': 'Zbraně',
    'travel-history': 'Historie cestování',
    'border-crossings': 'Historie cestování',
    'education': 'Vzdělání a zaměstnání',
    'employment': 'Vzdělání a zaměstnání',
    'medical': 'Zdravotní informace',
    'military': 'Bezpečnostní informace',
    'police': 'Bezpečnostní informace',
    'prison': 'Bezpečnostní informace',
    'property': 'Majetek',
    'vehicles-history': 'Historie vozidel',
    'weapons-history': 'Historie zbraní',
    'weapons-license': 'Zbraně',
    'weapons-registration': 'Zbraně'
  };

  // Normalizace typu modulu - odstranění případného ID subjektu
  let normalizedModuleType = moduleType;

  // Odstranění případného ID subjektu z názvu modulu (např. "evidence_obyvatel_abc123" -> "evidence_obyvatel")
  normalizedModuleType = normalizedModuleType.replace(/^([^_]+)_([^_]+)_.+$/, '$1_$2');

  // Pokud je formát jiný, zkusíme ještě jednu variantu
  if (normalizedModuleType.includes('_') && normalizedModuleType.split('_').length > 2) {
    normalizedModuleType = normalizedModuleType.split('_').slice(0, 2).join('_');
  }

  // Pokud máme kategorii pro tento typ modulu, vrátíme ji
  if (moduleCategories[normalizedModuleType]) {
    return moduleCategories[normalizedModuleType];
  }

  // Pokud ne, vrátíme výchozí kategorii
  return 'Ostatní';
}

function getModuleTitle(moduleType: string): string {
  const moduleTitles: { [key: string]: string } = {
    // Sociální sítě
    'facebook': 'Facebook profil',
    'instagram': 'Instagram profil',
    'twitter': 'Twitter profil',
    'linkedin': 'LinkedIn profil',
    'tiktok': 'TikTok profil',
    'youtube': 'YouTube kanál',
    'telegram': 'Telegram účet',
    'signal': 'Signal účet',
    'whatsapp': 'WhatsApp účet',

    // Komunikace
    'phone-numbers': 'Telefonní čísla',
    'email-analysis': 'Analýza e-mailů',

    // Analýza
    'network-analysis': 'Síťová analýza',
    'map-overlays': 'Mapové překryvy',
    'ip-addresses': 'IP adresy',

    // Nemovitosti
    'cadastre': 'Katastr nemovitostí',

    // Osobní údaje
    'training': 'Školení a výcvik',
    'evidence-obyvatel': 'Evidence obyvatel',
    'evidence_obyvatel': 'Evidence obyvatel',
    'driver-license': 'Řidičský průkaz',
    'registr_ridicske_prukazy': 'Registr řidičských průkazů',
    'gun_license': 'Zbrojní průkaz',
    'vehicles': 'Vozidla',
    'locations': 'Lokace',
    'family_members': 'Rodinní příslušníci',

    // Podnikání
    'firma': 'Informace o firmě',
    'business_activity': 'Podnikatelské aktivity',
    'ares': 'ARES - registr ekonomických subjektů',

    // Další moduly
    'notes': 'Poznámky',
    'documents': 'Dokumenty',
    'photos': 'Fotografie',
    'connections': 'Propojení',
    'timeline': 'Časová osa',
    'financial': 'Finanční informace',
    'bank-accounts': 'Bankovní účty',
    'criminal-records': 'Trestní záznamy',
    'weapons': 'Zbraně',
    'travel-history': 'Historie cestování',
    'border-crossings': 'Přechody hranic',
    'education': 'Vzdělání',
    'employment': 'Zaměstnání',
    'medical': 'Zdravotní informace',
    'military': 'Vojenská služba',
    'police': 'Policejní záznamy',
    'prison': 'Vězeňské záznamy',
    'property': 'Majetek',
    'vehicles-history': 'Historie vozidel',
    'weapons-history': 'Historie zbraní',
    'weapons-license': 'Zbrojní průkaz',
    'weapons-registration': 'Registrace zbraní'
  };

  // Normalizace typu modulu - odstranění případného ID subjektu
  let normalizedModuleType = moduleType;

  // Odstranění případného ID subjektu z názvu modulu (např. "evidence_obyvatel_abc123" -> "evidence_obyvatel")
  normalizedModuleType = normalizedModuleType.replace(/^([^_]+)_([^_]+)_.+$/, '$1_$2');

  // Pokud je formát jiný, zkusíme ještě jednu variantu
  if (normalizedModuleType.includes('_') && normalizedModuleType.split('_').length > 2) {
    normalizedModuleType = normalizedModuleType.split('_').slice(0, 2).join('_');
  }

  // Mapování typů modulů na jejich skutečné typy v databázi
  const moduleTypeMapping: Record<string, string> = {
    'personal_info': 'evidence_obyvatel',
    'personal-info': 'evidence_obyvatel',
    'company': 'business_activity',
    'real_estate': 'cadastre',
    'real-estate': 'cadastre',
    'ip_addresses': 'network_analysis',
    'ip-addresses': 'network_analysis'
  };

  // Pokud máme mapování pro tento typ modulu, použijeme ho
  if (moduleTypeMapping[normalizedModuleType]) {
    normalizedModuleType = moduleTypeMapping[normalizedModuleType];
  }

  // Pokud máme název modulu v seznamu, vrátíme ho
  if (moduleTitles[normalizedModuleType]) {
    return moduleTitles[normalizedModuleType];
  }

  // Pokud ne, pokusíme se vytvořit hezký název z moduleType
  // Převedeme snake_case nebo kebab-case na slova s velkým počátečním písmenem
  const formattedModuleType = normalizedModuleType
    .replace(/[-_]/g, ' ')
    .replace(/\b\w/g, (char) => char.toUpperCase());

  return formattedModuleType;
}

function generateModuleContent(moduleType: string, data: any, _caseId: string, _subjectId: string): string {
  console.log(`=== GENEROVÁNÍ MODULU: ${moduleType} ===`);
  console.log(`DEBUG: Po prvním logu`);

  // SPECIÁLNÍ ZPRACOVÁNÍ PRO VEHICLES NA ZAČÁTKU
  console.log(`DEBUG: Před vehicles check, moduleType: "${moduleType}"`);
  if (moduleType === 'vehicles' || moduleType.includes('vehicles')) {
    console.log('VEHICLES: Volám generateVehiclesContent na začátku funkce');
    return generateVehiclesContent(data);
  }
  console.log(`DEBUG: Po vehicles check`);

  try {
    // Kontrola, zda máme data
    if (!data) {
      console.log(`Žádná data pro modul ${moduleType}`);
      return `<div class="module">
        <p>Pro modul ${getModuleTitle(moduleType)} nejsou k dispozici žádná data.</p>
      </div>`;
    }

    // Normalizace typu modulu
    let normalizedModuleType = moduleType.replace(/-/g, '_');

    // Odstranění případného ID subjektu z názvu modulu
    normalizedModuleType = normalizedModuleType.replace(/^([^_]+)_([^_]+)_.+$/, '$1_$2');

    if (normalizedModuleType.includes('_') && normalizedModuleType.split('_').length > 2) {
      normalizedModuleType = normalizedModuleType.split('_').slice(0, 2).join('_');
    }

    console.log(`Normalizovaný typ modulu: ${normalizedModuleType}`);
    console.log(`CHECKPOINT 1: Po normalizaci`);

    // SPECIÁLNÍ ZPRACOVÁNÍ PRO VEHICLES
    console.log(`CHECKPOINT 2: Před vehicles check`);
    if (normalizedModuleType === 'vehicles' || moduleType === 'vehicles') {
      console.log('VEHICLES: Volám generateVehiclesContent přímo');
      return generateVehiclesContent(data);
    }
    console.log(`CHECKPOINT 3: Po vehicles check`);

    // FALLBACK PRO VEHICLES
    if (normalizedModuleType.includes('vehicles') || moduleType.includes('vehicles')) {
      console.log('VEHICLES FALLBACK: Volám generateVehiclesContent');
      return generateVehiclesContent(data);
    }

    // Mapování typů modulů
    const moduleTypeMapping: Record<string, string> = {
      'personal_info': 'evidence_obyvatel',
      'personal-info': 'evidence_obyvatel',
      'company': 'business_activity',
      'real_estate': 'cadastre',
      'real-estate': 'cadastre',
      'ip_addresses': 'network_analysis',
      'ip-addresses': 'network_analysis'
    };

    if (moduleTypeMapping[normalizedModuleType]) {
      normalizedModuleType = moduleTypeMapping[normalizedModuleType];
      console.log(`Mapování typu modulu: ${moduleType} -> ${normalizedModuleType}`);
    }

    switch (normalizedModuleType) {
      case 'evidence_obyvatel':
        console.log('Našel case evidence_obyvatel');
        return generateEvidenceObyvatelContent(data);
      case 'family_members':
        console.log('Našel case family_members');
        return generateFamilyMembersContent(data);
      case 'registr_ridicske_prukazy':
        console.log('Našel case registr_ridicske_prukazy');
        return generateDriverLicenseContent(data);
      case 'gun_license':
        console.log('Našel case gun_license');
        return generateGunLicenseContent(data);
      case 'vehicles':
        console.log('Našel case vehicles - volám generateVehiclesContent');
        return generateVehiclesContent(data);
      case 'locations':
        console.log('Našel case locations');
        return generateLocationsContent(data);
      case 'business_activity':
        console.log('Našel case business_activity');
        return generateBusinessActivityContent(data);
      case 'cadastre':
        console.log('Našel case cadastre');
        return generateCadastreContent(data);
      case 'training':
        console.log('Našel case training');
        return generateTrainingContent(data);
      case 'email_analysis':
      case 'email-analysis':
        console.log('Našel case email_analysis');
        return generateEmailAnalysisContent(data);
      case 'phone_numbers':
      case 'phone-numbers':
        console.log('Našel case phone_numbers');
        return generatePhoneNumbersContent(data);
      case 'network_analysis':
      case 'network-analysis':
        console.log('Našel case network_analysis');
        return generateNetworkAnalysisContent(data);
      case 'map_overlays':
      case 'map-overlays':
        console.log('Našel case map_overlays');
        return generateMapOverlaysContent(data);
      case 'facebook':
        console.log('Našel case facebook');
        return generateFacebookContent(data);
      case 'instagram':
        console.log('Našel case instagram');
        return generateInstagramContent(data);
      case 'twitter':
        console.log('Našel case twitter');
        return generateTwitterContent(data);
      case 'linkedin':
        console.log('Našel case linkedin');
        return generateLinkedInContent(data);
      case 'phone_analysis':
      case 'phone-analysis':
        console.log('Našel case phone_analysis');
        return generatePhoneAnalysisContent(data);
      case 'financial_monitoring':
      case 'financial-monitoring':
        console.log('Našel case financial_monitoring');
        return generateFinancialMonitoringContent(data);
      default:
        console.log(`Používám generický obsah pro modul typu: ${moduleType} (normalized: ${normalizedModuleType})`);
        console.log(`DEBUG: Před voláním generateGenericModuleContent`);
        console.log(`DEBUG: typeof generateGenericModuleContent: ${typeof generateGenericModuleContent}`);
        const result = generateGenericModuleContent(data);
        console.log(`DEBUG: Po volání generateGenericModuleContent`);
        return result;
    }
  } catch (error) {
    console.error(`Chyba při generování obsahu modulu ${moduleType}:`, error);
    return `<div class="module">
      <h3>Modul: ${getModuleTitle(moduleType)}</h3>
      <p class="error">Při generování obsahu modulu došlo k chybě: ${error instanceof Error ? error.message : 'Neznámá chyba'}</p>
      <p>Zkuste to prosím znovu nebo kontaktujte správce systému.</p>
    </div>`;
  }
}

// Funkce pro překlad anglických typů na české názvy
function translateContactType(type: string): string {
  const translations: { [key: string]: string } = {
    // Telefony
    'mobile': 'Mobilní',
    'landline': 'Pevná linka',
    'business': 'Firemní',
    'work': 'Pracovní',
    'home': 'Domácí',
    'personal': 'Osobní',
    'fax': 'Fax',
    'voip': 'VoIP',
    'other': 'Jiný',

    // E-maily
    'primary': 'Hlavní',
    'secondary': 'Vedlejší',
    'company': 'Firemní',
    'school': 'Školní',
    'government': 'Úřední',
    'temporary': 'Dočasný',

    // Sociální sítě
    'facebook': 'Facebook',
    'instagram': 'Instagram',
    'twitter': 'Twitter',
    'linkedin': 'LinkedIn',
    'youtube': 'YouTube',
    'tiktok': 'TikTok',
    'telegram': 'Telegram',
    'whatsapp': 'WhatsApp',
    'signal': 'Signal'
  };

  return translations[type.toLowerCase()] || type;
}

// ===== UNIVERZÁLNÍ FUNKCE PRO FOTODOKUMENTACI =====

function generatePhotoDocumentation(photos: any[], title: string = 'Fotodokumentace'): string {
  if (!photos || photos.length === 0) {
    return '';
  }

  let html = `<div style="margin-top: 20px;">
    <h4>${title} (${photos.length})</h4>
    <div style="display: flex; flex-direction: column; gap: 20px; align-items: center;">`;

  for (let i = 0; i < photos.length; i++) {
    const photo = photos[i];
    const imageSource = photo.downloadURL || photo.url || photo.photoUrl || photo.base64 || photo.imagePath;
    const isValidImagePath = imageSource &&
      (imageSource.startsWith('/') || imageSource.startsWith('http') || imageSource.startsWith('data:image/'));

    if (isValidImagePath) {
      html += `
        <div style="text-align: center; margin-bottom: 20px; max-width: 600px;">
          <img src="${imageSource}"
               alt="${photo.description || 'Fotografie'}"
               style="max-width: 100%; max-height: 400px; border: 1px solid #ddd; border-radius: 5px; display: block; margin: 0 auto;"
               onerror="this.style.border='2px solid red'; this.alt='CHYBA: ' + this.src;" />
          <div style="margin-top: 10px; text-align: center;">
            <div style="font-weight: bold; margin-bottom: 5px;">📷 Fotografie ${i + 1}</div>
            ${photo.description ? `<div style="margin-bottom: 3px;"><strong>Popis:</strong> ${photo.description}</div>` : ''}
            ${photo.dateTaken ? `<div style="margin-bottom: 3px;"><strong>Datum:</strong> ${photo.dateTaken}</div>` : ''}
            ${photo.fileName ? `<div style="margin-bottom: 3px;"><strong>Soubor:</strong> ${photo.fileName}</div>` : ''}
            ${photo.sourceURL ? `<div style="margin-bottom: 3px;"><strong>Zdroj:</strong> ${photo.sourceURL}</div>` : ''}
          </div>
        </div>
      `;
    } else {
      // Placeholder pro nedostupné fotografie
      html += `
        <div style="text-align: center; margin-bottom: 20px; max-width: 600px;">
          <div style="width: 300px; height: 200px; border: 2px dashed #ff6b6b; border-radius: 8px; background: #ffe0e0; display: flex; align-items: center; justify-content: center; flex-direction: column; margin: 0 auto;">
            <div style="font-size: 32px; margin-bottom: 10px;">📷</div>
            <div style="font-size: 14px; color: #d63031; font-weight: bold;">Fotografie nedostupná</div>
            <div style="font-size: 10px; color: #666; margin-top: 5px;">Cesta není k dispozici</div>
          </div>
          <div style="margin-top: 10px; text-align: center;">
            <div style="font-weight: bold; margin-bottom: 5px;">📷 Fotografie ${i + 1} (nedostupná)</div>
            ${photo.description ? `<div style="margin-bottom: 3px;"><strong>Popis:</strong> ${photo.description}</div>` : ''}
            ${photo.dateTaken ? `<div style="margin-bottom: 3px;"><strong>Datum:</strong> ${photo.dateTaken}</div>` : ''}
            ${photo.fileName ? `<div style="margin-bottom: 3px;"><strong>Soubor:</strong> ${photo.fileName}</div>` : ''}
          </div>
        </div>
      `;
    }
  }

  html += `</div></div>`;
  return html;
}

// ===== POMOCNÉ FUNKCE PRO PŘEKLAD =====

function translatePropertyType(type: string): string {
  const translations: { [key: string]: string } = {
    'residential_house': 'Rodinný dům',
    'residential_apartment': 'Byt',
    'commercial': 'Komerční nemovitost',
    'industrial': 'Průmyslová nemovitost',
    'agricultural': 'Zemědělská nemovitost',
    'land': 'Pozemek',
    'forest': 'Les',
    'other': 'Jiné'
  };
  return translations[type] || type;
}

function translateTrainingType(type: string): string {
  const translations: { [key: string]: string } = {
    'professional': 'Profesní',
    'military': 'Vojenské',
    'security': 'Bezpečnostní',
    'weapons': 'Zbraňové',
    'martial_arts': 'Bojová umění',
    'driving': 'Řidičské',
    'aviation': 'Letecké',
    'diving': 'Potápěčské',
    'survival': 'Přežití',
    'medical': 'Zdravotnické',
    'technical': 'Technické',
    'language': 'Jazykové',
    'police': 'Policejní',
    'intelligence': 'Zpravodajské',
    'special_forces': 'Speciální jednotky',
    'combat': 'Bojové',
    'tactical': 'Taktické',
    'explosives': 'Výbušniny',
    'sniper': 'Odstřelovačské',
    'reconnaissance': 'Průzkumné',
    'other': 'Jiné'
  };
  return translations[type] || type;
}

function translateTrainingLevel(level: string): string {
  const translations: { [key: string]: string } = {
    'basic': 'Základní',
    'intermediate': 'Pokročilé',
    'advanced': 'Expertní',
    'expert': 'Expert',
    'instructor': 'Instruktor'
  };
  return translations[level] || level;
}

function translateMapPointType(type: string): string {
  const translations: { [key: string]: string } = {
    'general': 'Obecný',
    'crime': 'Trestný čin',
    'person': 'Osoba',
    'vehicle': 'Vozidlo',
    'building': 'Budova',
    'surveillance': 'Sledování',
    'meeting': 'Setkání',
    'evidence': 'Důkaz',
    'other': 'Jiné'
  };
  return translations[type] || type;
}

function translateMapAreaType(type: string): string {
  const translations: { [key: string]: string } = {
    'general': 'Obecná',
    'crime_area': 'Oblast trestné činnosti',
    'surveillance': 'Oblast sledování',
    'restricted': 'Omezená oblast',
    'search': 'Oblast prohledávání',
    'operation': 'Oblast operace',
    'other': 'Jiná'
  };
  return translations[type] || type;
}

function translateMapRouteType(type: string): string {
  const translations: { [key: string]: string } = {
    'general': 'Obecná',
    'vehicle_route': 'Trasa vozidla',
    'person_route': 'Trasa osoby',
    'escape_route': 'Úniková trasa',
    'patrol': 'Hlídková trasa',
    'surveillance': 'Sledovací trasa',
    'other': 'Jiná'
  };
  return translations[type] || type;
}

function translatePhoneType(type: string): string {
  const translations: { [key: string]: string } = {
    'mobile': 'Mobilní',
    'landline': 'Pevná linka',
    'voip': 'VoIP',
    'toll_free': 'Bezplatná linka',
    'premium_rate': 'Prémiová linka',
    'shared_cost': 'Sdílené náklady',
    'personal_number': 'Osobní číslo',
    'pager': 'Pager',
    'uan': 'UAN',
    'unknown': 'Neznámý'
  };
  return translations[type] || type;
}

function translateFinancialRegistryType(type: string): string {
  const translations: { [key: string]: string } = {
    'business_registry': 'Obchodní rejstřík',
    'ares': 'ARES',
    'insolvency': 'Insolvenční rejstřík',
    'execution': 'Exekuční rejstřík',
    'tax_debtors': 'Seznam daňových dlužníků',
    'vat_registry': 'Registr plátců DPH',
    'cedr_subsidies': 'CEDR dotace',
    'public_contracts': 'Veřejné zakázky',
    'court_proceedings': 'Soudní řízení',
    'property_registry': 'Rejstřík nemovitostí',
    'land_registry': 'Katastr nemovitostí',
    'patents': 'Patenty',
    'trademarks': 'Ochranné známky',
    'databox': 'Datové schránky',
    'other': 'Jiné'
  };
  return translations[type] || type;
}

function translateConnectionType(type: string): string {
  const translations: { [key: string]: string } = {
    'ownership': 'Vlastnictví',
    'management': 'Vedení',
    'partnership': 'Partnerství',
    'supplier': 'Dodavatel',
    'customer': 'Zákazník',
    'competitor': 'Konkurent',
    'subsidiary': 'Dceřiná společnost',
    'parent_company': 'Mateřská společnost',
    'joint_venture': 'Společný podnik',
    'other': 'Jiné'
  };
  return translations[type] || type;
}

function translateAssetType(type: string): string {
  const translations: { [key: string]: string } = {
    'real_estate': 'Nemovitost',
    'vehicle': 'Vozidlo',
    'bank_account': 'Bankovní účet',
    'investment': 'Investice',
    'business_share': 'Obchodní podíl',
    'intellectual_property': 'Duševní vlastnictví',
    'artwork': 'Umělecké dílo',
    'jewelry': 'Šperky',
    'other_valuable': 'Jiné cennosti',
    'other': 'Jiné'
  };
  return translations[type] || type;
}

function generateModernEvidenceObyvatelContent(data: any): string {

  let html = '<div class="modern-evidence-container">';

  // Hlavní informace v krásných kartách
  html += `
    <div class="modern-info-grid">
      <div class="modern-info-card primary">
        <div class="modern-card-header">
          <div class="modern-card-icon person">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M12 11C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3C9.79086 3 8 4.79086 8 7C8 9.20914 9.79086 11 12 11Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3>Osobní údaje</h3>
        </div>
        <div class="modern-card-content">
          <div class="modern-data-row">
            <span class="modern-label">Jméno a příjmení:</span>
            <span class="modern-value primary">${data.fullName || 'Neuvedeno'}</span>
          </div>
          <div class="modern-data-row">
            <span class="modern-label">Datum narození:</span>
            <span class="modern-value">${data.birthDate || data.dateOfBirth || 'Neuvedeno'}</span>
          </div>
          <div class="modern-data-row">
            <span class="modern-label">Místo narození:</span>
            <span class="modern-value">${data.placeOfBirth || 'Neuvedeno'}</span>
          </div>
          <div class="modern-data-row">
            <span class="modern-label">Státní příslušnost:</span>
            <span class="modern-value">${data.nationality || 'Neuvedeno'}</span>
          </div>
          <div class="modern-data-row">
            <span class="modern-label">Rodné číslo:</span>
            <span class="modern-value highlight">${data.personalIdNumber || 'Neuvedeno'}</span>
          </div>
          <div class="modern-data-row">
            <span class="modern-label">Číslo OP:</span>
            <span class="modern-value highlight">${data.idCardNumber || 'Neuvedeno'}</span>
          </div>
          ${data.idCardIssuedBy ? `<div class="modern-data-row">
            <span class="modern-label">OP vydal:</span>
            <span class="modern-value">${data.idCardIssuedBy}</span>
          </div>` : ''}
          ${data.idCardValidity ? `<div class="modern-data-row">
            <span class="modern-label">OP platnost:</span>
            <span class="modern-value">${data.idCardValidity}</span>
          </div>` : ''}
          <div class="modern-data-row">
            <span class="modern-label">Adresa trvalého pobytu:</span>
            <span class="modern-value">${data.permanentAddress || 'Neuvedeno'}</span>
          </div>
        </div>
      </div>`;

  // Kontaktní údaje
  if ((data.phoneNumbers && data.phoneNumbers.length > 0) || (data.emails && data.emails.length > 0)) {
    html += `
      <div class="modern-info-card secondary">
        <div class="modern-card-header">
          <div class="modern-card-icon contact">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M22 16.92V19.92C22.0011 20.1985 21.9441 20.4742 21.8325 20.7293C21.7209 20.9845 21.5573 21.2136 21.3521 21.4019C21.1468 21.5901 20.9046 21.7335 20.6407 21.8227C20.3769 21.9119 20.0974 21.9451 19.82 21.92C16.7428 21.5856 13.787 20.5341 11.19 18.85C8.77382 17.3147 6.72533 15.2662 5.18999 12.85C3.49997 10.2412 2.44824 7.27099 2.11999 4.18C2.095 3.90347 2.12787 3.62476 2.2165 3.36162C2.30513 3.09849 2.44756 2.85669 2.63476 2.65162C2.82196 2.44655 3.0498 2.28271 3.30379 2.17052C3.55777 2.05833 3.83233 2.00026 4.10999 2H7.10999C7.59531 1.99522 8.06588 2.16708 8.43499 2.48353C8.80409 2.79999 9.04981 3.23945 9.10999 3.72C9.22399 4.68007 9.44824 5.62273 9.77999 6.53C9.93344 6.88792 9.97798 7.28454 9.90999 7.67C9.84199 8.05546 9.66408 8.41632 9.39999 8.7L8.07999 10.02C9.51355 12.558 11.442 14.4865 13.98 15.92L15.3 14.6C15.6836 14.3359 16.0445 14.158 16.43 14.09C16.8154 14.022 17.212 14.0665 17.57 14.22C18.4773 14.5518 19.4199 14.776 20.38 14.89C20.8672 14.9502 21.3122 15.2015 21.6279 15.5775C21.9435 15.9535 22.1101 16.4317 22.1 16.92Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3>Kontaktní údaje</h3>
        </div>
        <div class="modern-card-content">`;

    // Telefony
    if (data.phoneNumbers && data.phoneNumbers.length > 0) {
      html += `<div class="modern-subsection">
        <h4 class="modern-subsection-title">Telefonní čísla</h4>`;
      for (const phone of data.phoneNumbers) {
        html += `
          <div class="modern-contact-item">
            <div class="modern-contact-primary">${phone.value || phone.phoneNumber || phone.number || 'Neuvedeno'}</div>
            <div class="modern-contact-type">${translateContactType(phone.subType || phone.type || phone.phoneType || 'Neuvedeno')}</div>
            ${phone.operator ? `<div class="modern-contact-operator">Operátor: ${phone.operator}</div>` : ''}
          </div>
        `;
      }
      html += `</div>`;
    }

    // E-maily
    if (data.emails && data.emails.length > 0) {
      html += `<div class="modern-subsection">
        <h4 class="modern-subsection-title">E-mailové adresy</h4>`;
      for (const email of data.emails) {
        html += `
          <div class="modern-contact-item">
            <div class="modern-contact-primary">${email.value || email.address || email.emailAddress || 'Neuvedeno'}</div>
            <div class="modern-contact-type">${translateContactType(email.subType || email.type || 'Neuvedeno')}</div>
            ${email.provider ? `<div class="modern-contact-provider">Poskytovatel: ${email.provider}</div>` : ''}
          </div>
        `;
      }
      html += `</div>`;
    }

    html += `</div></div>`;
  }

  // Sociální sítě
  if (data.socialProfiles && data.socialProfiles.length > 0) {
    html += `
      <div class="modern-info-card accent">
        <div class="modern-card-header">
          <div class="modern-card-icon social">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M18 2H15C13.6739 2 12.4021 2.52678 11.4645 3.46447C10.5268 4.40215 10 5.67392 10 7V10H7V14H10V22H14V14H17L18 10H14V7C14 6.73478 14.1054 6.48043 14.2929 6.29289C14.4804 6.10536 14.7348 6 15 6H18V2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3>Sociální sítě</h3>
        </div>
        <div class="modern-card-content">`;

    for (const profile of data.socialProfiles) {
      html += `
        <div class="modern-social-item">
          <div class="modern-social-platform">${translateContactType(profile.subType || profile.platform || 'Neuvedeno')}</div>
          <div class="modern-social-url">
            <a href="${profile.value || profile.url || profile.profileUrl || '#'}" target="_blank" rel="noopener noreferrer">
              ${profile.value || profile.url || profile.profileUrl || 'Neuvedeno'}
            </a>
          </div>
        </div>
      `;
    }

    html += `</div></div>`;
  }

  // Přechodné adresy
  if (data.temporaryAddresses && data.temporaryAddresses.length > 0) {
    html += `
      <div class="modern-info-card warning">
        <div class="modern-card-header">
          <div class="modern-card-icon address">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M21 10C21 17 12 23 12 23C12 23 3 17 3 10C3 7.61305 3.94821 5.32387 5.63604 3.63604C7.32387 1.94821 9.61305 1 12 1C14.3869 1 16.6761 1.94821 18.3639 3.63604C20.0518 5.32387 21 7.61305 21 10Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M12 13C13.6569 13 15 11.6569 15 10C15 8.34315 13.6569 7 12 7C10.3431 7 9 8.34315 9 10C9 11.6569 10.3431 13 12 13Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3>Přechodné adresy</h3>
        </div>
        <div class="modern-card-content">`;

    for (let i = 0; i < data.temporaryAddresses.length; i++) {
      const address = data.temporaryAddresses[i];
      html += `
        <div class="modern-address-item">
          <div class="modern-address-primary">${address.address || 'Neuvedeno'}</div>
          ${address.validFrom || address.validTo ? `<div class="modern-address-validity">Platnost: ${address.validFrom || ''} - ${address.validTo || ''}</div>` : ''}
        </div>
      `;
    }

    html += `</div></div>`;
  }

  // Další doklady
  if (data.otherDocuments && data.otherDocuments.length > 0) {
    html += `
      <div class="modern-info-card neutral">
        <div class="modern-card-header">
          <div class="modern-card-icon documents">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16 13H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M16 17H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M10 9H9H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h3>Další doklady</h3>
        </div>
        <div class="modern-card-content">`;

    for (let i = 0; i < data.otherDocuments.length; i++) {
      const doc = data.otherDocuments[i];
      html += `
        <div class="modern-document-item">
          <div class="modern-document-type">${translateContactType(doc.documentType || 'other')}</div>
          <div class="modern-document-number">${doc.documentNumber || 'Neuvedeno'}</div>
          <div class="modern-document-details">
            ${doc.issuedBy ? `<span>Vydal: ${doc.issuedBy}</span>` : ''}
            ${doc.validity ? `<span>Platnost: ${doc.validity}</span>` : ''}
          </div>
        </div>
      `;
    }

    html += `</div></div>`;
  }

  html += `</div>`; // Konec modern-info-grid

  // Poznámky
  if (data.notes) {
    html += `
      <div class="modern-notes-section">
        <div class="modern-notes-header">
          <div class="modern-notes-icon">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M11 4H4C3.46957 4 2.96086 4.21071 2.58579 4.58579C2.21071 4.96086 2 5.46957 2 6V20C2 20.5304 2.21071 21.0391 2.58579 21.4142C2.96086 21.7893 3.46957 22 4 22H18C18.5304 22 19.0391 21.0391 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M18.5 2.5C18.8978 2.10217 19.4374 1.87868 20 1.87868C20.5626 1.87868 21.1022 2.10217 21.5 2.5C21.8978 2.89782 22.1213 3.43739 22.1213 4C22.1213 4.56261 21.8978 5.10217 21.5 5.5L12 15L8 16L9 12L18.5 2.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
          <h4>Poznámky</h4>
        </div>
        <div class="modern-notes-content">${data.notes}</div>
      </div>
    `;
  }

  // Fotodokumentace
  if (data.photos && Array.isArray(data.photos) && data.photos.length > 0) {
    html += generatePhotoDocumentation(data.photos, 'Fotografie osoby');
  }

  html += '</div>';
  return html;
}

function generateEvidenceObyvatelContent(data: any): string {

  let html = '<div class="module">';

  // Základní osobní údaje
  html += `<h3>Osobní údaje</h3>
    <table class="personal-info-table">
      <tr>
        <td>Jméno a příjmení:</td>
        <td>${data.fullName || 'Neuvedeno'}</td>
      </tr>
      ${data.useOriginalScriptName && data.fullNameOriginalScript ? `
      <tr>
        <td>Jméno (originální skript):</td>
        <td>${data.fullNameOriginalScript}</td>
      </tr>` : ''}
      <tr>
        <td>Datum narození:</td>
        <td>${data.birthDate || data.dateOfBirth || 'Neuvedeno'}</td>
      </tr>
      <tr>
        <td>Místo narození:</td>
        <td>${data.placeOfBirth || 'Neuvedeno'}</td>
      </tr>
      <tr>
        <td>Rodné číslo:</td>
        <td>${data.personalIdNumber || 'Neuvedeno'}</td>
      </tr>
      <tr>
        <td>Státní příslušnost:</td>
        <td>${data.nationality || 'Neuvedeno'}</td>
      </tr>
      <tr>
        <td>Číslo OP:</td>
        <td>${data.idCardNumber || 'Neuvedeno'}</td>
      </tr>
      ${data.idCardIssuedBy ? `
      <tr>
        <td>OP vydal:</td>
        <td>${data.idCardIssuedBy}</td>
      </tr>` : ''}
      ${data.idCardValidity ? `
      <tr>
        <td>OP platnost:</td>
        <td>${data.idCardValidity}</td>
      </tr>` : ''}
      <tr>
        <td>Adresa trvalého pobytu:</td>
        <td>${data.permanentAddress || 'Neuvedeno'}</td>
      </tr>
    </table>`;

  // Pokud máme telefonní čísla, zobrazíme je
  if (data.phoneNumbers && Array.isArray(data.phoneNumbers) && data.phoneNumbers.length > 0) {
    html += `<h3>Telefonní čísla</h3>
      <table class="communication-table">`;

    for (const phone of data.phoneNumbers) {
      html += `<tr>
        <td>Číslo:</td>
        <td>${phone.value || phone.phoneNumber || phone.number || 'Neuvedeno'}</td>
      </tr>
      <tr>
        <td>Typ:</td>
        <td>${translateContactType(phone.subType || phone.type || phone.phoneType || 'Neuvedeno')}</td>
      </tr>`;

      if (phone.operator) {
        html += `<tr>
          <td>Operátor:</td>
          <td>${phone.operator}</td>
        </tr>`;
      }

      html += `<tr><td colspan="2"><hr style="border: none; border-top: 1px dashed #ddd; margin: 5px 0;"></td></tr>`;
    }

    html += `</table>`;
  }

  // Pokud máme e-maily, zobrazíme je
  if (data.emails && Array.isArray(data.emails) && data.emails.length > 0) {
    html += `<h3>E-mailové adresy</h3>
      <table class="communication-table">`;

    for (const email of data.emails) {
      html += `<tr>
        <td>E-mail:</td>
        <td>${email.value || email.address || email.emailAddress || 'Neuvedeno'}</td>
      </tr>
      <tr>
        <td>Typ:</td>
        <td>${translateContactType(email.subType || email.type || 'Neuvedeno')}</td>
      </tr>`;

      if (email.provider) {
        html += `<tr>
          <td>Poskytovatel:</td>
          <td>${email.provider}</td>
        </tr>`;
      }

      html += `<tr><td colspan="2"><hr style="border: none; border-top: 1px dashed #ddd; margin: 5px 0;"></td></tr>`;
    }

    html += `</table>`;
  }

  // Pokud máme sociální profily, zobrazíme je
  if (data.socialProfiles && Array.isArray(data.socialProfiles) && data.socialProfiles.length > 0) {
    html += `<h3>Sociální sítě</h3>
      <table class="communication-table">`;

    for (const profile of data.socialProfiles) {
      html += `<tr>
        <td>Síť:</td>
        <td>${translateContactType(profile.subType || profile.platform || 'Neuvedeno')}</td>
      </tr>
      <tr>
        <td>Profil:</td>
        <td>${profile.value || profile.url || profile.profileUrl || 'Neuvedeno'}</td>
      </tr>`;

      html += `<tr><td colspan="2"><hr style="border: none; border-top: 1px dashed #ddd; margin: 5px 0;"></td></tr>`;
    }

    html += `</table>`;
  }

  // Pokud máme dočasné adresy, zobrazíme je
  if (data.temporaryAddresses && Array.isArray(data.temporaryAddresses) && data.temporaryAddresses.length > 0) {
    html += `<h3>Přechodné adresy</h3>
      <table class="communication-table">`;

    for (const address of data.temporaryAddresses) {
      html += `<tr>
        <td>Adresa:</td>
        <td>${address.address || 'Neuvedeno'}</td>
      </tr>`;

      if (address.validFrom || address.validTo) {
        html += `<tr>
          <td>Platnost:</td>
          <td>${address.validFrom || ''} - ${address.validTo || ''}</td>
        </tr>`;
      }

      html += `<tr><td colspan="2"><hr style="border: none; border-top: 1px dashed #ddd; margin: 5px 0;"></td></tr>`;
    }

    html += `</table>`;
  }

  // Pokud máme další doklady, zobrazíme je
  if (data.otherDocuments && Array.isArray(data.otherDocuments) && data.otherDocuments.length > 0) {
    html += `<h3>Další doklady</h3>
      <table class="communication-table">`;

    for (const doc of data.otherDocuments) {
      html += `<tr>
        <td>Typ dokladu:</td>
        <td>${translateContactType(doc.documentType || 'other')}</td>
      </tr>
      <tr>
        <td>Číslo dokladu:</td>
        <td>${doc.documentNumber || 'Neuvedeno'}</td>
      </tr>`;

      if (doc.issuedBy) {
        html += `<tr>
          <td>Vydal:</td>
          <td>${doc.issuedBy}</td>
        </tr>`;
      }

      if (doc.validity) {
        html += `<tr>
          <td>Platnost:</td>
          <td>${doc.validity}</td>
        </tr>`;
      }

      html += `<tr><td colspan="2"><hr style="border: none; border-top: 1px dashed #ddd; margin: 5px 0;"></td></tr>`;
    }

    html += `</table>`;
  }

  // Fotografie
  if (data.photos && Array.isArray(data.photos) && data.photos.length > 0) {

    html += `<div class="photos-section">
      <h3>Fotografie osoby (${data.photos.length})</h3>`;

    for (let i = 0; i < data.photos.length; i++) {
      const photo = data.photos[i];

      // Rozšířené možnosti pro cestu k obrázku
      const imageSource = photo.downloadURL || photo.url || photo.photoUrl || photo.base64 || photo.imagePath;

      if (imageSource && imageSource.trim()) {
        // Kontrola validní cesty
        const isValidPath = imageSource.startsWith('/') ||
                           imageSource.startsWith('http') ||
                           imageSource.startsWith('data:image/');

        if (isValidPath) {
          html += `
            <div class="photo-container">
              <img src="${imageSource}"
                   alt="${photo.description || 'Fotografie osoby'}"
                   onerror="this.style.border='2px solid red'; this.alt='CHYBA: ' + this.src;" />
              <div class="photo-details">
                ${photo.description ? `<div class="photo-description">${photo.description}</div>` : ''}
                ${photo.dateTaken ? `<div class="photo-meta">📅 Datum: ${photo.dateTaken}</div>` : ''}
                ${photo.fileName ? `<div class="photo-meta">📄 Soubor: ${photo.fileName}</div>` : ''}
                ${photo.sourceURL ? `<div class="photo-meta">🔗 Zdroj: ${photo.sourceURL}</div>` : ''}
              </div>
            </div>
          `;
        } else {
          // Nevalidní cesta - zobrazíme placeholder s informacemi
          html += `
            <div class="photo-container" style="border: 2px solid #ff6b6b; background: #fff5f5;">
              <div style="width: 260px; height: 200px; border: 2px dashed #ff6b6b; border-radius: 8px; background: #ffe0e0; display: flex; align-items: center; justify-content: center; flex-direction: column;">
                <div style="font-size: 32px; margin-bottom: 10px;">⚠️</div>
                <div style="font-size: 14px; color: #d63031; font-weight: bold;">Nevalidní cesta</div>
                <div style="font-size: 10px; color: #666; margin-top: 5px; word-break: break-all;">${imageSource}</div>
              </div>
              <div class="photo-details">
                ${photo.description ? `<div class="photo-description">${photo.description}</div>` : ''}
                ${photo.dateTaken ? `<div class="photo-meta">📅 Datum: ${photo.dateTaken}</div>` : ''}
                ${photo.fileName ? `<div class="photo-meta">📄 Soubor: ${photo.fileName}</div>` : ''}
              </div>
            </div>
          `;
        }
      } else {
        // Žádná cesta - zobrazíme placeholder
        html += `
          <div class="photo-container" style="border: 2px solid #ffa500; background: #fff8e1;">
            <div style="width: 260px; height: 200px; border: 2px dashed #ffa500; border-radius: 8px; background: #fff3cd; display: flex; align-items: center; justify-content: center; flex-direction: column;">
              <div style="font-size: 32px; margin-bottom: 10px;">📷</div>
              <div style="font-size: 14px; color: #856404; font-weight: bold;">Bez fotografie</div>
              <div style="font-size: 12px; color: #666; margin-top: 5px;">Cesta není uvedena</div>
            </div>
            <div class="photo-details">
              ${photo.description ? `<div class="photo-description">${photo.description}</div>` : ''}
              ${photo.dateTaken ? `<div class="photo-meta">📅 Datum: ${photo.dateTaken}</div>` : ''}
              ${photo.fileName ? `<div class="photo-meta">📄 Soubor: ${photo.fileName}</div>` : ''}
            </div>
          </div>
        `;
      }
    }

    html += `</div>`;
  }

  // Poznámky
  if (data.notes) {
    html += `<h3>Poznámky</h3>
      <div style="padding: 10px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9;">
        ${data.notes}
      </div>`;
  }

  html += '</div>';
  return html;
}

function generateFacebookContent(data: any): string {
  let html = '<div class="module">';

  // Základní informace o profilu
  html += `<h3>Základní informace</h3>
    <div class="info-grid">
      <div class="info-item">
        <label>Uživatelské jméno:</label>
        <span>${data.username || 'Neuvedeno'}</span>
      </div>
      <div class="info-item">
        <label>Zobrazované jméno:</label>
        <span>${data.displayName || 'Neuvedeno'}</span>
      </div>
      <div class="info-item">
        <label>URL profilu:</label>
        <span>${data.profileUrl || 'Neuvedeno'}</span>
      </div>
      <div class="info-item">
        <label>Ověřený profil:</label>
        <span>${data.isVerified ? 'Ano' : 'Ne'}</span>
      </div>
      <div class="info-item">
        <label>Veřejný profil:</label>
        <span>${data.isPublic ? 'Ano' : 'Ne'}</span>
      </div>
      <div class="info-item">
        <label>Počet přátel:</label>
        <span>${data.friendsCount || 'Neuvedeno'}</span>
      </div>
      <div class="info-item">
        <label>Počet sledujících:</label>
        <span>${data.followersCount || 'Neuvedeno'}</span>
      </div>
      <div class="info-item">
        <label>Datum připojení:</label>
        <span>${data.joinDate || 'Neuvedeno'}</span>
      </div>
    </div>`;

  // Profilový obrázek
  if (data.profileImageUrl || data.profileImageBase64) {
    const imageSource = data.profileImageUrl || data.profileImageBase64;
    html += `<div style="margin-top: 20px;">
      <h4>Profilový obrázek</h4>
      <div style="text-align: center;">
        <img src="${imageSource}" alt="Profilový obrázek" style="max-width: 200px; max-height: 200px; border: 1px solid #ddd; border-radius: 5px;">
      </div>
    </div>`;
  }

  // Úvodní obrázek
  if (data.coverImageUrl || data.coverImageBase64) {
    const imageSource = data.coverImageUrl || data.coverImageBase64;
    html += `<div style="margin-top: 20px;">
      <h4>Úvodní obrázek</h4>
      <div style="text-align: center;">
        <img src="${imageSource}" alt="Úvodní obrázek" style="max-width: 100%; max-height: 200px; border: 1px solid #ddd; border-radius: 5px;">
      </div>
    </div>`;
  }

  // Bio
  if (data.bio) {
    html += `<div style="margin-top: 20px;">
      <h4>O mně</h4>
      <p>${data.bio}</p>
    </div>`;
  }

  // Příspěvky
  if (data.posts && data.posts.length > 0) {
    html += `<div style="margin-top: 20px;">
      <h4>Příspěvky (${data.posts.length})</h4>
      <div class="posts-container">`;

    for (const post of data.posts.slice(0, 5)) { // Omezíme na 5 příspěvků
      html += `<div style="margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
        <div style="margin-bottom: 5px; font-size: 0.9em; color: #666;">
          Datum: ${post.date || 'Neuvedeno'}
        </div>
        <div style="margin-bottom: 10px;">
          ${post.content || 'Bez obsahu'}
        </div>`;

      if (post.imageUrl || post.imageBase64) {
        const imageSource = post.imageUrl || post.imageBase64;
        html += `<div style="text-align: center; margin: 10px 0;">
          <img src="${imageSource}" alt="Obrázek příspěvku" style="max-width: 100%; max-height: 200px; border: 1px solid #eee; border-radius: 5px;">
        </div>`;
      }

      html += `<div style="font-size: 0.8em; color: #666;">
        To se mi líbí: ${post.likes || '0'} | Komentářů: ${post.comments || '0'} | Sdílení: ${post.shares || '0'}
      </div>
      </div>`;
    }

    html += `</div></div>`;
  }

  html += '</div>';
  return html;
}

function generateInstagramContent(data: any): string {
  let html = '<div class="module">';

  // Základní informace o profilu
  html += `<h3>Základní informace</h3>
    <div class="info-grid">
      <div class="info-item">
        <label>Uživatelské jméno:</label>
        <span>${data.username || 'Neuvedeno'}</span>
      </div>
      <div class="info-item">
        <label>Zobrazované jméno:</label>
        <span>${data.displayName || 'Neuvedeno'}</span>
      </div>
      <div class="info-item">
        <label>URL profilu:</label>
        <span>${data.profileUrl || 'Neuvedeno'}</span>
      </div>
      <div class="info-item">
        <label>Ověřený profil:</label>
        <span>${data.isVerified ? 'Ano' : 'Ne'}</span>
      </div>
      <div class="info-item">
        <label>Soukromý profil:</label>
        <span>${data.isPrivate ? 'Ano' : 'Ne'}</span>
      </div>
      <div class="info-item">
        <label>Počet sledujících:</label>
        <span>${data.followersCount || 'Neuvedeno'}</span>
      </div>
      <div class="info-item">
        <label>Počet sledovaných:</label>
        <span>${data.followingCount || 'Neuvedeno'}</span>
      </div>
      <div class="info-item">
        <label>Počet příspěvků:</label>
        <span>${data.postsCount || 'Neuvedeno'}</span>
      </div>
    </div>`;

  // Profilový obrázek
  if (data.profileImageUrl || data.profileImageBase64) {
    const imageSource = data.profileImageUrl || data.profileImageBase64;
    html += `<div style="margin-top: 20px;">
      <h4>Profilový obrázek</h4>
      <div style="text-align: center;">
        <img src="${imageSource}" alt="Profilový obrázek" style="max-width: 200px; max-height: 200px; border: 1px solid #ddd; border-radius: 5px;">
      </div>
    </div>`;
  }

  // Bio
  if (data.bio) {
    html += `<div style="margin-top: 20px;">
      <h4>Bio</h4>
      <p>${data.bio}</p>
    </div>`;
  }

  // Příspěvky
  if (data.posts && data.posts.length > 0) {
    html += `<div style="margin-top: 20px;">
      <h4>Příspěvky (${data.posts.length})</h4>
      <div class="posts-container">`;

    for (const post of data.posts.slice(0, 5)) { // Omezíme na 5 příspěvků
      html += `<div style="margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
        <div style="margin-bottom: 5px; font-size: 0.9em; color: #666;">
          Datum: ${post.date || 'Neuvedeno'}
        </div>
        <div style="margin-bottom: 10px;">
          ${post.caption || 'Bez popisku'}
        </div>`;

      if (post.imageUrl || post.imageBase64) {
        const imageSource = post.imageUrl || post.imageBase64;
        html += `<div style="text-align: center; margin: 10px 0;">
          <img src="${imageSource}" alt="Obrázek příspěvku" style="max-width: 100%; max-height: 200px; border: 1px solid #eee; border-radius: 5px;">
        </div>`;
      }

      html += `<div style="font-size: 0.8em; color: #666;">
        To se mi líbí: ${post.likes || '0'} | Komentářů: ${post.comments || '0'}
      </div>
      </div>`;
    }

    html += `</div></div>`;
  }

  html += '</div>';
  return html;
}

function generatePhoneNumbersContent(data: any): string {
  let html = '<div class="module">';

  if (data.phones && data.phones.length > 0) {
    html += `<h3>Telefonní čísla (${data.phones.length})</h3>`;

    for (const phone of data.phones) {
      html += `<div style="margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
        <div class="info-grid">
          <div class="info-item">
            <label>Telefonní číslo:</label>
            <span>${phone.phoneNumber || 'Neuvedeno'}</span>
          </div>
          <div class="info-item">
            <label>Zdroj:</label>
            <span>${phone.source || 'Neuvedeno'}</span>
          </div>
          <div class="info-item">
            <label>Stav ověření:</label>
            <span>${phone.verificationStatus || 'Neuvedeno'}</span>
          </div>
          <div class="info-item">
            <label>Datum objevení:</label>
            <span>${phone.discoveryDate || 'Neuvedeno'}</span>
          </div>
        </div>`;

      if (phone.notes) {
        html += `<div style="margin-top: 10px;">
          <strong>Poznámky:</strong>
          <p>${phone.notes}</p>
        </div>`;
      }

      // Fotografie
      if (phone.photos && phone.photos.length > 0) {
        html += `<div style="margin-top: 10px;">
          <strong>Fotografie (${phone.photos.length}):</strong>
          <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 5px;">`;

        for (const photo of phone.photos.slice(0, 3)) { // Omezíme na 3 fotografie
          if (photo.downloadURL) {
            html += `<div style="text-align: center;">
              <img src="${photo.downloadURL}" alt="${photo.description || 'Fotografie'}" style="max-width: 150px; max-height: 150px; border: 1px solid #eee; border-radius: 5px;">
              <div style="font-size: 0.8em; color: #666; margin-top: 3px;">${photo.description || ''}</div>
            </div>`;
          }
        }

        html += `</div></div>`;
      }

      html += `</div>`;
    }
  } else {
    html += `<h3>Telefonní čísla</h3>
      <p>Žádná telefonní čísla nebyla nalezena.</p>`;
  }

  // Obecné poznámky
  if (data.generalNotes) {
    html += `<div style="margin-top: 20px;">
      <h4>Obecné poznámky</h4>
      <p>${data.generalNotes}</p>
    </div>`;
  }

  html += '</div>';
  return html;
}

function generateFamilyMembersContent(data: any): string {
  let html = '<div class="module">';

  if (!data || !data.familyMembers || data.familyMembers.length === 0) {
    html += '<p>Žádní rodinní příslušníci nebyli zaznamenáni.</p>';
    html += '</div>';
    return html;
  }

  // Přehled počtu příslušníků
  html += `<p>Celkový počet zaznamenaných rodinných příslušníků: <strong>${data.familyMembers.length}</strong></p>`;

  // Tabulka příslušníků
  html += `<table style="width: 100%; border-collapse: collapse; margin-top: 15px;">
    <thead>
      <tr style="background-color: #f2f2f2;">
        <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Jméno a příjmení</th>
        <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Vztah</th>
        <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Datum narození</th>
        <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Adresa</th>
        <th style="padding: 8px; text-align: left; border: 1px solid #ddd;">Stav</th>
      </tr>
    </thead>
    <tbody>`;

  // Mapování typů vztahů na české názvy
  const relationshipLabels: { [key: string]: string } = {
    'mother': 'Matka',
    'father': 'Otec',
    'sibling': 'Sourozenec',
    'spouse': 'Manžel/ka',
    'girlfriend': 'Přítelkyně',
    'boyfriend': 'Přítel',
    'child': 'Dítě',
    'child-in-law': 'Zeť/Snacha',
    'parent-in-law': 'Tchán/Tchyně',
    'grandparent': 'Prarodič',
    'grandchild': 'Vnouče',
    'other': 'Jiný'
  };

  const relationshipStatusLabels: { [key: string]: string } = {
    'active': 'Aktivní',
    'inactive': 'Neaktivní',
    'strained': 'Napjatý',
    'hostile': 'Nepřátelský',
    'unknown': 'Neznámý'
  };

  const relationshipIntensityLabels: { [key: string]: string } = {
    'very_close': 'Velmi blízký',
    'close': 'Blízký',
    'regular': 'Pravidelný',
    'occasional': 'Občasný',
    'minimal': 'Minimální',
    'unknown': 'Neznámý'
  };

  // Přidání řádků pro každého příslušníka
  for (const member of data.familyMembers) {
    const relationshipLabel = member.relationshipType ?
      (relationshipLabels[member.relationshipType] || 'Jiný') : 'Nespecifikováno';

    const relationshipText = member.relationshipType === 'other' && member.relationshipOtherDetail ?
      `${relationshipLabel} (${member.relationshipOtherDetail})` : relationshipLabel;

    const status = member.isDeceased ?
      `Zemřelý/á${member.dateOfDeath ? ` (${member.dateOfDeath})` : ''}` : 'Žijící';

    const address = member.permanentAddress || member.currentAddress || 'Neuvedeno';

    html += `<tr style="border-bottom: 1px solid #ddd;">
      <td style="padding: 8px; border: 1px solid #ddd;">${member.fullName || 'Neuvedeno'}</td>
      <td style="padding: 8px; border: 1px solid #ddd;">${relationshipText}</td>
      <td style="padding: 8px; border: 1px solid #ddd;">${member.dateOfBirth || 'Neuvedeno'}</td>
      <td style="padding: 8px; border: 1px solid #ddd;">${address}</td>
      <td style="padding: 8px; border: 1px solid #ddd;">${status}</td>
    </tr>`;
  }

  html += `</tbody></table>`;

  // Detailní informace o každém příslušníkovi
  html += '<div style="margin-top: 30px;">';
  html += '<h4>Detailní informace o příslušnících</h4>';

  for (const member of data.familyMembers) {
    html += `<div style="margin-top: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; page-break-inside: avoid;">`;
    html += `<h5 style="margin-top: 0; color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">${member.fullName || 'Neznámé jméno'}</h5>`;

    // Základní informace
    html += `<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 15px;">`;
    html += `<div><strong>Vztah:</strong> ${member.relationshipType ? (relationshipLabels[member.relationshipType] || 'Jiný') : 'Nespecifikováno'}</div>`;

    if (member.relationshipType === 'other' && member.relationshipOtherDetail) {
      html += `<div><strong>Upřesnění vztahu:</strong> ${member.relationshipOtherDetail}</div>`;
    }

    if (member.originalScriptName) {
      html += `<div><strong>Jméno (originální skript):</strong> ${member.originalScriptName}</div>`;
    }

    if (member.aliases) {
      html += `<div><strong>Aliasy a přezdívky:</strong> ${member.aliases}</div>`;
    }

    html += `<div><strong>Datum narození:</strong> ${member.dateOfBirth || 'Neuvedeno'}</div>`;

    if (member.placeOfBirth) {
      html += `<div><strong>Místo narození:</strong> ${member.placeOfBirth}</div>`;
    }

    if (member.personalIdNumber) {
      html += `<div><strong>Rodné číslo / ID:</strong> ${member.personalIdNumber}</div>`;
    }

    if (member.nationality) {
      html += `<div><strong>Státní příslušnost:</strong> ${member.nationality}</div>`;
    }

    if (member.permanentAddress) {
      html += `<div><strong>Trvalé bydliště:</strong> ${member.permanentAddress}</div>`;
    }

    if (member.currentAddress && member.currentAddress !== member.permanentAddress) {
      html += `<div><strong>Současné bydliště:</strong> ${member.currentAddress}</div>`;
    }

    // Informace o vztahu
    if (member.relationshipStatus && member.relationshipStatus !== 'unknown') {
      html += `<div><strong>Stav vztahu:</strong> ${relationshipStatusLabels[member.relationshipStatus] || member.relationshipStatus}</div>`;
    }

    if (member.relationshipIntensity && member.relationshipIntensity !== 'unknown') {
      html += `<div><strong>Intenzita vztahu:</strong> ${relationshipIntensityLabels[member.relationshipIntensity] || member.relationshipIntensity}</div>`;
    }

    if (member.contactFrequency) {
      html += `<div><strong>Frekvence kontaktu:</strong> ${member.contactFrequency}</div>`;
    }

    if (member.relationshipStartDate) {
      html += `<div><strong>Vztah od:</strong> ${member.relationshipStartDate}</div>`;
    }

    if (member.relationshipEndDate) {
      html += `<div><strong>Vztah do:</strong> ${member.relationshipEndDate}</div>`;
    }

    if (member.lastContact) {
      html += `<div><strong>Poslední kontakt:</strong> ${member.lastContact}</div>`;
    }

    html += `<div><strong>Stav:</strong> ${member.isDeceased ? 'Zemřelý/á' : 'Žijící'}</div>`;

    if (member.isDeceased) {
      if (member.dateOfDeath) {
        html += `<div><strong>Datum úmrtí:</strong> ${member.dateOfDeath}</div>`;
      }
      if (member.placeOfDeath) {
        html += `<div><strong>Místo úmrtí:</strong> ${member.placeOfDeath}</div>`;
      }
      if (member.causeOfDeath) {
        html += `<div><strong>Příčina úmrtí:</strong> ${member.causeOfDeath}</div>`;
      }
    }
    html += `</div>`;

    // Kontaktní informace
    if (member.contactInfo && member.contactInfo.length > 0) {
      html += `<div style="margin-top: 15px;">
        <strong>Kontaktní informace:</strong>
        <ul style="margin-top: 5px;">`;

      for (const contact of member.contactInfo) {
        const contactType = contact.type === 'phone' ? 'Telefon' :
                           contact.type === 'email' ? 'E-mail' :
                           contact.type === 'social' ? 'Sociální síť' :
                           contact.type === 'address' ? 'Adresa' : contact.type;

        html += `<li>${contactType}: ${contact.value}`;

        if (contact.platform) {
          html += ` (${contact.platform})`;
        }

        if (contact.verified) {
          html += ` <span style="color: green; font-size: 12px;">[Ověřeno]</span>`;
        }

        if (!contact.active) {
          html += ` <span style="color: red; font-size: 12px;">[Neaktivní]</span>`;
        }

        html += `</li>`;
      }

      html += `</ul></div>`;
    }

    // Fyzické charakteristiky
    if (member.physicalDescription) {
      const physDesc = member.physicalDescription;
      const hasPhysicalInfo = physDesc.height || physDesc.weight || physDesc.hairColor ||
                             physDesc.eyeColor || physDesc.build || physDesc.distinguishingMarks ||
                             physDesc.otherFeatures;

      if (hasPhysicalInfo) {
        html += `<div style="margin-top: 15px;">
          <strong>Fyzické charakteristiky:</strong>
          <ul style="margin-top: 5px;">`;

        if (physDesc.height) html += `<li>Výška: ${physDesc.height}</li>`;
        if (physDesc.weight) html += `<li>Váha: ${physDesc.weight}</li>`;
        if (physDesc.hairColor) html += `<li>Barva vlasů: ${physDesc.hairColor}</li>`;
        if (physDesc.eyeColor) html += `<li>Barva očí: ${physDesc.eyeColor}</li>`;
        if (physDesc.build) html += `<li>Postava: ${physDesc.build}</li>`;
        if (physDesc.distinguishingMarks) html += `<li>Rozpoznávací znaky: ${physDesc.distinguishingMarks}</li>`;
        if (physDesc.otherFeatures) html += `<li>Ostatní znaky: ${physDesc.otherFeatures}</li>`;

        html += `</ul></div>`;
      }
    }

    // Profesní informace
    if (member.professionalInfo) {
      const profInfo = member.professionalInfo;
      const hasProfessionalInfo = profInfo.currentJob || profInfo.employer || profInfo.workAddress ||
                                 profInfo.previousJobs || profInfo.education || profInfo.skills || profInfo.income;

      if (hasProfessionalInfo) {
        html += `<div style="margin-top: 15px;">
          <strong>Profesní informace:</strong>
          <ul style="margin-top: 5px;">`;

        if (profInfo.currentJob) html += `<li>Současné zaměstnání: ${profInfo.currentJob}</li>`;
        if (profInfo.employer) html += `<li>Zaměstnavatel: ${profInfo.employer}</li>`;
        if (profInfo.workAddress) html += `<li>Adresa zaměstnání: ${profInfo.workAddress}</li>`;
        if (profInfo.previousJobs) html += `<li>Předchozí zaměstnání: ${profInfo.previousJobs}</li>`;
        if (profInfo.education) html += `<li>Vzdělání: ${profInfo.education}</li>`;
        if (profInfo.skills) html += `<li>Dovednosti: ${profInfo.skills}</li>`;
        if (profInfo.income) html += `<li>Příjem: ${profInfo.income}</li>`;

        html += `</ul></div>`;
      }
    }

    // OSINT informace
    if (member.informationSource || member.sourceDetail || member.informationReliability ||
        member.verificationDate || member.lastSeen) {
      html += `<div style="margin-top: 15px;">
        <strong>OSINT informace:</strong>
        <ul style="margin-top: 5px;">`;

      if (member.informationSource) {
        const sourceLabels: { [key: string]: string } = {
          'social_media': 'Sociální média',
          'surveillance': 'Sledování',
          'witness': 'Svědek',
          'documents': 'Dokumenty',
          'phone_analysis': 'Analýza telefonu',
          'investigation': 'Vyšetřování',
          'open_source': 'Otevřené zdroje',
          'other': 'Jiný'
        };
        html += `<li>Zdroj informací: ${sourceLabels[member.informationSource] || member.informationSource}</li>`;
      }

      if (member.sourceDetail) html += `<li>Detail zdroje: ${member.sourceDetail}</li>`;
      if (member.informationReliability && member.informationReliability !== 'unknown') {
        const reliabilityLabels: { [key: string]: string } = {
          'verified': 'Ověřeno',
          'probable': 'Pravděpodobné',
          'possible': 'Možné',
          'unconfirmed': 'Nepotvrzeno',
          'unknown': 'Neznámo'
        };
        html += `<li>Spolehlivost: ${reliabilityLabels[member.informationReliability] || member.informationReliability}</li>`;
      }
      if (member.verificationDate) html += `<li>Datum ověření: ${member.verificationDate}</li>`;
      if (member.lastSeen) html += `<li>Naposledy viděn: ${member.lastSeen}</li>`;

      html += `</ul></div>`;
    }

    // Bezpečnostní hodnocení
    if (member.securityAssessment) {
      const secAssess = member.securityAssessment;
      const hasSecurityInfo = secAssess.weaponAccess || secAssess.weaponTraining || secAssess.criminalHistory ||
                             (secAssess.threatLevel && secAssess.threatLevel !== 'unknown') ||
                             secAssess.threatAssessment || secAssess.surveillanceNotes;

      if (hasSecurityInfo) {
        html += `<div style="margin-top: 15px;">
          <strong>Bezpečnostní hodnocení:</strong>
          <ul style="margin-top: 5px;">`;

        if (secAssess.weaponAccess) html += `<li>Přístup ke zbraním: Ano</li>`;
        if (secAssess.weaponTraining) html += `<li>Výcvik se zbraněmi: Ano</li>`;
        if (secAssess.criminalHistory) html += `<li>Trestní minulost: ${secAssess.criminalHistory}</li>`;

        if (secAssess.threatLevel && secAssess.threatLevel !== 'unknown') {
          const threatLabels: { [key: string]: string } = {
            'none': 'Žádná',
            'low': 'Nízká',
            'medium': 'Střední',
            'high': 'Vysoká',
            'critical': 'Kritická',
            'unknown': 'Neznámá'
          };
          html += `<li>Úroveň hrozby: ${threatLabels[secAssess.threatLevel] || secAssess.threatLevel}</li>`;
        }

        if (secAssess.threatAssessment) html += `<li>Hodnocení hrozby: ${secAssess.threatAssessment}</li>`;
        if (secAssess.surveillanceNotes) html += `<li>Poznámky ze sledování: ${secAssess.surveillanceNotes}</li>`;

        html += `</ul></div>`;
      }
    }

    // Poznámky
    if (member.investigationNotes || member.behaviorNotes || member.notes) {
      html += `<div style="margin-top: 15px;">
        <strong>Poznámky:</strong>`;

      if (member.investigationNotes) {
        html += `<div style="margin-top: 5px;">
          <em>Vyšetřovací poznámky:</em>
          <p style="margin-top: 2px; white-space: pre-wrap; background-color: #f8f9fa; padding: 8px; border-radius: 4px;">${member.investigationNotes}</p>
        </div>`;
      }

      if (member.behaviorNotes) {
        html += `<div style="margin-top: 8px;">
          <em>Poznámky o chování:</em>
          <p style="margin-top: 2px; white-space: pre-wrap; background-color: #f8f9fa; padding: 8px; border-radius: 4px;">${member.behaviorNotes}</p>
        </div>`;
      }

      if (member.notes) {
        html += `<div style="margin-top: 8px;">
          <em>Obecné poznámky:</em>
          <p style="margin-top: 2px; white-space: pre-wrap; background-color: #f8f9fa; padding: 8px; border-radius: 4px;">${member.notes}</p>
        </div>`;
      }

      html += `</div>`;
    }

    // Fotografie
    if (member.photos && member.photos.length > 0) {
      html += `<div style="margin-top: 15px;">
        <strong>Fotografie (${member.photos.length}):</strong>
        <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 10px;">`;

      for (const photo of member.photos) {
        const imageSource = photo.downloadURL || photo.sourceURL || '';
        if (imageSource) {
          html += `<div style="width: 150px; border: 1px solid #ddd; border-radius: 5px; padding: 5px; background-color: #f9f9f9;">
            <img src="${imageSource}" alt="Fotografie" style="width: 100%; height: 120px; object-fit: cover; border-radius: 3px;">`;

          if (photo.description) {
            html += `<p style="margin: 5px 0 2px 0; font-size: 11px; line-height: 1.3;">${photo.description}</p>`;
          }

          if (photo.dateTaken) {
            html += `<p style="margin: 2px 0; font-size: 10px; color: #666;">Datum: ${photo.dateTaken}</p>`;
          }

          if (photo.sourceURL && photo.sourceURL !== imageSource) {
            html += `<p style="margin: 2px 0; font-size: 10px; color: #666;">Zdroj: ${photo.sourceURL.length > 30 ? photo.sourceURL.substring(0, 30) + '...' : photo.sourceURL}</p>`;
          }

          if (photo.verified) {
            html += `<p style="margin: 2px 0; font-size: 10px; color: green;">✓ Ověřeno</p>`;
          }

          html += `</div>`;
        }
      }

      html += `</div></div>`;
    }

    html += `</div>`;
  }

  // Přehledové informace o rodině
  if (data.familyOverview || data.familyDynamics || data.keyRelationships ||
      data.securityConcerns || data.investigationPriorities || data.generalNotes) {

    html += '<div style="margin-top: 30px; padding: 15px; border: 2px solid #2563eb; border-radius: 8px; background-color: #f8fafc;">';
    html += '<h4 style="margin-top: 0; color: #2563eb;">Přehledové informace o rodině</h4>';

    if (data.familyOverview) {
      html += `<div style="margin-bottom: 15px;">
        <strong>Přehled rodiny:</strong>
        <p style="margin-top: 5px; white-space: pre-wrap;">${data.familyOverview}</p>
      </div>`;
    }

    if (data.familyDynamics) {
      html += `<div style="margin-bottom: 15px;">
        <strong>Rodinná dynamika:</strong>
        <p style="margin-top: 5px; white-space: pre-wrap;">${data.familyDynamics}</p>
      </div>`;
    }

    if (data.keyRelationships) {
      html += `<div style="margin-bottom: 15px;">
        <strong>Klíčové vztahy:</strong>
        <p style="margin-top: 5px; white-space: pre-wrap;">${data.keyRelationships}</p>
      </div>`;
    }

    if (data.securityConcerns) {
      html += `<div style="margin-bottom: 15px;">
        <strong>Bezpečnostní obavy:</strong>
        <p style="margin-top: 5px; white-space: pre-wrap; color: #dc2626;">${data.securityConcerns}</p>
      </div>`;
    }

    if (data.investigationPriorities) {
      html += `<div style="margin-bottom: 15px;">
        <strong>Priority vyšetřování:</strong>
        <p style="margin-top: 5px; white-space: pre-wrap; color: #059669;">${data.investigationPriorities}</p>
      </div>`;
    }

    if (data.generalNotes) {
      html += `<div style="margin-bottom: 10px;">
        <strong>Obecné poznámky:</strong>
        <p style="margin-top: 5px; white-space: pre-wrap;">${data.generalNotes}</p>
      </div>`;
    }

    html += '</div>';
  }

  html += '</div>';
  html += '</div>';
  return html;
}

function generateEmailAnalysisContent(data: any): string {
  let html = '<div class="module">';

  if (data.emails && data.emails.length > 0) {
    html += `<h3>E-mailové adresy (${data.emails.length})</h3>`;

    for (const email of data.emails) {
      html += `<div style="margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
        <div class="info-grid">
          <div class="info-item">
            <label>E-mailová adresa:</label>
            <span>${email.address || 'Neuvedeno'}</span>
          </div>
          <div class="info-item">
            <label>Zdroj:</label>
            <span>${email.source || 'Neuvedeno'}</span>
          </div>
          <div class="info-item">
            <label>Stav ověření:</label>
            <span>${email.verificationStatus || 'Neuvedeno'}</span>
          </div>
          <div class="info-item">
            <label>Datum objevení:</label>
            <span>${email.discoveryDate || 'Neuvedeno'}</span>
          </div>
        </div>`;

      if (email.notes) {
        html += `<div style="margin-top: 10px;">
          <strong>Poznámky:</strong>
          <p>${email.notes}</p>
        </div>`;
      }

      html += `</div>`;
    }
  } else {
    html += `<h3>E-mailové adresy</h3>
      <p>Žádné e-mailové adresy nebyly nalezeny.</p>`;
  }

  // Obecné poznámky
  if (data.generalNotes) {
    html += `<div style="margin-top: 20px;">
      <h4>Obecné poznámky</h4>
      <p>${data.generalNotes}</p>
    </div>`;
  }

  html += '</div>';
  return html;
}

// ===== NOVÉ FUNKCE PRO GENEROVÁNÍ MODULŮ =====

function generateCadastreContent(data: any): string {
  let html = '<div class="module">';

  html += '<h3>Katastr nemovitostí</h3>';

  if (data.properties && data.properties.length > 0) {
    html += `<h4>Nemovitosti (${data.properties.length})</h4>`;

    for (let i = 0; i < data.properties.length; i++) {
      const property = data.properties[i];
      html += `
        <div style="margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9;">
          <h5>Nemovitost ${i + 1}</h5>
          <table class="personal-info-table">
            <tr><td>Typ nemovitosti:</td><td>${translatePropertyType(property.propertyType) || 'Neuvedeno'}</td></tr>
            <tr><td>Adresa:</td><td>${property.address || 'Neuvedeno'}</td></tr>
            <tr><td>Katastrální území:</td><td>${property.cadastralArea || 'Neuvedeno'}</td></tr>
            <tr><td>Parcelní číslo:</td><td>${property.parcelNumber || 'Neuvedeno'}</td></tr>
            <tr><td>LV číslo:</td><td>${property.lvNumber || 'Neuvedeno'}</td></tr>
            <tr><td>Výměra:</td><td>${property.area || 'Neuvedeno'}</td></tr>
            <tr><td>Vlastnictví:</td><td>${property.ownershipType || 'Neuvedeno'}</td></tr>
            <tr><td>Podíl vlastnictví:</td><td>${property.ownershipShare || 'Neuvedeno'}</td></tr>
            <tr><td>Datum nabytí:</td><td>${property.acquisitionDate || 'Neuvedeno'}</td></tr>
            <tr><td>Způsob nabytí:</td><td>${property.acquisitionMethod || 'Neuvedeno'}</td></tr>
            <tr><td>Odhadní hodnota:</td><td>${property.estimatedValue ? `${property.estimatedValue} ${property.currency || 'CZK'}` : 'Neuvedeno'}</td></tr>
          </table>
          ${property.description ? `<div style="margin-top: 10px;"><strong>Popis:</strong> ${property.description}</div>` : ''}
          ${property.notes ? `<div style="margin-top: 10px;"><strong>Poznámky:</strong> ${property.notes}</div>` : ''}
        </div>
      `;
    }
  } else {
    html += '<p>Žádné nemovitosti nebyly nalezeny.</p>';
  }

  // Fotodokumentace
  if (data.photos && data.photos.length > 0) {
    html += generatePhotoDocumentation(data.photos, 'Fotodokumentace nemovitostí');
  }

  if (data.generalNotes) {
    html += `<div style="margin-top: 20px;">
      <h4>Obecné poznámky</h4>
      <p>${data.generalNotes}</p>
    </div>`;
  }

  html += '</div>';
  return html;
}

function generateTrainingContent(data: any): string {
  let html = '<div class="module">';

  html += '<h3>Školení a výcvik</h3>';

  if (data.trainings && data.trainings.length > 0) {
    html += `<h4>Absolvované školení (${data.trainings.length})</h4>`;

    for (let i = 0; i < data.trainings.length; i++) {
      const training = data.trainings[i];
      html += `
        <div style="margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9;">
          <h5>Školení ${i + 1}</h5>
          <table class="personal-info-table">
            <tr><td>Název školení:</td><td>${training.trainingName || 'Neuvedeno'}</td></tr>
            <tr><td>Typ školení:</td><td>${translateTrainingType(training.trainingType) || 'Neuvedeno'}</td></tr>
            <tr><td>Poskytovatel:</td><td>${training.provider || 'Neuvedeno'}</td></tr>
            <tr><td>Datum zahájení:</td><td>${training.startDate || 'Neuvedeno'}</td></tr>
            <tr><td>Datum ukončení:</td><td>${training.endDate || 'Neuvedeno'}</td></tr>
            <tr><td>Místo konání:</td><td>${training.location || 'Neuvedeno'}</td></tr>
            <tr><td>Délka školení:</td><td>${training.duration || 'Neuvedeno'}</td></tr>
            <tr><td>Certifikát:</td><td>${training.certificateNumber || 'Neuvedeno'}</td></tr>
            <tr><td>Platnost do:</td><td>${training.validUntil || 'Neuvedeno'}</td></tr>
            <tr><td>Úroveň:</td><td>${translateTrainingLevel(training.level) || 'Neuvedeno'}</td></tr>
          </table>
          ${training.description ? `<div style="margin-top: 10px;"><strong>Popis:</strong> ${training.description}</div>` : ''}
          ${training.notes ? `<div style="margin-top: 10px;"><strong>Poznámky:</strong> ${training.notes}</div>` : ''}
        </div>
      `;
    }
  } else {
    html += '<p>Žádná školení nebyla nalezena.</p>';
  }

  // Fotodokumentace
  if (data.photos && data.photos.length > 0) {
    html += generatePhotoDocumentation(data.photos, 'Fotodokumentace školení');
  }

  if (data.generalNotes) {
    html += `<div style="margin-top: 20px;">
      <h4>Obecné poznámky</h4>
      <p>${data.generalNotes}</p>
    </div>`;
  }

  html += '</div>';
  return html;
}

function generateNetworkAnalysisContent(data: any): string {
  let html = '<div class="module">';

  html += '<h3>Síťová analýza</h3>';

  if (data.ipAddresses && data.ipAddresses.length > 0) {
    html += `<h4>IP adresy (${data.ipAddresses.length})</h4>`;
    html += '<table class="personal-info-table">';
    html += '<tr><th>IP adresa</th><th>Typ</th><th>Geolokace</th><th>ISP</th><th>Poznámky</th></tr>';

    for (const ip of data.ipAddresses) {
      html += `
        <tr>
          <td>${ip.ipAddress || 'Neuvedeno'}</td>
          <td>${ip.type || 'Neuvedeno'}</td>
          <td>${ip.geolocation || 'Neuvedeno'}</td>
          <td>${ip.isp || 'Neuvedeno'}</td>
          <td>${ip.notes || ''}</td>
        </tr>
      `;
    }
    html += '</table>';
  }

  if (data.domains && data.domains.length > 0) {
    html += `<h4>Domény (${data.domains.length})</h4>`;
    html += '<table class="personal-info-table">';
    html += '<tr><th>Doména</th><th>Registrátor</th><th>Datum registrace</th><th>Datum expirace</th><th>Poznámky</th></tr>';

    for (const domain of data.domains) {
      html += `
        <tr>
          <td>${domain.domainName || 'Neuvedeno'}</td>
          <td>${domain.registrar || 'Neuvedeno'}</td>
          <td>${domain.registrationDate || 'Neuvedeno'}</td>
          <td>${domain.expirationDate || 'Neuvedeno'}</td>
          <td>${domain.notes || ''}</td>
        </tr>
      `;
    }
    html += '</table>';
  }

  if (data.generalNotes) {
    html += `<div style="margin-top: 20px;">
      <h4>Obecné poznámky</h4>
      <p>${data.generalNotes}</p>
    </div>`;
  }

  html += '</div>';
  return html;
}

function generateMapOverlaysContent(data: any): string {
  let html = '<div class="module">';

  html += '<h3>Mapové překryvy</h3>';

  if (data.points && data.points.length > 0) {
    html += `<h4>Body na mapě (${data.points.length})</h4>`;
    html += '<table class="personal-info-table">';
    html += '<tr><th>Název</th><th>Typ</th><th>Souřadnice</th><th>Popis</th></tr>';

    for (const point of data.points) {
      html += `
        <tr>
          <td>${point.name || 'Neuvedeno'}</td>
          <td>${translateMapPointType(point.type) || 'Neuvedeno'}</td>
          <td>${point.lat && point.lng ? `${point.lat}, ${point.lng}` : 'Neuvedeno'}</td>
          <td>${point.description || ''}</td>
        </tr>
      `;
    }
    html += '</table>';
  }

  if (data.areas && data.areas.length > 0) {
    html += `<h4>Oblasti na mapě (${data.areas.length})</h4>`;
    html += '<table class="personal-info-table">';
    html += '<tr><th>Název</th><th>Typ</th><th>Popis</th></tr>';

    for (const area of data.areas) {
      html += `
        <tr>
          <td>${area.name || 'Neuvedeno'}</td>
          <td>${translateMapAreaType(area.type) || 'Neuvedeno'}</td>
          <td>${area.description || ''}</td>
        </tr>
      `;
    }
    html += '</table>';
  }

  if (data.routes && data.routes.length > 0) {
    html += `<h4>Trasy na mapě (${data.routes.length})</h4>`;
    html += '<table class="personal-info-table">';
    html += '<tr><th>Název</th><th>Typ</th><th>Popis</th></tr>';

    for (const route of data.routes) {
      html += `
        <tr>
          <td>${route.name || 'Neuvedeno'}</td>
          <td>${translateMapRouteType(route.type) || 'Neuvedeno'}</td>
          <td>${route.description || ''}</td>
        </tr>
      `;
    }
    html += '</table>';
  }

  if (data.generalNotes) {
    html += `<div style="margin-top: 20px;">
      <h4>Obecné poznámky</h4>
      <p>${data.generalNotes}</p>
    </div>`;
  }

  html += '</div>';
  return html;
}

function generateTwitterContent(data: any): string {
  let html = '<div class="module">';

  html += '<h3>Twitter profily</h3>';

  if (data.profiles && data.profiles.length > 0) {
    for (let i = 0; i < data.profiles.length; i++) {
      const profile = data.profiles[i];
      html += `
        <div style="margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9;">
          <h4>Profil ${i + 1}</h4>
          <table class="personal-info-table">
            <tr><td>Uživatelské jméno:</td><td>${profile.username || 'Neuvedeno'}</td></tr>
            <tr><td>Zobrazované jméno:</td><td>${profile.displayName || 'Neuvedeno'}</td></tr>
            <tr><td>Bio:</td><td>${profile.bio || 'Neuvedeno'}</td></tr>
            <tr><td>Počet sledujících:</td><td>${profile.followersCount || 'Neuvedeno'}</td></tr>
            <tr><td>Počet sledovaných:</td><td>${profile.followingCount || 'Neuvedeno'}</td></tr>
            <tr><td>Počet tweetů:</td><td>${profile.tweetsCount || 'Neuvedeno'}</td></tr>
            <tr><td>Datum registrace:</td><td>${profile.joinDate || 'Neuvedeno'}</td></tr>
            <tr><td>Lokace:</td><td>${profile.location || 'Neuvedeno'}</td></tr>
            <tr><td>Webová stránka:</td><td>${profile.website || 'Neuvedeno'}</td></tr>
            <tr><td>Ověřený účet:</td><td>${profile.verified ? 'Ano' : 'Ne'}</td></tr>
          </table>
        </div>
      `;
    }
  } else {
    html += '<p>Žádné Twitter profily nebyly nalezeny.</p>';
  }

  // Tweety
  if (data.tweets && data.tweets.length > 0) {
    html += `<h4>Tweety (${Math.min(data.tweets.length, 10)})</h4>`;

    for (const tweet of data.tweets.slice(0, 10)) {
      html += `
        <div style="margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
          <div style="margin-bottom: 5px; font-size: 0.9em; color: #666;">
            Datum: ${tweet.date || 'Neuvedeno'}
          </div>
          <div style="margin-bottom: 10px;">
            ${tweet.content || 'Bez obsahu'}
          </div>
          <div style="font-size: 0.8em; color: #666;">
            Retweety: ${tweet.retweets || '0'} | To se mi líbí: ${tweet.likes || '0'} | Odpovědi: ${tweet.replies || '0'}
          </div>
        </div>
      `;
    }
  }

  // Fotodokumentace
  if (data.photos && data.photos.length > 0) {
    html += generatePhotoDocumentation(data.photos, 'Fotodokumentace Twitter');
  }

  if (data.generalNotes) {
    html += `<div style="margin-top: 20px;">
      <h4>Obecné poznámky</h4>
      <p>${data.generalNotes}</p>
    </div>`;
  }

  html += '</div>';
  return html;
}

function generateLinkedInContent(data: any): string {
  let html = '<div class="module">';

  html += '<h3>LinkedIn profily</h3>';

  if (data.profiles && data.profiles.length > 0) {
    for (let i = 0; i < data.profiles.length; i++) {
      const profile = data.profiles[i];
      html += `
        <div style="margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9;">
          <h4>Profil ${i + 1}</h4>
          <table class="personal-info-table">
            <tr><td>Jméno:</td><td>${profile.name || 'Neuvedeno'}</td></tr>
            <tr><td>Pozice:</td><td>${profile.headline || 'Neuvedeno'}</td></tr>
            <tr><td>Společnost:</td><td>${profile.company || 'Neuvedeno'}</td></tr>
            <tr><td>Lokace:</td><td>${profile.location || 'Neuvedeno'}</td></tr>
            <tr><td>Počet kontaktů:</td><td>${profile.connectionsCount || 'Neuvedeno'}</td></tr>
            <tr><td>Odvětví:</td><td>${profile.industry || 'Neuvedeno'}</td></tr>
            <tr><td>Vzdělání:</td><td>${profile.education || 'Neuvedeno'}</td></tr>
          </table>
          ${profile.summary ? `<div style="margin-top: 10px;"><strong>Shrnutí:</strong> ${profile.summary}</div>` : ''}
        </div>
      `;
    }
  } else {
    html += '<p>Žádné LinkedIn profily nebyly nalezeny.</p>';
  }

  // Fotodokumentace
  if (data.photos && data.photos.length > 0) {
    html += generatePhotoDocumentation(data.photos, 'Fotodokumentace LinkedIn');
  }

  if (data.generalNotes) {
    html += `<div style="margin-top: 20px;">
      <h4>Obecné poznámky</h4>
      <p>${data.generalNotes}</p>
    </div>`;
  }

  html += '</div>';
  return html;
}

function generatePhoneAnalysisContent(data: any): string {
  let html = '<div class="module">';

  html += '<h3>Analýza telefonních čísel</h3>';

  if (data.phoneNumbers && data.phoneNumbers.length > 0) {
    html += `<h4>Analyzovaná telefonní čísla (${data.phoneNumbers.length})</h4>`;

    for (let i = 0; i < data.phoneNumbers.length; i++) {
      const phone = data.phoneNumbers[i];
      html += `
        <div style="margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; background-color: #f9f9f9;">
          <h5>Telefonní číslo ${i + 1}</h5>
          <table class="personal-info-table">
            <tr><td>Číslo:</td><td><strong>${phone.phoneNumber || 'Neuvedeno'}</strong></td></tr>
            <tr><td>Typ:</td><td>${translatePhoneType(phone.type) || 'Neuvedeno'}</td></tr>
            <tr><td>Operátor:</td><td>${phone.carrier || 'Neuvedeno'}</td></tr>
            <tr><td>Země:</td><td>${phone.country || 'Neuvedeno'}</td></tr>
            <tr><td>Region:</td><td>${phone.region || 'Neuvedeno'}</td></tr>
            <tr><td>Časové pásmo:</td><td>${phone.timezone || 'Neuvedeno'}</td></tr>
            <tr><td>Platné:</td><td>${phone.isValid ? 'Ano' : 'Ne'}</td></tr>
            <tr><td>Možné:</td><td>${phone.isPossible ? 'Ano' : 'Ne'}</td></tr>
          </table>
          ${phone.notes ? `<div style="margin-top: 10px;"><strong>Poznámky:</strong> ${phone.notes}</div>` : ''}
        </div>
      `;
    }
  } else {
    html += '<p>Žádná telefonní čísla nebyla analyzována.</p>';
  }

  // Fotodokumentace
  if (data.photos && data.photos.length > 0) {
    html += generatePhotoDocumentation(data.photos, 'Fotodokumentace analýzy telefonních čísel');
  }

  if (data.generalNotes) {
    html += `<div style="margin-top: 20px;">
      <h4>Obecné poznámky</h4>
      <p>${data.generalNotes}</p>
    </div>`;
  }

  html += '</div>';
  return html;
}

function generateFinancialMonitoringContent(data: any): string {
  let html = '<div class="module">';

  html += '<h3>Finanční monitoring</h3>';

  // Registry records
  if (data.registryRecords && data.registryRecords.length > 0) {
    html += `<h4>Záznamy v registrech (${data.registryRecords.length})</h4>`;
    html += '<table class="personal-info-table">';
    html += '<tr><th>Registr</th><th>Identifikátor</th><th>Stav</th><th>Datum</th><th>Poznámky</th></tr>';

    for (const record of data.registryRecords) {
      html += `
        <tr>
          <td>${translateFinancialRegistryType(record.registryType) || 'Neuvedeno'}</td>
          <td>${record.identifier || 'Neuvedeno'}</td>
          <td>${record.status || 'Neuvedeno'}</td>
          <td>${record.date || 'Neuvedeno'}</td>
          <td>${record.notes || ''}</td>
        </tr>
      `;
    }
    html += '</table>';
  }

  // Business connections
  if (data.businessConnections && data.businessConnections.length > 0) {
    html += `<h4>Obchodní vazby (${data.businessConnections.length})</h4>`;
    html += '<table class="personal-info-table">';
    html += '<tr><th>Název</th><th>Typ vazby</th><th>Role</th><th>Datum</th><th>Poznámky</th></tr>';

    for (const connection of data.businessConnections) {
      html += `
        <tr>
          <td>${connection.entityName || 'Neuvedeno'}</td>
          <td>${translateConnectionType(connection.connectionType) || 'Neuvedeno'}</td>
          <td>${connection.role || 'Neuvedeno'}</td>
          <td>${connection.date || 'Neuvedeno'}</td>
          <td>${connection.notes || ''}</td>
        </tr>
      `;
    }
    html += '</table>';
  }

  // Assets
  if (data.assets && data.assets.length > 0) {
    html += `<h4>Majetek (${data.assets.length})</h4>`;
    html += '<table class="personal-info-table">';
    html += '<tr><th>Typ</th><th>Popis</th><th>Hodnota</th><th>Datum</th><th>Poznámky</th></tr>';

    for (const asset of data.assets) {
      html += `
        <tr>
          <td>${translateAssetType(asset.assetType) || 'Neuvedeno'}</td>
          <td>${asset.description || 'Neuvedeno'}</td>
          <td>${asset.value ? `${asset.value} ${asset.currency || 'CZK'}` : 'Neuvedeno'}</td>
          <td>${asset.date || 'Neuvedeno'}</td>
          <td>${asset.notes || ''}</td>
        </tr>
      `;
    }
    html += '</table>';
  }

  // Income and expenses analysis
  if (data.estimatedIncome || data.estimatedExpenses || data.lifestyleAnalysis) {
    html += '<h4>Analýza příjmů a výdajů</h4>';
    html += '<table class="personal-info-table">';
    if (data.estimatedIncome) {
      html += `<tr><td>Odhadované příjmy:</td><td>${data.estimatedIncome} ${data.incomeCurrency || 'CZK'}</td></tr>`;
    }
    if (data.incomeSource) {
      html += `<tr><td>Zdroj příjmů:</td><td>${data.incomeSource}</td></tr>`;
    }
    if (data.estimatedExpenses) {
      html += `<tr><td>Odhadované výdaje:</td><td>${data.estimatedExpenses} ${data.expensesCurrency || 'CZK'}</td></tr>`;
    }
    html += '</table>';

    if (data.lifestyleAnalysis) {
      html += `<div style="margin-top: 10px;">
        <strong>Analýza životního stylu:</strong>
        <p>${data.lifestyleAnalysis}</p>
      </div>`;
    }
  }

  // Fotodokumentace
  if (data.photos && data.photos.length > 0) {
    html += generatePhotoDocumentation(data.photos, 'Fotodokumentace finančního monitoringu');
  }

  if (data.generalNotes) {
    html += `<div style="margin-top: 20px;">
      <h4>Obecné poznámky</h4>
      <p>${data.generalNotes}</p>
    </div>`;
  }

  html += '</div>';
  return html;
}

function generateGenericModuleContent(data: any): string {
  console.log("Generuji generický obsah pro modul");

  let html = '<div class="module">';

  // Pokud máme data, zobrazíme je v jednoduché tabulce
  if (data) {
    try {
      console.log("Generuji generický obsah pro data:", JSON.stringify(data).substring(0, 200) + '...');

      // Zobrazíme data v jednoduché tabulce
      html += '<table class="info-table">';

      // Procházíme všechny vlastnosti objektu
      for (const key in data) {
        if (typeof data[key] === 'function' || data[key] === undefined || data[key] === null) {
          continue;
        }

        if (key.startsWith('_') || key === 'subjectName' || key === 'subjectType') {
          continue;
        }

        let value = data[key];
        let formattedValue = '';

        if (Array.isArray(value)) {
          if (value.length === 0) {
            formattedValue = 'Prázdné pole';
          } else {
            formattedValue = value.map(item => {
              if (typeof item === 'object') {
                return JSON.stringify(item);
              }
              return String(item);
            }).join(', ');
          }
        } else if (typeof value === 'object') {
          formattedValue = JSON.stringify(value);
        } else if (typeof value === 'boolean') {
          formattedValue = value ? 'Ano' : 'Ne';
        } else {
          formattedValue = String(value);
        }

        // Formátování klíče
        let formattedKey = key
          .replace(/([A-Z])/g, ' $1')
          .replace(/[-_]/g, ' ')
          .replace(/\b\w/g, (str) => str.toUpperCase());

        html += `<tr><td>${formattedKey}:</td><td>${formattedValue}</td></tr>`;
      }

      html += '</table>';

    } catch (error) {
      // Pokud se nepodaří zobrazit data v tabulce, zobrazíme je jako JSON
      console.error('Chyba při generování obsahu modulu:', error);
      html += `<pre style="white-space: pre-wrap; font-family: monospace; background-color: #f5f5f5; padding: 10px; border-radius: 5px; font-size: 12px;">${JSON.stringify(data, null, 2)}</pre>`;
    }
  } else {
    html += '<p>Žádná data k zobrazení</p>';
  }

  html += '</div>';
  return html;
}

// Funkce pro získání kategorie modulu
function getModuleCategory(moduleType: string): string {
  const categories: { [key: string]: string } = {
    'evidence_obyvatel': 'Osobní údaje',
    'family_members': 'Rodinné vztahy',
    'registr_ridicske_prukazy': 'Doklady a oprávnění',
    'gun_license': 'Doklady a oprávnění',
    'vehicles': 'Vozidla a majetek',
    'locations': 'Lokace a adresy',
    'business_activity': 'Podnikatelská činnost',
    'cadastre': 'Nemovitosti',
    'training': 'Vzdělání a školení',
    'email_analysis': 'Komunikace',
    'phone_numbers': 'Komunikace',
    'network_analysis': 'Síťová analýza',
    'map_overlays': 'Mapové analýzy',
    'facebook': 'Sociální sítě',
    'instagram': 'Sociální sítě',
    'twitter': 'Sociální sítě',
    'linkedin': 'Sociální sítě',
    'phone_analysis': 'Technická analýza',
    'financial_monitoring': 'Finanční monitoring'
  };

  return categories[moduleType] || 'Ostatní informace';
}

// Funkce pro získání názvu modulu
function getModuleTitle(moduleType: string): string {
  const titles: { [key: string]: string } = {
    'evidence_obyvatel': 'Evidence obyvatel',
    'family_members': 'Rodinní příslušníci',
    'registr_ridicske_prukazy': 'Registr řidičských průkazů',
    'gun_license': 'Zbrojní průkazy',
    'vehicles': 'Vozidla',
    'locations': 'Lokace',
    'business_activity': 'Podnikatelská činnost',
    'cadastre': 'Katastr nemovitostí',
    'training': 'Školení a výcviky',
    'email_analysis': 'Analýza emailů',
    'phone_numbers': 'Telefonní čísla',
    'network_analysis': 'Síťová analýza',
    'map_overlays': 'Mapové vrstvy',
    'facebook': 'Facebook',
    'instagram': 'Instagram',
    'twitter': 'Twitter',
    'linkedin': 'LinkedIn',
    'phone_analysis': 'Analýza telefonů',
    'financial_monitoring': 'Finanční monitoring'
  };

  return titles[moduleType] || moduleType.replace(/_/g, ' ').replace(/\b\w/g, (str) => str.toUpperCase());
}

// Funkce pro překlad stavů zbrojních průkazů
function translateGunOwnershipStatus(status: string): string {
  const translations: { [key: string]: string } = {
    'yes': 'Vlastní zbrojní průkaz',
    'no': 'Nevlastní zbrojní průkaz',
    'unknown': 'Neznámo'
  };
  return translations[status] || status;

}



// Globální čítač stránek pro moderní styl
let modernPageCounter = 2; // Začínáme od 2, protože 1. je evidence obyvatel

function wrapInModernStyle(content: string, title: string): string {
  const pageNumber = modernPageCounter++;

  // Ikony pro různé typy modulů
  const icons: { [key: string]: string } = {
    'Facebook': `<path d="M18 2H15C13.6739 2 12.4021 2.52678 11.4645 3.46447C10.5268 4.40215 10 5.67392 10 7V10H7V14H10V22H14V14H17L18 10H14V7C14 6.73478 14.1054 6.48043 14.2929 6.29289C14.4804 6.10536 14.7348 6 15 6H18V2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>`,
    'Instagram': `<rect x="2" y="2" width="20" height="20" rx="5" ry="5" stroke="currentColor" stroke-width="2"/><path d="M16 11.37C16.1234 12.2022 15.9813 13.0522 15.5938 13.7991C15.2063 14.5461 14.5931 15.1515 13.8416 15.5297C13.0901 15.9079 12.2384 16.0396 11.4078 15.9059C10.5771 15.7723 9.80976 15.3801 9.21484 14.7852C8.61992 14.1902 8.22773 13.4229 8.09407 12.5922C7.9604 11.7615 8.09207 10.9099 8.47032 10.1584C8.84856 9.40685 9.45397 8.79374 10.2009 8.40624C10.9479 8.01874 11.7978 7.87658 12.63 8C13.4789 8.12588 14.2649 8.52146 14.8717 9.1283C15.4785 9.73515 15.8741 10.5211 16 11.37Z" stroke="currentColor" stroke-width="2"/><path d="M17.5 6.5H17.51" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>`,
    'Telefonní čísla': `<path d="M22 16.92V19.92C22.0011 20.1985 21.9441 20.4742 21.8325 20.7293C21.7209 20.9845 21.5573 21.2136 21.3521 21.4019C21.1468 21.5901 20.9046 21.7335 20.6407 21.8227C20.3769 21.9119 20.0974 21.9451 19.82 21.92C16.7428 21.5856 13.787 20.5341 11.19 18.85C8.77382 17.3147 6.72533 15.2662 5.18999 12.85C3.49997 10.2412 2.44824 7.27099 2.11999 4.18C2.095 3.90347 2.12787 3.62476 2.2165 3.36162C2.30513 3.09849 2.44756 2.85669 2.63476 2.65162C2.82196 2.44655 3.0498 2.28271 3.30379 2.17052C3.55777 2.05833 3.83233 2.00026 4.10999 2H7.10999C7.59531 1.99522 8.06588 2.16708 8.43499 2.48353C8.80409 2.79999 9.04981 3.23945 9.10999 3.72C9.22399 4.68007 9.44824 5.62273 9.77999 6.53C9.93344 6.88792 9.97798 7.28454 9.90999 7.67C9.84199 8.05546 9.66408 8.41632 9.39999 8.7L8.07999 10.02C9.51355 12.558 11.442 14.4865 13.98 15.92L15.3 14.6C15.6836 14.3359 16.0445 14.158 16.43 14.09C16.8154 14.022 17.212 14.0665 17.57 14.22C18.4773 14.5518 19.4199 14.776 20.38 14.89C20.8672 14.9502 21.3122 15.2015 21.6279 15.5775C21.9435 15.9535 22.1101 16.4317 22.1 16.92Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>`,
    'E-mailová analýza': `<path d="M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/><polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>`,
    'default': `<path d="M9 11H15M9 15H15M17 21H7C5.89543 21 5 20.1046 5 19V5C5 3.89543 5.89543 3 7 3H12.5858C12.851 3 13.1054 3.10536 13.2929 3.29289L19.7071 9.70711C19.8946 9.89464 20 10.149 20 10.4142V19C20 20.1046 19.1046 21 18 21H17Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>`
  };

  const icon = icons[title] || icons['default'];

  return `
    <div class="modern-page-break">
      <div class="modern-page-header">
        <div class="modern-page-number">${pageNumber}. stránka</div>
        <div class="modern-page-divider"></div>
      </div>
      <div class="modern-section">
        <div class="modern-section-header">
          <div class="modern-section-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              ${icon}
            </svg>
          </div>
          <h2 class="modern-section-title">${title}</h2>
        </div>
        <div class="modern-module-content">
          ${content}
        </div>
      </div>
    </div>
  `;
}

function formatInvestigatorName(rawName: string): string {
  // Pokud není přiřazeno, vrátíme to
  if (!rawName || rawName === 'Nepřiřazeno') {
    return 'Nepřiřazeno';
  }

  // Odstraníme duplicitní hodnost na konci (např. "Bc. Václav Pospíšil por.")
  let formattedName = rawName.replace(/\s+(plk\.|pplk\.|mjr\.|kpt\.|npor\.|por\.|ppor\.|nprap\.|prap\.|pprap\.|nstržm\.|stržm\.|prap\.|rtm\.|gen\.|genmjr\.|genpor\.|genplk\.)$/i, '').trim();

  // Pokud už začíná hodností, vrátíme ho jak je
  if (/^(plk\.|pplk\.|mjr\.|kpt\.|npor\.|por\.|ppor\.|nprap\.|prap\.|pprap\.|nstržm\.|stržm\.|prap\.|rtm\.|gen\.|genmjr\.|genpor\.|genplk\.)\s+/.test(formattedName)) {
    return formattedName;
  }

  // Jinak předpokládáme, že formát je "Bc. Václav Pospíšil" a přidáme hodnost na začátek
  return `por. ${formattedName}`;
}

function generateDriverLicenseContent(data: any): string {
  let html = '<div class="module">';

  if (!data) {
    html += '<p>Nebyly zaznamenány žádné informace o řidičském průkazu.</p>';
    html += '</div>';
    return html;
  }

  // Přehled řidičského průkazu
  html += '<div style="margin-bottom: 25px;">';
  html += '<h4 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px; margin-bottom: 15px;">Přehled řidičského průkazu</h4>';

  // Základní údaje v tabulce
  html += `<table style="width: 100%; border-collapse: collapse; margin-bottom: 20px;">
    <tbody>`;

  if (data.licenseNumber) {
    html += `<tr style="border-bottom: 1px solid #e5e7eb;">
      <td style="padding: 8px; font-weight: bold; width: 30%; background-color: #f8fafc;">Číslo ŘP:</td>
      <td style="padding: 8px;">${data.licenseNumber}</td>
    </tr>`;
  }

  if (data.validity || (data.validFrom && data.validTo)) {
    const validityText = data.validity || `${data.validFrom || 'Neznámo'} - ${data.validTo || 'Neznámo'}`;
    html += `<tr style="border-bottom: 1px solid #e5e7eb;">
      <td style="padding: 8px; font-weight: bold; width: 30%; background-color: #f8fafc;">Platnost:</td>
      <td style="padding: 8px;">${validityText}</td>
    </tr>`;
  }

  if (data.issuedBy) {
    html += `<tr style="border-bottom: 1px solid #e5e7eb;">
      <td style="padding: 8px; font-weight: bold; width: 30%; background-color: #f8fafc;">Vydal:</td>
      <td style="padding: 8px;">${data.issuedBy}</td>
    </tr>`;
  }

  if (data.placeOfIssue) {
    html += `<tr style="border-bottom: 1px solid #e5e7eb;">
      <td style="padding: 8px; font-weight: bold; width: 30%; background-color: #f8fafc;">Místo vydání:</td>
      <td style="padding: 8px;">${data.placeOfIssue}</td>
    </tr>`;
  }

  if (data.categories) {
    html += `<tr style="border-bottom: 1px solid #e5e7eb;">
      <td style="padding: 8px; font-weight: bold; width: 30%; background-color: #f8fafc;">Skupiny oprávnění:</td>
      <td style="padding: 8px;">${data.categories}</td>
    </tr>`;
  }

  if (data.restrictions) {
    html += `<tr style="border-bottom: 1px solid #e5e7eb;">
      <td style="padding: 8px; font-weight: bold; width: 30%; background-color: #f8fafc;">Omezení:</td>
      <td style="padding: 8px;">${data.restrictions}</td>
    </tr>`;
  }

  html += `</tbody></table>`;
  html += '</div>';

  // Zdravotní způsobilost
  if (data.medicalFitness || data.medicalRestrictions || data.medicalCertificateDate) {
    html += '<div style="margin-bottom: 25px;">';
    html += '<h4 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px; margin-bottom: 15px;">Zdravotní způsobilost</h4>';

    if (data.medicalFitness) {
      const medicalFitnessLabels: { [key: string]: string } = {
        'yes': 'Způsobilý/á',
        'no': 'Nezpůsobilý/á',
        'limited': 'Způsobilý/á s omezením',
        'unknown': 'Neznámo'
      };
      html += `<p><strong>Zdravotní způsobilost:</strong> ${medicalFitnessLabels[data.medicalFitness] || data.medicalFitness}</p>`;
    }

    if (data.medicalCertificateDate) {
      html += `<p><strong>Datum lékařského vyšetření:</strong> ${data.medicalCertificateDate}</p>`;
    }

    if (data.medicalCertificateValidUntil) {
      html += `<p><strong>Platnost lékařského potvrzení do:</strong> ${data.medicalCertificateValidUntil}</p>`;
    }

    if (data.medicalRestrictions) {
      html += `<p><strong>Zdravotní omezení:</strong> ${data.medicalRestrictions}</p>`;
    }

    html += '</div>';
  }

  // Evidenční karta řidiče
  if (data.pointsBalance !== undefined || data.currentBanStatus || data.banHistory) {
    html += '<div style="margin-bottom: 25px;">';
    html += '<h4 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px; margin-bottom: 15px;">Evidenční karta řidiče</h4>';

    if (data.pointsBalance !== undefined && data.pointsBalance !== null) {
      html += `<p><strong>Stav bodového konta:</strong> ${data.pointsBalance} bodů</p>`;
    }

    if (data.currentBanStatus) {
      const banStatus = data.currentBanStatus === 'yes' ? 'Má zákaz řízení' : 'Nemá zákaz řízení';
      html += `<p><strong>Aktuální zákaz řízení:</strong> ${banStatus}</p>`;

      if (data.currentBanStatus === 'yes') {
        if (data.currentBanPeriod) {
          html += `<p><strong>Platnost zákazu:</strong> ${data.currentBanPeriod}</p>`;
        }
        if (data.currentBanReason) {
          html += `<p><strong>Důvod zákazu:</strong> ${data.currentBanReason}</p>`;
        }
      }
    }

    if (data.banHistory) {
      html += `<p><strong>Historie zákazů řízení:</strong> ${data.banHistory}</p>`;
    }

    html += '</div>';
  }

  // Historie řidičských průkazů
  if (data.previousLicenses || data.licenseHistory) {
    html += '<div style="margin-bottom: 25px;">';
    html += '<h4 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px; margin-bottom: 15px;">Historie řidičských průkazů</h4>';

    if (data.previousLicenses) {
      html += `<p><strong>Předchozí řidičské průkazy:</strong> ${data.previousLicenses}</p>`;
    }

    if (data.licenseHistory) {
      html += `<p><strong>Detailní historie ŘP:</strong> ${data.licenseHistory}</p>`;
    }

    html += '</div>';
  }

  // Přestupky v dopravě
  if (data.offenses && data.offenses.length > 0) {
    html += '<div style="margin-bottom: 25px;">';
    html += '<h4 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px; margin-bottom: 15px;">Přestupky v dopravě</h4>';
    html += `<p>Celkový počet zaznamenaných přestupků: <strong>${data.offenses.length}</strong></p>`;

    for (let i = 0; i < data.offenses.length; i++) {
      const offense = data.offenses[i];
      html += `<div style="margin-top: 20px; padding: 15px; border: 1px solid #e5e7eb; border-radius: 5px; page-break-inside: avoid;">`;
      html += `<h5 style="margin-top: 0; color: #dc2626; border-bottom: 1px solid #fecaca; padding-bottom: 8px;">Přestupek ${i + 1}</h5>`;

      // Základní informace o přestupku
      if (offense.date) {
        html += `<p><strong>Datum:</strong> ${offense.date}</p>`;
      }

      if (offense.offenseType) {
        html += `<p><strong>Typ přestupku:</strong> ${offense.offenseType}</p>`;
      }

      if (offense.description) {
        html += `<p><strong>Popis:</strong> ${offense.description}</p>`;
      }

      if (offense.location) {
        html += `<p><strong>Místo:</strong> ${offense.location}</p>`;
      }

      if (offense.exactAddress) {
        html += `<p><strong>Přesná adresa:</strong> ${offense.exactAddress}</p>`;
      }

      if (offense.gpsCoordinates) {
        html += `<p><strong>GPS souřadnice:</strong> ${offense.gpsCoordinates}</p>`;
      }

      // Sankce
      if (offense.penaltyAmount || offense.pointsDeducted) {
        html += '<div style="margin-top: 10px;">';
        html += '<strong>Sankce:</strong>';
        if (offense.penaltyAmount) {
          html += ` Pokuta: ${offense.penaltyAmount}`;
        }
        if (offense.pointsDeducted !== undefined && offense.pointsDeducted !== null) {
          html += ` Body: ${offense.pointsDeducted}`;
        }
        html += '</div>';
      }

      // Právní informace
      if (offense.officerName || offense.officerBadgeNumber || offense.caseNumber) {
        html += '<div style="margin-top: 10px;">';
        html += '<strong>Právní informace:</strong><br/>';
        if (offense.officerName) {
          html += `Policista: ${offense.officerName}<br/>`;
        }
        if (offense.officerBadgeNumber) {
          html += `Číslo odznaku: ${offense.officerBadgeNumber}<br/>`;
        }
        if (offense.caseNumber) {
          html += `Číslo případu: ${offense.caseNumber}<br/>`;
        }
        html += '</div>';
      }

      // Stav řízení
      if (offense.legalStatus) {
        const legalStatusLabels: { [key: string]: string } = {
          'pending': 'Probíhající',
          'resolved': 'Vyřešeno',
          'appealed': 'Odvoláno',
          'dismissed': 'Zamítnuto'
        };
        html += `<p><strong>Stav řízení:</strong> ${legalStatusLabels[offense.legalStatus] || offense.legalStatus}</p>`;
      }

      if (offense.courtDate) {
        html += `<p><strong>Datum soudu:</strong> ${offense.courtDate}</p>`;
      }

      // OSINT informace
      if (offense.source || offense.verified) {
        html += '<div style="margin-top: 10px; padding: 8px; background-color: #f8fafc; border-radius: 3px;">';
        html += '<strong>OSINT informace:</strong><br/>';

        if (offense.source) {
          const sourceLabels: { [key: string]: string } = {
            'social_media': 'Sociální média',
            'surveillance': 'Sledování',
            'witness': 'Svědek',
            'documents': 'Dokumenty',
            'phone_analysis': 'Analýza telefonu',
            'investigation': 'Vyšetřování',
            'open_source': 'Otevřené zdroje',
            'other': 'Jiný'
          };
          html += `Zdroj: ${sourceLabels[offense.source] || offense.source}<br/>`;
        }

        if (offense.sourceDetail) {
          html += `Detail zdroje: ${offense.sourceDetail}<br/>`;
        }

        if (offense.verified !== undefined) {
          html += `Ověřeno: ${offense.verified ? 'Ano' : 'Ne'}<br/>`;
        }

        if (offense.verificationDate) {
          html += `Datum ověření: ${offense.verificationDate}<br/>`;
        }

        html += '</div>';
      }

      // Fotografie přestupku
      if (offense.photos && offense.photos.length > 0) {
        html += '<div style="margin-top: 15px;">';
        html += '<strong>Dokumentace přestupku:</strong>';
        html += '<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(150px, 1fr)); gap: 10px; margin-top: 10px;">';
        for (const photo of offense.photos) {
          if (photo.downloadURL) {
            html += `<div style="border: 1px solid #e5e7eb; border-radius: 3px; overflow: hidden;">
              <img src="${photo.downloadURL}" alt="${photo.description || 'Fotografie přestupku'}" style="width: 100%; height: 120px; object-fit: cover;" />
              ${photo.description ? `<div style="padding: 5px; font-size: 11px; background-color: #f8fafc;">${photo.description}</div>` : ''}
            </div>`;
          }
        }
        html += '</div></div>';
      }

      if (offense.notes) {
        html += `<div style="margin-top: 10px; padding: 8px; background-color: #fffbeb; border-left: 3px solid #f59e0b;">
          <strong>Poznámky:</strong> ${offense.notes}
        </div>`;
      }

      html += '</div>';
    }
    html += '</div>';
  }

  // OSINT informace
  if (data.informationSource || data.informationReliability || data.verificationDate) {
    html += '<div style="margin-bottom: 25px;">';
    html += '<h4 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px; margin-bottom: 15px;">OSINT informace</h4>';

    if (data.informationSource) {
      const sourceLabels: { [key: string]: string } = {
        'social_media': 'Sociální média',
        'surveillance': 'Sledování',
        'witness': 'Svědek',
        'documents': 'Dokumenty',
        'phone_analysis': 'Analýza telefonu',
        'investigation': 'Vyšetřování',
        'open_source': 'Otevřené zdroje',
        'other': 'Jiný'
      };
      html += `<p><strong>Zdroj informací:</strong> ${sourceLabels[data.informationSource] || data.informationSource}</p>`;
    }

    if (data.sourceDetail) {
      html += `<p><strong>Detail zdroje:</strong> ${data.sourceDetail}</p>`;
    }

    if (data.informationReliability) {
      const reliabilityLabels: { [key: string]: string } = {
        'verified': 'Ověřeno',
        'probable': 'Pravděpodobné',
        'possible': 'Možné',
        'unconfirmed': 'Nepotvrzeno',
        'unknown': 'Neznámo'
      };
      html += `<p><strong>Spolehlivost informací:</strong> ${reliabilityLabels[data.informationReliability] || data.informationReliability}</p>`;
    }

    if (data.verificationDate) {
      html += `<p><strong>Datum ověření:</strong> ${data.verificationDate}</p>`;
    }

    if (data.lastVerified) {
      html += `<p><strong>Naposledy ověřeno:</strong> ${data.lastVerified}</p>`;
    }

    html += '</div>';
  }

  // Bezpečnostní hodnocení
  if (data.drivingRisk || data.riskAssessment || data.drivingBehaviorNotes) {
    html += '<div style="margin-bottom: 25px;">';
    html += '<h4 style="color: #dc2626; border-bottom: 2px solid #fecaca; padding-bottom: 10px; margin-bottom: 15px;">Bezpečnostní hodnocení</h4>';

    if (data.drivingRisk) {
      const threatLabels: { [key: string]: string } = {
        'none': 'Žádné',
        'low': 'Nízké',
        'medium': 'Střední',
        'high': 'Vysoké',
        'critical': 'Kritické',
        'unknown': 'Neznámé'
      };
      const riskColor = data.drivingRisk === 'high' || data.drivingRisk === 'critical' ? '#dc2626' :
                       data.drivingRisk === 'medium' ? '#f59e0b' : '#16a34a';
      html += `<p><strong>Riziko v dopravě:</strong> <span style="color: ${riskColor}; font-weight: bold;">${threatLabels[data.drivingRisk] || data.drivingRisk}</span></p>`;
    }

    if (data.riskAssessment) {
      html += `<p><strong>Hodnocení rizika:</strong> ${data.riskAssessment}</p>`;
    }

    if (data.drivingBehaviorNotes) {
      html += `<p><strong>Poznámky k chování při řízení:</strong> ${data.drivingBehaviorNotes}</p>`;
    }

    html += '</div>';
  }

  // Fotodokumentace
  if (data.photos && data.photos.length > 0) {
    html += '<div style="margin-bottom: 25px;">';
    html += '<h4 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px; margin-bottom: 15px;">Fotodokumentace</h4>';
    html += '<div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(200px, 1fr)); gap: 15px;">';

    for (const photo of data.photos) {
      if (photo.downloadURL) {
        html += `<div style="border: 1px solid #e5e7eb; border-radius: 5px; overflow: hidden; page-break-inside: avoid;">
          <img src="${photo.downloadURL}" alt="${photo.description || 'Fotografie řidičského průkazu'}" style="width: 100%; height: 150px; object-fit: cover;" />
          <div style="padding: 10px;">`;

        if (photo.description) {
          html += `<div style="font-weight: bold; margin-bottom: 5px;">${photo.description}</div>`;
        }

        if (photo.dateTaken) {
          html += `<div style="font-size: 12px; color: #6b7280;">Datum: ${photo.dateTaken}</div>`;
        }

        if (photo.sourceURL) {
          html += `<div style="font-size: 12px; color: #6b7280; word-break: break-all;">Zdroj: ${photo.sourceURL}</div>`;
        }

        if (photo.verified) {
          html += `<div style="font-size: 12px; color: #16a34a; margin-top: 5px;">✓ Ověřeno</div>`;
        }

        html += `</div></div>`;
      }
    }

    html += '</div></div>';
  }

  // Poznámky
  if (data.investigationNotes || data.notes) {
    html += '<div style="margin-bottom: 25px;">';
    html += '<h4 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px; margin-bottom: 15px;">Poznámky</h4>';

    if (data.investigationNotes) {
      html += `<div style="margin-bottom: 15px; padding: 10px; background-color: #fffbeb; border-left: 3px solid #f59e0b;">
        <strong>Poznámky vyšetřovatele:</strong><br/>
        ${data.investigationNotes.replace(/\n/g, '<br/>')}
      </div>`;
    }

    if (data.notes) {
      html += `<div style="margin-bottom: 15px; padding: 10px; background-color: #f8fafc; border-left: 3px solid #6b7280;">
        <strong>Obecné poznámky:</strong><br/>
        ${data.notes.replace(/\n/g, '<br/>')}
      </div>`;
    }

    html += '</div>';
  }

  html += '</div>';
  return html;
}

function generateGunLicenseContent(data: any): string {
  if (!data) return '<p>Žádná data o zbrojních průkazech nejsou k dispozici.</p>';

  let content = '';

  // Základní informace o zbrojním průkazu
  content += `
    <div class="module-section">
      <h3 class="section-title">📋 Základní informace o zbrojním průkazu</h3>
      <table class="info-table">
        <tr><td><strong>Stav vlastnictví ZP:</strong></td><td>${translateGunOwnershipStatus(data.ownershipStatus || 'unknown')}</td></tr>
        ${data.licenseNumber ? `<tr><td><strong>Číslo ZP:</strong></td><td>${data.licenseNumber}</td></tr>` : ''}
        ${data.issueDate ? `<tr><td><strong>Datum vydání:</strong></td><td>${data.issueDate}</td></tr>` : ''}
        ${data.validUntil ? `<tr><td><strong>Platnost do:</strong></td><td>${data.validUntil}</td></tr>` : ''}
        ${data.issuedBy ? `<tr><td><strong>Vydal:</strong></td><td>${data.issuedBy}</td></tr>` : ''}
        ${data.placeOfIssue ? `<tr><td><strong>Místo vydání:</strong></td><td>${data.placeOfIssue}</td></tr>` : ''}
        ${data.groups && data.groups.length > 0 ? `<tr><td><strong>Skupiny ZP:</strong></td><td>${data.groups.map((g: string) => translateGunLicenseGroup(g)).join(', ')}</td></tr>` : ''}
      </table>
    </div>
  `;

  // Zdravotní a bezpečnostní způsobilost
  if (data.healthFitness || data.securityClearance || data.healthRestrictions) {
    content += `
      <div class="module-section">
        <h3 class="section-title">🏥 Zdravotní a bezpečnostní způsobilost</h3>
        <table class="info-table">
          ${data.healthFitness ? `<tr><td><strong>Zdravotní způsobilost:</strong></td><td>${translateHealthFitness(data.healthFitness)}</td></tr>` : ''}
          ${data.securityClearance ? `<tr><td><strong>Bezpečnostní způsobilost:</strong></td><td>${translateSecurityClearance(data.securityClearance)}</td></tr>` : ''}
          ${data.medicalCertificateDate ? `<tr><td><strong>Datum lékařského vyšetření:</strong></td><td>${data.medicalCertificateDate}</td></tr>` : ''}
          ${data.medicalCertificateValidUntil ? `<tr><td><strong>Lékařské platné do:</strong></td><td>${data.medicalCertificateValidUntil}</td></tr>` : ''}
          ${data.medicalExaminer ? `<tr><td><strong>Vyšetřující lékař:</strong></td><td>${data.medicalExaminer}</td></tr>` : ''}
          ${data.securityClearanceDate ? `<tr><td><strong>Datum bezp. prověrky:</strong></td><td>${data.securityClearanceDate}</td></tr>` : ''}
        </table>
        ${data.healthRestrictions ? `<p><strong>Zdravotní omezení:</strong> ${data.healthRestrictions}</p>` : ''}
        ${data.securityClearanceRestrictions ? `<p><strong>Bezpečnostní omezení:</strong> ${data.securityClearanceRestrictions}</p>` : ''}
      </div>
    `;
  }

  // Registrované zbraně
  if (data.registeredWeapons && data.registeredWeapons.length > 0) {
    content += `
      <div class="module-section">
        <h3 class="section-title">🔫 Registrované zbraně (${data.registeredWeapons.length})</h3>
    `;

    data.registeredWeapons.forEach((weapon: any, index: number) => {
      const statusColor = getWeaponStatusColor(weapon.legalStatus);

      content += `
        <div class="weapon-card">
          <h4 class="weapon-title">Zbraň ${index + 1}: ${weapon.weaponType || 'Neurčeno'}</h4>
          <table class="info-table">
            ${weapon.brand ? `<tr><td><strong>Značka:</strong></td><td>${weapon.brand}</td></tr>` : ''}
            ${weapon.model ? `<tr><td><strong>Model:</strong></td><td>${weapon.model}</td></tr>` : ''}
            ${weapon.serialNumber ? `<tr><td><strong>Výrobní číslo:</strong></td><td>${weapon.serialNumber}</td></tr>` : ''}
            ${weapon.category ? `<tr><td><strong>Kategorie:</strong></td><td>${translateWeaponCategory(weapon.category)}</td></tr>` : ''}
            ${weapon.caliber ? `<tr><td><strong>Ráže:</strong></td><td>${weapon.caliber}</td></tr>` : ''}
            ${weapon.legalStatus ? `<tr><td><strong>Právní stav:</strong></td><td><span style="color: ${statusColor};">${translateWeaponLegalStatus(weapon.legalStatus)}</span></td></tr>` : ''}
          </table>

          ${weapon.photos && weapon.photos.length > 0 ? `
            <h5>Fotografie zbraně (${weapon.photos.length})</h5>
            <div class="photo-gallery">
              ${weapon.photos.map((photo: any) => `
                <div class="photo-item">
                  ${photo.downloadURL ? `<img src="${photo.downloadURL}" alt="${photo.description || 'Fotografie zbraně'}" style="max-width: 200px; max-height: 150px;" />` : ''}
                  ${photo.description ? `<p class="photo-desc">${photo.description}</p>` : ''}
                  ${photo.dateTaken ? `<p class="photo-date">Pořízeno: ${photo.dateTaken}</p>` : ''}
                </div>
              `).join('')}
            </div>
          ` : ''}

          ${weapon.notes ? `<p><strong>Poznámky:</strong> ${weapon.notes}</p>` : ''}
        </div>
      `;
    });

    content += '</div>';
  }

  // Poznámky
  const hasNotes = data.investigationNotes || data.generalNotes;
  if (hasNotes) {
    content += `
      <div class="module-section">
        <h3 class="section-title">📝 Poznámky</h3>
        ${data.investigationNotes ? `<div class="note-section"><h4>Poznámky vyšetřovatele:</h4><p>${data.investigationNotes}</p></div>` : ''}
        ${data.generalNotes ? `<div class="note-section"><h4>Obecné poznámky:</h4><p>${data.generalNotes}</p></div>` : ''}
      </div>
    `;
  }

  return content || '<p>Žádné detailní informace o zbrojních průkazech nejsou k dispozici.</p>';
}

// Pomocné funkce pro překlady specifické pro zbrojní průkazy
function translateGunOwnershipStatus(status: string): string {
  const translations: { [key: string]: string } = {
    'yes': 'Ano, vlastní platný ZP',
    'no': 'Ne, nevlastní ZP',
    'expired': 'Neplatný / Odňatý ZP',
    'in-process': 'V řízení o vydání ZP',
    'unknown': 'Nezjištěno'
  };
  return translations[status] || status;
}

function translateGunLicenseGroup(group: string): string {
  const translations: { [key: string]: string } = {
    'A': 'A - Sběratelské',
    'B': 'B - Sportovní',
    'C': 'C - Lovecké',
    'D': 'D - Výkon zaměstnání',
    'E': 'E - Ochrana života, zdraví a majetku',
    'F': 'F - Pyrotechnika'
  };
  return translations[group] || group;
}

function translateHealthFitness(status: string): string {
  const translations: { [key: string]: string } = {
    'yes': 'Zdravotně způsobilý/á',
    'limited': 'Způsobilý/á s omezením',
    'no': 'Zdravotně nezpůsobilý/á',
    'unknown': 'Nezjištěno'
  };
  return translations[status] || status;
}

function translateSecurityClearance(status: string): string {
  const translations: { [key: string]: string } = {
    'yes': 'Splňuje bezpečnostní podmínky',
    'no': 'Nesplňuje bezpečnostní podmínky',
    'unknown': 'Nezjištěno'
  };
  return translations[status] || status;
}

function translateWeaponCategory(category: string): string {
  const translations: { [key: string]: string } = {
    'A': 'A - Zakázané zbraně',
    'A-I': 'A-I - Zakázané (na výjimku)',
    'B': 'B - Podléhající povolení',
    'C': 'C - Podléhající ohlášení',
    'C-I': 'C-I - Ohlášené (nově)',
    'D': 'D - Ostatní (volně prodejné od 18 let)',
    'other': 'Jiná / Nezjištěno'
  };
  return translations[category] || category;
}

function translateWeaponLegalStatus(status: string): string {
  const translations: { [key: string]: string } = {
    'legal_registered': 'Legální registrovaná',
    'legal_unregistered': 'Legální neregistrovaná',
    'illegal_unregistered': 'Nelegální neregistrovaná',
    'stolen': 'Kradená',
    'converted': 'Přestavěná',
    'deactivated': 'Deaktivovaná',
    'unknown': 'Nezjištěno'
  };
  return translations[status] || status;
}

function getWeaponStatusColor(status: string): string {
  const colors: { [key: string]: string } = {
    'legal_registered': '#22c55e',
    'legal_unregistered': '#eab308',
    'illegal_unregistered': '#ef4444',
    'stolen': '#dc2626',
    'converted': '#f97316',
    'deactivated': '#6b7280'
  };
  return colors[status] || '#6b7280';
}

function generateVehiclesContent(data: any): string {
  let html = '<div class="module">';

  // Pokud máme pole vozidel
  if (data.vehicles && Array.isArray(data.vehicles) && data.vehicles.length > 0) {
    html += `<h3>Vozidla (${data.vehicles.length})</h3>`;

    for (let i = 0; i < data.vehicles.length; i++) {
      const vehicle = data.vehicles[i];

      html += `
        <div class="vehicle-section">
          <h4>Vozidlo ${i + 1}</h4>
          <table class="personal-info-table">
            <tr>
              <td>Značka a model:</td>
              <td><strong>${vehicle.brand || vehicle.make || 'Neuvedeno'} ${vehicle.model || ''}</strong></td>
            </tr>
            <tr>
              <td>SPZ/RZ:</td>
              <td><strong>${vehicle.licensePlate || 'Neuvedeno'}</strong></td>
            </tr>
            <tr>
              <td>VIN:</td>
              <td>${vehicle.vin || 'Neuvedeno'}</td>
            </tr>
            <tr>
              <td>Barva:</td>
              <td>${vehicle.color || 'Neuvedeno'}</td>
            </tr>
            <tr>
              <td>Rok výroby:</td>
              <td>${vehicle.yearManufactured || 'Neuvedeno'}</td>
            </tr>
            <tr>
              <td>Vztah k vozidlu:</td>
              <td>${translateRelationshipType(vehicle.relationshipType) || 'Neuvedeno'}</td>
            </tr>`;

      if (vehicle.otherRelationshipDetail) {
        html += `
            <tr>
              <td>Upřesnění vztahu:</td>
              <td>${vehicle.otherRelationshipDetail}</td>
            </tr>`;
      }

      if (vehicle.stkValidUntil) {
        html += `
            <tr>
              <td>Platnost STK do:</td>
              <td>${vehicle.stkValidUntil}</td>
            </tr>`;
      }

      if (vehicle.firstRegistered) {
        html += `
            <tr>
              <td>Datum první registrace:</td>
              <td>${vehicle.firstRegistered}</td>
            </tr>`;
      }

      html += `</table>`;

      // Poznámky k vozidlu
      if (vehicle.notes) {
        html += `
          <div class="notes-section">
            <h5>Poznámky:</h5>
            <p>${vehicle.notes}</p>
          </div>`;
      }

      // Fotografie vozidla
      if (vehicle.photos && Array.isArray(vehicle.photos) && vehicle.photos.length > 0) {
        html += generatePhotoDocumentation(vehicle.photos, `Fotodokumentace vozidla ${i + 1}`);
      }

      html += `</div>`;

      // Oddělovač mezi vozidly
      if (i < data.vehicles.length - 1) {
        html += `<hr style="border: none; border-top: 2px solid #ddd; margin: 20px 0;">`;
      }
    }
  } else {
    html += `<h3>Vozidla</h3>
      <p>Žádná vozidla nejsou evidována.</p>`;
  }

  html += '</div>';
  return html;
}

function translateRelationshipType(type: string): string {
  const translations: { [key: string]: string } = {
    'owner': 'Vlastník',
    'user': 'Uživatel',
    'driver': 'Řidič',
    'lessee': 'Nájemce',
    'other': 'Jiný'
  };
  return translations[type] || type;
}

function generateLocationsContent(data: any): string {
  let html = '<div class="module">';

  if (data.locations && Array.isArray(data.locations) && data.locations.length > 0) {
    html += `<h3>Lokace (${data.locations.length})</h3>`;

    for (let i = 0; i < data.locations.length; i++) {
      const location = data.locations[i];

      html += `
        <div class="location-section">
          <h4>Lokace ${i + 1}</h4>
          <table class="personal-info-table">
            <tr>
              <td>Název:</td>
              <td><strong>${location.name || 'Neuvedeno'}</strong></td>
            </tr>
            <tr>
              <td>Adresa:</td>
              <td>${location.address || 'Neuvedeno'}</td>
            </tr>
            <tr>
              <td>GPS souřadnice:</td>
              <td>${location.coordinates || location.gps || 'Neuvedeno'}</td>
            </tr>
            <tr>
              <td>Typ lokace:</td>
              <td>${location.type || 'Neuvedeno'}</td>
            </tr>`;

      if (location.description) {
        html += `
            <tr>
              <td>Popis:</td>
              <td>${location.description}</td>
            </tr>`;
      }

      if (location.visitDate) {
        html += `
            <tr>
              <td>Datum návštěvy:</td>
              <td>${location.visitDate}</td>
            </tr>`;
      }

      html += `</table>`;

      // Poznámky k lokaci
      if (location.notes) {
        html += `
          <div class="notes-section">
            <h5>Poznámky:</h5>
            <p>${location.notes}</p>
          </div>`;
      }

      // Fotografie lokace
      if (location.photos && Array.isArray(location.photos) && location.photos.length > 0) {
        html += `
          <div class="photo-gallery">
            <h5>Fotodokumentace lokace ${i + 1}</h5>`;

        for (let j = 0; j < location.photos.length; j++) {
          const photo = location.photos[j];
          const imageSource = photo.downloadURL || photo.url || photo.photoUrl;

          if (imageSource) {
            html += `
              <div class="photo-item" style="text-align: center; margin-bottom: 20px;">
                <img src="${imageSource}" alt="Fotografie lokace ${i + 1}" style="max-width: 100%; max-height: 400px; margin: 0 auto; display: block;">
                ${photo.description ? `<div class="photo-description" style="text-align: center; margin-top: 10px;">${photo.description}</div>` : ''}
                ${photo.dateTaken ? `<div class="photo-date" style="text-align: center; margin-top: 5px;">Datum pořízení: ${photo.dateTaken}</div>` : ''}
              </div>
            `;
          }
        }

        html += `</div>`;
      }

      html += `</div>`;

      // Oddělovač mezi lokacemi
      if (i < data.locations.length - 1) {
        html += `<hr style="border: none; border-top: 2px solid #ddd; margin: 20px 0;">`;
      }
    }
  } else {
    html += `<h3>Lokace</h3>
      <p>Žádné lokace nejsou evidovány.</p>`;
  }

  html += '</div>';
  return html;
}

function generateBusinessActivityContent(data: any): string {
  let html = '<div class="module">';

  html += `<h3>Podnikatelská činnost</h3>
    <table class="personal-info-table">
      <tr>
        <td>Název společnosti:</td>
        <td><strong>${data.companyName || data.name || 'Neuvedeno'}</strong></td>
      </tr>
      <tr>
        <td>IČO:</td>
        <td>${data.ico || 'Neuvedeno'}</td>
      </tr>
      <tr>
        <td>DIČ:</td>
        <td>${data.dic || 'Neuvedeno'}</td>
      </tr>
      <tr>
        <td>Právní forma:</td>
        <td>${data.legalForm || 'Neuvedeno'}</td>
      </tr>
      <tr>
        <td>Stav:</td>
        <td>${data.status || 'Neuvedeno'}</td>
      </tr>`;

  if (data.address) {
    html += `
      <tr>
        <td>Adresa sídla:</td>
        <td>${data.address}</td>
      </tr>`;
  }

  if (data.establishedDate) {
    html += `
      <tr>
        <td>Datum založení:</td>
        <td>${data.establishedDate}</td>
      </tr>`;
  }

  html += `</table>`;

  // Předměty podnikání
  if (data.businessActivities && Array.isArray(data.businessActivities) && data.businessActivities.length > 0) {
    html += `<h4>Předměty podnikání</h4>
      <ul class="business-activities-list">`;

    for (const activity of data.businessActivities) {
      html += `<li>${activity.name || activity}</li>`;
    }

    html += `</ul>`;
  }

  // Poznámky
  if (data.notes) {
    html += `
      <div class="notes-section">
        <h4>Poznámky:</h4>
        <p>${data.notes}</p>
      </div>`;
  }

  // Fotografie
  if (data.photos && Array.isArray(data.photos) && data.photos.length > 0) {
    html += `
      <div class="photo-gallery">
        <h4>Fotodokumentace</h4>`;

    for (let i = 0; i < data.photos.length; i++) {
      const photo = data.photos[i];
      const imageSource = photo.downloadURL || photo.url || photo.photoUrl;

      if (imageSource) {
        html += `
          <div class="photo-item" style="text-align: center; margin-bottom: 20px;">
            <img src="${imageSource}" alt="Fotografie ${i + 1}" style="max-width: 100%; max-height: 400px; margin: 0 auto; display: block;">
            ${photo.description ? `<div class="photo-description" style="text-align: center; margin-top: 10px;">${photo.description}</div>` : ''}
            ${photo.dateTaken ? `<div class="photo-date" style="text-align: center; margin-top: 5px;">Datum pořízení: ${photo.dateTaken}</div>` : ''}
          </div>
        `;
      }
    }

    html += `</div>`;
  }

  html += '</div>';
  return html;
}

function generateModernHtmlReport(settings: any, moduleData: ModuleDataMap, caseId: string): string {
  const currentDate = new Date().toLocaleDateString('cs-CZ', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  });

  // Generování obsahu modulů v moderním stylu
  let modulesHtml = '';
  let pageNumber = 1;

  // Určení pořadí modulů (stejná logika jako u oficiálního stylu)
  let moduleOrder: string[] = [];

  if (settings.moduleOrder && Array.isArray(settings.moduleOrder) && settings.moduleOrder.length > 0) {
    moduleOrder = settings.moduleOrder.filter((moduleId: string) => !!moduleData[moduleId]);
    const missingModuleIds = Object.keys(moduleData).filter((moduleId: string) => !moduleOrder.includes(moduleId));
    if (missingModuleIds.length > 0) {
      moduleOrder = [...moduleOrder, ...missingModuleIds];
    }
  } else {
    moduleOrder = Object.keys(moduleData).sort((a: string, b: string) => {
      let typeA = '', typeB = '';
      if (a.includes('_')) {
        const parts = a.split('_');
        if (parts.length >= 2) {
          typeA = parts[1];
        }
      }
      if (b.includes('_')) {
        const parts = b.split('_');
        if (parts.length >= 2) {
          typeB = parts[1];
        }
      }
      return typeA.localeCompare(typeB);
    });
  }

  // Seskupíme moduly podle subjektů
  const modulesBySubject: { [key: string]: string[] } = {};
  for (const moduleId of moduleOrder) {
    const parts = moduleId.split('_');
    if (parts.length < 2) continue;
    const subjectId = parts[0];
    if (!modulesBySubject[subjectId]) {
      modulesBySubject[subjectId] = [];
    }
    modulesBySubject[subjectId].push(moduleId);
  }

  // Generujeme moderní HTML pro každý subjekt a jeho moduly
  for (const [subjectId, subjectModules] of Object.entries(modulesBySubject)) {
    for (const moduleId of subjectModules) {
      try {
        const data = moduleData[moduleId];
        if (!data) continue;

        const parts = moduleId.split('_');
        let moduleType = parts.length >= 2 ? parts.slice(1).join('_') : moduleId;

        let moduleCategory = getCategoryForModule(moduleType);

        // Moderní stránka pro modul
        modulesHtml += `
          <div class="modern-page-break">
            <div class="modern-page-header">
              <div class="modern-page-number">${pageNumber++}. strana</div>
              <div class="modern-page-divider"></div>
            </div>

            <div class="modern-section">
              <div class="modern-section-header">
                <div class="modern-section-icon">
                  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </div>
                <div>
                  <div class="modern-section-title">${getModuleTitle(moduleType)}</div>
                  <div class="modern-section-subtitle">${moduleCategory}</div>
                </div>
              </div>
              ${generateModernModuleContent(moduleType, data, caseId, subjectId)}
            </div>
          </div>
        `;
      } catch (error) {
        console.error(`Chyba při generování moderního obsahu modulu ${moduleId}:`, error);
      }
    }
  }

  return `
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSINT Report - Moderní styl</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 12pt;
            line-height: 1.6;
            color: #1f2937;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
        }

        :root {
            --primary-color: #2563eb;
            --primary-light: #dbeafe;
            --primary-dark: #1e40af;
            --secondary-color: #475569;
            --accent-color: #f59e0b;
            --danger-color: #ef4444;
            --success-color: #10b981;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-700: #374151;
            --gray-800: #1f2937;
        }

        .document-container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
        }

        /* Moderní úvodní stránka */
        .modern-cover {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: white;
            padding: 60px 40px;
            text-align: center;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .modern-cover::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .modern-title {
            font-size: 48px;
            font-weight: 800;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
        }

        .modern-subtitle {
            font-size: 24px;
            font-weight: 300;
            margin-bottom: 40px;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }

        .modern-info-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            z-index: 1;
        }

        /* Moderní stránky modulů */
        .modern-page-break {
            page-break-before: always;
            margin-top: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }

        .modern-page-header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .modern-page-number {
            font-size: 18pt;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 15px;
        }

        .modern-page-divider {
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            border-radius: 2px;
            margin: 0 auto;
            width: 200px;
        }

        .modern-section {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .modern-section-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid var(--gray-200);
        }

        .modern-section-icon {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 20px;
            box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
        }

        .modern-section-title {
            font-size: 28px;
            font-weight: 700;
            color: var(--gray-800);
            margin: 0;
        }

        .modern-section-subtitle {
            font-size: 14px;
            color: var(--gray-700);
            margin-top: 5px;
        }

        /* Moderní tabulky */
        .modern-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .modern-table th,
        .modern-table td {
            padding: 15px 20px;
            text-align: left;
            border-bottom: 1px solid var(--gray-200);
        }

        .modern-table th {
            background: var(--gray-50);
            font-weight: 600;
            color: var(--gray-800);
        }

        .modern-table tr:hover {
            background: var(--gray-50);
        }

        /* Moderní fotografie */
        .modern-photo-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .modern-photo-container {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .modern-photo-container:hover {
            transform: translateY(-5px);
        }

        .modern-photo-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .modern-photo-details {
            padding: 15px;
        }

        .modern-photo-title {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 8px;
        }

        .modern-photo-desc,
        .modern-photo-date {
            font-size: 11px;
            color: var(--gray-700);
            margin-bottom: 4px;
        }

        /* Moderní poznámky */
        .modern-notes {
            background: var(--gray-50);
            border-left: 4px solid var(--primary-color);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }

        .modern-notes-title {
            font-weight: 600;
            color: var(--gray-800);
            margin-bottom: 10px;
        }

        .modern-notes-content {
            color: var(--gray-700);
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="document-container">
        <!-- Moderní úvodní stránka -->
        <div class="modern-cover">
            <div class="modern-title">${settings.title || 'OSINT Report'}</div>
            <div class="modern-subtitle">Analýza subjektu: ${settings.subject || 'Neuvedeno'}</div>

            <div class="modern-info-card">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; text-align: left;">
                    <div>
                        <strong>JID:</strong> ${settings.jid || 'Neuvedeno'}<br>
                        <strong>Číslo jednací:</strong> ${settings.documentNumber || 'Neuvedeno'}<br>
                        <strong>Místo:</strong> ${settings.location || 'Neuvedeno'}
                    </div>
                    <div>
                        <strong>Datum:</strong> ${settings.date || currentDate}<br>
                        <strong>Zpracoval:</strong> ${extractInvestigatorName(settings.department)}<br>
                        <strong>Styl:</strong> Moderní
                    </div>
                </div>
            </div>

            <div style="margin-top: 40px; font-size: 14px; opacity: 0.8;">
                ${settings.purpose || 'Provést komplexní OSINT (Open Source Intelligence) analýzu zájmové osoby'}
            </div>
        </div>

        <!-- Moderní moduly -->
        ${modulesHtml}
    </div>
</body>
</html>`;
}

function generateModernModuleContent(moduleType: string, data: any, caseId: string, subjectId: string): string {
  // Normalizace typu modulu
  let normalizedModuleType = moduleType.replace(/-/g, '_');

  // Generování obsahu podle typu modulu v moderním stylu
  switch (normalizedModuleType) {
    case 'evidence_obyvatel':
      return generateModernEvidenceObyvatelContent(data);
    case 'vehicles':
      return generateModernVehiclesContent(data);
    case 'gun_license':
      return generateModernGunLicenseContent(data);
    case 'locations':
      return generateModernLocationsContent(data);
    case 'business_activity':
      return generateModernBusinessActivityContent(data);
    default:
      // Pro ostatní moduly použijeme základní moderní zobrazení
      return generateModernGenericContent(data, moduleType);
  }
}

function generateModernGenericContent(data: any, moduleType: string): string {
  let html = '<div class="modern-module-content">';

  // Zobrazíme data v moderní tabulce
  html += `
    <table class="modern-table">
      <thead>
        <tr>
          <th>Vlastnost</th>
          <th>Hodnota</th>
        </tr>
      </thead>
      <tbody>
  `;

  // Projdeme všechny vlastnosti dat
  for (const [key, value] of Object.entries(data)) {
    if (key === 'photos' || key === 'createdAt' || key === 'updatedAt' || key === 'subjectId') {
      continue; // Přeskočíme speciální vlastnosti
    }

    let displayValue = '';
    if (Array.isArray(value)) {
      displayValue = value.length > 0 ? `${value.length} položek` : 'Žádné položky';
    } else if (typeof value === 'object' && value !== null) {
      displayValue = JSON.stringify(value, null, 2);
    } else {
      displayValue = String(value || 'Neuvedeno');
    }

    html += `
      <tr>
        <td><strong>${key}</strong></td>
        <td>${displayValue}</td>
      </tr>
    `;
  }

  html += `
      </tbody>
    </table>
  `;

  // Fotografie
  if (data.photos && Array.isArray(data.photos) && data.photos.length > 0) {
    html += `
      <div class="modern-notes">
        <div class="modern-notes-title">Fotodokumentace (${data.photos.length})</div>
        <div class="modern-photo-gallery">
    `;

    for (let i = 0; i < data.photos.length; i++) {
      const photo = data.photos[i];
      const imageSource = photo.downloadURL || photo.url || photo.photoUrl;

      if (imageSource) {
        html += `
          <div class="modern-photo-container">
            <img src="${imageSource}" alt="Fotografie ${i + 1}" class="modern-photo-image">
            <div class="modern-photo-details">
              <div class="modern-photo-title">📷 Fotografie ${i + 1}</div>
              ${photo.description ? `<div class="modern-photo-desc">${photo.description}</div>` : ''}
              ${photo.dateTaken ? `<div class="modern-photo-date">Datum: ${photo.dateTaken}</div>` : ''}
            </div>
          </div>
        `;
      }
    }

    html += `
        </div>
      </div>
    `;
  }

  html += '</div>';
  return html;
}

function generateModernVehiclesContent(data: any): string {
  let html = '<div class="modern-module-content">';

  if (data.vehicles && Array.isArray(data.vehicles) && data.vehicles.length > 0) {
    for (let i = 0; i < data.vehicles.length; i++) {
      const vehicle = data.vehicles[i];

      html += `
        <div style="margin-bottom: 30px;">
          <h4 style="color: var(--primary-color); margin-bottom: 15px;">🚗 Vozidlo ${i + 1}</h4>
          <table class="modern-table">
            <tbody>
              <tr>
                <td><strong>Značka a model</strong></td>
                <td><strong>${vehicle.brand || vehicle.make || 'Neuvedeno'} ${vehicle.model || ''}</strong></td>
              </tr>
              <tr>
                <td><strong>SPZ/RZ</strong></td>
                <td><strong>${vehicle.licensePlate || 'Neuvedeno'}</strong></td>
              </tr>
              <tr>
                <td>VIN</td>
                <td>${vehicle.vin || 'Neuvedeno'}</td>
              </tr>
              <tr>
                <td>Barva</td>
                <td>${vehicle.color || 'Neuvedeno'}</td>
              </tr>
              <tr>
                <td>Rok výroby</td>
                <td>${vehicle.yearManufactured || 'Neuvedeno'}</td>
              </tr>
              <tr>
                <td>Vztah k vozidlu</td>
                <td>${translateRelationshipType(vehicle.relationshipType) || 'Neuvedeno'}</td>
              </tr>
            </tbody>
          </table>

          ${vehicle.notes ? `
          <div class="modern-notes">
            <div class="modern-notes-title">Poznámky</div>
            <div class="modern-notes-content">${vehicle.notes}</div>
          </div>
          ` : ''}

          ${vehicle.photos && vehicle.photos.length > 0 ? `
          <div class="modern-photo-gallery">
            ${vehicle.photos.map((photo: any, j: number) => {
              const imageSource = photo.downloadURL || photo.url || photo.photoUrl;
              return imageSource ? `
                <div class="modern-photo-container">
                  <img src="${imageSource}" alt="Fotografie vozidla ${i + 1}" class="modern-photo-image">
                  <div class="modern-photo-details">
                    <div class="modern-photo-title">📷 Vozidlo ${i + 1} - Foto ${j + 1}</div>
                    ${photo.description ? `<div class="modern-photo-desc">${photo.description}</div>` : ''}
                    ${photo.dateTaken ? `<div class="modern-photo-date">Datum: ${photo.dateTaken}</div>` : ''}
                  </div>
                </div>
              ` : '';
            }).join('')}
          </div>
          ` : ''}
        </div>
      `;
    }
  } else {
    html += `<p style="text-align: center; color: var(--gray-700); font-style: italic;">Žádná vozidla nejsou evidována.</p>`;
  }

  html += '</div>';
  return html;
}

function generateModernGunLicenseContent(data: any): string {
  let html = '<div class="modern-module-content">';

  html += `
    <table class="modern-table">
      <tbody>
        <tr>
          <td><strong>Má zbrojní průkaz</strong></td>
          <td><span style="color: ${data.hasGunLicensePermit ? 'var(--success-color)' : 'var(--danger-color)'}; font-weight: bold;">${data.hasGunLicensePermit ? '✅ Ano' : '❌ Ne'}</span></td>
        </tr>
        ${data.hasGunLicensePermit ? `
        <tr>
          <td>Skupiny ZP</td>
          <td>${data.gunLicensePermitGroups || 'Neuvedeno'}</td>
        </tr>
        <tr>
          <td>Členství ve střeleckém klubu</td>
          <td>${data.shootingClubMembership || 'Neuvedeno'}</td>
        </tr>
        ` : ''}
      </tbody>
    </table>
  `;

  // Registrované zbraně
  if (data.registeredWeapons && Array.isArray(data.registeredWeapons) && data.registeredWeapons.length > 0) {
    html += `
      <div style="margin-top: 30px;">
        <h4 style="color: var(--primary-color); margin-bottom: 15px;">🔫 Registrované zbraně (${data.registeredWeapons.length})</h4>
    `;

    for (let i = 0; i < data.registeredWeapons.length; i++) {
      const weapon = data.registeredWeapons[i];
      html += `
        <div style="margin-bottom: 20px;">
          <table class="modern-table">
            <tbody>
              <tr>
                <td><strong>Zbraň ${i + 1}</strong></td>
                <td><strong>${weapon.manufacturer || 'Neuvedeno'} ${weapon.model || ''}</strong></td>
              </tr>
              <tr>
                <td>Typ zbraně</td>
                <td>${weapon.weaponType || 'Neuvedeno'}</td>
              </tr>
              <tr>
                <td>Sériové číslo</td>
                <td>${weapon.serialNumber || 'Neuvedeno'}</td>
              </tr>
              <tr>
                <td>Ráže</td>
                <td>${weapon.caliber || 'Neuvedeno'}</td>
              </tr>
              <tr>
                <td>Stav zbraně</td>
                <td>${weapon.weaponCondition || 'Neuvedeno'}</td>
              </tr>
            </tbody>
          </table>
        </div>
      `;
    }

    html += `</div>`;
  }

  html += '</div>';
  return html;
}

function generateModernLocationsContent(data: any): string {
  let html = '<div class="modern-module-content">';

  if (data.locations && Array.isArray(data.locations) && data.locations.length > 0) {
    for (let i = 0; i < data.locations.length; i++) {
      const location = data.locations[i];

      html += `
        <div style="margin-bottom: 30px;">
          <h4 style="color: var(--primary-color); margin-bottom: 15px;">📍 Lokace ${i + 1}</h4>
          <table class="modern-table">
            <tbody>
              <tr>
                <td><strong>Název</strong></td>
                <td><strong>${location.name || 'Neuvedeno'}</strong></td>
              </tr>
              <tr>
                <td>Adresa</td>
                <td>${location.address || 'Neuvedeno'}</td>
              </tr>
              <tr>
                <td>GPS souřadnice</td>
                <td>${location.coordinates || location.gps || 'Neuvedeno'}</td>
              </tr>
              <tr>
                <td>Typ lokace</td>
                <td>${location.type || 'Neuvedeno'}</td>
              </tr>
            </tbody>
          </table>

          ${location.notes ? `
          <div class="modern-notes">
            <div class="modern-notes-title">Poznámky</div>
            <div class="modern-notes-content">${location.notes}</div>
          </div>
          ` : ''}
        </div>
      `;
    }
  } else {
    html += `<p style="text-align: center; color: var(--gray-700); font-style: italic;">Žádné lokace nejsou evidovány.</p>`;
  }

  html += '</div>';
  return html;
}

function generateModernBusinessActivityContent(data: any): string {
  let html = '<div class="modern-module-content">';

  html += `
    <table class="modern-table">
      <tbody>
        <tr>
          <td><strong>Název společnosti</strong></td>
          <td><strong>${data.companyName || data.name || 'Neuvedeno'}</strong></td>
        </tr>
        <tr>
          <td>IČO</td>
          <td>${data.ico || 'Neuvedeno'}</td>
        </tr>
        <tr>
          <td>DIČ</td>
          <td>${data.dic || 'Neuvedeno'}</td>
        </tr>
        <tr>
          <td>Právní forma</td>
          <td>${data.legalForm || 'Neuvedeno'}</td>
        </tr>
        <tr>
          <td>Stav</td>
          <td>${data.status || 'Neuvedeno'}</td>
        </tr>
      </tbody>
    </table>
  `;

  // Předměty podnikání
  if (data.businessActivities && Array.isArray(data.businessActivities) && data.businessActivities.length > 0) {
    html += `
      <div style="margin-top: 30px;">
        <h4 style="color: var(--primary-color); margin-bottom: 15px;">💼 Předměty podnikání</h4>
        <ul style="list-style: none; padding: 0;">
    `;

    for (const activity of data.businessActivities) {
      html += `<li style="padding: 8px 0; border-bottom: 1px solid var(--gray-200);">• ${activity.name || activity}</li>`;
    }

    html += `</ul></div>`;
  }

  html += '</div>';
  return html;
}
