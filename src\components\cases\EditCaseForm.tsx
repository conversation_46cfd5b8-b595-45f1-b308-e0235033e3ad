
"use client";

import type { SubmitHandler} from 'react-hook-form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import type { Case } from '@/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label'; // Not strictly needed with FormField
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Loader2 } from 'lucide-react';

// Schema for editable fields
const formSchema = z.object({
  title: z.string().min(1, "Název případu je povinný."),
  referenceNumber: z.string().optional(),
  internalId: z.string().optional(),
  // creationDate is handled by Firestore timestamp, not directly editable in this form once set
  description: z.string().min(1, "Popis skutku je povinný."),
  deadlineInfo: z.string().min(1, "<PERSON>rm<PERSON> je povinný."),
  // Add other fields if they should be editable e.g. priority, status (would need Select components)
});

// Type for form values based on schema
type EditCaseFormValues = z.infer<typeof formSchema>;

interface EditCaseFormProps {
  caseData: Case; // Expect full Case object, including creationDate as string for defaultValues
  onSubmit: (data: EditCaseFormValues) => void;
  onCancel: () => void;
  isSaving?: boolean;
}

export function EditCaseForm({ caseData, onSubmit, onCancel, isSaving = false }: EditCaseFormProps) {
  const form = useForm<EditCaseFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: caseData.title || '',
      referenceNumber: caseData.referenceNumber || '',
      internalId: caseData.internalId || '',
      // creationDate is not part of formSchema as it's not directly editable here
      description: caseData.description || '',
      deadlineInfo: caseData.deadlineInfo || '',
    },
  });

  // This handler receives only the validated & typed form values
  const handleFormSubmit: SubmitHandler<EditCaseFormValues> = (data) => {
    onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6 py-4">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Název případu</FormLabel>
              <FormControl>
                <Input placeholder="Např. Kybernetický útok na banku" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="referenceNumber"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Č.J. (Číslo jednací)</FormLabel>
                <FormControl>
                  <Input placeholder="Např. ČJ-123/2024-KRA" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="internalId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>JID (Interní ID)</FormLabel>
                <FormControl>
                  <Input placeholder="Např. JID-XYZ-001" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
        
        {/* Display creationDate but don't make it editable in this form */}
        <FormItem>
            <FormLabel>Datum vytvoření</FormLabel>
            <FormControl>
                <Input 
                    value={caseData.creationDate || 'N/A'} // Use the string version of creationDate
                    readOnly 
                    className="bg-muted/50 cursor-not-allowed"
                />
            </FormControl>
            <FormDescription>
                Datum vytvoření případu (nelze změnit).
            </FormDescription>
        </FormItem>

        <FormField
            control={form.control}
            name="deadlineInfo"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Termín (Do kdy)</FormLabel>
                <FormControl>
                  <Input placeholder="Např. 31.12.2024 nebo 'ASAP'" {...field} />
                </FormControl>
                 <FormDescription>
                  Termín pro dokončení případu.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
       
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Popis skutku</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Detailní popis vyšetřovaného skutku, události nebo problému."
                  rows={5}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button type="button" variant="outline" onClick={onCancel} disabled={isSaving}>
            Zrušit
          </Button>
          <Button type="submit" disabled={isSaving}>
            {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isSaving ? "Ukládání..." : "Uložit změny"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
