// Systém pro ukládání a načítání vlastních stylů PDF

export interface CustomStyle {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  settings: {
    // Základní nastavení
    title: string;
    subject: string;
    jid: string;
    documentNumber: string;
    location: string;
    date: string;
    department: string;
    purpose: string;
    
    // Styling
    style: 'official' | 'modern';
    fontSize: number;
    lineHeight: number;
    margins: {
      top: number;
      bottom: number;
      left: number;
      right: number;
    };
    
    // Barvy
    primaryColor: string;
    secondaryColor: string;
    textColor: string;
    backgroundColor: string;
    
    // Rozložení
    headerHeight: number;
    footerHeight: number;
    showPageNumbers: boolean;
    pageNumberPosition: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
    showBorders: boolean;
    borderWidth: number;
    borderColor: string;
    
    // Fotografie
    photoSize: 'small' | 'medium' | 'large';
    photoAlignment: 'left' | 'center' | 'right';
    photoSpacing: number;
  };
}

// Přednastavené styly
export const defaultStyles: CustomStyle[] = [
  {
    id: 'official-default',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON> (výchozí)',
    description: 'Standardní byrokratický styl pro oficiální dokumenty',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    settings: {
      title: 'OSINT (Open Source Intelligence)',
      subject: '',
      jid: '',
      documentNumber: '',
      location: 'Pardubice',
      date: new Date().toLocaleDateString('cs-CZ', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      }),
      department: `Odbor analytiky a kyber. kriminality\nOddělení kybernetické kriminality\nNa Spravedlnosti 2516, 530 48 Pardubice`,
      purpose: 'Provést komplexní OSINT (Open Source Intelligence) analýzu zájmové osoby',
      style: 'official',
      fontSize: 11,
      lineHeight: 1.5,
      margins: { top: 20, bottom: 20, left: 20, right: 20 },
      primaryColor: '#000000',
      secondaryColor: '#333333',
      textColor: '#000000',
      backgroundColor: '#ffffff',
      headerHeight: 60,
      footerHeight: 40,
      showPageNumbers: true,
      pageNumberPosition: 'top-left',
      showBorders: true,
      borderWidth: 2,
      borderColor: '#000000',
      photoSize: 'large',
      photoAlignment: 'center',
      photoSpacing: 15
    }
  },
  {
    id: 'modern-default',
    name: 'Moderní (výchozí)',
    description: 'Moderní analytický styl s barevnými prvky',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    settings: {
      title: 'OSINT (Open Source Intelligence)',
      subject: '',
      jid: '',
      documentNumber: '',
      location: 'Pardubice',
      date: new Date().toLocaleDateString('cs-CZ', {
        day: 'numeric',
        month: 'long',
        year: 'numeric'
      }),
      department: `Odbor analytiky a kyber. kriminality\nOddělení kybernetické kriminality\nNa Spravedlnosti 2516, 530 48 Pardubice`,
      purpose: 'Provést komplexní OSINT (Open Source Intelligence) analýzu zájmové osoby',
      style: 'modern',
      fontSize: 11,
      lineHeight: 1.5,
      margins: { top: 15, bottom: 15, left: 15, right: 15 },
      primaryColor: '#2c3e50',
      secondaryColor: '#3498db',
      textColor: '#333333',
      backgroundColor: '#ffffff',
      headerHeight: 60,
      footerHeight: 40,
      showPageNumbers: true,
      pageNumberPosition: 'top-left',
      showBorders: true,
      borderWidth: 2,
      borderColor: '#2c3e50',
      photoSize: 'large',
      photoAlignment: 'center',
      photoSpacing: 15
    }
  }
];

// Klíč pro localStorage
const CUSTOM_STYLES_KEY = 'osint-custom-styles';

// Funkce pro načtení všech stylů
export function getCustomStyles(): CustomStyle[] {
  if (typeof window === 'undefined') return defaultStyles;
  
  try {
    const stored = localStorage.getItem(CUSTOM_STYLES_KEY);
    if (stored) {
      const customStyles = JSON.parse(stored);
      return [...defaultStyles, ...customStyles];
    }
  } catch (error) {
    console.error('Chyba při načítání vlastních stylů:', error);
  }
  
  return defaultStyles;
}

// Funkce pro uložení nového stylu
export function saveCustomStyle(style: Omit<CustomStyle, 'id' | 'createdAt' | 'updatedAt'>): CustomStyle {
  const newStyle: CustomStyle = {
    ...style,
    id: `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  };
  
  try {
    const existingStyles = getCustomStyles().filter(s => !s.id.startsWith('official-') && !s.id.startsWith('modern-'));
    const updatedStyles = [...existingStyles, newStyle];
    localStorage.setItem(CUSTOM_STYLES_KEY, JSON.stringify(updatedStyles));
    return newStyle;
  } catch (error) {
    console.error('Chyba při ukládání vlastního stylu:', error);
    throw new Error('Nepodařilo se uložit vlastní styl');
  }
}

// Funkce pro aktualizaci existujícího stylu
export function updateCustomStyle(id: string, updates: Partial<CustomStyle>): CustomStyle | null {
  try {
    const allStyles = getCustomStyles();
    const styleIndex = allStyles.findIndex(s => s.id === id);
    
    if (styleIndex === -1) {
      throw new Error('Styl nebyl nalezen');
    }
    
    const updatedStyle: CustomStyle = {
      ...allStyles[styleIndex],
      ...updates,
      updatedAt: new Date().toISOString()
    };
    
    // Aktualizujeme pouze vlastní styly (ne výchozí)
    if (!id.startsWith('official-') && !id.startsWith('modern-')) {
      const customStyles = allStyles.filter(s => !s.id.startsWith('official-') && !s.id.startsWith('modern-'));
      const updatedCustomStyles = customStyles.map(s => s.id === id ? updatedStyle : s);
      localStorage.setItem(CUSTOM_STYLES_KEY, JSON.stringify(updatedCustomStyles));
    }
    
    return updatedStyle;
  } catch (error) {
    console.error('Chyba při aktualizaci vlastního stylu:', error);
    return null;
  }
}

// Funkce pro smazání vlastního stylu
export function deleteCustomStyle(id: string): boolean {
  // Nelze smazat výchozí styly
  if (id.startsWith('official-') || id.startsWith('modern-')) {
    return false;
  }
  
  try {
    const allStyles = getCustomStyles();
    const customStyles = allStyles.filter(s => !s.id.startsWith('official-') && !s.id.startsWith('modern-') && s.id !== id);
    localStorage.setItem(CUSTOM_STYLES_KEY, JSON.stringify(customStyles));
    return true;
  } catch (error) {
    console.error('Chyba při mazání vlastního stylu:', error);
    return false;
  }
}

// Funkce pro získání konkrétního stylu
export function getCustomStyle(id: string): CustomStyle | null {
  const styles = getCustomStyles();
  return styles.find(s => s.id === id) || null;
}

// Funkce pro export stylů
export function exportCustomStyles(): string {
  const customStyles = getCustomStyles().filter(s => !s.id.startsWith('official-') && !s.id.startsWith('modern-'));
  return JSON.stringify(customStyles, null, 2);
}

// Funkce pro import stylů
export function importCustomStyles(jsonData: string): boolean {
  try {
    const importedStyles = JSON.parse(jsonData);
    
    if (!Array.isArray(importedStyles)) {
      throw new Error('Neplatný formát dat');
    }
    
    // Validace struktury
    for (const style of importedStyles) {
      if (!style.name || !style.settings) {
        throw new Error('Neplatná struktura stylu');
      }
    }
    
    // Přidáme nové ID a časové značky
    const processedStyles = importedStyles.map(style => ({
      ...style,
      id: `imported-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }));
    
    const existingStyles = getCustomStyles().filter(s => !s.id.startsWith('official-') && !s.id.startsWith('modern-'));
    const allCustomStyles = [...existingStyles, ...processedStyles];
    
    localStorage.setItem(CUSTOM_STYLES_KEY, JSON.stringify(allCustomStyles));
    return true;
  } catch (error) {
    console.error('Chyba při importu stylů:', error);
    return false;
  }
}
