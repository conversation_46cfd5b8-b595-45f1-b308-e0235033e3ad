import { CaseData } from '../DataCollector';

export function generatePhoneNumbersPDF(data: any[]): string {
  if (!data || data.length === 0) return '';

  return `
  <div class="contact-group">
      <div class="contact-header">Telefonní čísla</div>
      <div class="contact-list">
          ${data.map((phone) => `
          <div class="contact-entry">
              <div class="contact-main">
                  <div class="contact-primary">${phone.number}</div>
                  <div class="contact-secondary">${phone.operator || 'Neznámý operátor'} • ${phone.type || 'Mobilní'}</div>
              </div>
              <div class="contact-status">Aktivní</div>
          </div>
          `).join('')}
      </div>
  </div>`;
}

export function generateSocialMediaPDF(data: any): string {
  if (!data) return '';

  // Vytvoříme pole sociálních sítí pro lepší zpracování
  const socialNetworks = [
    { name: 'Facebook', url: data.facebook, verified: data.facebookVerified },
    { name: 'Instagram', url: data.instagram, verified: data.instagramVerified },
    { name: 'Twitter', url: data.twitter, verified: data.twitterVerified },
    { name: 'LinkedIn', url: data.linkedin, verified: data.linkedinVerified },
    { name: 'TikTok', url: data.tiktok, verified: data.tiktokVerified },
    { name: 'YouTube', url: data.youtube, verified: data.youtubeVerified }
  ].filter(network => network.url); // Filtrujeme jen ty, které mají URL

  if (socialNetworks.length === 0) return '';

  return `
  <div class="contact-group">
      <div class="contact-header">Sociální sítě</div>
      <div class="contact-list">
          ${socialNetworks.map(network => `
          <div class="contact-entry">
              <div class="contact-main">
                  <div class="contact-primary">${network.name}</div>
                  <div class="contact-secondary">${network.url}</div>
              </div>
              <div class="contact-status">${network.verified ? 'Ověřeno' : 'Neověřeno'}</div>
          </div>
          `).join('')}
      </div>
  </div>`;
}
