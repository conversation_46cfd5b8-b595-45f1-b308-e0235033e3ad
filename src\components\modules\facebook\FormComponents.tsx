import React from "react";
import { UseFormReturn, Control, FieldValues, FieldPath } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { cn } from "@/lib/utils";

// FormItemRHF komponenta podle evidence obyvatel
interface FormItemRHFProps<T extends FieldValues = any> {
  label: string;
  name: FieldPath<T>;
  control: Control<T>;
  placeholder?: string;
  type?: string;
  disabled?: boolean;
  className?: string;
  as?: 'input' | 'textarea';
  rows?: number;
  smallLabel?: boolean;
}

export const FormItemRHF = <T extends FieldValues>({ 
  label, 
  name, 
  control, 
  placeholder, 
  type = "text", 
  disabled = false, 
  className, 
  as = 'input', 
  rows = 3, 
  smallLabel = false 
}: FormItemRHFProps<T>) => (
  <FormField
    control={control} 
    name={name} 
    disabled={disabled}
    render={({ field, fieldState: { error } }) => (
      <FormItem className={className}>
        <FormLabel className={smallLabel ? "text-xs font-medium" : "font-semibold text-sm"}>
          {label}
        </FormLabel>
        <FormControl>
          {as === 'input' ? (
            <Input 
              {...field} 
              value={field.value || ''} 
              placeholder={placeholder} 
              type={type} 
              disabled={disabled}
              className={error ? "border-destructive" : ""} 
            />
          ) : (
            <Textarea 
              {...field} 
              value={field.value || ''} 
              placeholder={placeholder} 
              rows={rows} 
              disabled={disabled}
              className={error ? "border-destructive" : ""} 
            />
          )}
        </FormControl>
        {error && <FormMessage />}
      </FormItem>
    )}
  />
);

// Typy pro Facebook formulář
export interface FacebookFormValues {
  profileName?: string;
  username?: string;
  profileUrl?: string;
  currentLocation?: string;
  currentJob?: string;
  education?: string;
  relationshipStatus?: string;
  aboutMe?: string;
  contactInfo?: string;
  otherPersonalInfo?: string;
  friends?: Array<{
    id: string;
    name: string;
    profileUrl?: string;
    extraInfo?: string[];
    isPersonOfInterest?: boolean;
    notes?: string;
  }>;
  friendsRawData?: string;
  likedPagesRawData?: string;
  groupsRawData?: string;
  posts?: Array<{
    id: string;
    date?: string;
    text?: string;
    likesCount?: number;
    sharesCount?: number;
    reactionsList?: string;
    sharesList?: string;
    comments?: string;
    notes?: string;
  }>;
  events?: Array<{
    id: string;
    eventName?: string;
    organizer?: string;
    eventDate?: string;
    eventTime?: string;
    location?: string;
    participation?: "going" | "interested" | "invited" | "declined" | "unknown";
    description?: string;
    eventUrl?: string;
    participantsCount?: number;
    interestedCount?: number;
    notes?: string;
  }>;
  photos?: Array<{
    id: string;
    fileName: string;
    storagePath: string;
    downloadUrl: string;
    uploadedAt: Date;
    category?: string;
    description?: string;
  }>;
  osintNotes?: string;
  investigationNotes?: string;
}

// Zod schema bude v hlavním souboru FacebookForm.tsx, protože je specifický pro validaci 