"use client";

import { useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { Control, useFieldArray, useFormContext } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { NetworkAnalysisModuleFormValues } from "./schemas";
import { Image, Upload, X, ExternalLink } from "lucide-react";

interface PhotoUploadSectionProps {
  control: Control<NetworkAnalysisModuleFormValues>;
  entityIndex: number;
  entityType: "ipAddresses" | "domains" | "devices";
}

interface FormItemRHFProps {
  label: string;
  name: string;
  control: Control<NetworkAnalysisModuleFormValues>;
  placeholder?: string;
  type?: string;
  as?: 'input' | 'textarea';
  rows?: number;
}

export function PhotoUploadSection({ control, entityIndex, entityType }: PhotoUploadSectionProps) {
  const { watch } = useFormContext();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const {
    fields: photoFields,
    append: appendPhoto,
    remove: removePhoto
  } = useFieldArray({
    control,
    name: `${entityType}.${entityIndex}.photos` as any,
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setSelectedFile(file);

      // Vytvoření URL pro náhled
      const fileUrl = URL.createObjectURL(file);
      setPreviewUrl(fileUrl);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    setIsUploading(true);

    try {
      // Simulace nahrávání souboru
      await new Promise(resolve => setTimeout(resolve, 1500));

      // V reálné aplikaci by zde bylo nahrávání souboru na server
      // a získání URL pro stažení

      // Přidání nové fotografie do formuláře
      appendPhoto({
        id: uuidv4(),
        fileName: selectedFile.name,
        storagePath: `photos/${entityType}/${entityIndex}/${selectedFile.name}`,
        downloadURL: previewUrl || "",
        description: "",
        dateTaken: new Date().toISOString().split('T')[0],
        sourceURL: "",
      });

      // Vyčištění formuláře
      setSelectedFile(null);
      if (previewUrl) {
        URL.revokeObjectURL(previewUrl);
        setPreviewUrl(null);
      }
    } catch (error) {
      console.error("Error uploading file:", error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleRemovePhoto = (index: number) => {
    if (confirm("Opravdu chcete smazat tuto fotografii?")) {
      removePhoto(index);
    }
  };

  const FormItemRHF = ({ label, name, control, placeholder, type = "text", as = 'input', rows }: FormItemRHFProps) => (
    <FormField
      control={control}
      name={name as any}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            {as === 'input' ? (
              <Input
                {...field}
                placeholder={placeholder}
                type={type}
              />
            ) : (
              <Textarea
                {...field}
                placeholder={placeholder}
                rows={rows || 3}
              />
            )}
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Nahrát novou fotografii</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex flex-col items-center justify-center border-2 border-dashed border-muted-foreground/25 rounded-lg p-12 text-center">
              <Input
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                className="hidden"
                id="photo-upload"
              />
              <label
                htmlFor="photo-upload"
                className="flex flex-col items-center justify-center cursor-pointer"
              >
                <Upload className="h-10 w-10 text-muted-foreground mb-2" />
                <span className="text-muted-foreground font-medium">
                  Klikněte pro výběr fotografie
                </span>
                <span className="text-xs text-muted-foreground mt-1">
                  Podporované formáty: JPG, PNG, GIF
                </span>
              </label>
            </div>

            {previewUrl && (
              <div className="border rounded-md p-4">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-medium">Náhled fotografie</h3>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      setSelectedFile(null);
                      URL.revokeObjectURL(previewUrl);
                      setPreviewUrl(null);
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <div className="relative aspect-video w-full overflow-hidden rounded-md">
                  <img
                    src={previewUrl}
                    alt="Preview"
                    className="object-cover w-full h-full"
                  />
                </div>
                <div className="mt-2">
                  <p className="text-sm text-muted-foreground">
                    {selectedFile?.name} ({Math.round(selectedFile?.size / 1024)} KB)
                  </p>
                </div>
                <Button
                  type="button"
                  className="w-full mt-4"
                  onClick={handleUpload}
                  disabled={isUploading}
                >
                  {isUploading ? "Nahrávání..." : "Nahrát fotografii"}
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Fotodokumentace</CardTitle>
        </CardHeader>
        <CardContent>
          {photoFields.length === 0 ? (
            <div className="text-center py-8 border rounded-md bg-muted/20">
              <Image className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">Zatím nebyly přidány žádné fotografie</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {photoFields.map((photo, index) => (
                <div key={photo.id} className="border rounded-md overflow-hidden">
                  <div className="relative aspect-video w-full overflow-hidden">
                    <img
                      src={watch(`${entityType}.${entityIndex}.photos.${index}.downloadURL`) || ""}
                      alt={watch(`${entityType}.${entityIndex}.photos.${index}.description`) || "Fotografie"}
                      className="object-cover w-full h-full"
                    />
                    <Button
                      type="button"
                      variant="destructive"
                      size="icon"
                      className="absolute top-2 right-2"
                      onClick={() => handleRemovePhoto(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="p-3 space-y-3">
                    <FormItemRHF
                      control={control}
                      name={`${entityType}.${entityIndex}.photos.${index}.description`}
                      label="Popis"
                      placeholder="Zadejte popis fotografie"
                    />
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      <FormItemRHF
                        control={control}
                        name={`${entityType}.${entityIndex}.photos.${index}.dateTaken`}
                        label="Datum pořízení"
                        type="date"
                      />
                      <FormItemRHF
                        control={control}
                        name={`${entityType}.${entityIndex}.photos.${index}.sourceURL`}
                        label="Zdrojové URL"
                        placeholder="Např. https://example.com/image.jpg"
                      />
                    </div>
                    {watch(`${entityType}.${entityIndex}.photos.${index}.sourceURL`) && (
                      <div className="flex justify-end">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          asChild
                        >
                          <a
                            href={watch(`${entityType}.${entityIndex}.photos.${index}.sourceURL`) || "#"}
                            target="_blank"
                            rel="noopener noreferrer"
                          >
                            <ExternalLink className="h-4 w-4 mr-1" />
                            Otevřít zdroj
                          </a>
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
