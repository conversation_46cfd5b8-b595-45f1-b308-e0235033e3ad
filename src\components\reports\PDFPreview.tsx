"use client";

import React from 'react';

interface PDFPreviewSettings {
  title: string;
  subject: string;
  jid: string;
  documentNumber: string;
  location: string;
  date: string;
  department: string;
  purpose: string;
  style: 'official' | 'modern';
  fontSize: number;
  lineHeight: number;
  margins: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
  primaryColor: string;
  secondaryColor: string;
  textColor: string;
  backgroundColor: string;
  headerHeight: number;
  footerHeight: number;
  showPageNumbers: boolean;
  pageNumberPosition: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  showBorders: boolean;
  borderWidth: number;
  borderColor: string;
  photoSize: 'small' | 'medium' | 'large';
  photoAlignment: 'left' | 'center' | 'right';
  photoSpacing: number;

  // Pokročilé rozložení
  tableSpacing: number;
  tableBorderRadius: number;
  tableCellPadding: number;
  sectionSpacing: number;
  moduleSpacing: number;
  headerSpacing: number;
  footerSpacing: number;

  // Č<PERSON>ry a oddělení
  showSectionDividers: boolean;
  sectionDividerStyle: 'solid' | 'dashed' | 'dotted';
  sectionDividerColor: string;
  showModuleBorders: boolean;
  moduleBorderRadius: number;

  // Tabulky
  tableHeaderBackground: string;
  tableAlternateRows: boolean;
  tableAlternateRowColor: string;
  tableBorderStyle: 'solid' | 'dashed' | 'dotted' | 'none';
}

interface PDFPreviewProps {
  settings: PDFPreviewSettings;
}

export default function PDFPreview({ settings }: PDFPreviewProps) {
  const photoSizes = {
    small: '120px',
    medium: '180px',
    large: '240px'
  };

  const previewStyle = {
    fontFamily: settings.style === 'modern'
      ? "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif"
      : "'Times New Roman', serif",
    fontSize: `${settings.fontSize * 0.8}pt`, // Zmenšeno pro náhled
    lineHeight: settings.lineHeight,
    color: settings.textColor,
    backgroundColor: settings.backgroundColor,
    padding: `${settings.margins.top * 0.5}px ${settings.margins.right * 0.5}px ${settings.margins.bottom * 0.5}px ${settings.margins.left * 0.5}px`,
    border: settings.showBorders ? `${settings.borderWidth}px solid ${settings.borderColor}` : 'none',
    borderRadius: '4px',
    minHeight: '900px',
    maxHeight: '900px',
    overflow: 'auto',
    position: 'relative' as const,
    transform: 'scale(0.75)',
    transformOrigin: 'top left',
    width: '142%', // Kompenzace pro scale
    height: '142%'
  };

  const headerStyle = settings.style === 'modern' ? {
    background: `linear-gradient(135deg, ${settings.primaryColor} 0%, ${settings.secondaryColor} 100%)`,
    color: 'white',
    padding: '15px',
    marginBottom: '20px',
    borderRadius: '6px',
    textAlign: 'center' as const
  } : {
    textAlign: 'center' as const,
    marginBottom: '20px',
    padding: '15px 0',
    borderBottom: settings.showBorders ? `${settings.borderWidth}px solid ${settings.borderColor}` : '1px solid #ddd'
  };

  const pageNumberStyle = {
    position: 'absolute' as const,
    fontSize: `${settings.fontSize * 0.7}pt`,
    fontWeight: 'bold',
    color: settings.style === 'modern' ? settings.primaryColor : settings.textColor,
    ...(settings.pageNumberPosition === 'top-left' && { top: '25px', left: '10px' }),
    ...(settings.pageNumberPosition === 'top-right' && { top: '5px', right: '10px' }),
    ...(settings.pageNumberPosition === 'bottom-left' && { bottom: '5px', left: '10px' }),
    ...(settings.pageNumberPosition === 'bottom-right' && { bottom: '5px', right: '10px' })
  };

  return (
    <div className="bg-white border rounded-lg shadow-sm overflow-hidden h-full">
      <div className="bg-gray-50 px-4 py-3 border-b">
        <h3 className="text-lg font-medium text-gray-700">📄 Náhled PDF</h3>
        <p className="text-sm text-gray-500">Realtime náhled vašich změn</p>
      </div>

      <div className="p-6 bg-gray-100 h-full overflow-auto">
        <div
          className="bg-white shadow-xl mx-auto border"
          style={{
            width: '210mm',
            minHeight: '297mm',
            maxWidth: '100%',
            transform: 'scale(0.85)',
            transformOrigin: 'top center',
            marginBottom: '-15%'
          }}
        >
          <div style={previewStyle}>
            {/* Číslování stránek */}
            {settings.showPageNumbers && (
              <div style={pageNumberStyle}>
                2. strana
              </div>
            )}

            {/* Hlavička */}
            <div style={headerStyle}>
              <h1 style={{
                margin: '0 0 8px 0',
                fontSize: `${settings.fontSize * 1.4}pt`,
                fontWeight: settings.style === 'modern' ? '300' : 'bold',
                color: settings.style === 'modern' ? 'white' : settings.primaryColor
              }}>
                {settings.title}
              </h1>
              <div style={{ fontSize: `${settings.fontSize * 0.9}pt`, opacity: 0.9 }}>
                {settings.subject || 'Analýza subjektu'}
              </div>
            </div>

            {/* Adresa oddělení */}
            <div style={{
              textAlign: 'left',
              marginBottom: `${settings.headerSpacing * 0.5}px`,
              fontSize: `${settings.fontSize * 0.8}pt`,
              color: settings.textColor
            }}>
              <div style={{ fontWeight: 'bold', marginBottom: '3px' }}>POLICIE ČESKÉ REPUBLIKY</div>
              <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>Krajské ředitelství policie Pardubického kraje</div>
              <div style={{ whiteSpace: 'pre-line', lineHeight: 1.3 }}>
                {settings.department.split('\n').slice(0, 3).join('\n')}
              </div>
            </div>

            {/* Dokumentové info */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              margin: '15px 0',
              fontSize: `${settings.fontSize * 0.8}pt`
            }}>
              <div><strong>Č. j. {settings.documentNumber || 'XXXX-XXXX/XX'}</strong></div>
              <div><strong>JID: {settings.jid || 'XXXXXX'}</strong></div>
            </div>

            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              margin: '15px 0',
              fontSize: `${settings.fontSize * 0.8}pt`
            }}>
              <div>{settings.location} {settings.date}</div>
              <div>Počet stran: 3</div>
            </div>

            {/* Účel analýzy */}
            <div style={{ margin: `${settings.sectionSpacing * 0.5}px 0` }}>
              <h4 style={{
                color: settings.primaryColor,
                fontSize: `${settings.fontSize * 1.1}pt`,
                margin: '15px 0 8px 0',
                borderBottom: settings.showSectionDividers ? `1px ${settings.sectionDividerStyle} ${settings.sectionDividerColor}` : (settings.showBorders ? `1px solid ${settings.borderColor}` : '1px solid #ddd'),
                paddingBottom: '3px'
              }}>
                Účel analýzy
              </h4>
              <p style={{
                fontSize: `${settings.fontSize * 0.9}pt`,
                lineHeight: settings.lineHeight,
                margin: '8px 0'
              }}>
                {settings.purpose.substring(0, 100)}...
              </p>
            </div>

            {/* Oddělovač sekcí */}
            {settings.showSectionDividers && (
              <div style={{
                width: '100%',
                height: '1px',
                backgroundColor: settings.sectionDividerColor,
                borderTop: `1px ${settings.sectionDividerStyle} ${settings.sectionDividerColor}`,
                margin: `${settings.sectionSpacing * 0.3}px 0`
              }} />
            )}

            {/* Ukázkový modul */}
            <div style={{
              margin: `${settings.moduleSpacing * 0.5}px 0`,
              border: settings.showModuleBorders ? `${settings.borderWidth}px solid ${settings.borderColor}` : (settings.showBorders ? `1px solid ${settings.borderColor}` : '1px solid #eee'),
              borderRadius: `${settings.moduleBorderRadius}px`,
              overflow: 'hidden'
            }}>
              <h3 style={settings.style === 'modern' ? {
                background: `linear-gradient(90deg, ${settings.primaryColor} 0%, ${settings.secondaryColor} 100%)`,
                color: 'white',
                padding: '10px 15px',
                margin: 0,
                fontSize: `${settings.fontSize * 1.2}pt`,
                fontWeight: 'bold'
              } : {
                color: settings.primaryColor,
                padding: '8px 0',
                margin: '0 0 10px 0',
                borderBottom: settings.showBorders ? `${settings.borderWidth}px solid ${settings.borderColor}` : '1px solid #ddd',
                fontSize: `${settings.fontSize * 1.2}pt`,
                fontWeight: 'bold',
                paddingLeft: '15px'
              }}>
                Evidence obyvatel
              </h3>

              <div style={{ padding: settings.style === 'modern' ? '15px' : '10px 15px' }}>
                {/* Ukázková tabulka */}
                <table style={{
                  width: '100%',
                  borderCollapse: 'collapse',
                  margin: `${settings.tableSpacing * 0.5}px 0`,
                  fontSize: `${settings.fontSize * 0.8}pt`,
                  border: settings.tableBorderStyle !== 'none' ? `1px ${settings.tableBorderStyle} ${settings.borderColor}` : 'none',
                  borderRadius: `${settings.tableBorderRadius}px`,
                  overflow: 'hidden'
                }}>
                  <tbody>
                    <tr>
                      <td style={{
                        padding: `${settings.tableCellPadding * 0.75}px ${settings.tableCellPadding}px`,
                        border: settings.tableBorderStyle !== 'none' ? `1px ${settings.tableBorderStyle} ${settings.borderColor}` : 'none',
                        fontWeight: 'bold',
                        backgroundColor: settings.tableHeaderBackground,
                        width: '35%'
                      }}>
                        Jméno a příjmení
                      </td>
                      <td style={{
                        padding: `${settings.tableCellPadding * 0.75}px ${settings.tableCellPadding}px`,
                        border: settings.tableBorderStyle !== 'none' ? `1px ${settings.tableBorderStyle} ${settings.borderColor}` : 'none'
                      }}>
                        Petr Petříček
                      </td>
                    </tr>
                    <tr style={{ backgroundColor: settings.tableAlternateRows ? settings.tableAlternateRowColor : 'transparent' }}>
                      <td style={{
                        padding: `${settings.tableCellPadding * 0.75}px ${settings.tableCellPadding}px`,
                        border: settings.tableBorderStyle !== 'none' ? `1px ${settings.tableBorderStyle} ${settings.borderColor}` : 'none',
                        fontWeight: 'bold',
                        backgroundColor: settings.tableHeaderBackground
                      }}>
                        Datum narození
                      </td>
                      <td style={{
                        padding: `${settings.tableCellPadding * 0.75}px ${settings.tableCellPadding}px`,
                        border: settings.tableBorderStyle !== 'none' ? `1px ${settings.tableBorderStyle} ${settings.borderColor}` : 'none'
                      }}>
                        20-02-1961
                      </td>
                    </tr>
                  </tbody>
                </table>

                {/* Ukázková fotografie */}
                <h4 style={{
                  color: settings.primaryColor,
                  fontSize: `${settings.fontSize * 1.0}pt`,
                  margin: '15px 0 8px 0',
                  borderBottom: settings.showBorders ? `1px solid ${settings.borderColor}` : '1px solid #ddd',
                  paddingBottom: '3px'
                }}>
                  Fotodokumentace
                </h4>

                <div style={{
                  textAlign: settings.photoAlignment,
                  margin: `${settings.photoSpacing * 0.5}px 0`
                }}>
                  <div style={{
                    display: 'inline-block',
                    margin: `${settings.photoSpacing * 0.5}px`,
                    border: settings.showBorders ? `1px solid ${settings.borderColor}` : '1px solid #ddd',
                    borderRadius: '6px',
                    padding: '8px',
                    textAlign: 'center'
                  }}>
                    <div style={{
                      width: photoSizes[settings.photoSize],
                      height: '80px',
                      background: 'linear-gradient(45deg, #f0f0f0, #e0e0e0)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRadius: '4px',
                      border: settings.showBorders ? `1px solid ${settings.borderColor}` : '1px solid #ccc',
                      marginBottom: '6px'
                    }}>
                      <span style={{ color: '#999', fontSize: '10px' }}>Ukázková fotografie</span>
                    </div>
                    <div style={{
                      fontSize: `${settings.fontSize * 0.8}pt`,
                      color: settings.textColor,
                      fontStyle: 'italic'
                    }}>
                      Popis fotografie
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Podpis */}
            <div style={{
              marginTop: '30px',
              textAlign: 'right',
              fontSize: `${settings.fontSize * 0.9}pt`
            }}>
              <div style={{ marginBottom: '3px' }}><strong>Zpracoval:</strong></div>
              <div>por. Bc. Václav Pospíšil</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
