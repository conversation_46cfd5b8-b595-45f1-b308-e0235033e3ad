/* Styly pro modul časov<PERSON> osy */

/* <PERSON><PERSON><PERSON><PERSON> osy */
.timeline-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 20px;
}

/* Ovládací prvky časové osy */
.timeline-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.timeline-controls-left, .timeline-controls-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.timeline-view-controls, .timeline-scale-controls {
    display: flex;
    align-items: center;
    gap: 5px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.timeline-view-controls .btn-inline, .timeline-scale-controls .btn-inline {
    border-radius: 0;
    border: none;
    margin: 0;
    padding: 6px 10px;
    background-color: transparent;
}

.timeline-view-controls .btn-inline.active, .timeline-scale-controls .btn-inline.active {
    background-color: var(--primary-color);
    color: white;
}

/* Filtry časové osy */
.timeline-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    padding: 15px;
    background-color: #f9f9f9;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    align-items: flex-end;
}

.timeline-filter {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.timeline-filter label {
    font-size: 12px;
    color: var(--dark-text);
    font-weight: 500;
}

.timeline-filter select, .timeline-filter input {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    min-width: 150px;
}

.date-range-picker {
    display: flex;
    align-items: center;
    gap: 8px;
}

.date-range-picker input {
    min-width: 120px;
}

/* Vizualizace časové osy */
.timeline-visualization {
    display: flex;
    flex-direction: column;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    min-height: 400px;
    position: relative;
}

.timeline-scale {
    height: 40px;
    background-color: #f0f0f0;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    padding: 0 15px;
    position: relative;
}

.timeline-events {
    flex: 1;
    padding: 20px 15px;
    position: relative;
    min-height: 360px;
    overflow-y: auto;
}

.timeline-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--muted-text);
    text-align: center;
    padding: 30px;
}

.timeline-empty-state i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* Události na časové ose */
.timeline-event {
    display: flex;
    margin-bottom: 20px;
    position: relative;
}

.timeline-event-marker {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: var(--primary-color);
    margin-right: 15px;
    flex-shrink: 0;
    position: relative;
    z-index: 2;
}

.timeline-event-content {
    background-color: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    flex: 1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    position: relative;
}

.timeline-event-content::before {
    content: '';
    position: absolute;
    left: -10px;
    top: 15px;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-right: 10px solid white;
    z-index: 2;
}

.timeline-event-content::after {
    content: '';
    position: absolute;
    left: -11px;
    top: 15px;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-right: 10px solid var(--border-color);
    z-index: 1;
}

.timeline-event-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.timeline-event-title {
    font-weight: 500;
    font-size: 16px;
}

.timeline-event-time {
    color: var(--muted-text);
    font-size: 14px;
}

.timeline-event-description {
    margin-bottom: 10px;
    line-height: 1.5;
}

.timeline-event-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    font-size: 12px;
}

.timeline-event-source {
    color: var(--muted-text);
    display: flex;
    align-items: center;
    gap: 5px;
}

.timeline-event-actions {
    display: flex;
    gap: 8px;
}

.timeline-event-actions button {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    padding: 2px 5px;
    font-size: 12px;
    transition: all 0.2s ease;
}

.timeline-event-actions button:hover {
    color: var(--primary-color-dark);
    text-decoration: underline;
}

/* Vertikální čára spojující události */
.timeline-vertical-line {
    position: absolute;
    left: 10px;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--border-color);
    z-index: 1;
}

/* Typy událostí - barevné rozlišení */
.timeline-event-marker.type-communication {
    background-color: #4285F4;
}

.timeline-event-marker.type-movement {
    background-color: #34A853;
}

.timeline-event-marker.type-financial {
    background-color: #FBBC05;
}

.timeline-event-marker.type-social {
    background-color: #EA4335;
}

.timeline-event-marker.type-legal {
    background-color: #9C27B0;
}

.timeline-event-marker.type-other {
    background-color: #607D8B;
}

/* Detail události */
.event-detail-container {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    margin-top: 20px;
    background-color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.event-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: #f9f9f9;
    border-bottom: 1px solid var(--border-color);
}

.event-detail-header h4 {
    margin: 0;
    font-size: 18px;
    color: var(--primary-color);
}

.event-detail-content {
    padding: 20px;
}

.event-detail-section {
    margin-bottom: 20px;
}

.event-detail-section h5 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--dark-text);
}

.event-detail-field {
    margin-bottom: 15px;
}

.event-detail-field label {
    display: block;
    font-size: 12px;
    color: var(--muted-text);
    margin-bottom: 5px;
}

.event-detail-field-value {
    font-size: 14px;
    line-height: 1.5;
}

.event-detail-media {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.event-detail-media-item {
    width: 100px;
    height: 100px;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.event-detail-media-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Analytický panel */
.timeline-analysis-panel {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    margin-top: 20px;
    background-color: white;
}

.analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: #f9f9f9;
    border-bottom: 1px solid var(--border-color);
}

.analysis-header h4 {
    margin: 0;
    font-size: 18px;
    color: var(--primary-color);
}

.analysis-controls {
    display: flex;
    gap: 8px;
}

.analysis-content {
    padding: 0;
}

.analysis-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
}

.analysis-tab {
    padding: 10px 15px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 500;
    color: var(--dark-text);
    transition: all 0.2s ease;
}

.analysis-tab:hover {
    color: var(--primary-color);
}

.analysis-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.analysis-tab-content {
    display: none;
    padding: 20px;
}

.analysis-tab-content.active {
    display: block;
}

.analysis-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px;
    text-align: center;
    color: var(--muted-text);
}

.analysis-placeholder i {
    font-size: 32px;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* Fotogalerie pro časovou osu */
.timeline-gallery-container {
    margin-top: 30px;
}

.timeline-gallery-container h5 {
    margin-bottom: 15px;
    font-size: 16px;
    color: var(--primary-color);
}

/* Nastavení notifikací */
.timeline-notifications {
    margin-top: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

.timeline-notifications h5 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    color: var(--primary-color);
}

/* Responzivní design */
@media (max-width: 768px) {
    .timeline-controls {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .timeline-filters {
        flex-direction: column;
    }
    
    .timeline-filter {
        width: 100%;
    }
}
