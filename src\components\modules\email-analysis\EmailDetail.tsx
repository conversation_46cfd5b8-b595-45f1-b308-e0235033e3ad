"use client";

import { useState } from "react";
import { Control, useFieldArray, useFormContext } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from "@/components/ui/form";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Mail, Calendar, CheckCircle, XCircle, AlertCircle, Shield, AlertTriangle,
  User, Building, Database, PlusCircle, X, Globe, Search, Server,
  MapPin, Monitor, Lock, Route
} from "lucide-react";
import {
  emailSources,
  emailVerificationStatuses,
  platformTypes,
  EmailAnalysisModuleFormValues
} from "./schemas";
import { FormItemRHF } from "./FormComponents";
import { PhotoUploadSection } from "./PhotoUploadSection";
import { EmailValidator, EmailValidationResults } from "./EmailValidator";
import { EmailHeaderAnalyzer, EmailHeaderAnalysisResults } from "./EmailHeaderAnalyzer";

interface EmailDetailProps {
  control: Control<EmailAnalysisModuleFormValues>;
  emailIndex: number;
}

export function EmailDetail({ control, emailIndex }: EmailDetailProps) {
  const [activeTab, setActiveTab] = useState("basic");

  // Získání funkcí watch a setValue z formuláře
  const { watch, setValue } = useFormContext();

  // Data Breaches Field Array
  const {
    fields: dataBreachFields,
    append: appendDataBreach,
    remove: removeDataBreach
  } = useFieldArray({
    control,
    name: `emails.${emailIndex}.dataBreaches`,
  });

  // Connected Profiles Field Array
  const {
    fields: profileFields,
    append: appendProfile,
    remove: removeProfile
  } = useFieldArray({
    control,
    name: `emails.${emailIndex}.connectedProfiles`,
  });

  // Místo dynamického vytváření field arrays pro každý breach,
  // použijeme jeden useFieldArray pro všechny breach záznamy
  // a budeme pracovat s ním přímo

  // Email Route Field Array
  const {
    fields: routeFields,
    append: appendRoute,
    remove: removeRoute
  } = useFieldArray({
    control,
    name: `emails.${emailIndex}.headersAnalysis.emailRoute`,
  });

  const handleAddDataBreach = () => {
    appendDataBreach({
      id: uuidv4(),
      source: "",
      date: "",
      recordsCount: "",
      compromisedData: [],
      description: "",
    });
  };

  const handleAddProfile = () => {
    appendProfile({
      id: uuidv4(),
      platformType: "",
      platformName: "",
      username: "",
      profileUrl: "",
      notes: "",
      verificationStatus: "unknown",
    });
  };

  const handleAddCompromisedData = (breachIndex: number) => {
    // Použijeme setValue pro přímé přidání nové položky do pole
    const currentData = watch(`emails.${emailIndex}.dataBreaches.${breachIndex}.compromisedData`) || [];
    setValue(
      `emails.${emailIndex}.dataBreaches.${breachIndex}.compromisedData`,
      [...currentData, ""],
      { shouldDirty: true }
    );
  };

  const handleRemoveCompromisedData = (breachIndex: number, dataIndex: number) => {
    // Použijeme setValue pro přímé odstranění položky z pole
    const currentData = watch(`emails.${emailIndex}.dataBreaches.${breachIndex}.compromisedData`) || [];
    setValue(
      `emails.${emailIndex}.dataBreaches.${breachIndex}.compromisedData`,
      currentData.filter((_, i) => i !== dataIndex),
      { shouldDirty: true }
    );
  };

  const handleAddRoute = () => {
    appendRoute("");
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Detail emailové adresy</CardTitle>
        <CardDescription>
          Podrobné informace o emailové adrese a výsledky analýzy
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="mb-4">
            <TabsTrigger value="basic">Základní informace</TabsTrigger>
            <TabsTrigger value="verification">Ověření</TabsTrigger>
            <TabsTrigger value="data_breach">Úniky dat</TabsTrigger>
            <TabsTrigger value="profiles">Propojené profily</TabsTrigger>
            <TabsTrigger value="headers">Analýza hlaviček</TabsTrigger>
            <TabsTrigger value="validator">Validace emailu</TabsTrigger>
            <TabsTrigger value="photos">Fotodokumentace</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemRHF
                control={control}
                name={`emails.${emailIndex}.emailAddress`}
                label="Emailová adresa"
                placeholder="<EMAIL>"
                type="email"
              />

              <FormField
                control={control}
                name={`emails.${emailIndex}.source`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Zdroj emailu</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Vyberte zdroj emailu" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="osint">OSINT</SelectItem>
                        <SelectItem value="investigation">Vyšetřování</SelectItem>
                        <SelectItem value="public_data">Veřejná data</SelectItem>
                        <SelectItem value="social_media">Sociální sítě</SelectItem>
                        <SelectItem value="data_breach">Únik dat</SelectItem>
                        <SelectItem value="direct_communication">Přímá komunikace</SelectItem>
                        <SelectItem value="other">Jiný</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormItemRHF
              control={control}
              name={`emails.${emailIndex}.otherSourceDetail`}
              label="Upřesnění zdroje"
              placeholder="Upřesněte zdroj emailu, pokud jste vybrali 'Jiný'"
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemRHF
                control={control}
                name={`emails.${emailIndex}.probableName`}
                label="Pravděpodobné jméno"
                placeholder="Jméno odvozené z emailové adresy"
              />

              <FormItemRHF
                control={control}
                name={`emails.${emailIndex}.associatedOrganization`}
                label="Spojená organizace"
                placeholder="Organizace spojená s doménou emailu"
              />
            </div>

            <FormItemRHF
              control={control}
              name={`emails.${emailIndex}.discoveryDate`}
              label="Datum objevení"
              type="date"
            />

            <FormItemRHF
              control={control}
              name={`emails.${emailIndex}.notes`}
              label="Poznámky"
              placeholder="Zadejte další poznámky k emailové adrese"
              as="textarea"
              rows={4}
            />
          </TabsContent>

          <TabsContent value="verification" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Ověření emailové adresy</CardTitle>
                <CardDescription>
                  Informace o ověření platnosti emailové adresy
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <FormField
                  control={control}
                  name={`emails.${emailIndex}.verificationStatus`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Stav ověření</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Vyberte stav ověření" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="verified">Ověřeno</SelectItem>
                          <SelectItem value="unverified">Neověřeno</SelectItem>
                          <SelectItem value="invalid">Neplatné</SelectItem>
                          <SelectItem value="disposable">Jednorázové</SelectItem>
                          <SelectItem value="unknown">Neznámé</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormItemRHF
                  control={control}
                  name={`emails.${emailIndex}.verificationDate`}
                  label="Datum ověření"
                  type="date"
                />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="data_breach" className="space-y-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Úniky dat</CardTitle>
                  <CardDescription>
                    Informace o únicích dat spojených s emailovou adresou
                  </CardDescription>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAddDataBreach}
                >
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Přidat únik dat
                </Button>
              </CardHeader>
              <CardContent>
                {dataBreachFields.length === 0 ? (
                  <div className="flex flex-col items-center justify-center p-8 text-center border rounded-md">
                    <Shield className="h-12 w-12 text-muted-foreground mb-2" />
                    <h3 className="text-lg font-medium">Žádné úniky dat</h3>
                    <p className="text-sm text-muted-foreground mt-2">
                      Zatím nebyly přidány žádné úniky dat pro tuto emailovou adresu.
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="mt-4"
                      onClick={handleAddDataBreach}
                    >
                      <PlusCircle className="mr-2 h-4 w-4" />
                      Přidat únik dat
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {dataBreachFields.map((field, index) => (
                      <div key={field.id} className="border rounded-md p-4">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-lg font-medium">Únik dat #{index + 1}</h3>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => removeDataBreach(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormItemRHF
                            control={control}
                            name={`emails.${emailIndex}.dataBreaches.${index}.source`}
                            label="Zdroj úniku dat"
                            placeholder="Např. LinkedIn, Adobe, atd."
                          />

                          <FormItemRHF
                            control={control}
                            name={`emails.${emailIndex}.dataBreaches.${index}.date`}
                            label="Datum úniku"
                            type="date"
                          />
                        </div>

                        <FormItemRHF
                          control={control}
                          name={`emails.${emailIndex}.dataBreaches.${index}.recordsCount`}
                          label="Počet záznamů"
                          placeholder="Např. 164 milionů"
                        />

                        <div className="space-y-2 mt-4">
                          <FormLabel>Kompromitovaná data</FormLabel>
                          <div className="space-y-2">
                            {/* Použijeme watch pro získání aktuálních dat */}
                            {(watch(`emails.${emailIndex}.dataBreaches.${index}.compromisedData`) || []).map((data, dataIndex) => (
                              <div key={dataIndex} className="flex items-center space-x-2">
                                <FormItemRHF
                                  control={control}
                                  name={`emails.${emailIndex}.dataBreaches.${index}.compromisedData.${dataIndex}`}
                                  label=""
                                  placeholder="Zadejte typ kompromitovaných dat"
                                />
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => handleRemoveCompromisedData(index, dataIndex)}
                                  className="flex-shrink-0"
                                >
                                  <X className="h-4 w-4" />
                                </Button>
                              </div>
                            ))}
                          </div>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => handleAddCompromisedData(index)}
                            className="mt-2"
                          >
                            <PlusCircle className="mr-2 h-4 w-4" />
                            Přidat kompromitovaná data
                          </Button>
                        </div>

                        <FormItemRHF
                          control={control}
                          name={`emails.${emailIndex}.dataBreaches.${index}.description`}
                          label="Popis úniku dat"
                          placeholder="Zadejte popis úniku dat"
                          as="textarea"
                          rows={3}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="profiles" className="space-y-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <div>
                  <CardTitle>Propojené profily</CardTitle>
                  <CardDescription>
                    Profily na sociálních sítích a dalších platformách spojené s emailovou adresou
                  </CardDescription>
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAddProfile}
                >
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Přidat profil
                </Button>
              </CardHeader>
              <CardContent>
                {profileFields.length === 0 ? (
                  <div className="flex flex-col items-center justify-center p-8 text-center border rounded-md">
                    <User className="h-12 w-12 text-muted-foreground mb-2" />
                    <h3 className="text-lg font-medium">Žádné propojené profily</h3>
                    <p className="text-sm text-muted-foreground mt-2">
                      Zatím nebyly přidány žádné propojené profily pro tuto emailovou adresu.
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="mt-4"
                      onClick={handleAddProfile}
                    >
                      <PlusCircle className="mr-2 h-4 w-4" />
                      Přidat profil
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {profileFields.map((field, index) => (
                      <div key={field.id} className="border rounded-md p-4">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-lg font-medium">Profil #{index + 1}</h3>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => removeProfile(index)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormField
                            control={control}
                            name={`emails.${emailIndex}.connectedProfiles.${index}.platformType`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Typ platformy</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Vyberte typ platformy" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    <SelectItem value="social">Sociální síť</SelectItem>
                                    <SelectItem value="forum">Fórum</SelectItem>
                                    <SelectItem value="ecommerce">E-shop</SelectItem>
                                    <SelectItem value="service">Online služba</SelectItem>
                                    <SelectItem value="professional">Profesní síť</SelectItem>
                                    <SelectItem value="dating">Seznamka</SelectItem>
                                    <SelectItem value="gaming">Herní platforma</SelectItem>
                                    <SelectItem value="other">Jiná</SelectItem>
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormItemRHF
                            control={control}
                            name={`emails.${emailIndex}.connectedProfiles.${index}.platformName`}
                            label="Název platformy"
                            placeholder="Např. Facebook, LinkedIn, atd."
                          />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                          <FormItemRHF
                            control={control}
                            name={`emails.${emailIndex}.connectedProfiles.${index}.username`}
                            label="Uživatelské jméno / ID"
                            placeholder="Zadejte uživatelské jméno nebo ID"
                          />

                          <FormItemRHF
                            control={control}
                            name={`emails.${emailIndex}.connectedProfiles.${index}.profileUrl`}
                            label="URL profilu"
                            placeholder="https://..."
                          />
                        </div>

                        <FormField
                          control={control}
                          name={`emails.${emailIndex}.connectedProfiles.${index}.verificationStatus`}
                          render={({ field }) => (
                            <FormItem className="mt-4">
                              <FormLabel>Stav ověření</FormLabel>
                              <Select onValueChange={field.onChange} defaultValue={field.value}>
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Vyberte stav ověření" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  <SelectItem value="yes">Ověřeno</SelectItem>
                                  <SelectItem value="no">Neověřeno</SelectItem>
                                  <SelectItem value="unknown">Neznámý</SelectItem>
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormItemRHF
                          control={control}
                          name={`emails.${emailIndex}.connectedProfiles.${index}.notes`}
                          label="Poznámky"
                          placeholder="Zadejte další informace o profilu"
                          as="textarea"
                          rows={3}
                        />
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="headers" className="space-y-4">
            <EmailHeaderAnalyzer
              initialHeaders={watch(`emails.${emailIndex}.headersAnalysis.rawHeaders`) || ""}
              onAnalysisComplete={(results: EmailHeaderAnalysisResults) => {
                // Aktualizace formuláře s výsledky analýzy
                setValue(`emails.${emailIndex}.headersAnalysis.senderIp`, results.senderIp, { shouldDirty: true });
                setValue(`emails.${emailIndex}.headersAnalysis.ipGeolocation`, results.ipGeolocation, { shouldDirty: true });
                setValue(`emails.${emailIndex}.headersAnalysis.emailClient`, results.emailClient, { shouldDirty: true });
                setValue(`emails.${emailIndex}.headersAnalysis.authResults`,
                  `SPF: ${results.authResults.spf}, DKIM: ${results.authResults.dkim}, DMARC: ${results.authResults.dmarc}`,
                  { shouldDirty: true }
                );
                setValue(`emails.${emailIndex}.headersAnalysis.rawHeaders`, results.rawHeaders, { shouldDirty: true });
                setValue(`emails.${emailIndex}.headersAnalysis.analysisDate`, new Date().toISOString().split('T')[0], { shouldDirty: true });

                // Aktualizace cesty emailu
                const emailRoute = results.emailRoute.map(hop => hop.server);
                setValue(`emails.${emailIndex}.headersAnalysis.emailRoute`, emailRoute, { shouldDirty: true });
              }}
            />
          </TabsContent>

          <TabsContent value="validator" className="space-y-4">
            <EmailValidator
              initialEmail={watch(`emails.${emailIndex}.emailAddress`) || ""}
              onValidationComplete={(results: EmailValidationResults) => {
                // Aktualizace formuláře s výsledky validace
                setValue(`emails.${emailIndex}.verificationStatus`,
                  results.isActive ? "verified" :
                  results.isDisposable ? "disposable" :
                  !results.syntaxValid ? "invalid" :
                  "unverified",
                  { shouldDirty: true }
                );
                setValue(`emails.${emailIndex}.verificationDate`, new Date().toISOString().split('T')[0], { shouldDirty: true });
              }}
            />
          </TabsContent>

          <TabsContent value="photos">
            <PhotoUploadSection
              control={control}
              name={`emails.${emailIndex}.photos`}
              title="Fotodokumentace"
              description="Přidejte fotografie související s emailovou adresou"
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
