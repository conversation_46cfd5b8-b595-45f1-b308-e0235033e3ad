"use client";

import { UseFormReturn } from "react-hook-form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { MapOverlaysModuleFormValues, mapBasemapTypes } from "../schemas";
import { Separator } from "@/components/ui/separator";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { AlertCircle, RefreshCw, Save, Settings, Map } from "lucide-react";
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert";

interface MapSettingsTabProps {
  form: UseFormReturn<MapOverlaysModuleFormValues>;
}

export function MapSettingsTab({ form }: MapSettingsTabProps) {
  const { register, watch, setValue } = form;
  const formValues = watch();

  // Reset nastavení mapy na výchozí hodnoty
  const resetMapSettings = () => {
    if (window.confirm("Opravdu chcete resetovat všechna nastavení mapy na výchozí hodnoty?")) {
      setValue("defaultCenter", { lat: 50.0755, lng: 14.4378 }); // Praha jako výchozí
      setValue("defaultZoom", 10);
      setValue("defaultBasemap", "streets");
      setValue("visibleLayers", ["points", "areas", "routes"]);
      setValue("mapSettings", {
        showScale: true,
        showCompass: true,
        showZoomControls: true,
        showLayerControl: true,
        showCoordinates: true,
        enableClusteringPoints: true,
        clusterRadius: 80,
        enableHeatmap: false,
        heatmapRadius: 25,
        heatmapIntensity: 0.5,
        routeColor: "#3b82f6",
        areaColor: "#10b981",
        pointColor: "#ef4444",
        maxZoom: 18,
        minZoom: 3,
      });
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Nastavení mapy</CardTitle>
              <CardDescription>
                Konfigurace výchozích nastavení a chování mapy
              </CardDescription>
            </div>
            <Button variant="outline" onClick={resetMapSettings}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Resetovat
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="general">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="general">Obecné</TabsTrigger>
              <TabsTrigger value="appearance">Vzhled</TabsTrigger>
              <TabsTrigger value="advanced">Pokročilé</TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-4 pt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="defaultZoom">Výchozí přiblížení</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="defaultZoom"
                      type="number"
                      min={1}
                      max={18}
                      {...register("defaultZoom", { valueAsNumber: true })}
                    />
                    <span className="text-sm text-muted-foreground w-8">
                      {formValues.defaultZoom || 10}
                    </span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="defaultBasemap">Výchozí podkladová mapa</Label>
                  <Select
                    value={formValues.defaultBasemap || "streets"}
                    onValueChange={(value) => setValue("defaultBasemap", value as any)}
                  >
                    <SelectTrigger id="defaultBasemap">
                      <SelectValue placeholder="Vyberte podkladovou mapu" />
                    </SelectTrigger>
                    <SelectContent>
                      {mapBasemapTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type === "streets" ? "Ulice" :
                           type === "satellite" ? "Satelitní" :
                           type === "terrain" ? "Terén" :
                           type === "dark" ? "Tmavá" :
                           type === "hybrid" ? "Hybridní" :
                           type === "topo" ? "Topografická" : type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Výchozí střed mapy</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div className="space-y-2">
                    <Label htmlFor="defaultCenterLat">Zeměpisná šířka</Label>
                    <Input
                      id="defaultCenterLat"
                      type="number"
                      step="0.000001"
                      value={formValues.defaultCenter?.lat || 50.0755}
                      onChange={(e) => setValue("defaultCenter", {
                        ...formValues.defaultCenter || {},
                        lat: parseFloat(e.target.value) || 0
                      })}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="defaultCenterLng">Zeměpisná délka</Label>
                    <Input
                      id="defaultCenterLng"
                      type="number"
                      step="0.000001"
                      value={formValues.defaultCenter?.lng || 14.4378}
                      onChange={(e) => setValue("defaultCenter", {
                        ...formValues.defaultCenter || {},
                        lng: parseFloat(e.target.value) || 0
                      })}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <Label>Viditelné vrstvy</Label>
                <div className="grid grid-cols-2 gap-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="layer-points"
                      checked={(formValues.visibleLayers || []).includes("points")}
                      onCheckedChange={(checked) => {
                        const currentLayers = formValues.visibleLayers || [];
                        if (checked) {
                          setValue("visibleLayers", [...currentLayers, "points"]);
                        } else {
                          setValue("visibleLayers", currentLayers.filter(l => l !== "points"));
                        }
                      }}
                    />
                    <Label htmlFor="layer-points">Body</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="layer-areas"
                      checked={(formValues.visibleLayers || []).includes("areas")}
                      onCheckedChange={(checked) => {
                        const currentLayers = formValues.visibleLayers || [];
                        if (checked) {
                          setValue("visibleLayers", [...currentLayers, "areas"]);
                        } else {
                          setValue("visibleLayers", currentLayers.filter(l => l !== "areas"));
                        }
                      }}
                    />
                    <Label htmlFor="layer-areas">Oblasti</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="layer-routes"
                      checked={(formValues.visibleLayers || []).includes("routes")}
                      onCheckedChange={(checked) => {
                        const currentLayers = formValues.visibleLayers || [];
                        if (checked) {
                          setValue("visibleLayers", [...currentLayers, "routes"]);
                        } else {
                          setValue("visibleLayers", currentLayers.filter(l => l !== "routes"));
                        }
                      }}
                    />
                    <Label htmlFor="layer-routes">Trasy</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="layer-heatmap"
                      checked={(formValues.visibleLayers || []).includes("heatmap")}
                      onCheckedChange={(checked) => {
                        const currentLayers = formValues.visibleLayers || [];
                        if (checked) {
                          setValue("visibleLayers", [...currentLayers, "heatmap"]);
                        } else {
                          setValue("visibleLayers", currentLayers.filter(l => l !== "heatmap"));
                        }
                      }}
                    />
                    <Label htmlFor="layer-heatmap">Teplotní mapa</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="layer-clusters"
                      checked={(formValues.visibleLayers || []).includes("clusters")}
                      onCheckedChange={(checked) => {
                        const currentLayers = formValues.visibleLayers || [];
                        if (checked) {
                          setValue("visibleLayers", [...currentLayers, "clusters"]);
                        } else {
                          setValue("visibleLayers", currentLayers.filter(l => l !== "clusters"));
                        }
                      }}
                    />
                    <Label htmlFor="layer-clusters">Clustery</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="layer-external"
                      checked={(formValues.visibleLayers || []).includes("external")}
                      onCheckedChange={(checked) => {
                        const currentLayers = formValues.visibleLayers || [];
                        if (checked) {
                          setValue("visibleLayers", [...currentLayers, "external"]);
                        } else {
                          setValue("visibleLayers", currentLayers.filter(l => l !== "external"));
                        }
                      }}
                    />
                    <Label htmlFor="layer-external">Externí vrstvy</Label>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="appearance" className="space-y-4 pt-4">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="pointColor">Barva bodů</Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        id="pointColor"
                        type="color"
                        value={(formValues.mapSettings?.pointColor as string) || "#ef4444"}
                        onChange={(e) => setValue("mapSettings", {
                          ...formValues.mapSettings || {},
                          pointColor: e.target.value
                        })}
                        className="w-12 h-8 p-0"
                      />
                      <Input
                        value={(formValues.mapSettings?.pointColor as string) || "#ef4444"}
                        onChange={(e) => setValue("mapSettings", {
                          ...formValues.mapSettings || {},
                          pointColor: e.target.value
                        })}
                        className="flex-1"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="areaColor">Barva oblastí</Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        id="areaColor"
                        type="color"
                        value={(formValues.mapSettings?.areaColor as string) || "#10b981"}
                        onChange={(e) => setValue("mapSettings", {
                          ...formValues.mapSettings || {},
                          areaColor: e.target.value
                        })}
                        className="w-12 h-8 p-0"
                      />
                      <Input
                        value={(formValues.mapSettings?.areaColor as string) || "#10b981"}
                        onChange={(e) => setValue("mapSettings", {
                          ...formValues.mapSettings || {},
                          areaColor: e.target.value
                        })}
                        className="flex-1"
                      />
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="routeColor">Barva tras</Label>
                    <div className="flex items-center space-x-2">
                      <Input
                        id="routeColor"
                        type="color"
                        value={(formValues.mapSettings?.routeColor as string) || "#3b82f6"}
                        onChange={(e) => setValue("mapSettings", {
                          ...formValues.mapSettings || {},
                          routeColor: e.target.value
                        })}
                        className="w-12 h-8 p-0"
                      />
                      <Input
                        value={(formValues.mapSettings?.routeColor as string) || "#3b82f6"}
                        onChange={(e) => setValue("mapSettings", {
                          ...formValues.mapSettings || {},
                          routeColor: e.target.value
                        })}
                        className="flex-1"
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="heatmapIntensity">Intenzita teplotní mapy</Label>
                    <div className="flex items-center space-x-2">
                      <Slider
                        id="heatmapIntensity"
                        min={0.1}
                        max={1}
                        step={0.1}
                        value={[(formValues.mapSettings?.heatmapIntensity as number) || 0.5]}
                        onValueChange={(value) => setValue("mapSettings", {
                          ...formValues.mapSettings || {},
                          heatmapIntensity: value[0]
                        })}
                      />
                      <span className="text-sm text-muted-foreground w-8">
                        {((formValues.mapSettings?.heatmapIntensity as number) || 0.5).toFixed(1)}
                      </span>
                    </div>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label>Zobrazení ovládacích prvků</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="showScale"
                        checked={(formValues.mapSettings?.showScale as boolean) ?? true}
                        onCheckedChange={(checked) => setValue("mapSettings", {
                          ...formValues.mapSettings || {},
                          showScale: checked
                        })}
                      />
                      <Label htmlFor="showScale">Zobrazit měřítko</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="showCompass"
                        checked={(formValues.mapSettings?.showCompass as boolean) ?? true}
                        onCheckedChange={(checked) => setValue("mapSettings", {
                          ...formValues.mapSettings || {},
                          showCompass: checked
                        })}
                      />
                      <Label htmlFor="showCompass">Zobrazit kompas</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="showZoomControls"
                        checked={(formValues.mapSettings?.showZoomControls as boolean) ?? true}
                        onCheckedChange={(checked) => setValue("mapSettings", {
                          ...formValues.mapSettings || {},
                          showZoomControls: checked
                        })}
                      />
                      <Label htmlFor="showZoomControls">Zobrazit ovládání přiblížení</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="showLayerControl"
                        checked={(formValues.mapSettings?.showLayerControl as boolean) ?? true}
                        onCheckedChange={(checked) => setValue("mapSettings", {
                          ...formValues.mapSettings || {},
                          showLayerControl: checked
                        })}
                      />
                      <Label htmlFor="showLayerControl">Zobrazit ovládání vrstev</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch
                        id="showCoordinates"
                        checked={(formValues.mapSettings?.showCoordinates as boolean) ?? true}
                        onCheckedChange={(checked) => setValue("mapSettings", {
                          ...formValues.mapSettings || {},
                          showCoordinates: checked
                        })}
                      />
                      <Label htmlFor="showCoordinates">Zobrazit souřadnice</Label>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="advanced" className="space-y-4 pt-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Upozornění</AlertTitle>
                <AlertDescription>
                  Změna pokročilých nastavení může ovlivnit výkon a chování mapy. Měňte pouze pokud víte, co děláte.
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="minZoom">Minimální přiblížení</Label>
                  <Input
                    id="minZoom"
                    type="number"
                    min={1}
                    max={10}
                    value={(formValues.mapSettings?.minZoom as number) || 3}
                    onChange={(e) => setValue("mapSettings", {
                      ...formValues.mapSettings || {},
                      minZoom: parseInt(e.target.value) || 3
                    })}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="maxZoom">Maximální přiblížení</Label>
                  <Input
                    id="maxZoom"
                    type="number"
                    min={10}
                    max={22}
                    value={(formValues.mapSettings?.maxZoom as number) || 18}
                    onChange={(e) => setValue("mapSettings", {
                      ...formValues.mapSettings || {},
                      maxZoom: parseInt(e.target.value) || 18
                    })}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="clusterRadius">Poloměr clusterů</Label>
                  <div className="flex items-center space-x-2">
                    <Slider
                      id="clusterRadius"
                      min={20}
                      max={200}
                      step={10}
                      value={[(formValues.mapSettings?.clusterRadius as number) || 80]}
                      onValueChange={(value) => setValue("mapSettings", {
                        ...formValues.mapSettings || {},
                        clusterRadius: value[0]
                      })}
                    />
                    <span className="text-sm text-muted-foreground w-8">
                      {(formValues.mapSettings?.clusterRadius as number) || 80}
                    </span>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="heatmapRadius">Poloměr teplotní mapy</Label>
                  <div className="flex items-center space-x-2">
                    <Slider
                      id="heatmapRadius"
                      min={5}
                      max={50}
                      step={5}
                      value={[(formValues.mapSettings?.heatmapRadius as number) || 25]}
                      onValueChange={(value) => setValue("mapSettings", {
                        ...formValues.mapSettings || {},
                        heatmapRadius: value[0]
                      })}
                    />
                    <span className="text-sm text-muted-foreground w-8">
                      {(formValues.mapSettings?.heatmapRadius as number) || 25}
                    </span>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Pokročilé funkce</Label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="enableClusteringPoints"
                      checked={(formValues.mapSettings?.enableClusteringPoints as boolean) ?? true}
                      onCheckedChange={(checked) => setValue("mapSettings", {
                        ...formValues.mapSettings || {},
                        enableClusteringPoints: checked
                      })}
                    />
                    <Label htmlFor="enableClusteringPoints">Povolit shlukování bodů</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="enableHeatmap"
                      checked={(formValues.mapSettings?.enableHeatmap as boolean) ?? false}
                      onCheckedChange={(checked) => setValue("mapSettings", {
                        ...formValues.mapSettings || {},
                        enableHeatmap: checked
                      })}
                    />
                    <Label htmlFor="enableHeatmap">Povolit teplotní mapu</Label>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
