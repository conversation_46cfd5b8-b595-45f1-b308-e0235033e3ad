"use client";

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Globe, AlertTriangle, Image, Building2, Network } from 'lucide-react';

/**
 * Component that displays placeholder sections for future implementation
 */
export function AdditionalSections() {
  return (
    <>
      <Card className="opacity-60">
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Globe className="mr-2 h-5 w-5"/>Zahraniční aktivity
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-4 text-center text-muted-foreground">
          <p>Tato sekce bude implementována v další fázi vývoje.</p>
          <p className="text-sm mt-2">Zde budou informace o zahraničních aktivitách subjektu, včetně účasti v zahraničních společnostech, offshore strukturách a mezinárodních transakcích.</p>
        </CardContent>
      </Card>

      <Card className="opacity-60">
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <AlertTriangle className="mr-2 h-5 w-5"/>Insolvence a exekuce
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-4 text-center text-muted-foreground">
          <p>Tato sekce bude implementována v další fázi vývoje.</p>
          <p className="text-sm mt-2">Zde budou informace o případných insolvencích, exekucích a dalších finančních problémech subjektu.</p>
        </CardContent>
      </Card>

      <Card className="opacity-60">
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Image className="mr-2 h-5 w-5"/>Fotodokumentace
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-4 text-center text-muted-foreground">
          <p>Tato sekce bude implementována v další fázi vývoje.</p>
          <p className="text-sm mt-2">Zde bude možné nahrát a spravovat fotografie související s podnikatelskými aktivitami subjektu, jako jsou provozovny, sídla společností apod.</p>
        </CardContent>
      </Card>

      <Card className="opacity-60">
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Building2 className="mr-2 h-5 w-5"/>Nemovitosti
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-4 text-center text-muted-foreground">
          <p>Tato sekce bude implementována v další fázi vývoje.</p>
          <p className="text-sm mt-2">Zde budou informace o nemovitostech souvisejících s podnikatelskými aktivitami subjektu.</p>
        </CardContent>
      </Card>

      <Card className="opacity-60">
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Network className="mr-2 h-5 w-5"/>Personální propojení
          </CardTitle>
        </CardHeader>
        <CardContent className="pt-4 text-center text-muted-foreground">
          <p>Tato sekce bude implementována v další fázi vývoje.</p>
          <p className="text-sm mt-2">Zde budou informace o personálních propojeních subjektu s jinými osobami v rámci podnikatelských aktivit.</p>
        </CardContent>
      </Card>
    </>
  );
}
