"use client";

import type { Submit<PERSON>and<PERSON>} from 'react-hook-form';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import type { 
  EvidenceObyvatelData, 
  PhysicalPersonSubject, 
  AddressRecord, 
  DocumentRecord, 
  ContactRecord, 
  PhotoMetadata,
  PhysicalCharacteristics
} from '@/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { PlusCircle, Trash2, ImageUp, Languages, UserCircle, Building, Phone, MailIcon, Share2, ChevronUp, ChevronDown } from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/loading';
import { useToast } from '@/hooks/use-toast';
import { db } from '@/lib/firebase';
import { doc, setDoc, Timestamp } from 'firebase/firestore';
import { useState, useEffect, useCallback, ChangeEvent } from 'react';
import { cn } from '@/lib/utils';
import {
  Form,
  FormField,
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { PhotoDocumentationSection } from '@/components/modules/gun-license/PhotoDocumentationSection';
import { UseFormReturn } from 'react-hook-form';

// Schemas for array fields
const addressRecordSchema = z.object({
  id: z.string().optional(),
  address: z.string().min(1, "Adresa je povinná"),
  type: z.string().optional().default(''),
  dateFrom: z.string().optional().default(''),
  dateTo: z.string().optional().default(''),
  verified: z.boolean().optional().default(false),
  source: z.string().optional().default(''),
});

const documentRecordSchema = z.object({
  id: z.string().optional(),
  documentType: z.string().min(1, "Typ dokladu je povinný"),
  documentNumber: z.string().min(1, "Číslo dokladu je povinné"),
  issuedBy: z.string().optional().default(''),
  validity: z.string().optional().default(''),
  issuedDate: z.string().optional().default(''),
  status: z.string().optional().default(''),
  notes: z.string().optional().default(''),
});

const contactRecordSchema = z.object({
  id: z.string().optional(),
  value: z.string().min(1, "Hodnota kontaktu je povinná"),
  subType: z.string().optional().default(''),
  verified: z.boolean().optional().default(false),
  active: z.boolean().optional().default(true),
  lastSeen: z.string().optional().default(''),
  notes: z.string().optional().default(''),
});

const photoMetadataSchema = z.object({
  id: z.string().optional(),
  fileName: z.string().optional().default(''),
  downloadURL: z.string().optional().default(''),
  description: z.string().optional().default(''),
  dateTaken: z.string().optional().default(''),
  sourceURL: z.string().optional().default(''),
  storagePath: z.string().optional().default(''),
  location: z.string().optional().default(''),
  photographerInfo: z.string().optional().default(''),
  tags: z.string().optional().default(''),
  verified: z.boolean().optional().default(false),
});

const physicalCharacteristicsSchema = z.object({
  id: z.string().optional(),
  height: z.string().optional().default(''),
  weight: z.string().optional().default(''),
  eyeColor: z.string().optional().default(''),
  hairColor: z.string().optional().default(''),
  build: z.string().optional().default(''),
  scarsMarks: z.string().optional().default(''),
  tattoos: z.string().optional().default(''),
  otherDistinguishing: z.string().optional().default(''),
});

// Main form schema
const evidenceObyvatelSchema = z.object({
  // Základní údaje
  fullName: z.string().optional(),
  useOriginalScriptName: z.boolean().optional().default(false),
  fullNameOriginalScript: z.string().optional().default(''),
  dateOfBirth: z.string().optional(),
  placeOfBirth: z.string().optional().default(''),
  personalIdNumber: z.string().optional().default(''),
  nationality: z.string().optional(),
  
  // Bydliště
  permanentAddress: z.string().optional().default(''),
  temporaryAddresses: z.array(addressRecordSchema).optional().default([]),
  
  // Doklady
  idCardNumber: z.string().optional().default(''),
  idCardIssuedBy: z.string().optional().default(''),
  idCardValidity: z.string().optional().default(''),
  otherDocuments: z.array(documentRecordSchema).optional().default([]),
  
  // Kontakty
  phoneNumbers: z.array(contactRecordSchema).optional().default([]),
  emails: z.array(contactRecordSchema).optional().default([]),
  socialProfiles: z.array(contactRecordSchema).optional().default([]),
  
  // Fotografie
  photos: z.array(photoMetadataSchema).optional().default([]),
  
  // Fyzické charakteristiky
  physicalCharacteristics: physicalCharacteristicsSchema.optional(),
  
  // Základní osobní údaje
  aliases: z.string().optional().default(''),
  criminalRecord: z.string().optional().default(''),
  languages: z.string().optional().default(''),
  interests: z.string().optional().default(''),
  politicalAffiliation: z.string().optional().default(''),
  religiousAffiliation: z.string().optional().default(''),
  travelHistory: z.string().optional().default(''),
  medicalInfo: z.string().optional().default(''),
  emergencyContact: z.string().optional().default(''),
  
  // Vyšetřovací poznámky
  investigationNotes: z.string().optional().default(''),
  threatLevel: z.string().optional().default('low'),
  surveillanceNotes: z.string().optional().default(''),
  lastUpdatedBy: z.string().optional().default(''),
}).refine(data => {
  if (data.useOriginalScriptName && !data.fullNameOriginalScript?.trim()) {
    return false;
  }
  return true;
}, {
  message: "Jméno v originálním skriptu je povinné, pokud je zaškrtnuta volba.",
  path: ["fullNameOriginalScript"],
});

type EvidenceObyvatelFormValues = z.infer<typeof evidenceObyvatelSchema>;

interface EvidenceObyvatelFormProps {
  caseId: string;
  subject: PhysicalPersonSubject;
  existingData: EvidenceObyvatelData | null;
  onSave: (moduleId: string, wasNew: boolean) => void;
}

// Helper to generate initial form values
const getInitialFormValues = (subject: PhysicalPersonSubject, existingData: EvidenceObyvatelData | null): EvidenceObyvatelFormValues => {
  return {
    // Základní údaje
    fullName: existingData?.fullName || `${subject.firstName} ${subject.lastName}`,
    useOriginalScriptName: existingData?.useOriginalScriptName || false,
    fullNameOriginalScript: existingData?.fullNameOriginalScript || '',
    dateOfBirth: existingData?.dateOfBirth || subject.dateOfBirth || '',
    nationality: existingData?.nationality || subject.nationality || '',
    placeOfBirth: existingData?.placeOfBirth || '',
    personalIdNumber: existingData?.personalIdNumber || '',
    
    // Bydliště
    permanentAddress: existingData?.permanentAddress || '',
    temporaryAddresses: existingData?.temporaryAddresses?.map(a => ({
      ...a, 
      id: a.id || crypto.randomUUID(),
      type: a.type || '',
      dateFrom: a.dateFrom || '',
      dateTo: a.dateTo || '',
      verified: a.verified || false,
      source: a.source || ''
    })) || [],
    
    // Doklady
    idCardNumber: existingData?.idCardNumber || '',
    idCardIssuedBy: existingData?.idCardIssuedBy || '',
    idCardValidity: existingData?.idCardValidity || '',
    otherDocuments: existingData?.otherDocuments?.map(d => ({
      ...d, 
      id: d.id || crypto.randomUUID(),
      issuedBy: d.issuedBy || '',
      validity: d.validity || '',
      issuedDate: d.issuedDate || '',
      status: d.status || '',
      notes: d.notes || ''
    })) || [],
    
    // Kontakty
    phoneNumbers: existingData?.phoneNumbers?.map(p => ({
      ...p, 
      id: p.id || crypto.randomUUID(),
      subType: p.subType || '',
      verified: p.verified || false,
      active: p.active !== undefined ? p.active : true,
      lastSeen: p.lastSeen || '',
      notes: p.notes || ''
    })) || [],
    emails: existingData?.emails?.map(e => ({
      ...e, 
      id: e.id || crypto.randomUUID(),
      subType: e.subType || '',
      verified: e.verified || false,
      active: e.active !== undefined ? e.active : true,
      lastSeen: e.lastSeen || '',
      notes: e.notes || ''
    })) || [],
    socialProfiles: existingData?.socialProfiles?.map(s => ({
      ...s, 
      id: s.id || crypto.randomUUID(),
      subType: s.subType || '',
      verified: s.verified || false,
      active: s.active !== undefined ? s.active : true,
      lastSeen: s.lastSeen || '',
      notes: s.notes || ''
    })) || [],
    
    // Fotografie
    photos: existingData?.photos?.map(p => ({
      ...p,
      id: p.id || crypto.randomUUID(),
      downloadURL: p.downloadURL || '',
      sourceURL: p.sourceURL || '',
      fileName: p.fileName || '',
      description: p.description || '',
      dateTaken: p.dateTaken || '',
      storagePath: p.storagePath || '',
      location: p.location || '',
      photographerInfo: p.photographerInfo || '',
      tags: p.tags || '',
      verified: p.verified || false,
    })) || [],
    
    // Fyzické charakteristiky
    physicalCharacteristics: existingData?.physicalCharacteristics ? {
      ...existingData.physicalCharacteristics,
      id: existingData.physicalCharacteristics.id || crypto.randomUUID(),
      height: existingData.physicalCharacteristics.height || '',
      weight: existingData.physicalCharacteristics.weight || '',
      eyeColor: existingData.physicalCharacteristics.eyeColor || '',
      hairColor: existingData.physicalCharacteristics.hairColor || '',
      build: existingData.physicalCharacteristics.build || '',
      scarsMarks: existingData.physicalCharacteristics.scarsMarks || '',
      tattoos: existingData.physicalCharacteristics.tattoos || '',
      otherDistinguishing: existingData.physicalCharacteristics.otherDistinguishing || ''
    } : {
      id: crypto.randomUUID(),
      height: '',
      weight: '',
      eyeColor: '',
      hairColor: '',
      build: '',
      scarsMarks: '',
      tattoos: '',
      otherDistinguishing: ''
    },
    
    // Základní osobní údaje
    aliases: existingData?.aliases || '',
    criminalRecord: existingData?.criminalRecord || '',
    languages: existingData?.languages || '',
    interests: existingData?.interests || '',
    politicalAffiliation: existingData?.politicalAffiliation || '',
    religiousAffiliation: existingData?.religiousAffiliation || '',
    travelHistory: existingData?.travelHistory || '',
    medicalInfo: existingData?.medicalInfo || '',
    emergencyContact: existingData?.emergencyContact || '',
    
    // Vyšetřovací poznámky
    investigationNotes: existingData?.investigationNotes || '',
    threatLevel: existingData?.threatLevel || 'low',
    surveillanceNotes: existingData?.surveillanceNotes || '',
    lastUpdatedBy: existingData?.lastUpdatedBy || '',
  };
};

export function EvidenceObyvatelForm({ caseId, subject, existingData, onSave }: EvidenceObyvatelFormProps) {
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const moduleId = "evidence_obyvatel";

  const form = useForm<EvidenceObyvatelFormValues>({
    resolver: zodResolver(evidenceObyvatelSchema),
    defaultValues: getInitialFormValues(subject, existingData),
  });

  // Field arrays
  const { fields: tempAddressFields, append: appendTempAddress, remove: removeTempAddress } = useFieldArray({ control: form.control, name: "temporaryAddresses" });
  const { fields: docFields, append: appendDoc, remove: removeDoc } = useFieldArray({ control: form.control, name: "otherDocuments" });
  const { fields: phoneFields, append: appendPhone, remove: removePhone } = useFieldArray({ control: form.control, name: "phoneNumbers" });
  const { fields: emailFields, append: appendEmail, remove: removeEmail } = useFieldArray({ control: form.control, name: "emails" });
  const { fields: socialFields, append: appendSocial, remove: removeSocial } = useFieldArray({ control: form.control, name: "socialProfiles" });

  // Lokální state pro UI kontrolu
  const [showOriginalScript, setShowOriginalScript] = useState(existingData?.useOriginalScriptName || false);

  const onSubmitHandler: SubmitHandler<EvidenceObyvatelFormValues> = async (data) => {
    setIsSaving(true);
    try {
      const moduleDocRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);

      // Zpracování fotografií
      const validPhotos = data.photos?.filter(p => p.downloadURL && p.downloadURL.trim()) || [];
      
      // Kompletní data
      const saveData: any = {
        fullName: String(data.fullName || ''),
        useOriginalScriptName: Boolean(data.useOriginalScriptName),
        fullNameOriginalScript: String(data.fullNameOriginalScript || ''),
        dateOfBirth: String(data.dateOfBirth || ''),
        placeOfBirth: String(data.placeOfBirth || ''),
        personalIdNumber: String(data.personalIdNumber || ''),
        permanentAddress: String(data.permanentAddress || ''),
        idCardNumber: String(data.idCardNumber || ''),
        nationality: String(data.nationality || ''),
        idCardIssuedBy: String(data.idCardIssuedBy || ''),
        idCardValidity: String(data.idCardValidity || ''),
        
        // Pole arrays
        temporaryAddresses: data.temporaryAddresses || [],
        otherDocuments: data.otherDocuments || [],
        phoneNumbers: data.phoneNumbers || [],
        emails: data.emails || [],
        socialProfiles: data.socialProfiles || [],
        
        // Fotografie
        photos: validPhotos.map(photo => ({
          id: photo.id || crypto.randomUUID(),
          downloadURL: photo.downloadURL,
          description: photo.description || '',
          dateTaken: photo.dateTaken || '',
          fileName: photo.fileName || '',
          sourceURL: photo.sourceURL || '',
          storagePath: photo.downloadURL
        })),
        
        photosCount: validPhotos.length,
        hasPhotos: validPhotos.length > 0,
        
        // Fyzické charakteristiky
        physicalCharacteristics: data.physicalCharacteristics || null,
        
        // Základní osobní údaje
        aliases: String(data.aliases || ''),
        criminalRecord: String(data.criminalRecord || ''),
        languages: String(data.languages || ''),
        interests: String(data.interests || ''),
        politicalAffiliation: String(data.politicalAffiliation || ''),
        religiousAffiliation: String(data.religiousAffiliation || ''),
        travelHistory: String(data.travelHistory || ''),
        medicalInfo: String(data.medicalInfo || ''),
        emergencyContact: String(data.emergencyContact || ''),
        investigationNotes: String(data.investigationNotes || ''),
        threatLevel: String(data.threatLevel || 'low'),
        surveillanceNotes: String(data.surveillanceNotes || ''),
        lastUpdatedBy: String(data.lastUpdatedBy || ''),
        
        subjectId: String(subject.id),
        lastUpdatedAt: Timestamp.now(),
        createdAt: existingData?.createdAt || Timestamp.now(),
      };
      
      if (!data.useOriginalScriptName) {
        saveData.fullNameOriginalScript = '';
      }

      await setDoc(moduleDocRef, saveData);
      toast({ title: "Data modulu uložena", description: "Informace z modulu Evidence obyvatel byly úspěšně uloženy." });

      const wasNew = !existingData || !existingData.createdAt;
      onSave(moduleId, wasNew);

    } catch (error: any) {
      console.error("Error saving Evidence Obyvatel data:", error);
      toast({ title: "Chyba ukládání", description: error.message, variant: "destructive" });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmitHandler)} className="space-y-8">
        <Card className="shadow-md">
          <CardHeader><CardTitle className="text-xl flex items-center"><UserCircle className="mr-3 h-6 w-6 text-primary" />Základní údaje</CardTitle></CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormItemRHF label="Jméno a příjmení (dle OP/pasu)" name="fullName" control={form.control} placeholder="Celé jméno subjektu" />
              <FormItemRHF label="Datum narození" name="dateOfBirth" control={form.control} type="date" />
            </div>

            <FormField
              control={form.control}
              name="useOriginalScriptName"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-3 shadow-sm bg-card-foreground/5 hover:bg-card-foreground/10 transition-colors">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={(checked) => {
                        field.onChange(checked);
                        setShowOriginalScript(checked === true);
                      }}
                      id="useOriginalScriptNameCheckbox"
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <Label htmlFor="useOriginalScriptNameCheckbox" className="flex items-center cursor-pointer text-sm font-medium">
                      <Languages className="mr-2 h-4 w-4 text-primary" /> Zadat jméno v originálním skriptu (např. azbuka, arabština)
                    </Label>
                  </div>
                </FormItem>
              )}
            />
            {showOriginalScript && (
              <FormItemRHF label="Jméno a příjmení v originálním skriptu" name="fullNameOriginalScript" control={form.control} placeholder="Např. Иван Иванов nebo اسم الشخص" />
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
              <FormItemRHF label="Místo narození" name="placeOfBirth" control={form.control} placeholder="Např. Praha" />
              <FormItemRHF label="Rodné číslo" name="personalIdNumber" control={form.control} placeholder="RRMMDD/XXXX" />
            </div>
            <FormItemRHF label="Státní příslušnost" name="nationality" control={form.control} placeholder="Např. Česká republika" />
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader><CardTitle className="text-xl flex items-center"><Building className="mr-3 h-6 w-6 text-primary" />Bydliště</CardTitle></CardHeader>
          <CardContent className="space-y-6">
            <FormItemRHF label="Trvalé bydliště" name="permanentAddress" control={form.control} placeholder="Ulice 123, Město, PSČ" as="textarea" rows={2}/>
            <Separator className="my-6"/>
            <Label className="text-lg font-medium block mb-3">Přechodná bydliště</Label>
            {tempAddressFields.map((field, index) => (
              <Card key={field.id} className="p-4 shadow-sm bg-card-foreground/5">
                <div className="flex items-end gap-3">
                    <FormItemRHF label={`Adresa ${index + 1}`} name={`temporaryAddresses.${index}.address`} control={form.control} placeholder="Ulice, Město, PSČ" className="flex-grow" as="textarea" rows={2}/>
                    <Button type="button" variant="ghost" size="icon" onClick={() => removeTempAddress(index)} className="text-destructive hover:bg-destructive/10 shrink-0"><Trash2 className="h-5 w-5" /></Button>
                </div>
              </Card>
            ))}
            <Button type="button" variant="outline" onClick={() => appendTempAddress({ id: crypto.randomUUID(), address: '', type: '', dateFrom: '', dateTo: '', verified: false, source: '' })} size="sm"><PlusCircle className="mr-2 h-4 w-4" /> Přidat přechodné bydliště</Button>
          </CardContent>
        </Card>

        <Card className="shadow-md">
          <CardHeader><CardTitle className="text-xl">Doklady totožnosti</CardTitle></CardHeader>
          <CardContent className="space-y-6">
            <Card className="p-4 shadow-sm border-primary/50 border-2">
                <CardHeader className='p-0 pb-4'><CardTitle className='text-lg'>Občanský průkaz</CardTitle></CardHeader>
                <CardContent className='p-0 space-y-4'>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <FormItemRHF label="Číslo OP" name="idCardNumber" control={form.control} placeholder="XXXXXXXXX" />
                        <FormItemRHF label="Vydal (OP)" name="idCardIssuedBy" control={form.control} placeholder="Název úřadu" />
                    </div>
                    <FormItemRHF label="Platnost (OP)" name="idCardValidity" control={form.control} placeholder="DD.MM.RRRR - DD.MM.RRRR" />
                </CardContent>
            </Card>
            <Separator className="my-6"/>
            <Label className="text-lg font-medium block mb-3">Další doklady</Label>
            {docFields.map((field, index) => (
              <Card key={field.id} className="p-4 space-y-4 shadow-sm bg-card-foreground/5">
                  <div className="flex justify-between items-center mb-2">
                    <p className="font-semibold text-md">Doklad {index + 1}</p>
                    <Button type="button" variant="ghost" size="icon" onClick={() => removeDoc(index)} className="text-destructive hover:bg-destructive/10"><Trash2 className="h-5 w-5" /></Button>
                  </div>
                  <FormItemSelectRHF label="Typ dokladu" name={`otherDocuments.${index}.documentType`} control={form.control} placeholder="Vyberte typ..." options={[
                        {value: "passport", label: "Cestovní pas"}, {value: "driving_license", label: "Řidičský průkaz"},
                        {value: "gun_license", label: "Zbrojní průkaz"}, {value: "other", label: "Jiný"}
                    ]}/>
                  <FormItemRHF label="Číslo dokladu" name={`otherDocuments.${index}.documentNumber`} control={form.control} placeholder="Číslo dokladu" />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormItemRHF label="Vydal" name={`otherDocuments.${index}.issuedBy`} control={form.control} placeholder="Vydávající úřad" />
                      <FormItemRHF label="Platnost" name={`otherDocuments.${index}.validity`} control={form.control} placeholder="DD.MM.RRRR - DD.MM.RRRR" />
                  </div>
              </Card>
            ))}
            <Button type="button" variant="outline" onClick={() => appendDoc({ id: crypto.randomUUID(), documentType: '', documentNumber: '', issuedBy: '', validity: '', issuedDate: '', status: '', notes: '' })} size="sm"><PlusCircle className="mr-2 h-4 w-4" /> Přidat další doklad</Button>
          </CardContent>
        </Card>

        {renderContactSection(form, phoneFields, appendPhone, removePhone, "Telefonní čísla", "phoneNumbers", "phone", [{value: "mobile", label: "Mobilní"}, {value: "home", label: "Domácí"}, {value: "work", label: "Pracovní"}, {value: "other", label: "Jiný"}], Phone, "+420 123 456 789")}
        {renderContactSection(form, emailFields, appendEmail, removeEmail, "E-mailové adresy", "emails", "email", [{value: "personal", label: "Osobní"}, {value: "work", label: "Pracovní"}, {value: "other", label: "Jiný"}], MailIcon, "<EMAIL>")}
        {renderContactSection(form, socialFields, appendSocial, removeSocial, "Profily na sociálních sítích", "socialProfiles", "social", [{value: "facebook", label: "Facebook"}, {value: "instagram", label: "Instagram"}, {value: "twitter_x", label: "Twitter / X"}, {value: "linkedin", label: "LinkedIn"}, {value: "telegram", label: "Telegram"}, {value: "tiktok", label: "TikTok"}, {value: "vk", label: "VKontakte"}, {value: "other", label: "Jiná síť"}], Share2, "URL profilu nebo @uživatelské_jméno")}

        <Card className="shadow-md">
          <PhotoDocumentationSection
            form={form as UseFormReturn<any>}
            namePrefix="photos"
            title="Fotografie osoby"
            description="Nahrajte a spravujte fotografie osoby. Fotografie se ukládají trvale do aplikace."
            photoHint="person face document id_card"
            caseId={caseId}
            subjectId={subject.id}
            moduleId={moduleId}
          />
        </Card>

        {/* Fyzické charakteristiky */}
        <Card className="shadow-md">
          <CardHeader><CardTitle className="text-xl flex items-center"><UserCircle className="mr-3 h-6 w-6 text-primary" />Fyzické charakteristiky</CardTitle></CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormItemRHF label="Výška" name="physicalCharacteristics.height" control={form.control} placeholder="např. 180 cm" />
              <FormItemRHF label="Váha" name="physicalCharacteristics.weight" control={form.control} placeholder="např. 75 kg" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormItemRHF label="Barva očí" name="physicalCharacteristics.eyeColor" control={form.control} placeholder="např. hnědé, modré, zelené" />
              <FormItemRHF label="Barva vlasů" name="physicalCharacteristics.hairColor" control={form.control} placeholder="např. černé, blond, šedivé" />
            </div>
            <FormItemRHF label="Postava" name="physicalCharacteristics.build" control={form.control} placeholder="např. štíhlá, atletická, robustní" />
            <FormItemRHF label="Jizvy a znamínka" name="physicalCharacteristics.scarsMarks" control={form.control} placeholder="Popis viditelných jizev, znamínek, mateřských znamének" as="textarea" rows={2}/>
            <FormItemRHF label="Tetování" name="physicalCharacteristics.tattoos" control={form.control} placeholder="Popis tetování, jejich umístění a motivy" as="textarea" rows={2}/>
            <FormItemRHF label="Jiné rozpoznávací znaky" name="physicalCharacteristics.otherDistinguishing" control={form.control} placeholder="Piercing, neobvyklé rysy, způsob chůze, atd." as="textarea" rows={2}/>
          </CardContent>
        </Card>

        {/* Základní osobní údaje */}
        <Card className="shadow-md">
          <CardHeader><CardTitle className="text-xl flex items-center"><UserCircle className="mr-3 h-6 w-6 text-primary" />Základní osobní údaje</CardTitle></CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormItemRHF label="Aliasy a přezdívky" name="aliases" control={form.control} placeholder="Jiná jména, kterými je osoba známa" as="textarea" rows={2}/>
              <FormItemRHF label="Jazykové znalosti" name="languages" control={form.control} placeholder="Jazyky, kterými mluví" as="textarea" rows={2}/>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormItemRHF label="Zájmy a koníčky" name="interests" control={form.control} placeholder="Volnočasové aktivity, záliby" as="textarea" rows={2}/>
              <FormItemRHF label="Politická příslušnost" name="politicalAffiliation" control={form.control} placeholder="Politické názory, členství" as="textarea" rows={2}/>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormItemRHF label="Náboženská příslušnost" name="religiousAffiliation" control={form.control} placeholder="Náboženské vyznání" as="textarea" rows={2}/>
              <FormItemRHF label="Nouzový kontakt" name="emergencyContact" control={form.control} placeholder="Kontakt v případě nouze" as="textarea" rows={2}/>
            </div>
            <FormItemRHF label="Trestní minulost" name="criminalRecord" control={form.control} placeholder="Informace o trestní minulosti" as="textarea" rows={3}/>
            <FormItemRHF label="Cestovní historie" name="travelHistory" control={form.control} placeholder="Významné cesty, pobyt v zahraničí" as="textarea" rows={3}/>
            <FormItemRHF label="Zdravotní informace" name="medicalInfo" control={form.control} placeholder="Relevantní zdravotní údaje" as="textarea" rows={2}/>
          </CardContent>
        </Card>

        {/* Vyšetřovací poznámky */}
        <Card className="shadow-md">
          <CardHeader><CardTitle className="text-xl flex items-center"><UserCircle className="mr-3 h-6 w-6 text-primary" />Vyšetřovací poznámky</CardTitle></CardHeader>
          <CardContent className="space-y-6">
            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
              <h4 className="font-semibold text-yellow-800 mb-3">Interní poznámky</h4>
              <div className="space-y-4">
                <FormItemSelectRHF label="Úroveň hrozby" name="threatLevel" control={form.control} placeholder="Vyberte..." options={[
                  {value: "low", label: "Nízká"}, {value: "medium", label: "Střední"}, 
                  {value: "high", label: "Vysoká"}, {value: "critical", label: "Kritická"}
                ]}/>
                <FormItemRHF label="Poznámky vyšetřovatele" name="investigationNotes" control={form.control} placeholder="Interní poznámky o vyšetřování" as="textarea" rows={3}/>
                <FormItemRHF label="Poznámky ze sledování" name="surveillanceNotes" control={form.control} placeholder="Pozorování a sledování osoby" as="textarea" rows={3}/>
                <FormItemRHF label="Naposledy aktualizoval" name="lastUpdatedBy" control={form.control} placeholder="Jméno vyšetřovatele" />
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="flex justify-end pt-8 mt-8 border-t border-border">
          <Button type="submit" disabled={isSaving} className="w-full md:w-auto text-lg py-3 px-6 shadow-md hover:shadow-lg transition-shadow">
            {isSaving ? (
              <div className="flex items-center">
                <LoadingSpinner size="sm" className="mr-2 text-white" />
                Ukládání dat...
              </div>
            ) : (
              "Uložit data modulu"
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}

// Helper components for react-hook-form with ShadCN
interface FormItemRHFProps {
  label: string;
  name: string;
  control: any;
  placeholder?: string;
  type?: string;
  disabled?: boolean;
  className?: string;
  as?: 'input' | 'textarea';
  rows?: number;
}
const FormItemRHF = ({ label, name, control, placeholder, type = "text", disabled = false, className, as = 'input', rows }: FormItemRHFProps) => (
  <FormField
    control={control}
    name={name as any}
    disabled={disabled}
    render={({ field, fieldState: { error } }) => (
      <FormItem className={cn("w-full", className)}>
        <FormLabel className="font-semibold text-sm">{label}</FormLabel>
        <FormControl>
          {as === 'input' ? (
            <Input 
              name={field.name}
              ref={field.ref}
              value={field.value || ''} 
              onChange={field.onChange}
              onBlur={field.onBlur}
              placeholder={placeholder} 
              type={type} 
              className={cn("text-base md:text-sm", error ? "border-destructive focus-visible:ring-destructive" : "")} 
            />
          ) : (
            <Textarea 
              name={field.name}
              ref={field.ref}
              value={field.value || ''} 
              onChange={field.onChange}
              onBlur={field.onBlur}
              placeholder={placeholder} 
              rows={rows} 
              className={cn("text-base md:text-sm",error ? "border-destructive focus-visible:ring-destructive" : "")} 
            />
          )}
        </FormControl>
        {error && <FormMessage className="mt-1 text-xs" />}
      </FormItem>
    )}
  />
);

interface FormItemSelectRHFProps {
    label: string;
    name: string;
    control: any;
    placeholder?: string;
    options: {value: string; label: string}[];
    disabled?: boolean;
    className?: string;
}
const FormItemSelectRHF = ({ label, name, control, placeholder, options, disabled = false, className }: FormItemSelectRHFProps) => (
    <FormField
      control={control}
      name={name as any}
      disabled={disabled}
      render={({ field, fieldState: { error } }) => (
        <FormItem className={cn("w-full", className)}>
            <FormLabel className="font-semibold text-sm">{label}</FormLabel>
            <Select onValueChange={field.onChange} value={field.value || ''}>
                <FormControl>
                    <SelectTrigger className={cn("w-full text-base md:text-sm", error ? "border-destructive focus:ring-destructive" : "")}>
                        <SelectValue placeholder={placeholder} />
                    </SelectTrigger>
                </FormControl>
                <SelectContent>
                    {options.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
                </SelectContent>
            </Select>
            {error && <FormMessage className="mt-1 text-xs" />}
        </FormItem>
      )}
    />
);

const renderContactSection = (
  form: any,
  fields: any[],
  append: (obj: any) => void,
  remove: (index: number) => void,
  title: string,
  fieldName: keyof Pick<EvidenceObyvatelFormValues, "phoneNumbers" | "emails" | "socialProfiles">,
  contactType: 'phone' | 'email' | 'social',
  subTypeOptions: {value: string; label: string}[],
  IconComponent?: React.ElementType,
  valuePlaceholder?: string
) => (
  <Card className="shadow-md">
    <CardHeader><CardTitle className="text-xl flex items-center">{IconComponent && <IconComponent className="mr-3 h-6 w-6 text-primary" />}{title}</CardTitle></CardHeader>
    <CardContent className="space-y-6">
      {fields.map((field, index) => (
        <Card key={field.id} className="p-4 space-y-4 shadow-sm bg-card-foreground/5">
            <div className="flex justify-between items-center mb-2">
                <p className="font-semibold text-md">
                  {contactType === 'phone' ? 'Kontakt' : contactType === 'email' ? 'E-mail' : 'Profil'} {index + 1}
                </p>
                <Button type="button" variant="ghost" size="icon" onClick={() => remove(index)} className="text-destructive hover:bg-destructive/10"><Trash2 className="h-5 w-5" /></Button>
            </div>
          <FormItemRHF
            label={contactType === 'phone' ? 'Číslo' : contactType === 'email' ? 'Adresa' : 'Odkaz / Uživatelské jméno'}
            name={`${fieldName}.${index}.value`}
            control={form.control}
            placeholder={valuePlaceholder || (contactType === 'phone' ? '+420 xxx xxx xxx' : '<EMAIL>')}
            type={contactType === 'email' ? 'email' : contactType === 'social' ? 'text' : 'text'}
          />
          <FormItemSelectRHF label="Typ kontaktu" name={`${fieldName}.${index}.subType`} control={form.control} placeholder="Vyberte typ..." options={subTypeOptions}/>
        </Card>
      ))}
      <Button type="button" variant="outline" onClick={() => append({ id: crypto.randomUUID(), value: '', subType: subTypeOptions[0]?.value || '', verified: false, active: true, lastSeen: '', notes: '' })} size="sm"><PlusCircle className="mr-2 h-4 w-4" /> Přidat {contactType === 'phone' ? 'telefonní číslo' : contactType === 'email' ? 'e-mail' : 'profil'}</Button>
    </CardContent>
  </Card>
); 