"use client";

import { Control, useFieldArray } from "react-hook-form";
import { NetworkAnalysisModuleFormValues } from "./schemas";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Laptop, MoreHorizontal, Plus, Trash2, Edit, Eye } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { formatDate } from "@/lib/utils";

interface DeviceListProps {
  control: Control<NetworkAnalysisModuleFormValues>;
  onSelectDevice: (index: number) => void;
  onAddDevice: () => void;
}

export function DeviceList({ control, onSelectDevice, onAddDevice }: DeviceListProps) {
  const { fields, remove } = useFieldArray({
    control,
    name: "devices",
  });

  const handleDeleteDevice = (index: number) => {
    if (confirm("Opravdu chcete smazat toto zařízení?")) {
      remove(index);
    }
  };

  const getDeviceTypeBadge = (type: string) => {
    switch (type) {
      case "router":
        return <Badge className="bg-blue-500">Router</Badge>;
      case "switch":
        return <Badge className="bg-green-500">Switch</Badge>;
      case "firewall":
        return <Badge className="bg-red-500">Firewall</Badge>;
      case "server":
        return <Badge className="bg-purple-500">Server</Badge>;
      case "desktop":
        return <Badge className="bg-gray-500">Desktop</Badge>;
      case "laptop":
        return <Badge className="bg-indigo-500">Laptop</Badge>;
      case "mobile":
        return <Badge className="bg-yellow-500">Mobilní zařízení</Badge>;
      case "iot":
        return <Badge className="bg-teal-500">IoT</Badge>;
      default:
        return <Badge variant="outline">Jiné</Badge>;
    }
  };

  if (fields.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Zařízení</CardTitle>
          <CardDescription>
            Zatím nebyla přidána žádná zařízení
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <Laptop className="h-16 w-16 text-muted-foreground mb-4" />
          <p className="text-muted-foreground mb-4 text-center">
            Přidejte zařízení spojená s tímto subjektem pro analýzu síťové aktivity
          </p>
          <Button onClick={onAddDevice}>
            <Plus className="mr-2 h-4 w-4" />
            Přidat zařízení
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Zařízení</CardTitle>
        <CardDescription>
          Seznam zařízení spojených se subjektem
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Název</TableHead>
              <TableHead>Typ</TableHead>
              <TableHead>IP adresa</TableHead>
              <TableHead>MAC adresa</TableHead>
              <TableHead>Výrobce</TableHead>
              <TableHead>Poslední aktivita</TableHead>
              <TableHead className="text-right">Akce</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {fields.map((device, index) => (
              <TableRow key={device.id} className="cursor-pointer hover:bg-muted/50" onClick={() => {
                setTimeout(() => {
                  onSelectDevice(index);
                }, 0);
              }}>
                <TableCell className="font-medium">{device.name || "Neuvedeno"}</TableCell>
                <TableCell>{getDeviceTypeBadge(device.deviceType)}</TableCell>
                <TableCell>{device.ipAddress || "Neuvedeno"}</TableCell>
                <TableCell>{device.macAddress || "Neuvedeno"}</TableCell>
                <TableCell>{device.manufacturer || "Neuvedeno"}</TableCell>
                <TableCell>{device.lastSeenDate ? formatDate(device.lastSeenDate) : "Neuvedeno"}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        setTimeout(() => {
                          onSelectDevice(index);
                        }, 0);
                      }}>
                        <Eye className="mr-2 h-4 w-4" />
                        Zobrazit detail
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        setTimeout(() => {
                          onSelectDevice(index);
                        }, 0);
                      }}>
                        <Edit className="mr-2 h-4 w-4" />
                        Upravit
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        className="text-destructive focus:text-destructive"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteDevice(index);
                        }}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Smazat
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
