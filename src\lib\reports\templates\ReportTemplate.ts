export class ReportTemplate {
  
  generatePersonalInfoModule(data: any): string {
    if (!data) return '';

    return `
<div class="module">
    <div class="module-header">
        <div class="module-icon"><i class="fas fa-user"></i></div>
        <h3>Osobn<PERSON></h3>
    </div>
    <div class="module-content">
        <div class="info-grid">
            <div class="info-item">
                <label>Jméno a příjmení:</label>
                <span>${data.firstName || ''} ${data.lastName || ''}</span>
            </div>
            <div class="info-item">
                <label>Datum narození:</label>
                <span>${data.birthDate ? new Date(data.birthDate).toLocaleDateString('cs-CZ') : ''}</span>
            </div>
            <div class="info-item">
                <label>Místo narození:</label>
                <span>${data.birthPlace || ''}</span>
            </div>
            <div class="info-item">
                <label>Státní příslušnost:</label>
                <span>${data.nationality || ''}</span>
            </div>
            <div class="info-item">
                <label>Číslo OP:</label>
                <span>${data.idNumber || ''}</span>
            </div>
        </div>
        
        ${data.addresses && data.addresses.length > 0 ? `
        <div class="subsection">
            <h4>Adresy</h4>
            <ul>
                ${data.addresses.map((addr: any) => `<li>${addr.type}: ${addr.address}</li>`).join('')}
            </ul>
        </div>
        ` : ''}
        
        ${data.contacts && data.contacts.length > 0 ? `
        <div class="subsection">
            <h4>Kontakty</h4>
            <ul>
                ${data.contacts.map((contact: any) => `<li>${contact.type}: ${contact.value}</li>`).join('')}
            </ul>
        </div>
        ` : ''}
        
        ${data.notes ? `
        <div class="subsection">
            <h4>Poznámky</h4>
            <p>${data.notes}</p>
        </div>
        ` : ''}
    </div>
</div>`;
  }

  generateCompanyModule(data: any): string {
    if (!data) return '';

    return `
<div class="module">
    <div class="module-header">
        <div class="module-icon"><i class="fas fa-building"></i></div>
        <h3>Firma</h3>
    </div>
    <div class="module-content">
        <div class="info-grid">
            <div class="info-item">
                <label>Název:</label>
                <span>${data.name || ''}</span>
            </div>
            <div class="info-item">
                <label>IČO:</label>
                <span>${data.ico || ''}</span>
            </div>
            <div class="info-item">
                <label>DIČ:</label>
                <span>${data.dic || ''}</span>
            </div>
            <div class="info-item">
                <label>Právní forma:</label>
                <span>${data.legalForm || ''}</span>
            </div>
            <div class="info-item">
                <label>Datum založení:</label>
                <span>${data.establishedDate ? new Date(data.establishedDate).toLocaleDateString('cs-CZ') : ''}</span>
            </div>
            <div class="info-item">
                <label>Základní kapitál:</label>
                <span>${data.capital || ''}</span>
            </div>
        </div>
        
        ${data.address ? `
        <div class="subsection">
            <h4>Adresa</h4>
            <p>${data.address}</p>
        </div>
        ` : ''}
        
        ${data.activities && data.activities.length > 0 ? `
        <div class="subsection">
            <h4>Předmět podnikání</h4>
            <ul>
                ${data.activities.map((activity: string) => `<li>${activity}</li>`).join('')}
            </ul>
        </div>
        ` : ''}
        
        ${data.representatives && data.representatives.length > 0 ? `
        <div class="subsection">
            <h4>Statutární orgány</h4>
            <ul>
                ${data.representatives.map((rep: any) => `<li>${rep.name} - ${rep.position}</li>`).join('')}
            </ul>
        </div>
        ` : ''}
        
        ${data.notes ? `
        <div class="subsection">
            <h4>Poznámky</h4>
            <p>${data.notes}</p>
        </div>
        ` : ''}
    </div>
</div>`;
  }

  generateRealEstateModule(data: any[]): string {
    if (!data || data.length === 0) return '';

    return `
<div class="module">
    <div class="module-header">
        <div class="module-icon"><i class="fas fa-home"></i></div>
        <h3>Nemovitosti</h3>
    </div>
    <div class="module-content">
        ${data.map((property, index) => `
        <div class="property-item ${index > 0 ? 'border-top' : ''}">
            <h4>Nemovitost ${index + 1}</h4>
            <div class="info-grid">
                <div class="info-item">
                    <label>Typ:</label>
                    <span>${property.type || ''}</span>
                </div>
                <div class="info-item">
                    <label>Adresa:</label>
                    <span>${property.address || ''}</span>
                </div>
                <div class="info-item">
                    <label>Katastrální číslo:</label>
                    <span>${property.cadastralNumber || ''}</span>
                </div>
                <div class="info-item">
                    <label>Výměra:</label>
                    <span>${property.area || ''}</span>
                </div>
                <div class="info-item">
                    <label>Vlastník:</label>
                    <span>${property.owner || ''}</span>
                </div>
                <div class="info-item">
                    <label>Hodnota:</label>
                    <span>${property.value || ''}</span>
                </div>
            </div>
            
            ${property.coOwners && property.coOwners.length > 0 ? `
            <div class="subsection">
                <h5>Spoluvlastníci</h5>
                <ul>
                    ${property.coOwners.map((owner: any) => `<li>${owner.name} (${owner.share})</li>`).join('')}
                </ul>
            </div>
            ` : ''}
            
            ${property.notes ? `
            <div class="subsection">
                <h5>Poznámky</h5>
                <p>${property.notes}</p>
            </div>
            ` : ''}
        </div>
        `).join('')}
    </div>
</div>`;
  }

  generateTrainingModule(data: any): string {
    if (!data) return '';

    return `
<div class="module">
    <div class="module-header">
        <div class="module-icon"><i class="fas fa-graduation-cap"></i></div>
        <h3>Výcvik a bezpečnostní rizika</h3>
    </div>
    <div class="module-content">
        ${data.policeTraining && data.policeTraining.length > 0 ? `
        <div class="subsection">
            <h4>Policejní výcvik</h4>
            <ul>
                ${data.policeTraining.map((training: any) => `
                <li>
                    <strong>${training.rank || ''}</strong>
                    ${training.unit ? ` - ${training.unit}` : ''}
                    ${training.period ? ` (${training.period})` : ''}
                    ${training.specialization ? `<br>Specializace: ${training.specialization}` : ''}
                </li>
                `).join('')}
            </ul>
        </div>
        ` : ''}
        
        ${data.militaryTraining && data.militaryTraining.length > 0 ? `
        <div class="subsection">
            <h4>Vojenský výcvik</h4>
            <ul>
                ${data.militaryTraining.map((training: any) => `
                <li>
                    <strong>${training.rank || ''}</strong>
                    ${training.unit ? ` - ${training.unit}` : ''}
                    ${training.period ? ` (${training.period})` : ''}
                    ${training.specialization ? `<br>Specializace: ${training.specialization}` : ''}
                </li>
                `).join('')}
            </ul>
        </div>
        ` : ''}
        
        ${data.combatSports && data.combatSports.length > 0 ? `
        <div class="subsection">
            <h4>Bojové sporty</h4>
            <ul>
                ${data.combatSports.map((sport: any) => `
                <li>
                    <strong>${sport.sport || ''}</strong>
                    ${sport.level ? ` - ${sport.level}` : ''}
                    ${sport.achievements ? `<br>Úspěchy: ${sport.achievements}` : ''}
                </li>
                `).join('')}
            </ul>
        </div>
        ` : ''}
        
        ${data.securityRisks && data.securityRisks.length > 0 ? `
        <div class="subsection">
            <h4>Bezpečnostní rizika</h4>
            <ul>
                ${data.securityRisks.map((risk: string) => `<li>${risk}</li>`).join('')}
            </ul>
        </div>
        ` : ''}
        
        ${data.notes ? `
        <div class="subsection">
            <h4>Poznámky</h4>
            <p>${data.notes}</p>
        </div>
        ` : ''}
    </div>
</div>`;
  }

  generateEmailAnalysisModule(data: any[]): string {
    if (!data || data.length === 0) return '';

    return `
<div class="module">
    <div class="module-header">
        <div class="module-icon"><i class="fas fa-envelope"></i></div>
        <h3>Analýza emailových adres</h3>
    </div>
    <div class="module-content">
        ${data.map((email, index) => `
        <div class="email-item ${index > 0 ? 'border-top' : ''}">
            <h4>${email.address}</h4>
            <div class="info-grid">
                <div class="info-item">
                    <label>Poskytovatel:</label>
                    <span>${email.provider || ''}</span>
                </div>
                <div class="info-item">
                    <label>Platnost:</label>
                    <span class="${email.isValid ? 'valid' : 'invalid'}">${email.isValid ? 'Platný' : 'Neplatný'}</span>
                </div>
            </div>
            
            ${email.breaches && email.breaches.length > 0 ? `
            <div class="subsection">
                <h5>Úniky dat</h5>
                <ul>
                    ${email.breaches.map((breach: any) => `<li>${breach.name} (${breach.date})</li>`).join('')}
                </ul>
            </div>
            ` : ''}
            
            ${email.socialMedia && email.socialMedia.length > 0 ? `
            <div class="subsection">
                <h5>Sociální sítě</h5>
                <ul>
                    ${email.socialMedia.map((social: any) => `<li>${social.platform}: ${social.profile}</li>`).join('')}
                </ul>
            </div>
            ` : ''}
            
            ${email.notes ? `
            <div class="subsection">
                <h5>Poznámky</h5>
                <p>${email.notes}</p>
            </div>
            ` : ''}
        </div>
        `).join('')}
    </div>
</div>`;
  }

  generatePhoneNumbersModule(data: any[]): string {
    if (!data || data.length === 0) return '';

    return `
<div class="module">
    <div class="module-header">
        <div class="module-icon"><i class="fas fa-phone"></i></div>
        <h3>Telefonní čísla</h3>
    </div>
    <div class="module-content">
        ${data.map((phone, index) => `
        <div class="phone-item ${index > 0 ? 'border-top' : ''}">
            <h4>${phone.number}</h4>
            <div class="info-grid">
                <div class="info-item">
                    <label>Typ:</label>
                    <span>${phone.type || ''}</span>
                </div>
                <div class="info-item">
                    <label>Operátor:</label>
                    <span>${phone.operator || ''}</span>
                </div>
                <div class="info-item">
                    <label>Platnost:</label>
                    <span class="${phone.isValid ? 'valid' : 'invalid'}">${phone.isValid ? 'Platný' : 'Neplatný'}</span>
                </div>
                <div class="info-item">
                    <label>Lokace:</label>
                    <span>${phone.location || ''}</span>
                </div>
            </div>
            
            ${phone.notes ? `
            <div class="subsection">
                <h5>Poznámky</h5>
                <p>${phone.notes}</p>
            </div>
            ` : ''}
        </div>
        `).join('')}
    </div>
</div>`;
  }

  generateIpAddressesModule(data: any[]): string {
    if (!data || data.length === 0) return '';

    return `
<div class="module">
    <div class="module-header">
        <div class="module-icon"><i class="fas fa-network-wired"></i></div>
        <h3>IP adresy a síťová analýza</h3>
    </div>
    <div class="module-content">
        ${data.map((ip, index) => `
        <div class="ip-item ${index > 0 ? 'border-top' : ''}">
            <h4>${ip.address}</h4>
            <div class="info-grid">
                <div class="info-item">
                    <label>Typ:</label>
                    <span>${ip.type || ''}</span>
                </div>
                <div class="info-item">
                    <label>Poskytovatel:</label>
                    <span>${ip.provider || ''}</span>
                </div>
                <div class="info-item">
                    <label>Lokace:</label>
                    <span>${ip.location || ''}</span>
                </div>
            </div>
            
            ${ip.ports && ip.ports.length > 0 ? `
            <div class="subsection">
                <h5>Otevřené porty</h5>
                <ul>
                    ${ip.ports.map((port: any) => `<li>Port ${port.number}: ${port.service}</li>`).join('')}
                </ul>
            </div>
            ` : ''}
            
            ${ip.threats && ip.threats.length > 0 ? `
            <div class="subsection">
                <h5>Bezpečnostní hrozby</h5>
                <ul>
                    ${ip.threats.map((threat: any) => `<li class="threat">${threat.type}: ${threat.description}</li>`).join('')}
                </ul>
            </div>
            ` : ''}
            
            ${ip.notes ? `
            <div class="subsection">
                <h5>Poznámky</h5>
                <p>${ip.notes}</p>
            </div>
            ` : ''}
        </div>
        `).join('')}
    </div>
</div>`;
  }

  generateMapOverlaysModule(data: any): string {
    if (!data) return '';

    return `
<div class="module">
    <div class="module-header">
        <div class="module-icon"><i class="fas fa-map"></i></div>
        <h3>Mapové překryvy</h3>
    </div>
    <div class="module-content">
        <div class="info-grid">
            <div class="info-item">
                <label>Výchozí centrum:</label>
                <span>${data.defaultCenter ? `${data.defaultCenter[0]}, ${data.defaultCenter[1]}` : ''}</span>
            </div>
            <div class="info-item">
                <label>Výchozí zoom:</label>
                <span>${data.defaultZoom || ''}</span>
            </div>
            <div class="info-item">
                <label>Podkladová mapa:</label>
                <span>${data.defaultBasemap || ''}</span>
            </div>
        </div>
        
        ${data.points && data.points.length > 0 ? `
        <div class="subsection">
            <h4>Body (${data.points.length})</h4>
            <ul>
                ${data.points.map((point: any) => `
                <li>
                    <strong>${point.name}</strong> - ${point.pointType}
                    <br>Souřadnice: ${point.latitude}, ${point.longitude}
                    ${point.description ? `<br>${point.description}` : ''}
                </li>
                `).join('')}
            </ul>
        </div>
        ` : ''}
        
        ${data.areas && data.areas.length > 0 ? `
        <div class="subsection">
            <h4>Oblasti (${data.areas.length})</h4>
            <ul>
                ${data.areas.map((area: any) => `
                <li>
                    <strong>${area.name}</strong> - ${area.areaType}
                    ${area.description ? `<br>${area.description}` : ''}
                </li>
                `).join('')}
            </ul>
        </div>
        ` : ''}
        
        ${data.routes && data.routes.length > 0 ? `
        <div class="subsection">
            <h4>Trasy (${data.routes.length})</h4>
            <ul>
                ${data.routes.map((route: any) => `
                <li>
                    <strong>${route.name}</strong> - ${route.routeType}
                    ${route.description ? `<br>${route.description}` : ''}
                </li>
                `).join('')}
            </ul>
        </div>
        ` : ''}
    </div>
</div>`;
  }
}
