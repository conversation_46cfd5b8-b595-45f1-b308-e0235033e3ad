'use server';

/**
 * @fileOverview An AI agent that analyzes case files to identify relationships and associations between entities.
 *
 * - smartLinkAnalysis - A function that performs smart link analysis on case files.
 * - SmartLinkAnalysisInput - The input type for the smartLinkAnalysis function.
 * - SmartLinkAnalysisOutput - The return type for the smartLinkAnalysis function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const SmartLinkAnalysisInputSchema = z.object({
  caseFileContent: z
    .string()
    .describe('The content of the case file to analyze.'),
});
export type SmartLinkAnalysisInput = z.infer<typeof SmartLinkAnalysisInputSchema>;

const SmartLinkAnalysisOutputSchema = z.object({
  entities: z
    .array(z.string())
    .describe('List of identified entities (individuals, companies, events).'),
  relationships: z
    .array(z.string())
    .describe('List of identified relationships and associations between entities.'),
  summary: z.string().describe('A summary of the key relationships and associations.'),
});
export type SmartLinkAnalysisOutput = z.infer<typeof SmartLinkAnalysisOutputSchema>;

export async function smartLinkAnalysis(input: SmartLinkAnalysisInput): Promise<SmartLinkAnalysisOutput> {
  return smartLinkAnalysisFlow(input);
}

export const smartLinkAnalysisPrompt = ai.definePrompt({
  name: 'smartLinkAnalysisPrompt',
  input: {schema: SmartLinkAnalysisInputSchema},
  output: {schema: SmartLinkAnalysisOutputSchema},
  prompt: `Jste expert na OSINT (Open Source Intelligence) analýzu.

  Analyzujte následující obsah spisu a identifikujte klíčové entity (jednotlivce, společnosti, události) a jejich vztahy.
  Poskytněte shrnutí klíčových vztahů a asociací.

  Obsah spisu: {{{caseFileContent}}}

  Výstup by měl být strukturován jako JSON objekt s poli "entities", "relationships" a "summary".
  Entities by měl být seznam řetězců, kde každý řetězec reprezentuje unikátní entitu identifikovanou ve spisu.
  Relationships by měl být seznam řetězců, kde každý řetězec popisuje vztah nebo asociaci mezi entitami.
  Summary by měl být stručný odstavec shrnující nejdůležitější vztahy a asociace.
`,
});

const smartLinkAnalysisFlow = ai.defineFlow(
  {
    name: 'smartLinkAnalysisFlow',
    inputSchema: SmartLinkAnalysisInputSchema,
    outputSchema: SmartLinkAnalysisOutputSchema,
  },
  async input => {
    const {output} = await smartLinkAnalysisPrompt(input);
    return output!;
  }
);
