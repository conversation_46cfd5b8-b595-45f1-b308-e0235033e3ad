"use client";

import { <PERSON>, Controller, FieldPath, FieldValues } from "react-hook-form";
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";

interface FormItemRHFProps<T extends FieldValues> {
  label: string;
  name: FieldPath<T>;
  control: Control<T>;
  placeholder?: string;
  description?: string;
  type?: string;
  as?: "input" | "textarea" | "switch";
  rows?: number;
  className?: string;
  disabled?: boolean;
}

export function FormItemRHF<T extends FieldValues>({
  label,
  name,
  control,
  placeholder,
  description,
  type = "text",
  as = "input",
  rows = 3,
  className,
  disabled = false,
}: FormItemRHFProps<T>) {
  return (
    <FormField
      control={control}
      name={name}
      disabled={disabled}
      render={({ field }) => (
        <FormItem className={className}>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            {as === "textarea" ? (
              <Textarea
                placeholder={placeholder}
                rows={rows}
                disabled={disabled}
                {...field}
                value={field.value || ""}
              />
            ) : as === "switch" ? (
              <div className="flex items-center space-x-2">
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                  disabled={disabled}
                  id={name}
                />
                <FormLabel htmlFor={name} className="!mt-0 cursor-pointer">
                  {label}
                </FormLabel>
              </div>
            ) : (
              <Input
                type={type}
                placeholder={placeholder}
                disabled={disabled}
                {...field}
                value={field.value || ""}
              />
            )}
          </FormControl>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}

interface SelectOption {
  value: string;
  label: string;
}

interface FormItemSelectRHFProps<T extends FieldValues> {
  label: string;
  name: FieldPath<T>;
  control: Control<T>;
  options: SelectOption[];
  placeholder?: string;
  description?: string;
  className?: string;
}

export function FormItemSelectRHF<T extends FieldValues>({
  label,
  name,
  control,
  options,
  placeholder,
  description,
  className,
}: FormItemSelectRHFProps<T>) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          <FormLabel>{label}</FormLabel>
          <Select
            onValueChange={field.onChange}
            defaultValue={field.value}
            value={field.value}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {options.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {description && <FormDescription>{description}</FormDescription>}
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
