"use client";

import type { SubmitHand<PERSON> } from 'react-hook-form';
import { useForm, useFieldArray, Controller, UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import type {
  FinancialMonitoringModuleData, PhysicalPersonSubject, LegalEntitySubject, PhotoMetadata,
  FinancialRegistryType, FinancialInformationReliability, FinancialInformationSource,
  FinancialStatus, ExecutionStatus, InsolvencyStatus, TaxDebtorStatus, PropertyOwnershipStatus,
  YesNoUnknown, ThreatLevel
} from '@/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { PlusCircle, Trash2, DollarSign, Building, Users, AlertTriangle, Search, FileText, Database, Link, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { db } from '@/lib/firebase';
import { doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { cn } from '@/lib/utils';
import { useState, useCallback, useMemo } from "react";
import {
  Form,
  FormField,
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { PhotoDocumentationSection } from '@/components/modules/gun-license/PhotoDocumentationSection';

const photoMetadataSchema = z.object({
  id: z.string().optional(),
  fileName: z.string().optional(),
  downloadURL: z.string().optional(),
  description: z.string().optional(),
  dateTaken: z.string().optional(),
  sourceURL: z.string().optional(),
  storagePath: z.string().optional(),
  location: z.string().optional(),
  photographerInfo: z.string().optional(),
  tags: z.string().optional(),
  verified: z.boolean().optional(),
});

const financialRegistryRecordSchema = z.object({
  id: z.string(),
  registryType: z.enum(['business_registry', 'ares', 'insolvency', 'execution', 'tax_debtors', 'vat_registry', 'cedr_subsidies', 'public_contracts', 'court_proceedings', 'property_registry', 'land_registry', 'patents', 'trademarks', 'databox', 'other']).optional(),
  otherRegistryTypeDetail: z.string().optional(),
  subjectName: z.string().optional(),
  ico: z.string().optional(),
  dic: z.string().optional(),
  personalIdNumber: z.string().optional(),
  birthDate: z.string().optional(),
  registryNumber: z.string().optional(),
  registrationDate: z.string().optional(),
  validFrom: z.string().optional(),
  validTo: z.string().optional(),
  issuingAuthority: z.string().optional(),
  currentStatus: z.enum(['active', 'inactive', 'suspended', 'liquidated', 'bankrupt', 'unknown']).optional(),
  statusDate: z.string().optional(),
  statusDetails: z.string().optional(),
  legalForm: z.string().optional(),
  businessScope: z.string().optional(),
  registeredOffice: z.string().optional(),
  insolvencyStatus: z.enum(['no_proceedings', 'petition_filed', 'proceedings_started', 'reorganization', 'bankruptcy', 'discharge', 'proceedings_stopped', 'unknown']).optional(),
  insolvencyNumber: z.string().optional(),
  insolvencyDate: z.string().optional(),
  insolvencyAdministrator: z.string().optional(),
  debtAmount: z.number().optional(),
  creditorsList: z.string().optional(),
  executionStatus: z.enum(['active', 'completed', 'stopped', 'unsuccessful', 'unknown']).optional(),
  executionNumber: z.string().optional(),
  executionDate: z.string().optional(),
  executor: z.string().optional(),
  debtorAmount: z.number().optional(),
  executionReason: z.string().optional(),
  taxDebtorStatus: z.enum(['no_debt', 'has_debt', 'cleared', 'enforcement', 'unknown']).optional(),
  taxDebtAmount: z.number().optional(),
  taxDebtDetails: z.string().optional(),
  subsidyAmount: z.number().optional(),
  subsidyProvider: z.string().optional(),
  subsidyPurpose: z.string().optional(),
  subsidyDate: z.string().optional(),
  contractAmount: z.number().optional(),
  contractDescription: z.string().optional(),
  contractingAuthority: z.string().optional(),
  contractDate: z.string().optional(),
  propertyType: z.string().optional(),
  propertyAddress: z.string().optional(),
  propertyOwnershipStatus: z.enum(['sole_owner', 'co_owner', 'former_owner', 'lessee', 'user', 'unknown']).optional(),
  propertyValue: z.number().optional(),
  propertyAcquisitionDate: z.string().optional(),
  informationSource: z.enum(['official_registry', 'court_records', 'tax_authority', 'social_media', 'surveillance', 'witness', 'documents', 'investigation', 'open_source', 'other']).optional(),
  sourceDetail: z.string().optional(),
  informationReliability: z.enum(['verified', 'probable', 'possible', 'unconfirmed', 'unknown']).optional(),
  verificationDate: z.string().optional(),
  lastVerified: z.string().optional(),
  photos: z.array(photoMetadataSchema).optional().default([]),
  documentReferences: z.string().optional(),
  notes: z.string().optional(),
});

const financialConnectionRecordSchema = z.object({
  id: z.string(),
  connectedEntityName: z.string().optional(),
  connectedEntityICO: z.string().optional(),
  connectionType: z.string().optional(),
  connectionDescription: z.string().optional(),
  ownershipPercentage: z.number().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  isActive: z.boolean().optional(),
  notes: z.string().optional(),
});

const financialAssetRecordSchema = z.object({
  id: z.string(),
  assetType: z.string().optional(),
  assetName: z.string().optional(),
  assetValue: z.number().optional(),
  valueCurrency: z.string().optional(),
  acquisitionDate: z.string().optional(),
  acquisitionMethod: z.string().optional(),
  currentLocation: z.string().optional(),
  ownershipStatus: z.enum(['sole_owner', 'co_owner', 'former_owner', 'lessee', 'user', 'unknown']).optional(),
  mortgageInfo: z.string().optional(),
  legalRestrictions: z.string().optional(),
  photos: z.array(photoMetadataSchema).optional().default([]),
  notes: z.string().optional(),
});

const financialTransactionRecordSchema = z.object({
  id: z.string(),
  date: z.string().optional(),
  amount: z.number().optional(),
  currency: z.string().optional(),
  transactionType: z.string().optional(),
  counterparty: z.string().optional(),
  description: z.string().optional(),
  accountNumber: z.string().optional(),
  bankName: z.string().optional(),
  reference: z.string().optional(),
  isRegular: z.boolean().optional(),
  frequency: z.string().optional(),
  suspiciousActivity: z.boolean().optional(),
  riskLevel: z.enum(['none', 'low', 'medium', 'high', 'critical', 'unknown']).optional(),
  source: z.enum(['official_registry', 'court_records', 'tax_authority', 'social_media', 'surveillance', 'witness', 'documents', 'investigation', 'open_source', 'other']).optional(),
  notes: z.string().optional(),
});

const financialRiskAssessmentSchema = z.object({
  overallRisk: z.enum(['none', 'low', 'medium', 'high', 'critical', 'unknown']).optional(),
  insolvencyRisk: z.enum(['none', 'low', 'medium', 'high', 'critical', 'unknown']).optional(),
  liquidityRisk: z.enum(['none', 'low', 'medium', 'high', 'critical', 'unknown']).optional(),
  legalRisk: z.enum(['none', 'low', 'medium', 'high', 'critical', 'unknown']).optional(),
  reputationRisk: z.enum(['none', 'low', 'medium', 'high', 'critical', 'unknown']).optional(),
  riskFactors: z.array(z.string()).optional(),
  mitigatingFactors: z.array(z.string()).optional(),
  riskAssessmentDate: z.string().optional(),
  assessedBy: z.string().optional(),
  nextReviewDate: z.string().optional(),
  riskNotes: z.string().optional(),
});

const financialMonitoringModuleSchema = z.object({
  registryRecords: z.array(financialRegistryRecordSchema).optional().default([]),
  businessConnections: z.array(financialConnectionRecordSchema).optional().default([]),
  familyConnections: z.array(financialConnectionRecordSchema).optional().default([]),
  assets: z.array(financialAssetRecordSchema).optional().default([]),
  liabilities: z.string().optional(),
  transactions: z.array(financialTransactionRecordSchema).optional().default([]),
  bankAccounts: z.array(z.string()).optional().default([]),
  estimatedIncome: z.number().optional(),
  incomeCurrency: z.string().optional(),
  incomeSource: z.string().optional(),
  estimatedExpenses: z.number().optional(),
  expensesCurrency: z.string().optional(),
  lifestyleAnalysis: z.string().optional(),
  taxCompliance: z.enum(['yes', 'no', 'unknown']).optional(),
  taxIssues: z.string().optional(),
  legalProceedings: z.string().optional(),
  courtCases: z.string().optional(),
  businessActivity: z.enum(['yes', 'no', 'unknown']).optional(),
  businessType: z.string().optional(),
  businessIncome: z.number().optional(),
  businessAssets: z.string().optional(),
  suspiciousTransactions: z.string().optional(),
  mlRiskFactors: z.array(z.string()).optional().default([]),
  complianceIssues: z.string().optional(),
  sanctionsCheck: z.enum(['yes', 'no', 'unknown']).optional(),
  riskAssessment: financialRiskAssessmentSchema.optional(),
  investigationPriorities: z.string().optional(),
  recommendedActions: z.string().optional(),
  followUpRequired: z.enum(['yes', 'no', 'unknown']).optional(),
  nextReviewDate: z.string().optional(),
  informationSources: z.string().optional(),
  lastVerificationDate: z.string().optional(),
  dataQualityAssessment: z.string().optional(),
  executiveSummary: z.string().optional(),
  keyFindings: z.string().optional(),
  redFlags: z.string().optional(),
  investigationNotes: z.string().optional(),
  generalNotes: z.string().optional(),
  photos: z.array(photoMetadataSchema).optional().default([]),
});

type FinancialMonitoringFormValues = z.infer<typeof financialMonitoringModuleSchema>;

interface FinancialMonitoringFormProps {
  caseId: string;
  subject: PhysicalPersonSubject | LegalEntitySubject;
  existingData: FinancialMonitoringModuleData | null;
  onSave: (moduleId: string, wasNew: boolean) => void;
}

const registryTypeOptions = [
  { value: 'business_registry', label: 'Obchodní rejstřík' },
  { value: 'ares', label: 'ARES' },
  { value: 'insolvency', label: 'Insolvenční rejstřík' },
  { value: 'execution', label: 'Centrální evidence exekucí' },
  { value: 'tax_debtors', label: 'Registr daňových dlužníků' },
  { value: 'vat_registry', label: 'Registr plátců DPH' },
  { value: 'cedr_subsidies', label: 'CEDR - dotace' },
  { value: 'public_contracts', label: 'Veřejné zakázky' },
  { value: 'court_proceedings', label: 'Soudní řízení' },
  { value: 'property_registry', label: 'Katastr nemovitostí' },
  { value: 'land_registry', label: 'Registr půdy' },
  { value: 'patents', label: 'Patenty' },
  { value: 'trademarks', label: 'Ochranné známky' },
  { value: 'databox', label: 'Datové schránky' },
  { value: 'other', label: 'Jiné' },
];

const statusOptions = [
  { value: 'active', label: 'Aktivní' },
  { value: 'inactive', label: 'Neaktivní' },
  { value: 'suspended', label: 'Pozastaveno' },
  { value: 'liquidated', label: 'V likvidaci' },
  { value: 'bankrupt', label: 'V úpadku' },
  { value: 'unknown', label: 'Neznámé' },
];

const insolvencyStatusOptions = [
  { value: 'no_proceedings', label: 'Bez řízení' },
  { value: 'petition_filed', label: 'Návrh podán' },
  { value: 'proceedings_started', label: 'Řízení zahájeno' },
  { value: 'reorganization', label: 'Reorganizace' },
  { value: 'bankruptcy', label: 'Konkurs' },
  { value: 'discharge', label: 'Oddlužení' },
  { value: 'proceedings_stopped', label: 'Řízení zastaveno' },
  { value: 'unknown', label: 'Neznámé' },
];

const executionStatusOptions = [
  { value: 'active', label: 'Aktivní' },
  { value: 'completed', label: 'Dokončeno' },
  { value: 'stopped', label: 'Zastaveno' },
  { value: 'unsuccessful', label: 'Neúspěšné' },
  { value: 'unknown', label: 'Neznámé' },
];

const taxDebtorStatusOptions = [
  { value: 'no_debt', label: 'Bez dluhu' },
  { value: 'has_debt', label: 'Má dluh' },
  { value: 'cleared', label: 'Vyrovnáno' },
  { value: 'enforcement', label: 'Ve vymáhání' },
  { value: 'unknown', label: 'Neznámé' },
];

const reliabilityOptions = [
  { value: 'verified', label: 'Ověřené' },
  { value: 'probable', label: 'Pravděpodobné' },
  { value: 'possible', label: 'Možné' },
  { value: 'unconfirmed', label: 'Nepotvrzené' },
  { value: 'unknown', label: 'Neznámé' },
];

const sourceOptions = [
  { value: 'official_registry', label: 'Oficiální rejstřík' },
  { value: 'court_records', label: 'Soudní spisy' },
  { value: 'tax_authority', label: 'Daňový úřad' },
  { value: 'social_media', label: 'Sociální sítě' },
  { value: 'surveillance', label: 'Sledování' },
  { value: 'witness', label: 'Svědek' },
  { value: 'documents', label: 'Dokumenty' },
  { value: 'investigation', label: 'Vyšetřování' },
  { value: 'open_source', label: 'Otevřené zdroje' },
  { value: 'other', label: 'Jiné' },
];

const yesNoUnknownOptions = [
  { value: 'yes', label: 'Ano' },
  { value: 'no', label: 'Ne' },
  { value: 'unknown', label: 'Neznámé' },
];

const threatLevelOptions = [
  { value: 'none', label: 'Žádné' },
  { value: 'low', label: 'Nízké' },
  { value: 'medium', label: 'Střední' },
  { value: 'high', label: 'Vysoké' },
  { value: 'critical', label: 'Kritické' },
  { value: 'unknown', label: 'Neznámé' },
];

export function FinancialMonitoringForm({ caseId, subject, existingData, onSave }: FinancialMonitoringFormProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const generateId = () => Date.now().toString(36) + Math.random().toString(36).substr(2);

  const defaultValues: FinancialMonitoringFormValues = useMemo(() => {
    if (existingData) {
      return {
        registryRecords: existingData.registryRecords?.map(record => ({
          ...record,
          photos: record.photos || []
        })) || [],
        businessConnections: existingData.businessConnections || [],
        familyConnections: existingData.familyConnections || [],
        assets: existingData.assets?.map(asset => ({
          ...asset,
          photos: asset.photos || []
        })) || [],
        liabilities: existingData.liabilities || '',
        transactions: existingData.transactions || [],
        bankAccounts: existingData.bankAccounts || [],
        estimatedIncome: existingData.estimatedIncome || undefined,
        incomeCurrency: existingData.incomeCurrency || 'CZK',
        incomeSource: existingData.incomeSource || '',
        estimatedExpenses: existingData.estimatedExpenses || undefined,
        expensesCurrency: existingData.expensesCurrency || 'CZK',
        lifestyleAnalysis: existingData.lifestyleAnalysis || '',
        taxCompliance: existingData.taxCompliance || undefined,
        taxIssues: existingData.taxIssues || '',
        legalProceedings: existingData.legalProceedings || '',
        courtCases: existingData.courtCases || '',
        businessActivity: existingData.businessActivity || undefined,
        businessType: existingData.businessType || '',
        businessIncome: existingData.businessIncome || undefined,
        businessAssets: existingData.businessAssets || '',
        suspiciousTransactions: existingData.suspiciousTransactions || '',
        mlRiskFactors: existingData.mlRiskFactors || [],
        complianceIssues: existingData.complianceIssues || '',
        sanctionsCheck: existingData.sanctionsCheck || undefined,
        riskAssessment: existingData.riskAssessment || {},
        investigationPriorities: existingData.investigationPriorities || '',
        recommendedActions: existingData.recommendedActions || '',
        followUpRequired: existingData.followUpRequired || undefined,
        nextReviewDate: existingData.nextReviewDate || '',
        informationSources: existingData.informationSources || '',
        lastVerificationDate: existingData.lastVerificationDate || '',
        dataQualityAssessment: existingData.dataQualityAssessment || '',
        executiveSummary: existingData.executiveSummary || '',
        keyFindings: existingData.keyFindings || '',
        redFlags: existingData.redFlags || '',
        investigationNotes: existingData.investigationNotes || '',
        generalNotes: existingData.generalNotes || '',
        photos: existingData.photos || [],
      };
    }

    return {
      registryRecords: [],
      businessConnections: [],
      familyConnections: [],
      assets: [],
      liabilities: '',
      transactions: [],
      bankAccounts: [],
      estimatedIncome: undefined,
      incomeCurrency: 'CZK',
      incomeSource: '',
      estimatedExpenses: undefined,
      expensesCurrency: 'CZK',
      lifestyleAnalysis: '',
      taxCompliance: undefined,
      taxIssues: '',
      legalProceedings: '',
      courtCases: '',
      businessActivity: undefined,
      businessType: '',
      businessIncome: undefined,
      businessAssets: '',
      suspiciousTransactions: '',
      mlRiskFactors: [],
      complianceIssues: '',
      sanctionsCheck: undefined,
      riskAssessment: {},
      investigationPriorities: '',
      recommendedActions: '',
      followUpRequired: undefined,
      nextReviewDate: '',
      informationSources: '',
      lastVerificationDate: '',
      dataQualityAssessment: '',
      executiveSummary: '',
      keyFindings: '',
      redFlags: '',
      investigationNotes: '',
      generalNotes: '',
      photos: [],
    };
  }, [existingData]);

  const form = useForm<FinancialMonitoringFormValues>({
    resolver: zodResolver(financialMonitoringModuleSchema),
    defaultValues,
  });

  // Field arrays pro dynamické sekce
  const { fields: registryFields, append: appendRegistry, remove: removeRegistry } = useFieldArray({
    control: form.control,
    name: "registryRecords",
  });

  const { fields: businessConnectionFields, append: appendBusinessConnection, remove: removeBusinessConnection } = useFieldArray({
    control: form.control,
    name: "businessConnections",
  });

  const { fields: familyConnectionFields, append: appendFamilyConnection, remove: removeFamilyConnection } = useFieldArray({
    control: form.control,
    name: "familyConnections",
  });

  const { fields: assetFields, append: appendAsset, remove: removeAsset } = useFieldArray({
    control: form.control,
    name: "assets",
  });

  const { fields: transactionFields, append: appendTransaction, remove: removeTransaction } = useFieldArray({
    control: form.control,
    name: "transactions",
  });

  const removeUndefinedValues = (obj: any): any => {
    if (obj === null || obj === undefined) return obj;
    if (Array.isArray(obj)) return obj.map(removeUndefinedValues);
    if (typeof obj === 'object') {
      const cleaned: any = {};
      for (const [key, value] of Object.entries(obj)) {
        if (value !== undefined) {
          cleaned[key] = removeUndefinedValues(value);
        }
      }
      return cleaned;
    }
    return obj;
  };

  const onSubmitHandler: SubmitHandler<FinancialMonitoringFormValues> = async (data) => {
    if (isSubmitting) return;
    
    setIsSubmitting(true);
    
    try {
      const cleanedData = removeUndefinedValues(data);
      const wasNew = !existingData;
      
      const moduleData: FinancialMonitoringModuleData = {
        id: existingData?.id || generateId(),
        subjectId: subject.id,
        ...cleanedData,
        lastUpdatedAt: serverTimestamp(),
        createdAt: existingData?.createdAt || serverTimestamp(),
      };

      const moduleRef = doc(db, 'cases', caseId, 'subjects', subject.id, 'modules', 'financial_monitoring');
      await setDoc(moduleRef, moduleData);

      onSave('financial_monitoring', wasNew);
    } catch (error) {
      console.error('Chyba při ukládání dat finančního monitoringu:', error);
      toast({
        title: "Chyba při ukládání",
        description: "Nepodařilo se uložit data finančního monitoringu. Zkuste to prosím znovu.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-8">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmitHandler)} className="space-y-8">
          
          {/* Registrové záznamy */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Database className="h-5 w-5" />
                <span>Registrové záznamy</span>
              </CardTitle>
              <CardDescription>
                Záznamy z různých registrů a databází včetně insolvenčního řízení, exekucí, daňových dluhů, atd.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {registryFields.map((field, index) => (
                <RegistryRecordCard
                  key={field.id}
                  form={form}
                  recordIndex={index}
                  onRemove={() => removeRegistry(index)}
                  generateStableId={generateId}
                  caseId={caseId}
                  subjectId={subject.id}
                  moduleId="financial_monitoring"
                />
              ))}
              
              <Button
                type="button"
                variant="outline"
                onClick={() => appendRegistry({
                  id: generateId(),
                  registryType: undefined,
                  photos: [],
                })}
                className="w-full"
              >
                <PlusCircle className="h-4 w-4 mr-2" />
                Přidat registrový záznam
              </Button>
            </CardContent>
          </Card>

          {/* Finanční přehled */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5" />
                <span>Finanční přehled</span>
              </CardTitle>
              <CardDescription>
                Přehled příjmů, výdajů a finančního stavu
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItemRHF
                  label="Odhadované příjmy"
                  name="estimatedIncome"
                  control={form.control}
                  type="number"
                  placeholder="Částka v měně"
                />
                
                <FormItemRHF
                  label="Měna příjmů"
                  name="incomeCurrency"
                  control={form.control}
                  placeholder="CZK, EUR, USD..."
                />
              </div>

              <FormItemRHF
                label="Zdroj příjmů"
                name="incomeSource"
                control={form.control}
                placeholder="Popis zdroje příjmů"
                as="textarea"
                rows={3}
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItemRHF
                  label="Odhadované výdaje"
                  name="estimatedExpenses"
                  control={form.control}
                  type="number"
                  placeholder="Částka v měně"
                />
                
                <FormItemRHF
                  label="Měna výdajů"
                  name="expensesCurrency"
                  control={form.control}
                  placeholder="CZK, EUR, USD..."
                />
              </div>

              <FormItemRHF
                label="Analýza životního stylu"
                name="lifestyleAnalysis"
                control={form.control}
                placeholder="Popis životního stylu a způsobu života"
                as="textarea"
                rows={4}
              />
            </CardContent>
          </Card>

          {/* Daňové a právní záležitosti */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Daňové a právní záležitosti</span>
              </CardTitle>
              <CardDescription>
                Informace o daňových povinnostech a právních řízeních
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormItemSelectRHF
                label="Daňová compliance"
                name="taxCompliance"
                control={form.control}
                options={yesNoUnknownOptions}
                placeholder="Vyberte stav"
              />

              <FormItemRHF
                label="Daňové problémy"
                name="taxIssues"
                control={form.control}
                placeholder="Popis daňových problémů nebo dluhů"
                as="textarea"
                rows={3}
              />

              <FormItemRHF
                label="Právní řízení"
                name="legalProceedings"
                control={form.control}
                placeholder="Popis probíhajících právních řízení"
                as="textarea"
                rows={3}
              />

              <FormItemRHF
                label="Soudní případy"
                name="courtCases"
                control={form.control}
                placeholder="Historie soudních případů"
                as="textarea"
                rows={3}
              />
            </CardContent>
          </Card>

          {/* Obchodní činnost */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building className="h-5 w-5" />
                <span>Obchodní činnost</span>
              </CardTitle>
              <CardDescription>
                Informace o podnikatelské činnosti
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormItemSelectRHF
                label="Podnikatelská činnost"
                name="businessActivity"
                control={form.control}
                options={yesNoUnknownOptions}
                placeholder="Vyberte"
              />

              <FormItemRHF
                label="Typ podnikání"
                name="businessType"
                control={form.control}
                placeholder="OSVČ, s.r.o., a.s., atd."
              />

              <FormItemRHF
                label="Obchodní příjmy"
                name="businessIncome"
                control={form.control}
                type="number"
                placeholder="Roční obrat"
              />

              <FormItemRHF
                label="Obchodní majetek"
                name="businessAssets"
                control={form.control}
                placeholder="Popis obchodního majetku"
                as="textarea"
                rows={3}
              />
            </CardContent>
          </Card>

          {/* Bezpečnostní hodnocení */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5" />
                <span>Bezpečnostní hodnocení a rizika</span>
              </CardTitle>
              <CardDescription>
                Hodnocení finančních rizik a podezřelých aktivit
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormItemRHF
                label="Podezřelé transakce"
                name="suspiciousTransactions"
                control={form.control}
                placeholder="Popis podezřelých finančních transakcí"
                as="textarea"
                rows={4}
              />

              <FormItemRHF
                label="Compliance problémy"
                name="complianceIssues"
                control={form.control}
                placeholder="Problémy s dodržováním předpisů"
                as="textarea"
                rows={3}
              />

              <FormItemSelectRHF
                label="Kontrola sankcí"
                name="sanctionsCheck"
                control={form.control}
                options={yesNoUnknownOptions}
                placeholder="Vyberte"
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="riskAssessment.overallRisk"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Celkové riziko</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Vyberte úroveň rizika" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {threatLevelOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="riskAssessment.insolvencyRisk"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Riziko insolvence</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Vyberte úroveň rizika" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {threatLevelOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormItemRHF
                label="Poznámky k riziku"
                name="riskAssessment.riskNotes"
                control={form.control}
                placeholder="Detailní poznámky k hodnocení rizika"
                as="textarea"
                rows={4}
              />
            </CardContent>
          </Card>

          {/* Vyšetřovací poznámky */}
          <Card>
            <CardHeader>
              <CardTitle>Vyšetřovací poznámky a závěry</CardTitle>
              <CardDescription>
                Shrnutí, klíčová zjištění a doporučení
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <FormItemRHF
                label="Exekutivní shrnutí"
                name="executiveSummary"
                control={form.control}
                placeholder="Stručné shrnutí finančního stavu"
                as="textarea"
                rows={4}
              />

              <FormItemRHF
                label="Klíčová zjištění"
                name="keyFindings"
                control={form.control}
                placeholder="Nejdůležitější zjištění z analýzy"
                as="textarea"
                rows={4}
              />

              <FormItemRHF
                label="Červené vlajky"
                name="redFlags"
                control={form.control}
                placeholder="Podezřelé nebo problematické skutečnosti"
                as="textarea"
                rows={3}
              />

              <FormItemRHF
                label="Vyšetřovací priority"
                name="investigationPriorities"
                control={form.control}
                placeholder="Priority pro další vyšetřování"
                as="textarea"
                rows={3}
              />

              <FormItemRHF
                label="Doporučené kroky"
                name="recommendedActions"
                control={form.control}
                placeholder="Doporučení dalších opatření"
                as="textarea"
                rows={3}
              />

              <FormItemRHF
                label="Vyšetřovací poznámky"
                name="investigationNotes"
                control={form.control}
                placeholder="Interní poznámky vyšetřovatele"
                as="textarea"
                rows={4}
              />

              <FormItemRHF
                label="Obecné poznámky"
                name="generalNotes"
                control={form.control}
                placeholder="Další poznámky a informace"
                as="textarea"
                rows={4}
              />
            </CardContent>
          </Card>

          {/* Fotodokumentace */}
          <Card>
            <CardHeader>
              <CardTitle>Fotodokumentace</CardTitle>
              <CardDescription>
                Fotografie dokumentů, nemovitostí, podnikových objektů a další dokumentace
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PhotoDocumentationSection
                form={form as UseFormReturn<any>}
                title="Fotodokumentace finančního monitoringu"
                description="Nahrajte fotografie dokumentů, výpisy z registrů, snímky nemovitostí a další relevantní dokumentaci."
                namePrefix="photos"
                photoHint="financial monitoring document registry"
                caseId={caseId}
                subjectId={subject.id}
                moduleId="financial_monitoring"
              />
            </CardContent>
          </Card>

          {/* Tlačítka pro uložení */}
          <div className="flex justify-end space-x-4 pt-6">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Ukládání...
                </>
              ) : (
                'Uložit modul'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
}

// Pomocné komponenty pro formulář
interface RegistryRecordCardProps {
  form: UseFormReturn<FinancialMonitoringFormValues>;
  recordIndex: number;
  onRemove: () => void;
  generateStableId: () => string;
  caseId: string;
  subjectId: string;
  moduleId: string;
}

function RegistryRecordCard({ form, recordIndex, onRemove, generateStableId, caseId, subjectId, moduleId }: RegistryRecordCardProps) {
  const registryType = form.watch(`registryRecords.${recordIndex}.registryType`);

  return (
    <Card className="relative">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Registrový záznam #{recordIndex + 1}</CardTitle>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={onRemove}
            className="text-destructive hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItemSelectRHF
            label="Typ registru"
            name={`registryRecords.${recordIndex}.registryType`}
            control={form.control}
            options={registryTypeOptions}
            placeholder="Vyberte typ registru"
          />
          
          {registryType === 'other' && (
            <FormItemRHF
              label="Upřesnění typu registru"
              name={`registryRecords.${recordIndex}.otherRegistryTypeDetail`}
              control={form.control}
              placeholder="Upřesněte typ registru"
            />
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItemRHF
            label="Název subjektu"
            name={`registryRecords.${recordIndex}.subjectName`}
            control={form.control}
            placeholder="Název fyzické nebo právnické osoby"
          />
          
          <FormItemRHF
            label="IČO"
            name={`registryRecords.${recordIndex}.ico`}
            control={form.control}
            placeholder="IČO (pro právnické osoby)"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItemRHF
            label="Číslo záznamu"
            name={`registryRecords.${recordIndex}.registryNumber`}
            control={form.control}
            placeholder="Číslo v registru"
          />
          
          <FormItemRHF
            label="Datum registrace"
            name={`registryRecords.${recordIndex}.registrationDate`}
            control={form.control}
            type="date"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItemSelectRHF
            label="Aktuální stav"
            name={`registryRecords.${recordIndex}.currentStatus`}
            control={form.control}
            options={statusOptions}
            placeholder="Vyberte stav"
          />
          
          <FormItemRHF
            label="Datum stavu"
            name={`registryRecords.${recordIndex}.statusDate`}
            control={form.control}
            type="date"
          />
        </div>

        {/* Podmíněná pole pro insolvenci */}
        {registryType === 'insolvency' && (
          <>
            <Separator />
            <h4 className="font-medium text-sm">Insolvenční řízení</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemSelectRHF
                label="Stav insolvence"
                name={`registryRecords.${recordIndex}.insolvencyStatus`}
                control={form.control}
                options={insolvencyStatusOptions}
                placeholder="Vyberte stav"
              />
              
              <FormItemRHF
                label="Číslo insolvenčního řízení"
                name={`registryRecords.${recordIndex}.insolvencyNumber`}
                control={form.control}
                placeholder="Číslo řízení"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemRHF
                label="Výše dluhu"
                name={`registryRecords.${recordIndex}.debtAmount`}
                control={form.control}
                type="number"
                placeholder="Částka v CZK"
              />
              
              <FormItemRHF
                label="Správce"
                name={`registryRecords.${recordIndex}.insolvencyAdministrator`}
                control={form.control}
                placeholder="Jméno správce"
              />
            </div>
          </>
        )}

        {/* Podmíněná pole pro exekuce */}
        {registryType === 'execution' && (
          <>
            <Separator />
            <h4 className="font-medium text-sm">Exekuční řízení</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemSelectRHF
                label="Stav exekuce"
                name={`registryRecords.${recordIndex}.executionStatus`}
                control={form.control}
                options={executionStatusOptions}
                placeholder="Vyberte stav"
              />
              
              <FormItemRHF
                label="Číslo exekuce"
                name={`registryRecords.${recordIndex}.executionNumber`}
                control={form.control}
                placeholder="Číslo exekuce"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemRHF
                label="Výše dluhu"
                name={`registryRecords.${recordIndex}.debtorAmount`}
                control={form.control}
                type="number"
                placeholder="Částka v CZK"
              />
              
              <FormItemRHF
                label="Exekutor"
                name={`registryRecords.${recordIndex}.executor`}
                control={form.control}
                placeholder="Jméno exekutora"
              />
            </div>
          </>
        )}

        <FormItemRHF
          label="Poznámky"
          name={`registryRecords.${recordIndex}.notes`}
          control={form.control}
          placeholder="Další poznámky k záznamu"
          as="textarea"
          rows={3}
        />

        {/* Fotodokumentace pro každý záznam */}
        <div className="pt-4">
          <h5 className="font-medium text-sm mb-3">Fotodokumentace záznamu</h5>
          <PhotoDocumentationSection
            form={form as UseFormReturn<any>}
            title=""
            description="Fotografie dokumentů z registru, výpisů a souvisejících materiálů"
            namePrefix={`registryRecords.${recordIndex}.photos`}
            photoHint="registry document financial"
            caseId={caseId}
            subjectId={subjectId}
            moduleId={moduleId}
          />
        </div>
      </CardContent>
    </Card>
  );
}

interface FormItemRHFProps {
  label: string; 
  name: string; 
  control: any; 
  placeholder?: string;
  type?: string; 
  disabled?: boolean; 
  className?: string;
  as?: 'input' | 'textarea'; 
  rows?: number; 
  smallLabel?: boolean;
}

const FormItemRHF = ({ 
  label, 
  name, 
  control, 
  placeholder, 
  type = "text", 
  disabled = false, 
  className, 
  as = 'input', 
  rows, 
  smallLabel 
}: FormItemRHFProps) => (
  <FormField
    control={control}
    name={name}
    render={({ field }) => (
      <FormItem className={className}>
        <FormLabel className={cn(smallLabel && "text-sm")}>{label}</FormLabel>
        <FormControl>
          {as === 'textarea' ? (
            <Textarea
              placeholder={placeholder}
              disabled={disabled}
              rows={rows}
              {...field}
              value={field.value || ''}
            />
          ) : (
            <Input
              type={type}
              placeholder={placeholder}
              disabled={disabled}
              {...field}
              value={field.value || ''}
              onChange={(e) => {
                const value = type === 'number' ? 
                  (e.target.value === '' ? undefined : Number(e.target.value)) : 
                  e.target.value;
                field.onChange(value);
              }}
            />
          )}
        </FormControl>
        <FormMessage />
      </FormItem>
    )}
  />
);

interface FormItemSelectRHFProps {
  label: string; 
  name: string; 
  control: any; 
  placeholder?: string;
  options: {value: string; label: string}[];
  disabled?: boolean; 
  className?: string; 
  smallLabel?: boolean;
}

const FormItemSelectRHF = ({ 
  label, 
  name, 
  control, 
  placeholder, 
  options, 
  disabled = false, 
  className, 
  smallLabel 
}: FormItemSelectRHFProps) => (
  <FormField
    control={control}
    name={name}
    render={({ field }) => (
      <FormItem className={className}>
        <FormLabel className={cn(smallLabel && "text-sm")}>{label}</FormLabel>
        <Select onValueChange={field.onChange} value={field.value || ''} disabled={disabled}>
          <FormControl>
            <SelectTrigger>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
          </FormControl>
          <SelectContent>
            {options.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <FormMessage />
      </FormItem>
    )}
  />
); 