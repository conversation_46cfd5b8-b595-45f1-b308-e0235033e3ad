"use client";

import { cn } from '@/lib/utils';
import { FormField, FormControl, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import * as z from 'zod';

const photoMetadataSchema = z.object({
  id: z.string().optional(),
  fileName: z.string().optional().default(''),
  downloadURL: z.string().optional().default(''),
  description: z.string().optional().default(''),
  dateTaken: z.string().optional().default(''),
  sourceURL: z.string().optional().default(''),
  storagePath: z.string().optional().default(''),
});

const locationTypes = ["home", "work", "family", "leisure", "travel", "hideout", "meeting", "other"] as const;
const locationFrequencies = ["frequent", "occasional", "rare", "one-time"] as const;
const locationSources = ["social-media", "surveillance", "witness", "documents", "database", "other"] as const;

const locationRecordSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  locationType: z.enum(locationTypes).optional(),
  otherLocationTypeDetail: z.string().optional(),
  address: z.string().optional(),
  gpsCoordinates: z.string().optional().refine(val => {
    if (!val || val.trim() === "") return true; // Allow empty
    const parts = val.split(',').map(p => parseFloat(p.trim()));
    return parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1]) && parts[0] >= -90 && parts[0] <= 90 && parts[1] >= -180 && parts[1] <= 180;
  }, { message: "Neplatný formát GPS (např. 50.08, 14.42)" }),
  frequency: z.enum(locationFrequencies).optional(),
  firstSeen: z.string().optional(),
  lastSeen: z.string().optional(),
  source: z.enum(locationSources).optional(),
  otherSourceDetail: z.string().optional(),
  descriptionNotes: z.string().optional(),
  photos: z.array(photoMetadataSchema).optional().default([]),
}).superRefine((data, ctx) => {
  if (data.locationType === 'other' && !data.otherLocationTypeDetail?.trim()) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Upřesnění typu lokace je povinné.", path: ['otherLocationTypeDetail'] });
  }
  if (data.source === 'other' && !data.otherSourceDetail?.trim()) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Upřesnění zdroje je povinné.", path: ['otherSourceDetail'] });
  }
});

const locationsModuleSchema = z.object({
  locations: z.array(locationRecordSchema).optional().default([]),
});

export { locationsModuleSchema };
export type LocationsFormValues = z.infer<typeof locationsModuleSchema>;

// Pomocné komponenty pro formulář
interface FormItemRHFProps {
  label: string; 
  name: string; 
  control: any; 
  placeholder?: string;
  type?: string; 
  disabled?: boolean; 
  className?: string;
  as?: 'input' | 'textarea'; 
  rows?: number; 
  smallLabel?: boolean;
}

export const FormItemRHF = ({ 
  label, 
  name, 
  control, 
  placeholder, 
  type = "text", 
  disabled = false, 
  className, 
  as = 'input', 
  rows, 
  smallLabel 
}: FormItemRHFProps) => (
  <FormField
    control={control} 
    name={name as any} 
    disabled={disabled}
    render={({ field, fieldState: { error } }) => (
      <FormItem className={cn("w-full", className)}>
        <FormLabel className={cn("font-semibold", smallLabel ? "text-xs" : "text-sm")}>
          {label}
        </FormLabel>
        <FormControl>
          {as === 'input' ? (
            <Input 
              {...field} 
              value={field.value || ''} 
              placeholder={placeholder} 
              type={type} 
              className={cn(
                smallLabel ? "text-xs h-8 py-1" : "text-sm h-10", 
                error ? "border-destructive" : ""
              )} 
            />
          ) : (
            <Textarea 
              {...field} 
              value={field.value || ''} 
              placeholder={placeholder} 
              rows={rows} 
              className={cn(
                smallLabel ? "text-xs py-1" : "text-sm", 
                error ? "border-destructive" : ""
              )} 
            />
          )}
        </FormControl>
        {error && <FormMessage className="text-xs" />}
      </FormItem>
    )}
  />
);

interface FormItemSelectRHFProps {
  label: string; 
  name: string; 
  control: any; 
  placeholder?: string;
  options: {value: string; label: string}[];
  disabled?: boolean; 
  className?: string; 
  smallLabel?: boolean;
}

export const FormItemSelectRHF = ({ 
  label, 
  name, 
  control, 
  placeholder, 
  options, 
  disabled = false, 
  className, 
  smallLabel 
}: FormItemSelectRHFProps) => (
  <FormField
    control={control} 
    name={name as any} 
    disabled={disabled}
    render={({ field, fieldState: { error } }) => (
      <FormItem className={cn("w-full", className)}>
        <FormLabel className={cn("font-semibold", smallLabel ? "text-xs" : "text-sm")}>
          {label}
        </FormLabel>
        <Select onValueChange={field.onChange} value={field.value || undefined}>
          <FormControl>
            <SelectTrigger className={cn(
              smallLabel ? "text-xs h-8 py-1" : "text-sm h-10", 
              error ? "border-destructive" : ""
            )}>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
          </FormControl>
          <SelectContent>
            {options.map(opt => (
              <SelectItem key={opt.value} value={opt.value}>
                {opt.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {error && <FormMessage className="text-xs" />}
      </FormItem>
    )}
  />
); 