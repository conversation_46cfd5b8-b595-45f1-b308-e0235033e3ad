/**
 * Přidání fotografie do galerie
 * @param {HTMLElement} gallery - Element galerie
 * @param {string} imageUrl - URL obrázku
 * @param {string} fileName - Název souboru
 */
function addPhotoToGallery(gallery, imageUrl, fileName = '') {
    try {
        console.log('Přidávání fotografie do galerie:', gallery.id || 'bez ID');

        // Vytvořit unikátní ID pro fotografii
        const photoId = `photo-${Date.now()}-${Math.floor(Math.random() * 1000)}`;

        // Vytvořit nový element pro fotografii
        const photoItem = document.createElement('div');
        photoItem.className = 'photo-gallery-item';
        photoItem.setAttribute('data-photo-id', photoId);

        // Přidat obrázek a ovládací prvky
        photoItem.innerHTML = `
            <img src="${imageUrl}" alt="${fileName || 'Fotografie'}">
            <div class="photo-gallery-controls">
                <button type="button" class="photo-gallery-btn remove" title="Odstranit fotografii">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
            <div class="photo-caption-container">
                <textarea class="photo-caption" placeholder="Přidejte popisek k fotografii..."></textarea>
            </div>
        `;

        // Přidat před tlačítko pro přidání
        const addButton = gallery.querySelector('.photo-gallery-add');
        if (addButton) {
            gallery.insertBefore(photoItem, addButton);
        } else {
            gallery.appendChild(photoItem);
        }

        // Přidat event listener pro smazání
        const removeBtn = photoItem.querySelector('.photo-gallery-btn.remove');
        if (removeBtn) {
            removeBtn.addEventListener('click', function() {
                if (confirm('Opravdu chcete odstranit tuto fotografii?')) {
                    photoItem.remove();
                }
            });
        }

        console.log('Fotografie byla úspěšně přidána do galerie');
        return photoItem;
    } catch (error) {
        console.error('Chyba při přidávání fotografie do galerie:', error);
        alert('Došlo k chybě při přidávání fotografie. Zkuste to prosím znovu.');
        return null;
    }
}

/**
 * Zpracování fotografie pro galerii
 * @param {File} file - Soubor fotografie
 * @param {HTMLElement} gallery - Element galerie
 */
function processPhotoForGallery(file, gallery) {
    try {
        console.log('Zpracování fotografie pro galerii:', gallery.id || 'bez ID');

        if (!file) {
            console.error('Nebyl poskytnut žádný soubor pro zpracování');
            return;
        }

        if (!gallery) {
            console.error('Nebyla poskytnuta žádná galerie pro přidání fotografie');
            return;
        }

        if (!file.type.startsWith('image/')) {
            alert('Prosím vyberte obrázek. Vybraný soubor není podporovaný formát obrázku.');
            console.warn('Nepodporovaný typ souboru:', file.type);
            return;
        }

        // Kontrola, zda je galerie inicializována
        if (!gallery.getAttribute('data-gallery-initialized')) {
            console.warn('Galerie není inicializována, pokus o inicializaci...');

            // Pokus o inicializaci galerie
            if (typeof initPhotoGallery === 'function') {
                initPhotoGallery(gallery.parentNode || document.body, gallery, gallery.querySelector('.photo-gallery-add'));
            } else {
                console.error('Nelze inicializovat galerii - funkce initPhotoGallery není dostupná');
                return;
            }
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            if (typeof addPhotoToGallery === 'function') {
                addPhotoToGallery(gallery, e.target.result, file.name);
                console.log('Fotografie byla úspěšně přidána do galerie');
            } else {
                console.error('Nelze přidat fotografii - funkce addPhotoToGallery není dostupná');
            }
        };
        reader.onerror = function() {
            console.error('Chyba při čtení souboru:', reader.error);
            alert('Došlo k chybě při čtení souboru. Zkuste to prosím znovu.');
        };
        reader.readAsDataURL(file);
    } catch (error) {
        console.error('Chyba při zpracování fotografie:', error);
        alert('Došlo k chybě při zpracování fotografie. Zkuste to prosím znovu.');
    }
}

/**
 * Inicializace galerie fotografií
 * @param {HTMLElement} container - Kontejner, ve kterém se nachází galerie
 * @param {HTMLElement|string} gallerySelector - Element galerie nebo selektor
 * @param {HTMLElement|string} addButtonSelector - Element tlačítka nebo selektor
 */
function initPhotoGallery(container, gallerySelector, addButtonSelector) {
    try {
        console.log('Inicializace galerie fotografií:',
                    'container:', container,
                    'gallerySelector:', gallerySelector,
                    'addButtonSelector:', addButtonSelector);

        // Najít galerii a tlačítko pro přidání
        let gallery, addButton;
        let withCaptions = true; // Vždy povolíme popisky

        // Pokud je gallerySelector přímo element galerie
        if (gallerySelector instanceof HTMLElement) {
            gallery = gallerySelector;
        }
        // Pokud je gallerySelector boolean (pro zpětnou kompatibilitu s naší novou implementací)
        else if (typeof gallerySelector === 'boolean') {
            // V tomto případě předpokládáme, že container je přímo galerie
            gallery = container;
            withCaptions = gallerySelector; // Boolean hodnota určuje, zda povolit popisky
            // A hledáme tlačítko přidání uvnitř
            addButton = gallery.querySelector('.photo-gallery-add');
        }
        // Pokud je gallerySelector string (selektor)
        else if (typeof gallerySelector === 'string') {
            if (gallerySelector.startsWith('.') || gallerySelector.startsWith('#')) {
                gallery = container.querySelector(gallerySelector);
            } else {
                gallery = container.querySelector('#' + gallerySelector) ||
                          container.querySelector('.' + gallerySelector);
            }
        }

        // Pokud jsme ještě nenašli addButton a máme addButtonSelector
        if (!addButton && addButtonSelector) {
            if (addButtonSelector instanceof HTMLElement) {
                addButton = addButtonSelector;
            } else if (typeof addButtonSelector === 'string') {
                if (addButtonSelector.startsWith('.') || addButtonSelector.startsWith('#')) {
                    addButton = container.querySelector(addButtonSelector);
                } else {
                    addButton = container.querySelector('#' + addButtonSelector) ||
                                container.querySelector('.' + addButtonSelector);
                }
            }
        }

        // Pokud stále nemáme addButton, zkusíme ho najít v galerii
        if (!addButton && gallery) {
            addButton = gallery.querySelector('.photo-gallery-add');
        }

        console.log('Nalezena galerie:', gallery, 'a tlačítko:', addButton);

        if (!gallery) {
            console.error('Galerie nebyla nalezena:', gallerySelector);
            return;
        }

        if (!addButton) {
            console.error('Tlačítko pro přidání fotografie nebylo nalezeno');
            return;
        }

        // Kontrola, zda již byla galerie inicializována
        if (gallery.getAttribute('data-gallery-initialized')) return;

        // Přidáme unikátní ID pro galerii, pokud ještě nemá
        if (!gallery.id) {
            // Získáme ID nadřazeného kontejneru (např. vozidla nebo nemovitosti)
            const parentId = container.getAttribute('data-vehicle-id') ||
                             container.getAttribute('data-property-id') ||
                             container.getAttribute('data-item-id') ||
                             'item-' + Date.now() + '-' + Math.floor(Math.random() * 10000);

            console.log('Inicializace galerie pro kontejner s ID:', parentId);

            // Nastavíme ID galerie s prefixem nadřazeného kontejneru
            gallery.id = parentId + '-gallery-' + Date.now() + '-' + Math.floor(Math.random() * 10000);
            console.log('Nastaveno ID galerie:', gallery.id);

            // Nastavíme atribut data-parent-id pro galerii
            gallery.setAttribute('data-parent-id', parentId);
        } else {
            console.log('Galerie již má ID:', gallery.id);
        }

        // Označíme galerii jako inicializovanou
        gallery.setAttribute('data-gallery-initialized', 'true');

        // Označíme galerii jako podporující popisky, pokud je to požadováno
        if (withCaptions) {
            gallery.setAttribute('data-with-captions', 'true');
        }

        // Přidat event listener pro tlačítko přidání fotografie
        addButton.addEventListener('click', function() {
            // Otevřít dialog pro výběr souboru
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = 'image/*';
            fileInput.multiple = true;
            fileInput.click();

            fileInput.addEventListener('change', function() {
                if (this.files && this.files.length > 0) {
                    for (let i = 0; i < this.files.length; i++) {
                        processPhotoForGallery(this.files[i], gallery);
                    }
                }
            });
        });

        // Přidat event listener pro drag and drop
        gallery.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.add('dragover');
        });

        gallery.addEventListener('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.remove('dragover');
        });

        gallery.addEventListener('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            this.classList.remove('dragover');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                for (let i = 0; i < files.length; i++) {
                    if (files[i].type.startsWith('image/')) {
                        processPhotoForGallery(files[i], gallery);
                    }
                }
            }
        });

        // Přidat event listener pro Ctrl+V
        gallery.addEventListener('paste', function(e) {
            if (e.clipboardData && e.clipboardData.items) {
                for (let i = 0; i < e.clipboardData.items.length; i++) {
                    if (e.clipboardData.items[i].type.indexOf('image') !== -1) {
                        const file = e.clipboardData.items[i].getAsFile();
                        processPhotoForGallery(file, gallery);
                        e.preventDefault();
                        break;
                    }
                }
            }
        });

        // Přidat CSS pro popisky, pokud jsou povoleny
        if (withCaptions) {
            const style = document.createElement('style');
            style.textContent = `
                .photo-caption-item {
                    display: flex;
                    margin-bottom: 10px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    padding: 8px;
                    background-color: #f9f9f9;
                }
                .photo-caption-thumbnail {
                    width: 60px;
                    height: 60px;
                    margin-right: 10px;
                    flex-shrink: 0;
                }
                .photo-caption-thumbnail img {
                    width: 100%;
                    height: 100%;
                    object-fit: cover;
                    border-radius: 4px;
                }
                .photo-caption-text {
                    flex-grow: 1;
                    resize: vertical;
                }
            `;
            document.head.appendChild(style);
        }

        console.log('Galerie fotografií byla úspěšně inicializována:', gallery.id);
    } catch (error) {
        console.error('Chyba při inicializaci galerie fotografií:', error);
    }
}
