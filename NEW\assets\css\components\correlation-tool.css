/* <PERSON><PERSON>y pro kore<PERSON><PERSON><PERSON><PERSON> nástroj */

/* <PERSON><PERSON><PERSON><PERSON> kontejner korelač<PERSON><PERSON>ho nás<PERSON> */
.correlation-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    min-height: 600px;
    background-color: #f9f9f9;
    border-radius: 5px;
    overflow: hidden;
    font-family: 'Segoe UI', Arial, sans-serif;
}

/* Ovládací prvky korelačního nástroje */
.correlation-controls {
    display: flex;
    flex-wrap: wrap;
    padding: 10px;
    background-color: #f0f0f0;
    border-bottom: 1px solid #ddd;
}

/* Tlačítka */
.btn-primary,
.btn-secondary,
.btn-inline,
.view-mode,
.add-entity,
.add-relationship,
.import-data,
.export-data,
.apply-filters,
.reset-filters,
.run-analysis,
.photo-gallery-add,
.save-notification-settings,
.test-email-alert {
    padding: 4px 8px;
    border-radius: 3px;
    border: 1px solid #ccc;
    background-color: #f8f9fa;
    cursor: pointer;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    margin: 0 2px;
    white-space: nowrap;
    color: #333;
}

.btn-primary:hover,
.add-entity:hover,
.add-relationship:hover,
.import-data:hover,
.export-data:hover,
.apply-filters:hover,
.run-analysis:hover,
.photo-gallery-add:hover,
.save-notification-settings:hover,
.test-email-alert:hover,
.btn-secondary:hover,
.reset-filters:hover,
.view-mode:hover {
    background-color: #e2e6ea;
    border-color: #adb5bd;
}

.btn-inline {
    padding: 3px 6px;
    font-size: 12px;
}

.view-mode.active {
    background-color: #1a3c89;
    color: white;
    border-color: #1a3c89;
}

.view-mode.active:hover {
    background-color: #15306d;
}

/* Ikony v tlačítkách */
button i {
    margin-right: 4px;
}

/* Speciální styly pro tlačítka v horní liště */
.correlation-controls button {
    margin-right: 3px;
}

/* Filtry korelačního nástroje */
.correlation-filters {
    display: flex;
    flex-wrap: wrap;
    padding: 10px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
}

.correlation-filter {
    display: flex;
    flex-direction: column;
    margin-right: 15px;
    margin-bottom: 10px;
}

.correlation-filter label {
    font-size: 12px;
    font-weight: bold;
    margin-bottom: 3px;
    color: #333;
}

.correlation-filter select,
.correlation-filter input {
    padding: 4px 6px;
    border: 1px solid #ccc;
    border-radius: 3px;
    font-size: 13px;
    background-color: white;
}

.correlation-filter select:focus,
.correlation-filter input:focus {
    border-color: #1a3c89;
    outline: none;
}

.correlation-filter select[multiple] {
    height: 100px;
    min-width: 150px;
}

.date-range-picker {
    display: flex;
    align-items: center;
}

.date-range-picker span {
    margin: 0 5px;
}

.date-range-picker input {
    width: 130px;
}

.correlation-filter-buttons {
    display: flex;
    margin-left: auto;
    align-self: flex-end;
}

/* Vyhledávací pole */
.filter-search {
    min-width: 200px;
}

/* Vizualizace korelačního nástroje */
.correlation-visualization {
    flex: 1;
    position: relative;
    overflow: hidden;
    background-color: #fff;
    border: 1px solid #ddd;
    margin: 0;
    min-height: 300px;
}

.correlation-network,
.correlation-timeline,
.correlation-map,
.correlation-matrix {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
}

.visualization-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    padding: 20px;
    color: #777;
    background-color: #f9f9f9;
}

.visualization-empty-state i {
    font-size: 36px;
    margin-bottom: 10px;
    color: #ccc;
}

.visualization-empty-state p {
    margin-bottom: 10px;
    max-width: 400px;
    font-size: 13px;
}

.visualization-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    text-align: center;
    padding: 20px;
    color: #555;
    background-color: #f9f9f9;
}

.visualization-placeholder i {
    font-size: 36px;
    margin-bottom: 10px;
    color: #1a3c89;
}

.visualization-placeholder p {
    margin-bottom: 8px;
    font-size: 13px;
}

/* Panel detailů */
.correlation-details-panel {
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 100%;
    background-color: #fff;
    border-left: 1px solid #ddd;
    box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
    transform: translateX(100%);
    transition: transform 0.3s ease;
    z-index: 10;
    overflow-y: auto;
}

.correlation-details-panel.active {
    transform: translateX(0);
}

.correlation-details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #1a3c89;
    color: white;
    border-bottom: 1px solid #15306d;
}

.correlation-details-header h4 {
    margin: 0;
    font-size: 16px;
}

.correlation-details-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    font-size: 16px;
}

.correlation-details-content {
    padding: 15px;
}

/* Analytický panel */
.correlation-analysis-panel {
    margin: 0;
    border-top: 1px solid #ddd;
    overflow: hidden;
    background-color: #fff;
}

.analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 10px;
    background-color: #1a3c89;
    color: white;
}

.analysis-header h4 {
    margin: 0;
    font-size: 14px;
    font-weight: normal;
}

.analysis-header-controls {
    display: flex;
    gap: 5px;
}

.toggle-analysis-panel {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0;
    font-size: 14px;
}

.analysis-content {
    padding: 0;
    max-height: 200px;
    overflow-y: auto;
}

.analysis-tabs {
    display: flex;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
}

.analysis-tab {
    padding: 6px 12px;
    background-color: transparent;
    border: none;
    border-right: 1px solid #ddd;
    cursor: pointer;
    font-size: 13px;
    color: #333;
    transition: all 0.2s ease;
}

.analysis-tab:hover {
    background-color: #e9ecef;
}

.analysis-tab.active {
    background-color: #fff;
    color: #1a3c89;
    font-weight: bold;
    border-bottom: 2px solid #1a3c89;
}

.analysis-tab-content {
    display: none;
    padding: 10px;
}

.analysis-tab-content.active {
    display: block;
}

.analysis-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    text-align: center;
    color: #777;
}

.analysis-placeholder i {
    font-size: 24px;
    margin-bottom: 8px;
    color: #ccc;
}

.analysis-placeholder p {
    font-size: 13px;
    margin: 0;
}

.analysis-results {
    padding: 8px 0;
}

.analysis-section {
    margin-bottom: 15px;
}

.analysis-section h5 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 4px;
}

.analysis-results-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.analysis-result-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border: 1px solid #eee;
    border-radius: 3px;
    background-color: #f9f9f9;
}

.analysis-result-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #1a3c89;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 8px;
    font-size: 14px;
}

.analysis-result-content {
    flex: 1;
}

.analysis-result-title {
    font-weight: bold;
    margin-bottom: 3px;
    font-size: 13px;
}

.analysis-result-description {
    font-size: 12px;
    color: #666;
}

.analysis-metric {
    display: inline-block;
    margin-right: 8px;
    font-size: 11px;
    color: #555;
}

.analysis-interpretation {
    background-color: #f8f9fa;
    padding: 8px;
    border-radius: 3px;
    font-size: 12px;
    color: #555;
}

.analysis-interpretation p {
    margin-top: 0;
    margin-bottom: 5px;
}

.analysis-interpretation ul {
    margin-bottom: 0;
    padding-left: 18px;
}

/* Fotogalerie */
.correlation-gallery-container {
    margin: 0;
    border-top: 1px solid #ddd;
    background-color: #fff;
    overflow: hidden;
    display: none; /* Skryjeme fotogalerii, protože není na snímku obrazovky */
}

.correlation-gallery-container h4 {
    margin: 0;
    padding: 8px 10px;
    background-color: #1a3c89;
    color: white;
    font-size: 14px;
    font-weight: normal;
}

.photo-gallery {
    padding: 0;
}

.photo-gallery-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 10px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
}

.photo-gallery-title {
    font-weight: bold;
    color: #555;
    font-size: 13px;
}

.photo-gallery-add {
    padding: 4px 8px;
    border-radius: 3px;
    border: 1px solid #ccc;
    background-color: #f8f9fa;
    color: #333;
    cursor: pointer;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    transition: all 0.2s ease;
}

.photo-gallery-add:hover {
    background-color: #e2e6ea;
    border-color: #adb5bd;
}

.photo-gallery-add i {
    margin-right: 4px;
}

.photo-gallery-content {
    padding: 10px;
    min-height: 100px;
}

.photo-gallery-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 15px;
    color: #777;
}

.photo-gallery-empty i {
    font-size: 24px;
    margin-bottom: 8px;
    color: #ccc;
}

.photo-gallery-empty p {
    font-size: 13px;
    margin: 0;
}

.photo-gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 10px;
}

.photo-item {
    border: 1px solid #ddd;
    border-radius: 3px;
    overflow: hidden;
    background-color: #fff;
    transition: all 0.2s ease;
}

.photo-item:hover {
    border-color: #adb5bd;
}

.photo-preview {
    height: 100px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
}

.photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-info {
    padding: 6px;
    border-top: 1px solid #eee;
}

.photo-name {
    font-size: 11px;
    font-weight: bold;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.photo-size {
    font-size: 10px;
    color: #777;
}

.photo-actions {
    display: flex;
    justify-content: flex-end;
    padding: 3px 6px;
    background-color: #f5f5f5;
    border-top: 1px solid #eee;
}

.photo-action-btn {
    background: none;
    border: none;
    color: #777;
    cursor: pointer;
    padding: 2px;
    font-size: 11px;
    transition: color 0.2s ease;
}

.photo-action-btn:hover {
    color: #e74c3c;
}

.photo-gallery.drag-over {
    background-color: rgba(26, 60, 137, 0.05);
    border: 1px dashed #1a3c89;
}

/* Dialogy */
.correlation-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.correlation-dialog-content {
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    width: 500px;
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
}

.correlation-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #1a3c89;
    color: white;
    border-bottom: 1px solid #15306d;
}

.correlation-dialog-header h4 {
    margin: 0;
    font-size: 16px;
}

.correlation-dialog-close {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
}

.correlation-dialog-body {
    padding: 15px;
}

.correlation-dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 10px 15px;
    background-color: #f5f5f5;
    border-top: 1px solid #ddd;
}

/* Styly pro import dat */
.import-options {
    margin-bottom: 15px;
}

.import-options h5 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #333;
}

.import-option {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.import-option input[type="radio"] {
    margin-right: 8px;
}

.import-option label {
    font-size: 14px;
    color: #333;
}

.import-source-options {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #eee;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.checkbox-item {
    display: flex;
    align-items: center;
    min-width: 150px;
}

.checkbox-item input[type="checkbox"] {
    margin-right: 5px;
}

.checkbox-item label {
    font-size: 14px;
    color: #333;
}

.export-options {
    margin-bottom: 15px;
}

.export-option {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.export-preview {
    margin-top: 15px;
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
    border: 1px solid #eee;
}

.export-preview h5 {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #333;
}

.export-preview-text {
    font-family: monospace;
    font-size: 12px;
    white-space: pre-wrap;
    overflow-x: auto;
    max-height: 200px;
    overflow-y: auto;
    padding: 10px;
    background-color: #fff;
    border: 1px solid #eee;
    border-radius: 3px;
}

/* Notifikace */
.correlation-notifications {
    margin: 0;
    border-top: 1px solid #ddd;
    background-color: #fff;
    overflow: hidden;
    display: none; /* Skryjeme notifikace, protože nejsou na snímku obrazovky */
}

.correlation-notifications h4 {
    margin: 0;
    padding: 8px 10px;
    background-color: #1a3c89;
    color: white;
    font-size: 14px;
    font-weight: normal;
}

.notification-settings {
    padding: 10px;
}

.form-group {
    margin-bottom: 10px;
}

.form-group label {
    display: block;
    font-weight: bold;
    margin-bottom: 3px;
    color: #555;
    font-size: 13px;
}

.form-control {
    width: 100%;
    padding: 4px 6px;
    border: 1px solid #ccc;
    border-radius: 3px;
    font-size: 13px;
}

.form-control:focus {
    border-color: #1a3c89;
    outline: none;
}

.notification-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 5px;
}

.notification-checkboxes label {
    display: flex;
    align-items: center;
    font-weight: normal;
    margin-bottom: 0;
    font-size: 13px;
}

.notification-checkboxes input[type="checkbox"] {
    margin-right: 4px;
}

.notification-buttons {
    display: flex;
    gap: 5px;
    margin-top: 10px;
}

.correlation-notification {
    position: fixed;
    bottom: 15px;
    right: 15px;
    padding: 10px;
    border-radius: 3px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 9999;
    display: flex;
    align-items: center;
    min-width: 250px;
    max-width: 350px;
    font-size: 13px;
}

.correlation-notification.success {
    background-color: #27ae60;
    color: white;
}

.correlation-notification.error {
    background-color: #e74c3c;
    color: white;
}

.correlation-notification.warning {
    background-color: #f39c12;
    color: white;
}

.correlation-notification.info {
    background-color: #3498db;
    color: white;
}

.notification-icon {
    margin-right: 8px;
}

.notification-content {
    flex: 1;
}

.notification-close {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    margin-left: 8px;
    font-size: 12px;
}

/* Responsivní úpravy */
@media (max-width: 768px) {
    .correlation-controls {
        flex-direction: column;
        gap: 10px;
    }

    .correlation-filter {
        min-width: 100%;
    }

    .correlation-details-panel {
        width: 100%;
    }
}

/* Styly pro entity a vztahy */
.entity-node {
    cursor: pointer;
    transition: all 0.2s ease;
}

.entity-node:hover {
    stroke-width: 2px;
    stroke: #3498db;
}

.relationship-edge {
    stroke-width: 1.5px;
    transition: all 0.2s ease;
}

.relationship-edge:hover {
    stroke-width: 3px;
}

/* Styly pro různé typy entit */
.entity-person, .legend-color.entity-person {
    fill: #3498db;
    background-color: #3498db;
}

.entity-organization, .legend-color.entity-organization {
    fill: #e74c3c;
    background-color: #e74c3c;
}

.entity-location, .legend-color.entity-location {
    fill: #2ecc71;
    background-color: #2ecc71;
}

.entity-event, .legend-color.entity-event {
    fill: #f39c12;
    background-color: #f39c12;
}

.entity-asset, .legend-color.entity-asset {
    fill: #9b59b6;
    background-color: #9b59b6;
}

.entity-digital, .legend-color.entity-digital {
    fill: #1abc9c;
    background-color: #1abc9c;
}

/* Styly pro různé typy vztahů */
.relationship-family, .legend-color.relationship-family {
    stroke: #3498db;
    background-color: #3498db;
}

.relationship-business, .legend-color.relationship-business {
    stroke: #e74c3c;
    background-color: #e74c3c;
}

.relationship-social, .legend-color.relationship-social {
    stroke: #2ecc71;
    background-color: #2ecc71;
}

.relationship-ownership, .legend-color.relationship-ownership {
    stroke: #f39c12;
    background-color: #f39c12;
}

.relationship-communication, .legend-color.relationship-communication {
    stroke: #9b59b6;
    background-color: #9b59b6;
}

.relationship-transaction, .legend-color.relationship-transaction {
    stroke: #1abc9c;
    background-color: #1abc9c;
}

/* Styly pro různé úrovně důvěryhodnosti */
.confidence-high {
    opacity: 1;
}

.confidence-medium {
    opacity: 0.7;
}

.confidence-low {
    opacity: 0.4;
}

/* Styly pro legendu */
.correlation-legend {
    position: absolute;
    bottom: 5px;
    left: 5px;
    background-color: rgba(255, 255, 255, 0.95);
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 5px;
    z-index: 5;
    max-width: 200px;
    font-size: 11px;
}

.legend-section {
    margin-bottom: 5px;
}

.legend-section h5 {
    margin: 0 0 3px 0;
    font-size: 11px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 2px;
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 2px;
}

.legend-color {
    width: 12px;
    height: 12px;
    margin-right: 4px;
    border-radius: 2px;
}

.legend-label {
    font-size: 11px;
    color: #333;
}

/* Chybové stavy */
.correlation-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 20px;
    text-align: center;
    color: #e74c3c;
}

.correlation-error i {
    font-size: 48px;
    margin-bottom: 15px;
}

.correlation-error p {
    margin-bottom: 10px;
    max-width: 400px;
}

/* Typy entit a vztahů v seznamu */
.typy-entit, .typy-vztahu {
    list-style: none;
    padding: 0;
    margin: 0;
    border: 1px solid #ddd;
    border-radius: 3px;
    max-height: 200px;
    overflow-y: auto;
    background-color: #fff;
    font-size: 13px;
}

.typy-entit li, .typy-vztahu li {
    padding: 3px 8px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.typy-entit li:last-child, .typy-vztahu li:last-child {
    border-bottom: none;
}

.typy-entit li:hover, .typy-vztahu li:hover {
    background-color: #f5f5f5;
}

.typy-entit li.selected, .typy-vztahu li.selected {
    background-color: #e3f2fd;
    font-weight: bold;
}

/* Barevné značky pro typy entit */
.typy-entit li::before, .typy-vztahu li::before {
    content: "";
    display: inline-block;
    width: 10px;
    height: 10px;
    margin-right: 5px;
    border-radius: 2px;
}

.typy-entit li[data-type="person"]::before {
    background-color: #3498db;
}

.typy-entit li[data-type="organization"]::before {
    background-color: #e74c3c;
}

.typy-entit li[data-type="location"]::before {
    background-color: #2ecc71;
}

.typy-entit li[data-type="event"]::before {
    background-color: #f39c12;
}

.typy-entit li[data-type="asset"]::before {
    background-color: #9b59b6;
}

.typy-entit li[data-type="digital"]::before {
    background-color: #1abc9c;
}

/* Barevné značky pro typy vztahů */
.typy-vztahu li[data-type="family"]::before {
    background-color: #3498db;
}

.typy-vztahu li[data-type="business"]::before {
    background-color: #e74c3c;
}

.typy-vztahu li[data-type="social"]::before {
    background-color: #2ecc71;
}

.typy-vztahu li[data-type="ownership"]::before {
    background-color: #f39c12;
}

.typy-vztahu li[data-type="communication"]::before {
    background-color: #9b59b6;
}

.typy-vztahu li[data-type="transaction"]::before {
    background-color: #1abc9c;
}
