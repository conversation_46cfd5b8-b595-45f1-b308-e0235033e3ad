"use client";

import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, UseFormReturn } from 'react-hook-form';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import type { VehiclesModuleData, VehicleRecord, PhysicalPersonSubject, LegalEntitySubject, PhotoMetadata, VehicleRelationshipType } from '@/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PlusCircle, Trash2, ImageUp, UserPlus, CarFront, StickyNote, Palette, CalendarClock, Hash, Edit3, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { db } from '@/lib/firebase';
import { doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { useState, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils';
import {
  Form,
  FormField,
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { PhotoDocumentationSection } from './PhotoDocumentationSection';
import { FormItemRHF, FormItemSelectRHF, type VehiclesFormValues, vehiclesModuleSchema } from './FormComponents';

interface VehiclesFormProps {
  caseId: string;
  subject: PhysicalPersonSubject | LegalEntitySubject;
  existingData: VehiclesModuleData | null;
  onSave: (moduleId: string, wasNew: boolean) => void;
}

const vehicleRelationshipOptions: { value: VehicleRelationshipType; label: string }[] = [
  { value: "owner", label: "Vlastněné" },
  { value: "user", label: "Užívané" },
  { value: "former", label: "Dříve vlastněné/užívané" },
  { value: "other", label: "Jiný vztah" },
];

export function VehiclesForm({ caseId, subject, existingData, onSave }: VehiclesFormProps) {
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const moduleId = "vehicles";

  const form = useForm<VehiclesFormValues>({
    resolver: zodResolver(vehiclesModuleSchema),
    defaultValues: {
      vehicles: existingData?.vehicles?.map(v => ({
        ...v,
        id: v.id || crypto.randomUUID(),
        relationshipType: v.relationshipType || undefined,
        yearManufactured: v.yearManufactured === 0 ? null : v.yearManufactured,
        photos: v.photos?.map(p => ({ ...p, id: p.id || crypto.randomUUID(), downloadURL: p.downloadURL || '', sourceURL: p.sourceURL || '' })) || [],
      })) || [],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "vehicles",
  });

  const addNewVehicle = () => {
    append({
      id: crypto.randomUUID(),
      relationshipType: undefined as unknown as VehicleRelationshipType,
      otherRelationshipDetail: "",
      make: "",
      model: "",
      licensePlate: "",
      vin: "",
      color: "",
      yearManufactured: null,
      stkValidUntil: "",
      firstRegistered: "",
      notes: "",
      photos: [],
    });
  };

  const onSubmitHandler: SubmitHandler<VehiclesFormValues> = async (data) => {
    setIsSaving(true);
    try {
      const moduleDocRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);

      // Funkce pro odstranění undefined hodnot z objektu (rekurzivně)
      const removeUndefinedValues = (obj: any): any => {
        if (obj === null || obj === undefined) {
          return null;
        }
        
        if (Array.isArray(obj)) {
          return obj.map(removeUndefinedValues).filter(item => item !== undefined);
        }
        
        if (typeof obj === 'object') {
          const cleaned: any = {};
          for (const [key, value] of Object.entries(obj)) {
            if (value !== undefined) {
              const cleanedValue = removeUndefinedValues(value);
              if (cleanedValue !== undefined) {
                cleaned[key] = cleanedValue;
              }
            }
          }
          return cleaned;
        }
        
        return obj;
      };

      // Odstranit undefined hodnoty před uložením
      const cleanedData = removeUndefinedValues(data);

      const dataToSave: VehiclesModuleData = {
        ...cleanedData,
        subjectId: subject.id,
        lastUpdatedAt: serverTimestamp(),
        createdAt: existingData?.createdAt || serverTimestamp(),
      };

      await setDoc(moduleDocRef, dataToSave, { merge: true });
      toast({ title: "Data modulu Vozidla uložena", description: "Informace byly úspěšně zaznamenány." });

      const wasNew = !existingData || !existingData.createdAt;
      onSave(moduleId, wasNew);

    } catch (error: any) {
      console.error("Error saving Vehicles data:", error);
      toast({ title: "Chyba ukládání", description: error.message, variant: "destructive" });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmitHandler)} className="space-y-8">
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-xl flex items-center">
              <CarFront className="mr-3 h-6 w-6 text-primary" />
              Vozidla subjektu: {subject.type === 'physical' ? `${subject.firstName} ${subject.lastName}` : subject.name}
            </CardTitle>
            <CardDescription>Zadejte informace o vozidlech vlastněných, užívaných nebo jinak spojených se subjektem.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {fields.length === 0 && (
                <p className="text-muted-foreground text-center py-4">Nebyly přidány žádné záznamy o vozidlech.</p>
            )}
            {fields.map((field, index) => {
              // Use getValues for conditional rendering instead of watch to avoid "update during render" errors
              const currentRelationshipType = form.getValues(`vehicles.${index}.relationshipType`);
              return (
                <Card key={field.id} className="p-4 shadow-inner bg-card-foreground/5 relative">
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => remove(index)}
                    className="absolute top-3 right-3 text-destructive hover:bg-destructive/10"
                    aria-label="Odebrat vozidlo"
                  >
                    <Trash2 className="h-5 w-5" />
                  </Button>
                  <CardHeader className="px-0 pt-0 pb-4">
                    <CardTitle className="text-lg">Vozidlo {index + 1}</CardTitle>
                  </CardHeader>
                  <CardContent className="px-0 pb-0 space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormItemSelectRHF label="Typ vztahu k vozidlu" name={`vehicles.${index}.relationshipType`} control={form.control} options={vehicleRelationshipOptions} placeholder="-- Vyberte vztah --"/>
                      {currentRelationshipType === 'other' && (
                        <FormItemRHF label="Upřesnění vztahu" name={`vehicles.${index}.otherRelationshipDetail`} control={form.control} placeholder="Popište vztah" />
                      )}
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItemRHF label="Značka" name={`vehicles.${index}.make`} control={form.control} placeholder="Např. Škoda" />
                        <FormItemRHF label="Model" name={`vehicles.${index}.model`} control={form.control} placeholder="Např. Octavia" />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItemRHF label="Registrační značka (SPZ/RZ)" name={`vehicles.${index}.licensePlate`} control={form.control} placeholder="Např. 1AB 2345" />
                        <FormItemRHF label="VIN" name={`vehicles.${index}.vin`} control={form.control} placeholder="Identifikační číslo vozidla" />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItemRHF label="Barva" name={`vehicles.${index}.color`} control={form.control} placeholder="Např. Modrá metalíza" />
                        <FormItemRHF label="Rok výroby" name={`vehicles.${index}.yearManufactured`} control={form.control} type="number" placeholder="Např. 2020" />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItemRHF label="Platnost STK do" name={`vehicles.${index}.stkValidUntil`} control={form.control} type="date" />
                        <FormItemRHF label="Datum první registrace" name={`vehicles.${index}.firstRegistered`} control={form.control} type="date" />
                    </div>
                    <FormItemRHF label="Poznámka k vozidlu" name={`vehicles.${index}.notes`} control={form.control} placeholder="Další informace, poškození, úpravy..." as="textarea" rows={3}/>

                    <PhotoDocumentationSection 
                      form={form} 
                      title="Fotografie vozidla"
                      description="Fotografie tohoto konkrétního vozidla"
                      namePrefix={`vehicles.${index}.photos`} 
                      photoHint="vehicle car automobile"
                      caseId={caseId}
                      subjectId={subject.id}
                      moduleId={moduleId}
                    />
                  </CardContent>
                </Card>
              );
            })}
            <Button type="button" variant="outline" onClick={addNewVehicle} className="w-full md:w-auto">
              <PlusCircle className="mr-2 h-4 w-4" /> Přidat vozidlo
            </Button>
          </CardContent>
        </Card>

        <div className="flex justify-end pt-8 mt-8 border-t border-border">
          <Button type="submit" disabled={isSaving} className="w-full md:w-auto text-lg py-3 px-6 shadow-md hover:shadow-lg transition-shadow">
            {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isSaving ? "Ukládání..." : "Uložit data modulu"}
          </Button>
        </div>
      </form>
    </Form>
  );
}
