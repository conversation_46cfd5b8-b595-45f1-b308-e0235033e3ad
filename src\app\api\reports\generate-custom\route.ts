import { NextRequest, NextResponse } from 'next/server';
import puppeteer from 'puppeteer';

interface CustomPDFSettings {
  // Základní nastavení
  title: string;
  subject: string;
  jid: string;
  documentNumber: string;
  location: string;
  date: string;
  department: string;
  purpose: string;
  
  // Styling
  style: 'official' | 'modern';
  fontSize: number;
  lineHeight: number;
  margins: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
  
  // Barvy
  primaryColor: string;
  secondaryColor: string;
  textColor: string;
  backgroundColor: string;
  
  // Rozložení
  headerHeight: number;
  footerHeight: number;
  showPageNumbers: boolean;
  pageNumberPosition: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  showBorders: boolean;
  borderWidth: number;
  borderColor: string;
  
  // Fotografie
  photoSize: 'small' | 'medium' | 'large';
  photoAlignment: 'left' | 'center' | 'right';
  photoSpacing: number;
}

export async function POST(request: NextRequest) {
  try {
    const { caseId, settings, reportType } = await request.json();
    
    console.log('Generuji vlastní PDF s nastavením:', settings);

    // Vygenerujeme HTML obsah s vlastním stylingem
    const htmlContent = generateCustomHTML(settings, caseId);
    
    // Vytvoříme PDF pomocí Puppeteer
    const pdfBuffer = await generateCustomPDF(htmlContent, settings);
    
    // Nastavíme správné hlavičky pro PDF
    const headers = new Headers();
    headers.set('Content-Type', 'application/pdf');
    headers.set('Content-Disposition', `attachment; filename="custom-osint-report-${caseId}-${Date.now()}.pdf"`);
    
    return new NextResponse(pdfBuffer, { headers });
    
  } catch (error) {
    console.error('Chyba při generování vlastního PDF:', error);
    return NextResponse.json(
      { error: 'Chyba při generování PDF', details: error instanceof Error ? error.message : 'Neznámá chyba' },
      { status: 500 }
    );
  }
}

function generateCustomHTML(settings: CustomPDFSettings, caseId: string): string {
  const photoSizes = {
    small: '200px',
    medium: '300px',
    large: '400px'
  };

  const pageNumberCSS = generatePageNumberCSS(settings);
  
  return `<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${settings.title}</title>
    <style>
        @page {
            size: A4;
            margin: ${settings.margins.top}mm ${settings.margins.right}mm ${settings.margins.bottom}mm ${settings.margins.left}mm;
            ${pageNumberCSS}
        }
        
        @page:first {
            ${settings.showPageNumbers ? '@top-left { content: ""; } @top-right { content: ""; } @bottom-left { content: ""; } @bottom-right { content: ""; }' : ''}
        }

        body {
            font-family: ${settings.style === 'modern' ? "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif" : "'Times New Roman', serif"};
            font-size: ${settings.fontSize}pt;
            line-height: ${settings.lineHeight};
            color: ${settings.textColor};
            background-color: ${settings.backgroundColor};
            margin: 0;
            padding: 0;
        }

        .document-container {
            max-width: 210mm;
            margin: 0 auto;
            background: ${settings.backgroundColor};
        }

        .header {
            ${settings.style === 'modern' ? `
                background: linear-gradient(135deg, ${settings.primaryColor} 0%, ${settings.secondaryColor} 100%);
                color: white;
                padding: 25px;
                margin-bottom: 30px;
                border-radius: 8px;
            ` : `
                text-align: center;
                margin-bottom: 30px;
                padding: 20px 0;
                ${settings.showBorders ? `border-bottom: ${settings.borderWidth}px solid ${settings.borderColor};` : ''}
            `}
        }

        .header h1 {
            margin: 0 0 10px 0;
            font-size: ${settings.fontSize + 8}pt;
            font-weight: ${settings.style === 'modern' ? '300' : 'bold'};
            color: ${settings.style === 'modern' ? 'white' : settings.primaryColor};
        }

        .department-info {
            text-align: left;
            margin-bottom: 20px;
            font-size: ${settings.fontSize - 1}pt;
            color: ${settings.textColor};
        }

        .document-info {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            font-size: ${settings.fontSize - 1}pt;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            font-size: ${settings.fontSize - 1}pt;
            ${settings.showBorders ? `border: 1px solid ${settings.borderColor};` : ''}
        }

        .info-table td {
            padding: 8px 12px;
            ${settings.showBorders ? `border: 1px solid ${settings.borderColor};` : 'border-bottom: 1px solid #eee;'}
            vertical-align: top;
        }

        .info-table td:first-child {
            font-weight: bold;
            background-color: ${settings.style === 'modern' ? '#f8f9fa' : '#f5f5f5'};
            width: 35%;
        }

        .module {
            margin: 25px 0;
            ${settings.showBorders ? `border: ${settings.borderWidth}px solid ${settings.borderColor}; border-radius: 8px;` : ''}
            page-break-inside: avoid;
        }

        .module h3 {
            ${settings.style === 'modern' ? `
                background: linear-gradient(90deg, ${settings.primaryColor} 0%, ${settings.secondaryColor} 100%);
                color: white;
                padding: 15px 20px;
                margin: 0 0 20px 0;
                border-radius: 8px 8px 0 0;
            ` : `
                color: ${settings.primaryColor};
                padding: 10px 0;
                margin: 0 0 15px 0;
                ${settings.showBorders ? `border-bottom: ${settings.borderWidth}px solid ${settings.borderColor};` : ''}
            `}
            font-size: ${settings.fontSize + 2}pt;
            font-weight: bold;
        }

        .module-content {
            padding: ${settings.style === 'modern' ? '20px' : '15px'};
        }

        .photo-section {
            margin: ${settings.photoSpacing}px 0;
            text-align: ${settings.photoAlignment};
        }

        .photo-container {
            display: inline-block;
            margin: ${settings.photoSpacing}px;
            ${settings.showBorders ? `border: 1px solid ${settings.borderColor}; border-radius: 8px; padding: 10px;` : ''}
            text-align: center;
        }

        .photo-container img {
            max-width: ${photoSizes[settings.photoSize]};
            max-height: ${photoSizes[settings.photoSize]};
            object-fit: contain;
            ${settings.showBorders ? `border: 1px solid ${settings.borderColor}; border-radius: 4px;` : ''}
        }

        .photo-description {
            margin-top: 8px;
            font-size: ${settings.fontSize - 1}pt;
            color: ${settings.textColor};
            font-style: italic;
        }

        .signature-block {
            margin-top: 40px;
            text-align: right;
            font-size: ${settings.fontSize}pt;
        }

        .page-break {
            page-break-before: always;
        }

        h4 {
            color: ${settings.primaryColor};
            font-size: ${settings.fontSize + 1}pt;
            margin: 20px 0 10px 0;
            ${settings.showBorders ? `border-bottom: 1px solid ${settings.borderColor}; padding-bottom: 5px;` : ''}
        }
    </style>
</head>
<body>
    <div class="document-container">
        <div class="header">
            <h1>${settings.title}</h1>
            <div class="subtitle">${settings.subject || 'Analýza subjektu'}</div>
        </div>

        <div class="department-info">
            <div style="font-weight: bold; margin-bottom: 5px;">POLICIE ČESKÉ REPUBLIKY</div>
            <div style="font-weight: bold; margin-bottom: 10px;">Krajské ředitelství policie Pardubického kraje</div>
            <div style="white-space: pre-line;">${settings.department}</div>
        </div>

        <div class="document-info">
            <div><strong>Č. j. ${settings.documentNumber || ''}</strong></div>
            <div><strong>JID: ${settings.jid || ''}</strong></div>
        </div>

        <div class="document-info">
            <div>${settings.location} ${settings.date}</div>
            <div>Počet stran: <span id="page-count">__</span></div>
        </div>

        <div style="margin: 30px 0;">
            <h4>Účel analýzy</h4>
            <p>${settings.purpose}</p>
        </div>

        <!-- Zde by byly moduly s daty -->
        <div class="module">
            <h3>Ukázkový modul</h3>
            <div class="module-content">
                <p>Toto je ukázka vlastního PDF reportu s vašimi nastaveními.</p>
                <table class="info-table">
                    <tr><td>Styl</td><td>${settings.style === 'modern' ? 'Moderní' : 'Oficiální'}</td></tr>
                    <tr><td>Velikost písma</td><td>${settings.fontSize}pt</td></tr>
                    <tr><td>Primární barva</td><td>${settings.primaryColor}</td></tr>
                    <tr><td>Velikost fotografií</td><td>${settings.photoSize}</td></tr>
                </table>
                
                <div class="photo-section">
                    <div class="photo-container">
                        <div style="width: ${photoSizes[settings.photoSize]}; height: 150px; background: linear-gradient(45deg, #f0f0f0, #e0e0e0); display: flex; align-items: center; justify-content: center; border-radius: 4px;">
                            <span style="color: #999; font-size: 14px;">Ukázková fotografie</span>
                        </div>
                        <div class="photo-description">Ukázka zobrazení fotografie</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="signature-block">
            <div style="margin-bottom: 5px;"><strong>Zpracoval:</strong></div>
            <div>Vlastní PDF Editor</div>
        </div>
    </div>
    
    <script>
        // Automatické vyplnění počtu stran
        window.addEventListener('load', function() {
            setTimeout(function() {
                const pageCountElements = document.querySelectorAll('#page-count');
                const contentHeight = document.body.scrollHeight;
                const pageHeight = 842;
                const totalPages = Math.max(1, Math.ceil(contentHeight / pageHeight));
                
                pageCountElements.forEach(element => {
                    element.textContent = totalPages.toString();
                });
            }, 200);
        });
    </script>
</body>
</html>`;
}

function generatePageNumberCSS(settings: CustomPDFSettings): string {
  if (!settings.showPageNumbers) return '';
  
  const position = settings.pageNumberPosition;
  const content = 'counter(page) ". strana"';
  
  switch (position) {
    case 'top-left':
      return `@top-left { content: ${content}; font-size: 12pt; font-weight: bold; }`;
    case 'top-right':
      return `@top-right { content: ${content}; font-size: 12pt; font-weight: bold; }`;
    case 'bottom-left':
      return `@bottom-left { content: ${content}; font-size: 12pt; font-weight: bold; }`;
    case 'bottom-right':
      return `@bottom-right { content: ${content}; font-size: 12pt; font-weight: bold; }`;
    default:
      return `@top-left { content: ${content}; font-size: 12pt; font-weight: bold; }`;
  }
}

async function generateCustomPDF(htmlContent: string, settings: CustomPDFSettings): Promise<Buffer> {
  console.log('Spouštím Puppeteer pro vlastní PDF...');
  
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    await page.setContent(htmlContent, {
      waitUntil: 'networkidle0'
    });
    
    const pdfBuffer = await page.pdf({
      format: 'A4',
      printBackground: true,
      margin: {
        top: `${settings.margins.top}mm`,
        bottom: `${settings.margins.bottom}mm`,
        left: `${settings.margins.left}mm`,
        right: `${settings.margins.right}mm`
      }
    });
    
    console.log('Vlastní PDF úspěšně vygenerováno, velikost:', pdfBuffer.length, 'bytů');
    
    return pdfBuffer;
  } finally {
    await browser.close();
  }
}
