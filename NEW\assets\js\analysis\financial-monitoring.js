/**
 * Finanční monitoring - funkce pro práci s finančn<PERSON>mi daty a jejich analýzu
 */

// API klíče pro různé služby
const FINANCIAL_API_KEYS = {
    ares: '', // ARES API - není potřeba pro základní použití
    justice: '', // Justice.cz API - není veřejně dostupné
    cuzk: '' // ČÚZK API - vyžaduje registraci
};

/**
 * Inicializace funkcí pro finanční monitoring
 */
function initFinancialMonitoring() {
    console.log('Inicializace modulu finančního monitoringu');

    // Najít aktuální modul finančního monitoringu
    const financialModule = document.querySelector('.module[id^="module-financni-monitoring"]');
    if (!financialModule) {
        console.error('Modul finančního monitoringu nebyl nalezen');
        return;
    }

    console.log('Nalezen modul finančního monitoringu:', financialModule.id);

    // Přidání event listenerů pro tlačítka v modulu finančního monitoringu
    financialModule.addEventListener('click', function(event) {
        // Vyhledání firmy
        if (event.target.closest('.search-company')) {
            searchCompany();
        }

        // Vyhledání osoby
        if (event.target.closest('.search-person')) {
            searchPerson();
        }

        // Získání finančních dat
        if (event.target.closest('.get-financial-data')) {
            getFinancialData();
        }

        // Kontrola rizik
        if (event.target.closest('.check-risks')) {
            checkRisks();
        }

        // Vyhledání majetku
        if (event.target.closest('.search-property')) {
            searchProperty();
        }

        // Vyhledání datové schránky
        if (event.target.closest('.search-databox')) {
            searchDatabox();
        }

        // Vyhledání veřejných zakázek
        if (event.target.closest('.search-public-contracts')) {
            searchPublicContracts();
        }

        // Přidání subjektu do monitoringu
        if (event.target.closest('.add-to-monitoring')) {
            addToMonitoring();
        }
    });

    // Přidání event listenerů pro formuláře
    const companySearchForm = financialModule.querySelector('#company-search-form');
    if (companySearchForm) {
        companySearchForm.addEventListener('submit', function(event) {
            event.preventDefault();
            searchCompany();
        });
    }

    // Inicializace grafu pro vizualizaci vztahů
    initRelationshipGraph(financialModule);

    // Inicializace fotogalerie
    initFinancialPhotoGallery(financialModule);
}

/**
 * Inicializace grafu pro vizualizaci vztahů
 * @param {HTMLElement} moduleElement - Element modulu finančního monitoringu
 */
function initRelationshipGraph(moduleElement) {
    const graphContainer = moduleElement.querySelector('.relationship-graph-container');
    if (!graphContainer) return;

    // Zde by byla inicializace grafu pomocí knihovny jako je D3.js nebo Vis.js
    // Pro účely tohoto příkladu pouze zobrazíme placeholder
    graphContainer.innerHTML = `
        <div class="graph-placeholder">
            <i class="fas fa-project-diagram"></i>
            <p>Graf vztahů se zobrazí po vyhledání subjektu</p>
        </div>
    `;
}

/**
 * Vyhledání firmy podle IČO nebo názvu
 */
function searchCompany() {
    console.log('Vyhledání firmy');

    // Najít aktuální modul finančního monitoringu
    const financialModule = document.querySelector('.module[id^="module-financni-monitoring"]');
    if (!financialModule) {
        console.error('Modul finančního monitoringu nebyl nalezen');
        return;
    }

    // Získání hodnoty z formuláře
    const companyIdInput = financialModule.querySelector('#company-id');
    const companyNameInput = financialModule.querySelector('#company-name');

    let companyId = '';
    let companyName = '';

    if (companyIdInput && companyIdInput.value) {
        companyId = companyIdInput.value.trim();
    }

    if (companyNameInput && companyNameInput.value) {
        companyName = companyNameInput.value.trim();
    }

    if (!companyId && !companyName) {
        alert('Zadejte IČO nebo název firmy.');
        return;
    }

    // Zobrazení načítání
    const resultsContainer = financialModule.querySelector('#company-results');
    if (resultsContainer) {
        resultsContainer.innerHTML = `
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Vyhledávání firmy...</span>
            </div>
        `;
        resultsContainer.style.display = 'block';
    }

    // Vyhledání firmy v ARES
    if (companyId) {
        // Vyhledání podle IČO
        fetchCompanyByID(companyId)
            .then(data => {
                displayCompanyResults(data);
            })
            .catch(error => {
                console.error('Chyba při vyhledávání firmy:', error);
                if (resultsContainer) {
                    resultsContainer.innerHTML = `
                        <div class="error-message">
                            <i class="fas fa-exclamation-circle"></i>
                            <span>Nepodařilo se vyhledat firmu. Zkuste to prosím znovu.</span>
                        </div>
                    `;
                }
            });
    } else {
        // Vyhledání podle názvu
        fetchCompanyByName(companyName)
            .then(data => {
                displayCompanyResults(data);
            })
            .catch(error => {
                console.error('Chyba při vyhledávání firmy:', error);
                if (resultsContainer) {
                    resultsContainer.innerHTML = `
                        <div class="error-message">
                            <i class="fas fa-exclamation-circle"></i>
                            <span>Nepodařilo se vyhledat firmu. Zkuste to prosím znovu.</span>
                        </div>
                    `;
                }
            });
    }
}

/**
 * Vyhledání firmy podle IČO v ARES
 * @param {string} companyId - IČO firmy
 * @returns {Promise<Object>} - Data o firmě
 */
function fetchCompanyByID(companyId) {
    console.log('Vyhledání firmy podle IČO:', companyId);

    // ARES API URL pro vyhledání podle IČO
    const url = `https://ares.gov.cz/ekonomicke-subjekty-v-be/rest/ekonomicke-subjekty/${companyId}`;

    return fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se získat data z ARES.');
            }
            return response.json();
        })
        .then(data => {
            // Zpracování dat z ARES
            return processAresData(data);
        });
}

/**
 * Vyhledání firmy podle názvu v ARES
 * @param {string} companyName - Název firmy
 * @returns {Promise<Array>} - Seznam nalezených firem
 */
function fetchCompanyByName(companyName) {
    console.log('Vyhledání firmy podle názvu:', companyName);

    // ARES API URL pro vyhledání podle názvu
    const url = `https://ares.gov.cz/ekonomicke-subjekty-v-be/rest/ekonomicke-subjekty/vyhledat?obchodniJmeno=${encodeURIComponent(companyName)}`;

    return fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se získat data z ARES.');
            }
            return response.json();
        })
        .then(data => {
            // Zpracování dat z ARES
            if (data.ekonomickeSubjekty && data.ekonomickeSubjekty.length > 0) {
                // Vrátíme seznam nalezených firem
                return {
                    type: 'list',
                    companies: data.ekonomickeSubjekty.map(company => ({
                        ico: company.ico,
                        name: company.obchodniJmeno,
                        address: company.sidlo ? `${company.sidlo.textovaAdresa}` : 'Adresa není k dispozici'
                    }))
                };
            } else {
                throw new Error('Nebyly nalezeny žádné firmy.');
            }
        });
}

/**
 * Zpracování dat z ARES
 * @param {Object} data - Data z ARES
 * @returns {Object} - Zpracovaná data
 */
function processAresData(data) {
    console.log('Zpracování dat z ARES:', data);

    // Kontrola, zda data obsahují potřebné informace
    if (!data) {
        throw new Error('Data z ARES neobsahují potřebné informace.');
    }

    // Vytvoření strukturovaných dat o firmě
    const companyData = {
        type: 'detail',
        ico: data.ico || '',
        name: data.obchodniJmeno || '',
        legalForm: data.pravniForma ? data.pravniForma.nazev : '',
        address: data.sidlo ? `${data.sidlo.textovaAdresa}` : 'Adresa není k dispozici',
        dateEstablished: data.datumVzniku || null,
        dateTerminated: data.datumZaniku || null,
        status: data.stavSubjektu || '',
        statutoryBodies: []
    };

    // Logování pro debugging
    console.log('Datum vzniku:', data.datumVzniku, 'Typ:', typeof data.datumVzniku);
    console.log('Datum zániku:', data.datumZaniku, 'Typ:', typeof data.datumZaniku);

    // Zpracování statutárních orgánů
    if (data.statutarniOrgany && data.statutarniOrgany.length > 0) {
        companyData.statutoryBodies = data.statutarniOrgany.map(person => {
            // Logování pro debugging
            if (person.datumOd) {
                console.log('Datum od (statutární orgán):', person.datumOd, 'Typ:', typeof person.datumOd);
            }

            return {
                name: person.jmeno ? `${person.jmeno} ${person.prijmeni}` : person.nazev || '',
                role: person.funkce || '',
                dateFrom: person.datumOd || null
            };
        });
    }

    return companyData;
}

/**
 * Zobrazení výsledků vyhledávání firmy
 * @param {Object} data - Data o firmě nebo seznam firem
 */
function displayCompanyResults(data) {
    console.log('Zobrazení výsledků vyhledávání firmy:', data);

    // Najít aktuální modul finančního monitoringu
    const financialModule = document.querySelector('.module[id^="module-financni-monitoring"]');
    if (!financialModule) {
        console.error('Modul finančního monitoringu nebyl nalezen');
        return;
    }

    const resultsContainer = financialModule.querySelector('#company-results');
    if (!resultsContainer) return;

    // Zobrazení výsledků podle typu dat
    if (data.type === 'list') {
        // Zobrazení seznamu nalezených firem
        let html = `
            <div class="results-header">
                <h4>Nalezené firmy</h4>
            </div>
            <div class="company-list">
        `;

        data.companies.forEach(company => {
            html += `
                <div class="company-list-item" data-ico="${company.ico}">
                    <div class="company-list-name">${company.name}</div>
                    <div class="company-list-ico">IČO: ${company.ico}</div>
                    <div class="company-list-address">${company.address}</div>
                    <div class="company-list-actions">
                        <button type="button" class="btn-inline company-detail" data-ico="${company.ico}">
                            <i class="fas fa-info-circle"></i> Detail
                        </button>
                    </div>
                </div>
            `;
        });

        html += `</div>`;
        resultsContainer.innerHTML = html;

        // Přidání event listenerů pro tlačítka detailu
        resultsContainer.querySelectorAll('.company-detail').forEach(button => {
            button.addEventListener('click', function() {
                const ico = this.getAttribute('data-ico');
                if (ico) {
                    // Vyhledání detailu firmy
                    fetchCompanyByID(ico)
                        .then(data => {
                            displayCompanyResults(data);
                        })
                        .catch(error => {
                            console.error('Chyba při vyhledávání detailu firmy:', error);
                        });
                }
            });
        });
    } else if (data.type === 'detail') {
        // Zobrazení detailu firmy
        let html = `
            <div class="results-header">
                <h4>Detail firmy</h4>
                <div class="results-actions">
                    <button type="button" class="btn-inline get-financial-data" data-ico="${data.ico}">
                        <i class="fas fa-chart-line"></i> Finanční data
                    </button>
                    <button type="button" class="btn-inline check-risks" data-ico="${data.ico}">
                        <i class="fas fa-exclamation-triangle"></i> Kontrola rizik
                    </button>
                    <button type="button" class="btn-inline search-property" data-ico="${data.ico}">
                        <i class="fas fa-building"></i> Majetek
                    </button>
                    <button type="button" class="btn-inline search-databox" data-ico="${data.ico}">
                        <i class="fas fa-envelope"></i> Datová schránka
                    </button>
                    <button type="button" class="btn-inline search-public-contracts" data-ico="${data.ico}">
                        <i class="fas fa-file-contract"></i> Veřejné zakázky
                    </button>
                    <button type="button" class="btn-inline add-to-monitoring" data-ico="${data.ico}">
                        <i class="fas fa-eye"></i> Přidat do monitoringu
                    </button>
                </div>
            </div>
            <div class="company-detail">
                <div class="company-detail-header">
                    <div class="company-detail-name">${data.name}</div>
                    <div class="company-detail-ico">IČO: ${data.ico}</div>
                </div>
                <div class="company-detail-info">
                    <div class="company-detail-section">
                        <h5>Základní informace</h5>
                        <table class="company-detail-table">
                            <tr>
                                <td>Právní forma:</td>
                                <td>${data.legalForm || 'Není k dispozici'}</td>
                            </tr>
                            <tr>
                                <td>Adresa:</td>
                                <td>${data.address || 'Není k dispozici'}</td>
                            </tr>
                            <tr>
                                <td>Datum vzniku:</td>
                                <td>${data.dateEstablished ? formatFinancialDate(data.dateEstablished) : 'Není k dispozici'}</td>
                            </tr>
                            <tr>
                                <td>Datum zániku:</td>
                                <td>${data.dateTerminated ? formatFinancialDate(data.dateTerminated) : 'Aktivní'}</td>
                            </tr>
                            <tr>
                                <td>Stav:</td>
                                <td>${data.status || 'Není k dispozici'}</td>
                            </tr>
                        </table>
                    </div>
        `;

        // Přidání statutárních orgánů, pokud existují
        if (data.statutoryBodies && data.statutoryBodies.length > 0) {
            html += `
                <div class="company-detail-section">
                    <h5>Statutární orgány</h5>
                    <table class="company-detail-table">
                        <tr>
                            <th>Jméno</th>
                            <th>Funkce</th>
                            <th>Od</th>
                        </tr>
            `;

            data.statutoryBodies.forEach(person => {
                html += `
                    <tr>
                        <td>${person.name || 'Není k dispozici'}</td>
                        <td>${person.role || 'Není k dispozici'}</td>
                        <td>${person.dateFrom ? formatFinancialDate(person.dateFrom) : 'Není k dispozici'}</td>
                    </tr>
                `;
            });

            html += `
                    </table>
                </div>
            `;
        }

        // Přidání odkazu na obchodní rejstřík
        html += `
                <div class="company-detail-section">
                    <h5>Externí odkazy</h5>
                    <div class="external-links">
                        <a href="https://or.justice.cz/ias/ui/rejstrik-firma.vysledky?subjektId=&ico=${data.ico}&obchodniJmeno=&typHledani=STARTS_WITH&lokalita=&charakterPravniFormy=&sidlo=&okres=&zdrojZapisuRejstrik=&kategorieOrganizace=&pouze1Subjekt=false&zapisZaRejstrikovehoSoudu=&idOsobyPuvodce=" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> Obchodní rejstřík
                        </a>
                        <a href="https://apl.czso.cz/res/detail?ico=${data.ico}" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> RES ČSÚ
                        </a>
                        <a href="https://www.mfcr.cz/cs/o-ministerstvu/informacni-systemy/ares/ekonomicke-subjekty/vyhledavani-ekonomickeho-subjektu-podle-ico/${data.ico}" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> ARES
                        </a>
                        <a href="https://esm.justice.cz/ias/issm/rejstrik-$firma?ico=${data.ico}" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> Evidence skutečných majitelů
                        </a>
                    </div>
                </div>
            </div>
        `;

        // Přidání grafu vztahů
        html += `
            <div class="company-detail-section">
                <h5>Graf vztahů</h5>
                <div class="relationship-graph-container">
                    <div class="graph-placeholder">
                        <i class="fas fa-project-diagram"></i>
                        <p>Pro zobrazení grafu vztahů klikněte na tlačítko "Finanční data"</p>
                    </div>
                </div>
            </div>
        `;

        resultsContainer.innerHTML = html;
    }
}

/**
 * Inicializace fotogalerie pro finanční monitoring
 * @param {HTMLElement} moduleElement - Element modulu finančního monitoringu
 */
function initFinancialPhotoGallery(moduleElement) {
    console.log('Inicializace fotogalerie pro finanční monitoring');

    if (!moduleElement) {
        console.error('Modul finančního monitoringu nebyl předán do initFinancialPhotoGallery');
        return;
    }

    // Najít galerii v modulu
    const gallery = moduleElement.querySelector('.financial-photo-gallery');
    if (!gallery) {
        console.error('Fotogalerie nebyla nalezena v modulu finančního monitoringu');
        return;
    }

    // Najít input pro nahrávání fotografií
    const uploadInput = moduleElement.querySelector('#financial-photo-upload');
    if (!uploadInput) {
        console.error('Input pro nahrávání fotografií nebyl nalezen');
        return;
    }

    // Přidání event listeneru pro tlačítko přidání fotografie
    const addButton = gallery.querySelector('.photo-gallery-add');
    if (addButton) {
        console.log('Nalezeno tlačítko pro přidání fotografie');

        // Odstranění existujících event listenerů
        const newAddButton = addButton.cloneNode(true);
        addButton.parentNode.replaceChild(newAddButton, addButton);

        // Přidání nového event listeneru
        newAddButton.addEventListener('click', function() {
            uploadInput.click();
        });
    }

    // Přidání event listenerů pro tlačítka nahrávání
    const uploadButtons = moduleElement.querySelectorAll('.upload-photo[data-gallery="financial-photo-gallery"]');
    uploadButtons.forEach(button => {
        button.addEventListener('click', function() {
            uploadInput.click();
        });
    });

    // Přidání event listeneru pro změnu input[type=file]
    uploadInput.addEventListener('change', function(event) {
        handleFinancialPhotoUpload(event, gallery);
    });

    // Přidání event listeneru pro tlačítko vložení ze schránky
    const pasteButtons = moduleElement.querySelectorAll('.paste-photo[data-gallery="financial-photo-gallery"]');
    pasteButtons.forEach(button => {
        button.addEventListener('click', function() {
            alert('Pro vložení obrázku ze schránky stiskněte Ctrl+V kdekoli v modulu finančního monitoringu (mimo textová pole).');
        });
    });

    // Přidání event listeneru pro tlačítko přidání z URL
    const urlButtons = moduleElement.querySelectorAll('.add-photo-url[data-gallery="financial-photo-gallery"]');
    urlButtons.forEach(button => {
        button.addEventListener('click', function() {
            addFinancialPhotoFromUrl(gallery);
        });
    });

    // Přidání event listeneru pro vložení ze schránky (Ctrl+V)
    moduleElement.addEventListener('paste', function(event) {
        const activeElement = document.activeElement;

        // Kontrola, zda kurzor není v textovém poli
        if (!activeElement.tagName.match(/input|textarea/i)) {
            // Zabránit výchozímu chování
            event.preventDefault();
            event.stopPropagation();

            handleFinancialPhotoPaste(event, gallery);
        }
    });
}

/**
 * Zpracování nahrání fotografie z počítače
 * @param {Event} event - Událost změny input[type=file]
 * @param {HTMLElement} gallery - Element galerie
 */
function handleFinancialPhotoUpload(event, gallery) {
    console.log('Zpracování nahrání fotografie z počítače pro finanční monitoring');

    // Resetování input[type=file] pro možnost nahrání stejného souboru znovu
    const input = event.target;

    const file = input.files[0];
    if (!file) {
        console.error('Nebyl vybrán žádný soubor');
        return;
    }

    console.log('Vybraný soubor:', file.name, file.type, file.size);

    // Kontrola, zda jde o obrázek
    if (!file.type.match('image.*')) {
        alert('Vybraný soubor není obrázek.');
        input.value = ''; // Reset input
        return;
    }

    // Vytvoření URL pro náhled
    const imageUrl = URL.createObjectURL(file);

    // Přidání fotografie do galerie
    addFinancialPhotoToGallery(gallery, file.name, imageUrl);

    // Reset input pro možnost nahrání stejného souboru znovu
    input.value = '';
}

/**
 * Zpracování vložení obrázku ze schránky
 * @param {Event} event - Událost vložení
 * @param {HTMLElement} gallery - Element galerie
 */
function handleFinancialPhotoPaste(event, gallery) {
    console.log('Zpracování vložení obrázku ze schránky pro finanční monitoring');

    // Kontrola, zda schránka obsahuje obrázek
    const clipboardData = event.clipboardData || window.clipboardData;
    if (!clipboardData) {
        console.error('Clipboard data nejsou k dispozici');
        return;
    }

    const items = clipboardData.items;
    if (!items) {
        console.error('Clipboard items nejsou k dispozici');
        return;
    }

    let blob = null;

    // Procházení položek ve schránce
    for (let i = 0; i < items.length; i++) {
        console.log('Clipboard item:', items[i].type);
        if (items[i].type.indexOf('image') === 0) {
            blob = items[i].getAsFile();
            console.log('Nalezen obrázek ve schránce:', blob);
            break;
        }
    }

    if (!blob) {
        console.error('Schránka neobsahuje obrázek');
        alert('Schránka neobsahuje obrázek. Zkopírujte obrázek do schránky a zkuste to znovu.');
        return;
    }

    // Vytvoření URL pro náhled
    const imageUrl = URL.createObjectURL(blob);
    console.log('Vytvořeno URL pro náhled:', imageUrl);

    // Přidání fotografie do galerie
    addFinancialPhotoToGallery(gallery, 'Vložený obrázek', imageUrl);
}

/**
 * Přidání fotografie z URL
 * @param {HTMLElement} gallery - Element galerie
 */
function addFinancialPhotoFromUrl(gallery) {
    console.log('Přidání fotografie z URL pro finanční monitoring');

    // Zobrazení dialogu pro zadání URL
    const url = prompt('Zadejte URL obrázku:');
    if (!url) return;

    // Kontrola, zda URL je platná
    if (!url.match(/^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i)) {
        alert('Zadejte platnou URL obrázku (končící na .jpg, .png, .gif nebo .webp).');
        return;
    }

    console.log('Přidání obrázku z URL:', url);

    // Přidání fotografie do galerie
    addFinancialPhotoToGallery(gallery, 'Obrázek z URL', url);
}

/**
 * Přidání fotografie do galerie
 * @param {HTMLElement} gallery - Element galerie
 * @param {string} title - Název fotografie
 * @param {string} imageUrl - URL obrázku
 */
function addFinancialPhotoToGallery(gallery, title, imageUrl) {
    console.log('Přidání fotografie do galerie finančního monitoringu:', title, imageUrl);

    if (!gallery) {
        console.error('Galerie nebyla předána do addFinancialPhotoToGallery');
        return;
    }

    // Vytvoření nové fotografie
    const photoId = `financial-photo-${Date.now()}`;
    const photoHTML = `
        <div class="photo-item" id="${photoId}" data-image-url="${imageUrl}">
            <div class="photo-preview">
                <img src="${imageUrl}" alt="${title}" onerror="this.src='https://via.placeholder.com/150x150?text=Chyba+načítání'">
            </div>
            <div class="photo-info">
                <div class="photo-title">${title}</div>
                <div class="photo-actions">
                    <button type="button" class="photo-action-btn view-photo" data-photo-id="${photoId}" title="Zobrazit fotografii">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" class="photo-action-btn delete-photo" data-photo-id="${photoId}" title="Odstranit fotografii">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;

    // Přidání fotografie do galerie
    const addButton = gallery.querySelector('.photo-gallery-add');
    if (addButton) {
        gallery.insertBefore(document.createRange().createContextualFragment(photoHTML), addButton);
    } else {
        gallery.insertAdjacentHTML('beforeend', photoHTML);
    }

    // Přidání event listenerů pro tlačítka
    const photoElement = document.getElementById(photoId);
    if (!photoElement) {
        console.error('Nově přidaná fotografie nebyla nalezena v DOM');
        return;
    }

    const viewButton = photoElement.querySelector('.view-photo');
    if (viewButton) {
        viewButton.addEventListener('click', function() {
            const photoId = this.getAttribute('data-photo-id');
            const photoElement = document.getElementById(photoId);
            if (photoElement) {
                const imageUrl = photoElement.getAttribute('data-image-url');
                const photoTitle = photoElement.querySelector('.photo-title').textContent;
                showFinancialPhotoViewer(imageUrl, photoTitle);
            }
        });
    }

    const deleteButton = photoElement.querySelector('.delete-photo');
    if (deleteButton) {
        deleteButton.addEventListener('click', function() {
            const photoId = this.getAttribute('data-photo-id');
            const photoElement = document.getElementById(photoId);
            if (photoElement && confirm('Opravdu chcete odstranit tuto fotografii?')) {
                photoElement.remove();
            }
        });
    }
}

/**
 * Zobrazení prohlížeče fotografií
 * @param {string} imageUrl - URL obrázku
 * @param {string} title - Název fotografie
 */
function showFinancialPhotoViewer(imageUrl, title) {
    console.log('Zobrazení prohlížeče fotografií pro finanční monitoring:', title, imageUrl);

    // Vytvoření prohlížeče fotografií
    const viewer = document.createElement('div');
    viewer.className = 'photo-viewer';
    viewer.innerHTML = `
        <div class="photo-viewer-content">
            <div class="photo-viewer-header">
                <div class="photo-viewer-title">${title}</div>
                <div class="photo-viewer-actions">
                    <button type="button" class="photo-viewer-action-btn close-viewer" title="Zavřít">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="photo-viewer-body">
                <img src="${imageUrl}" alt="${title}" onerror="this.src='https://via.placeholder.com/800x600?text=Chyba+načítání'">
            </div>
        </div>
    `;

    // Přidání prohlížeče do stránky
    document.body.appendChild(viewer);

    // Přidání event listenerů pro tlačítka
    viewer.querySelector('.close-viewer').addEventListener('click', function() {
        viewer.remove();
    });

    // Přidání event listeneru pro kliknutí mimo obsah prohlížeče
    viewer.addEventListener('click', function(event) {
        if (event.target === viewer) {
            viewer.remove();
        }
    });
}

/**
 * Formátování data do čitelného formátu pro finanční monitoring
 * @param {string|Date|null} dateInput - Datum ve formátu YYYY-MM-DD nebo objekt Date
 * @returns {string} - Formátované datum
 */
function formatFinancialDate(dateInput) {
    // Pokud je vstup prázdný, vrátíme prázdný řetězec
    if (!dateInput) return '';

    // Pokud je vstup řetězec, pokusíme se ho převést na objekt Date
    let date;
    if (typeof dateInput === 'string') {
        // Pokud je datum ve formátu DD.MM.YYYY, převedeme ho na YYYY-MM-DD
        if (dateInput.match(/^\d{1,2}\.\d{1,2}\.\d{4}$/)) {
            const parts = dateInput.split('.');
            dateInput = `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
        }

        date = new Date(dateInput);
    } else if (dateInput instanceof Date) {
        date = dateInput;
    } else {
        // Pokud vstup není ani řetězec, ani objekt Date, vrátíme ho jako řetězec
        return String(dateInput);
    }

    // Kontrola, zda je datum platné
    if (isNaN(date.getTime())) {
        console.warn('Neplatné datum:', dateInput);
        return String(dateInput);
    }

    // Formátování data
    try {
        return date.toLocaleDateString('cs-CZ', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
        });
    } catch (error) {
        console.error('Chyba při formátování data:', error);
        return String(dateInput);
    }
}
