"use client";

import { Users } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface StatutoryRepresentativesListProps {
  representatives: string[];
}

export function StatutoryRepresentativesList({ representatives }: StatutoryRepresentativesListProps) {
  if (!representatives || representatives.length === 0) {
    return null;
  }

  return (
    <div className="mt-2 border rounded-md p-3">
      <div className="flex items-center gap-2 mb-2">
        <Users className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm font-medium">Statutární orgány</span>
        <Badge variant="outline" className="ml-1">
          {representatives.length}
        </Badge>
      </div>
      <ul className="space-y-1 text-sm">
        {representatives.map((representative, index) => (
          <li key={index} className="pl-4 py-1 border-l-2 border-muted">
            {representative}
          </li>
        ))}
      </ul>
    </div>
  );
}
