/**
 * IP adresy a síťová analýza - funkce pro práci s IP adresami a doménami
 */

// API klíče pro různé služby
const IP_API_KEYS = {
    ipinfo: '', // Není potřeba pro základní použití
    ipgeolocation: '', // Není potřeba pro základní použití
    whoisxmlapi: '' // Doplňte svůj API klíč pro WHOIS XML API
};

/**
 * Inicializace funkcí pro IP analýzu
 */
function initIpAnalysis() {
    // Přidání event listenerů pro tlačítka v modulu IP analýzy
    document.addEventListener('click', function(event) {
        // Validace IP adresy
        if (event.target.closest('.validate-ip')) {
            validateIp();
        }

        // Validace domény
        if (event.target.closest('.validate-domain')) {
            validateDomain();
        }

        // WHOIS lookup
        if (event.target.closest('.whois-lookup')) {
            performWhoisLookup();
        }

        // DNS lookup
        if (event.target.closest('.dns-lookup')) {
            performDnsLookup();
        }

        // Reverzní IP lookup
        if (event.target.closest('.reverse-ip-lookup')) {
            performReverseIpLookup();
        }

        // Kontrola blacklistů
        if (event.target.closest('.check-blacklists')) {
            checkBlacklists();
        }

        // Přidání portu
        if (event.target.closest('.add-port')) {
            addPort();
        }

        // Přidání subdomény
        if (event.target.closest('.add-subdomain')) {
            addSubdomain();
        }

        // Nahrání fotografie z počítače
        if (event.target.closest('.upload-photo')) {
            const uploadInput = document.getElementById('ip-photo-upload');
            if (uploadInput) {
                uploadInput.click();
            }
        }

        // Přidání fotografie z URL
        if (event.target.closest('.add-photo-url')) {
            const galleryId = event.target.closest('.add-photo-url').getAttribute('data-gallery');
            addPhotoFromUrl(galleryId);
        }
    });

    // Změna event listenery
    document.addEventListener('change', function(event) {
        // Nahrání fotografie z počítače
        if (event.target.id === 'ip-photo-upload') {
            handlePhotoUpload(event);
        }
    });

    // Vložení fotografie ze schránky (Ctrl+V) - globální handler
    document.addEventListener('paste', function(event) {
        const activeElement = document.activeElement;
        const ipModule = document.querySelector('.module[id^="module-ip-analyza"]');

        // Kontrola, zda je aktivní modul IP analýzy a kurzor není v textovém poli
        if (ipModule && !activeElement.tagName.match(/input|textarea/i)) {
            // Najít ID galerie v modulu
            const gallery = ipModule.querySelector('.ip-photo-gallery');
            if (gallery) {
                handlePasteImage(event, gallery.id);
                // Zabránit výchozímu chování
                event.preventDefault();
                event.stopPropagation();
            }
        }
    });

    // Kliknutí na tlačítko pro vložení ze schránky
    document.querySelectorAll('.paste-photo').forEach(button => {
        button.addEventListener('click', function() {
            alert('Pro vložení obrázku ze schránky stiskněte Ctrl+V kdekoli v modulu IP analýzy (mimo textová pole).');
        });
    });

    // Inicializace fotogalerie
    initIpPhotoGallery();

    // Skrytí nefunkčních tlačítek
    hideNonFunctionalButtons();
}

/**
 * Inicializace fotogalerie pro IP analýzu
 */
function initIpPhotoGallery() {
    console.log('Inicializace fotogalerie pro IP analýzu');

    // Najít všechny fotogalerie v modulu IP analýzy
    const galleries = document.querySelectorAll('.ip-photo-gallery');
    console.log('Nalezeno galerií:', galleries.length);

    // Přidat event listenery pro tlačítko přidání fotografie v galerii
    galleries.forEach(gallery => {
        const addButton = gallery.querySelector('.photo-gallery-add');
        if (addButton) {
            console.log('Přidání event listeneru pro tlačítko přidání fotografie v galerii:', gallery.id);

            // Odstranění existujících event listenerů
            const newAddButton = addButton.cloneNode(true);
            addButton.parentNode.replaceChild(newAddButton, addButton);

            // Přidání nového event listeneru
            newAddButton.addEventListener('click', function() {
                console.log('Kliknuto na tlačítko přidání fotografie v galerii:', gallery.id);
                const uploadInput = document.getElementById('ip-photo-upload');
                if (uploadInput) {
                    uploadInput.click();
                }
            });
        }
    });

    // Zajistíme, že event listenery pro tlačítka fotogalerie nebudou interferovat s tlačítky modulu
    document.querySelectorAll('.upload-photo, .paste-photo, .add-photo-url').forEach(button => {
        console.log('Přidání event listeneru pro tlačítko:', button.className);

        // Odstranění existujících event listenerů
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);

        // Přidání nového event listeneru
        newButton.addEventListener('click', function(event) {
            // Zastavíme propagaci události, aby se nespustily jiné event listenery
            event.stopPropagation();
            event.preventDefault();

            console.log('Kliknuto na tlačítko:', this.className);

            // Zpracování podle typu tlačítka
            if (this.classList.contains('upload-photo')) {
                const uploadInput = document.getElementById('ip-photo-upload');
                if (uploadInput) {
                    uploadInput.click();
                }
            } else if (this.classList.contains('add-photo-url')) {
                const galleryId = this.getAttribute('data-gallery');
                addPhotoFromUrl(galleryId);
            } else if (this.classList.contains('paste-photo')) {
                alert('Pro vložení obrázku ze schránky stiskněte Ctrl+V kdekoli v modulu IP analýzy (mimo textová pole).');
            }
        });
    });

    // Přidání event listeneru pro input[type=file]
    const uploadInput = document.getElementById('ip-photo-upload');
    if (uploadInput) {
        console.log('Přidání event listeneru pro input[type=file]:', uploadInput.id);

        // Odstranění existujících event listenerů
        const newUploadInput = uploadInput.cloneNode(true);
        uploadInput.parentNode.replaceChild(newUploadInput, uploadInput);

        // Přidání nového event listeneru
        newUploadInput.addEventListener('change', function(event) {
            console.log('Změna input[type=file]:', this.id);
            handlePhotoUpload(event);
        });
    }
}

/**
 * Přidání fotografie z URL
 * @param {string} galleryId - ID galerie
 */
function addPhotoFromUrl(galleryId) {
    const gallery = document.getElementById(galleryId);
    if (!gallery) return;

    // Vytvoření dialogu pro přidání fotografie
    const dialog = document.createElement('div');
    dialog.className = 'photo-dialog';
    dialog.innerHTML = `
        <div class="photo-dialog-content">
            <h4>Přidat fotografii z URL</h4>
            <div class="form-row">
                <div class="form-group long">
                    <label>Název fotografie</label>
                    <input type="text" class="form-control" id="photo-title" placeholder="Zadejte název fotografie">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group long">
                    <label>URL fotografie</label>
                    <input type="text" class="form-control" id="photo-url" placeholder="Zadejte URL fotografie">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>Datum pořízení</label>
                    <input type="date" class="form-control" id="photo-date" value="${new Date().toISOString().split('T')[0]}">
                </div>
                <div class="form-group">
                    <label>Zdroj fotografie</label>
                    <select class="form-control" id="photo-source">
                        <option value="">-- Vyberte zdroj --</option>
                        <option value="social-media">Sociální sítě</option>
                        <option value="surveillance">Sledování</option>
                        <option value="evidence">Důkazní materiál</option>
                        <option value="public-source">Veřejný zdroj</option>
                        <option value="other">Jiný zdroj</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group long">
                    <label>Popis fotografie</label>
                    <textarea class="form-control" id="photo-description" rows="3" placeholder="Popište fotografii..."></textarea>
                </div>
            </div>
            <div class="dialog-buttons">
                <button type="button" class="btn-inline cancel-photo">Zrušit</button>
                <button type="button" class="btn-inline save-photo" data-gallery="${galleryId}">Uložit</button>
            </div>
        </div>
    `;

    // Přidání dialogu do stránky
    document.body.appendChild(dialog);

    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.cancel-photo').addEventListener('click', function() {
        dialog.remove();
    });

    dialog.querySelector('.save-photo').addEventListener('click', function() {
        const galleryId = this.getAttribute('data-gallery');
        const title = dialog.querySelector('#photo-title').value;
        const url = dialog.querySelector('#photo-url').value;
        const date = dialog.querySelector('#photo-date').value;
        const source = dialog.querySelector('#photo-source').value;
        const description = dialog.querySelector('#photo-description').value;

        if (!title || !url) {
            alert('Vyplňte název a URL fotografie.');
            return;
        }

        // Přidání fotografie do galerie
        addPhotoToGallery(galleryId, title, url, date, description, source);

        // Zavření dialogu
        dialog.remove();
    });
}

/**
 * Zpracování nahrání fotografie z počítače
 * @param {Event} event - Událost změny input[type=file]
 */
function handlePhotoUpload(event) {
    console.log('Zpracování nahrání fotografie z počítače', event);

    const file = event.target.files[0];
    if (!file) {
        console.error('Nebyl vybrán žádný soubor');
        return;
    }

    console.log('Vybraný soubor:', file.name, file.type, file.size);

    // Kontrola, zda jde o obrázek
    if (!file.type.match('image.*')) {
        alert('Vybraný soubor není obrázek.');
        return;
    }

    // Vytvoření URL pro náhled
    const imageUrl = URL.createObjectURL(file);
    console.log('Vytvořeno URL pro náhled:', imageUrl);

    // Najít ID galerie v modulu IP analýzy
    const ipModule = document.querySelector('.module[id^="module-ip-analyza"]');
    let galleryId = 'ip-photo-gallery';

    if (ipModule) {
        const gallery = ipModule.querySelector('.ip-photo-gallery');
        if (gallery && gallery.id) {
            galleryId = gallery.id;
            console.log('Nalezeno ID galerie:', galleryId);
        }
    }

    // Otevření dialogu pro zadání informací o fotografii
    openPhotoInfoDialog(galleryId, file.name, imageUrl);
}

/**
 * Zpracování vložení obrázku ze schránky
 * @param {Event} event - Událost vložení
 * @param {string} galleryId - ID galerie
 */
function handlePasteImage(event, galleryId) {
    console.log('Zpracování vložení obrázku ze schránky', event, galleryId);

    // Kontrola, zda schránka obsahuje obrázek
    const clipboardData = event.clipboardData || window.clipboardData;
    if (!clipboardData) {
        console.error('Clipboard data nejsou k dispozici');
        return;
    }

    const items = clipboardData.items;
    if (!items) {
        console.error('Clipboard items nejsou k dispozici');
        return;
    }

    let blob = null;

    // Procházení položek ve schránce
    for (let i = 0; i < items.length; i++) {
        console.log('Clipboard item:', items[i].type);
        if (items[i].type.indexOf('image') === 0) {
            blob = items[i].getAsFile();
            console.log('Nalezen obrázek ve schránce:', blob);
            break;
        }
    }

    if (!blob) {
        console.error('Schránka neobsahuje obrázek');
        alert('Schránka neobsahuje obrázek. Zkopírujte obrázek do schránky a zkuste to znovu.');
        return;
    }

    // Vytvoření URL pro náhled
    const imageUrl = URL.createObjectURL(blob);
    console.log('Vytvořeno URL pro náhled:', imageUrl);

    // Otevření dialogu pro zadání informací o fotografii
    openPhotoInfoDialog(galleryId, 'Vložený obrázek', imageUrl);
}

/**
 * Otevření dialogu pro zadání informací o fotografii
 * @param {string} galleryId - ID galerie
 * @param {string} fileName - Název souboru
 * @param {string} imageUrl - URL obrázku
 */
function openPhotoInfoDialog(galleryId, fileName, imageUrl) {
    // Vytvoření dialogu pro zadání informací o fotografii
    const dialog = document.createElement('div');
    dialog.className = 'photo-dialog';
    dialog.innerHTML = `
        <div class="photo-dialog-content">
            <h4>Informace o fotografii</h4>
            <div class="form-row">
                <div class="form-group long">
                    <div class="photo-preview-dialog">
                        <img src="${imageUrl}" alt="${fileName}" style="max-width: 100%; max-height: 200px;">
                    </div>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group long">
                    <label>Název fotografie</label>
                    <input type="text" class="form-control" id="photo-title" value="${fileName.replace(/\.[^/.]+$/, '')}" placeholder="Zadejte název fotografie">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>Datum pořízení</label>
                    <input type="date" class="form-control" id="photo-date" value="${new Date().toISOString().split('T')[0]}">
                </div>
                <div class="form-group">
                    <label>Zdroj fotografie</label>
                    <select class="form-control" id="photo-source">
                        <option value="">-- Vyberte zdroj --</option>
                        <option value="social-media">Sociální sítě</option>
                        <option value="surveillance">Sledování</option>
                        <option value="evidence">Důkazní materiál</option>
                        <option value="public-source">Veřejný zdroj</option>
                        <option value="other">Jiný zdroj</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group long">
                    <label>Popis fotografie</label>
                    <textarea class="form-control" id="photo-description" rows="3" placeholder="Popište fotografii..."></textarea>
                </div>
            </div>
            <div class="dialog-buttons">
                <button type="button" class="btn-inline cancel-photo">Zrušit</button>
                <button type="button" class="btn-inline save-photo" data-gallery="${galleryId}" data-image-url="${imageUrl}">Uložit</button>
            </div>
        </div>
    `;

    // Přidání dialogu do stránky
    document.body.appendChild(dialog);

    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.cancel-photo').addEventListener('click', function() {
        dialog.remove();
        URL.revokeObjectURL(imageUrl); // Uvolnění URL
    });

    dialog.querySelector('.save-photo').addEventListener('click', function() {
        const galleryId = this.getAttribute('data-gallery');
        const imageUrl = this.getAttribute('data-image-url');
        const title = dialog.querySelector('#photo-title').value;
        const date = dialog.querySelector('#photo-date').value;
        const source = dialog.querySelector('#photo-source').value;
        const description = dialog.querySelector('#photo-description').value;

        if (!title) {
            alert('Vyplňte název fotografie.');
            return;
        }

        // Přidání fotografie do galerie
        addPhotoToGallery(galleryId, title, imageUrl, date, description, source);

        // Zavření dialogu
        dialog.remove();
    });
}

/**
 * Přidání fotografie do galerie
 * @param {string} galleryId - ID galerie
 * @param {string} title - Název fotografie
 * @param {string} imageUrl - URL obrázku
 * @param {string} date - Datum pořízení
 * @param {string} description - Popis fotografie
 * @param {string} source - Zdroj fotografie
 */
function addPhotoToGallery(galleryId, title, imageUrl, date, description, source) {
    const gallery = document.getElementById(galleryId);
    if (!gallery) return;

    // Vytvoření nové fotografie
    const photoId = `photo-${Date.now()}`;
    const photoHTML = `
        <div class="photo-item" id="${photoId}">
            <div class="photo-preview">
                <img src="${imageUrl}" alt="${title}" onerror="this.src='https://via.placeholder.com/150x150?text=Chyba+načítání'">
            </div>
            <div class="photo-info">
                <div class="photo-title">${title}</div>
                <div class="photo-date">${formatDate(date)}</div>
                <div class="photo-description">${description || 'Bez popisu'}</div>
            </div>
            <div class="photo-actions">
                <button type="button" class="photo-view" data-photo-url="${imageUrl}" data-photo-title="${title}">
                    <i class="fas fa-search-plus"></i>
                </button>
                <button type="button" class="photo-delete" data-photo-id="${photoId}">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;

    // Přidání fotografie do galerie
    const addButton = gallery.querySelector('.photo-gallery-add');
    if (addButton) {
        gallery.insertBefore(document.createRange().createContextualFragment(photoHTML), addButton);
    } else {
        gallery.insertAdjacentHTML('beforeend', photoHTML);
    }

    // Přidání event listenerů pro tlačítka
    const viewButton = document.querySelector(`#${photoId} .photo-view`);
    if (viewButton) {
        viewButton.addEventListener('click', function() {
            const photoUrl = this.getAttribute('data-photo-url');
            const photoTitle = this.getAttribute('data-photo-title');
            showPhotoViewer(photoUrl, photoTitle);
        });
    }

    const deleteButton = document.querySelector(`#${photoId} .photo-delete`);
    if (deleteButton) {
        deleteButton.addEventListener('click', function() {
            const photoId = this.getAttribute('data-photo-id');
            const photoElement = document.getElementById(photoId);
            if (photoElement) {
                photoElement.remove();
            }
        });
    }
}

/**
 * Zobrazení prohlížeče fotografií
 * @param {string} photoUrl - URL fotografie
 * @param {string} photoTitle - Název fotografie
 */
function showPhotoViewer(photoUrl, photoTitle) {
    // Vytvoření prohlížeče fotografií
    const viewer = document.createElement('div');
    viewer.className = 'photo-viewer';
    viewer.innerHTML = `
        <div class="photo-viewer-content">
            <div class="photo-viewer-header">
                <div class="photo-viewer-title">${photoTitle}</div>
                <button type="button" class="photo-viewer-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="photo-viewer-body">
                <img src="${photoUrl}" alt="${photoTitle}" onerror="this.src='https://via.placeholder.com/800x600?text=Chyba+načítání'">
            </div>
        </div>
    `;

    // Přidání prohlížeče do stránky
    document.body.appendChild(viewer);

    // Přidání event listeneru pro tlačítko zavření
    viewer.querySelector('.photo-viewer-close').addEventListener('click', function() {
        viewer.remove();
    });

    // Přidání event listeneru pro kliknutí mimo obsah prohlížeče
    viewer.addEventListener('click', function(event) {
        if (event.target === viewer) {
            viewer.remove();
        }
    });
}

/**
 * Skrytí nefunkčních tlačítek a sekcí
 */
function hideNonFunctionalButtons() {
    // Zde můžete skrýt tlačítka, která nejsou funkční
    // Například pokud nemáte API klíč pro některou službu
}

/**
 * Formátování data do čitelného formátu
 * @param {string} dateString - Datum ve formátu YYYY-MM-DD
 * @returns {string} - Formátované datum
 */
function formatDate(dateString) {
    if (!dateString) return 'Datum není k dispozici';

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return dateString;

    return date.toLocaleDateString('cs-CZ', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
}

/**
 * Validace IP adresy
 */
function validateIp() {
    const ipAddress = document.getElementById('ip-address').value.trim();
    const resultContainer = document.querySelector('.ip-validation-results');

    if (!ipAddress) {
        alert('Zadejte IP adresu.');
        return;
    }

    // Zobrazení výsledků validace
    resultContainer.style.display = 'block';

    // Validace formátu IP adresy
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    const ipv6Regex = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/;

    const isIPv4 = ipv4Regex.test(ipAddress);
    const isIPv6 = ipv6Regex.test(ipAddress);

    // Aktualizace výsledků validace
    document.getElementById('ip-format-result').innerHTML = isIPv4 || isIPv6 ?
        `<span class="valid"><i class="fas fa-check-circle"></i> ${isIPv4 ? 'IPv4' : 'IPv6'}</span>` :
        `<span class="invalid"><i class="fas fa-times-circle"></i> Neplatný formát</span>`;

    // Pokud je IP adresa neplatná, nepokračujeme
    if (!isIPv4 && !isIPv6) return;

    // Určení typu IP adresy (veřejná, privátní, rezervovaná)
    let ipType = '';
    if (isIPv4) {
        if (/^(10\.|172\.(1[6-9]|2[0-9]|3[0-1])\.|192\.168\.)/.test(ipAddress)) {
            ipType = 'Privátní';
        } else if (/^127\./.test(ipAddress)) {
            ipType = 'Localhost';
        } else if (/^169\.254\./.test(ipAddress)) {
            ipType = 'Link-local';
        } else if (/^(224\.|239\.)/.test(ipAddress)) {
            ipType = 'Multicast';
        } else {
            ipType = 'Veřejná';
        }
    } else {
        if (/^fe80:/i.test(ipAddress)) {
            ipType = 'Link-local';
        } else if (/^fc00:/i.test(ipAddress) || /^fd00:/i.test(ipAddress)) {
            ipType = 'Unikátní lokální';
        } else if (/^ff00:/i.test(ipAddress)) {
            ipType = 'Multicast';
        } else if (/^::1$/.test(ipAddress)) {
            ipType = 'Localhost';
        } else {
            ipType = 'Veřejná';
        }
    }

    document.getElementById('ip-type-result').innerHTML =
        `<span class="${ipType === 'Veřejná' ? 'valid' : 'info'}"><i class="fas fa-info-circle"></i> ${ipType}</span>`;

    // Pokud je IP adresa veřejná, získáme geolokační informace
    if (ipType === 'Veřejná') {
        // Získání geolokačních informací pomocí ipinfo.io (bez API klíče)
        fetch(`https://ipinfo.io/${ipAddress}/json`)
            .then(response => response.json())
            .then(data => {
                // Aktualizace výsledků geolokace
                document.getElementById('ip-country-result').innerHTML = data.country ?
                    `<span class="info"><i class="fas fa-globe"></i> ${data.country}</span>` :
                    `<span class="pending"><i class="fas fa-question-circle"></i> Neznámá</span>`;

                document.getElementById('ip-city-result').innerHTML = data.city ?
                    `<span class="info"><i class="fas fa-city"></i> ${data.city}</span>` :
                    `<span class="pending"><i class="fas fa-question-circle"></i> Neznámá</span>`;

                document.getElementById('ip-org-result').innerHTML = data.org ?
                    `<span class="info"><i class="fas fa-building"></i> ${data.org}</span>` :
                    `<span class="pending"><i class="fas fa-question-circle"></i> Neznámá</span>`;

                // Aktualizace mapy
                if (data.loc) {
                    const [lat, lon] = data.loc.split(',');
                    document.getElementById('ip-map-container').innerHTML =
                        `<iframe width="100%" height="300" frameborder="0" scrolling="no" marginheight="0" marginwidth="0"
                        src="https://www.openstreetmap.org/export/embed.html?bbox=${lon-0.01},${lat-0.01},${lon+0.01},${lat+0.01}&layer=mapnik&marker=${lat},${lon}"
                        style="border: 1px solid #ddd; border-radius: 4px;"></iframe>`;
                }
            })
            .catch(error => {
                console.error('Chyba při získávání geolokačních informací:', error);
                document.getElementById('ip-country-result').innerHTML =
                    `<span class="error"><i class="fas fa-exclamation-circle"></i> Chyba při získávání dat</span>`;
                document.getElementById('ip-city-result').innerHTML =
                    `<span class="error"><i class="fas fa-exclamation-circle"></i> Chyba při získávání dat</span>`;
                document.getElementById('ip-org-result').innerHTML =
                    `<span class="error"><i class="fas fa-exclamation-circle"></i> Chyba při získávání dat</span>`;
            });
    } else {
        // Pro nepublikované IP adresy nezobrazujeme geolokační informace
        document.getElementById('ip-country-result').innerHTML =
            `<span class="info"><i class="fas fa-info-circle"></i> Není k dispozici pro ${ipType.toLowerCase()} IP</span>`;
        document.getElementById('ip-city-result').innerHTML =
            `<span class="info"><i class="fas fa-info-circle"></i> Není k dispozici pro ${ipType.toLowerCase()} IP</span>`;
        document.getElementById('ip-org-result').innerHTML =
            `<span class="info"><i class="fas fa-info-circle"></i> Není k dispozici pro ${ipType.toLowerCase()} IP</span>`;
        document.getElementById('ip-map-container').innerHTML =
            `<div class="map-placeholder"><i class="fas fa-map-marked-alt"></i><p>Mapa není k dispozici pro ${ipType.toLowerCase()} IP adresy</p></div>`;
    }
}

/**
 * Validace domény
 */
function validateDomain() {
    const domain = document.getElementById('domain-name').value.trim();
    const resultContainer = document.querySelector('.domain-validation-results');

    if (!domain) {
        alert('Zadejte doménu.');
        return;
    }

    // Zobrazení výsledků validace
    resultContainer.style.display = 'block';

    // Validace formátu domény
    const domainRegex = /^(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z0-9][a-z0-9-]{0,61}[a-z0-9]$/i;
    const isValid = domainRegex.test(domain);

    // Aktualizace výsledků validace
    document.getElementById('domain-format-result').innerHTML = isValid ?
        `<span class="valid"><i class="fas fa-check-circle"></i> Platný formát</span>` :
        `<span class="invalid"><i class="fas fa-times-circle"></i> Neplatný formát</span>`;

    // Pokud je doména neplatná, nepokračujeme
    if (!isValid) return;

    // Kontrola existence domény pomocí DNS lookup
    checkDomainExistence(domain);
}

/**
 * Kontrola existence domény
 */
function checkDomainExistence(domain) {
    // Použijeme Google DNS API pro kontrolu existence domény
    fetch(`https://dns.google/resolve?name=${domain}`)
        .then(response => response.json())
        .then(data => {
            const exists = data.Status === 0; // Status 0 znamená, že doména existuje

            document.getElementById('domain-existence-result').innerHTML = exists ?
                `<span class="valid"><i class="fas fa-check-circle"></i> Doména existuje</span>` :
                `<span class="invalid"><i class="fas fa-times-circle"></i> Doména neexistuje</span>`;

            // Pokud doména existuje, získáme informace o IP adrese
            if (exists && data.Answer && data.Answer.length > 0) {
                const ipAddress = data.Answer[0].data;
                document.getElementById('domain-ip-result').innerHTML =
                    `<span class="info"><i class="fas fa-network-wired"></i> ${ipAddress}</span>`;

                // Získání informací o IP adrese
                fetch(`https://ipinfo.io/${ipAddress}/json`)
                    .then(response => response.json())
                    .then(ipData => {
                        document.getElementById('domain-country-result').innerHTML = ipData.country ?
                            `<span class="info"><i class="fas fa-globe"></i> ${ipData.country}</span>` :
                            `<span class="pending"><i class="fas fa-question-circle"></i> Neznámá</span>`;

                        document.getElementById('domain-org-result').innerHTML = ipData.org ?
                            `<span class="info"><i class="fas fa-building"></i> ${ipData.org}</span>` :
                            `<span class="pending"><i class="fas fa-question-circle"></i> Neznámá</span>`;
                    })
                    .catch(error => {
                        console.error('Chyba při získávání informací o IP adrese:', error);
                    });
            } else {
                document.getElementById('domain-ip-result').innerHTML =
                    `<span class="invalid"><i class="fas fa-times-circle"></i> Není k dispozici</span>`;
                document.getElementById('domain-country-result').innerHTML =
                    `<span class="invalid"><i class="fas fa-times-circle"></i> Není k dispozici</span>`;
                document.getElementById('domain-org-result').innerHTML =
                    `<span class="invalid"><i class="fas fa-times-circle"></i> Není k dispozici</span>`;
            }
        })
        .catch(error => {
            console.error('Chyba při kontrole existence domény:', error);
            document.getElementById('domain-existence-result').innerHTML =
                `<span class="error"><i class="fas fa-exclamation-circle"></i> Chyba při kontrole</span>`;
        });
}

/**
 * Provedení WHOIS lookup
 */
function performWhoisLookup() {
    const target = document.getElementById('whois-target').value.trim();
    const resultContainer = document.getElementById('whois-results');

    if (!target) {
        alert('Zadejte IP adresu nebo doménu pro WHOIS lookup.');
        return;
    }

    // Zobrazení načítání
    resultContainer.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i> Načítání WHOIS informací...</div>';
    resultContainer.style.display = 'block';

    // Použití veřejného WHOIS API
    fetch(`https://api.whoapi.com/?domain=${target}&r=whois&apikey=${IP_API_KEYS.whoisxmlapi}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 0) {
                // Formátování WHOIS dat
                const whoisData = data.whois_raw || 'Žádná WHOIS data nebyla nalezena.';
                resultContainer.innerHTML = `<pre class="whois-data">${whoisData}</pre>`;
            } else {
                resultContainer.innerHTML = `<div class="error-message"><i class="fas fa-exclamation-circle"></i> Chyba: ${data.status_desc || 'Neznámá chyba'}</div>`;
            }
        })
        .catch(error => {
            console.error('Chyba při WHOIS lookup:', error);
            resultContainer.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    Chyba při získávání WHOIS informací. Zkuste použít online WHOIS službu:
                </div>
                <div class="external-links">
                    <a href="https://who.is/whois/${target}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> who.is
                    </a>
                    <a href="https://whois.domaintools.com/${target}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> DomainTools
                    </a>
                    <a href="https://lookup.icann.org/en/lookup?q=${target}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> ICANN Lookup
                    </a>
                </div>
            `;
        });
}

/**
 * Provedení DNS lookup
 */
function performDnsLookup() {
    const domain = document.getElementById('dns-domain').value.trim();
    const resultContainer = document.getElementById('dns-results');

    if (!domain) {
        alert('Zadejte doménu pro DNS lookup.');
        return;
    }

    // Zobrazení načítání
    resultContainer.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i> Načítání DNS záznamů...</div>';
    resultContainer.style.display = 'block';

    // Použití Google DNS API pro získání různých typů DNS záznamů
    const recordTypes = ['A', 'AAAA', 'MX', 'NS', 'TXT', 'SOA', 'CNAME'];
    const promises = recordTypes.map(type =>
        fetch(`https://dns.google/resolve?name=${domain}&type=${type}`)
            .then(response => response.json())
            .then(data => ({ type, data }))
    );

    Promise.all(promises)
        .then(results => {
            let html = '<div class="dns-records">';

            results.forEach(({ type, data }) => {
                if (data.Status === 0 && data.Answer && data.Answer.length > 0) {
                    html += `
                        <div class="dns-record-type">
                            <h4>${type} záznamy</h4>
                            <div class="dns-record-list">
                    `;

                    data.Answer.forEach(record => {
                        html += `
                            <div class="dns-record-item">
                                <div class="dns-record-name">${record.name}</div>
                                <div class="dns-record-value">${record.data}</div>
                                <div class="dns-record-ttl">TTL: ${record.TTL}</div>
                            </div>
                        `;
                    });

                    html += `
                            </div>
                        </div>
                    `;
                }
            });

            html += '</div>';

            // Kontrola, zda byly nalezeny nějaké záznamy
            if (html === '<div class="dns-records"></div>') {
                html = '<div class="error-message"><i class="fas fa-exclamation-circle"></i> Nebyly nalezeny žádné DNS záznamy.</div>';
            }

            resultContainer.innerHTML = html;
        })
        .catch(error => {
            console.error('Chyba při DNS lookup:', error);
            resultContainer.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    Chyba při získávání DNS záznamů. Zkuste použít online DNS lookup službu:
                </div>
                <div class="external-links">
                    <a href="https://toolbox.googleapps.com/apps/dig/#A/${domain}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Google Dig
                    </a>
                    <a href="https://dnschecker.org/#A/${domain}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> DNS Checker
                    </a>
                </div>
            `;
        });
}

/**
 * Provedení reverzního IP lookup
 */
function performReverseIpLookup() {
    const ipAddress = document.getElementById('reverse-ip-address').value.trim();
    const resultContainer = document.getElementById('reverse-ip-results');

    if (!ipAddress) {
        alert('Zadejte IP adresu pro reverzní lookup.');
        return;
    }

    // Zobrazení načítání
    resultContainer.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i> Načítání domén...</div>';
    resultContainer.style.display = 'block';

    // Použití veřejného API pro reverzní IP lookup
    // Poznámka: Většina veřejných API pro reverzní IP lookup vyžaduje API klíč
    // Zde použijeme odkaz na externí služby
    resultContainer.innerHTML = `
        <div class="info-message">
            <i class="fas fa-info-circle"></i>
            Pro reverzní IP lookup použijte některou z následujících služeb:
        </div>
        <div class="external-links">
            <a href="https://viewdns.info/reverseip/?host=${ipAddress}" target="_blank" class="btn-inline">
                <i class="fas fa-external-link-alt"></i> ViewDNS.info
            </a>
            <a href="https://securitytrails.com/list/ip/${ipAddress}" target="_blank" class="btn-inline">
                <i class="fas fa-external-link-alt"></i> SecurityTrails
            </a>
            <a href="https://www.robtex.com/ip-lookup/${ipAddress}" target="_blank" class="btn-inline">
                <i class="fas fa-external-link-alt"></i> Robtex
            </a>
        </div>
    `;
}

/**
 * Kontrola blacklistů
 */
function checkBlacklists() {
    const target = document.getElementById('blacklist-target').value.trim();
    const resultContainer = document.getElementById('blacklist-results');

    if (!target) {
        alert('Zadejte IP adresu nebo doménu pro kontrolu blacklistů.');
        return;
    }

    // Zobrazení načítání
    resultContainer.innerHTML = '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i> Kontrola blacklistů...</div>';
    resultContainer.style.display = 'block';

    // Použití odkazu na externí služby pro kontrolu blacklistů
    resultContainer.innerHTML = `
        <div class="info-message">
            <i class="fas fa-info-circle"></i>
            Pro kontrolu blacklistů použijte některou z následujících služeb:
        </div>
        <div class="external-links">
            <a href="https://mxtoolbox.com/blacklists.aspx?lookup=${target}" target="_blank" class="btn-inline">
                <i class="fas fa-external-link-alt"></i> MX Toolbox
            </a>
            <a href="https://www.ipvoid.com/ip-blacklist-check/" target="_blank" class="btn-inline">
                <i class="fas fa-external-link-alt"></i> IPVoid
            </a>
            <a href="https://www.virustotal.com/gui/search/${target}" target="_blank" class="btn-inline">
                <i class="fas fa-external-link-alt"></i> VirusTotal
            </a>
        </div>
    `;
}

/**
 * Přidání portu do seznamu portů
 */
function addPort() {
    const portNumber = document.getElementById('port-number').value.trim();
    const portService = document.getElementById('port-service').value.trim();
    const portStatus = document.getElementById('port-status').value;
    const portsList = document.getElementById('ports-list');

    if (!portNumber) {
        alert('Zadejte číslo portu.');
        return;
    }

    // Validace čísla portu
    if (!/^\d+$/.test(portNumber) || parseInt(portNumber) < 1 || parseInt(portNumber) > 65535) {
        alert('Zadejte platné číslo portu (1-65535).');
        return;
    }

    // Vytvoření nového portu
    const portId = `port-${Date.now()}`;
    const portHTML = `
        <div class="port-item" id="${portId}">
            <div class="port-number">${portNumber}</div>
            <div class="port-service">${portService || 'Neznámá služba'}</div>
            <div class="port-status ${portStatus}">${getPortStatusText(portStatus)}</div>
            <div class="port-actions">
                <button type="button" class="btn-icon delete-port" data-port-id="${portId}">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;

    // Přidání portu do seznamu
    portsList.insertAdjacentHTML('beforeend', portHTML);

    // Přidání event listeneru pro tlačítko smazání
    document.querySelector(`#${portId} .delete-port`).addEventListener('click', function() {
        document.getElementById(portId).remove();
    });

    // Vyčištění formuláře
    document.getElementById('port-number').value = '';
    document.getElementById('port-service').value = '';
}

/**
 * Získání textu pro stav portu
 */
function getPortStatusText(status) {
    switch (status) {
        case 'open':
            return 'Otevřený';
        case 'closed':
            return 'Zavřený';
        case 'filtered':
            return 'Filtrovaný';
        default:
            return 'Neznámý';
    }
}

/**
 * Přidání subdomény do seznamu subdomén
 */
function addSubdomain() {
    const subdomainName = document.getElementById('subdomain-name').value.trim();
    const subdomainIp = document.getElementById('subdomain-ip').value.trim();
    const subdomainsList = document.getElementById('subdomains-list');

    if (!subdomainName) {
        alert('Zadejte název subdomény.');
        return;
    }

    // Vytvoření nové subdomény
    const subdomainId = `subdomain-${Date.now()}`;
    const subdomainHTML = `
        <div class="subdomain-item" id="${subdomainId}">
            <div class="subdomain-name">${subdomainName}</div>
            <div class="subdomain-ip">${subdomainIp || 'IP adresa není k dispozici'}</div>
            <div class="subdomain-actions">
                <button type="button" class="btn-icon delete-subdomain" data-subdomain-id="${subdomainId}">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;

    // Přidání subdomény do seznamu
    subdomainsList.insertAdjacentHTML('beforeend', subdomainHTML);

    // Přidání event listeneru pro tlačítko smazání
    document.querySelector(`#${subdomainId} .delete-subdomain`).addEventListener('click', function() {
        document.getElementById(subdomainId).remove();
    });

    // Vyčištění formuláře
    document.getElementById('subdomain-name').value = '';
    document.getElementById('subdomain-ip').value = '';
}

// Inicializace modulu při načtení stránky
document.addEventListener('DOMContentLoaded', function() {
    initIpAnalysis();
});
