"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, Globe, CheckCircle, XCircle, AlertTriangle } from "lucide-react";
import { IpAddressUsage } from "@/types";

interface IpAddressValidatorProps {
  initialIp: string;
  onValidationComplete: (results: any) => void;
  savedResults?: any;
}

export function IpAddressValidator({ initialIp, onValidationComplete, savedResults }: IpAddressValidatorProps) {
  const [ipAddress, setIpAddress] = useState(initialIp);
  const [isValidating, setIsValidating] = useState(false);
  const [validationResults, setValidationResults] = useState<any>(savedResults || null);
  const [error, setError] = useState<string | null>(null);

  // Použití uložených výsledků validace, pokud jsou k dispozici
  useEffect(() => {
    if (savedResults) {
      setValidationResults(savedResults);
    }
  }, [savedResults]);

  const validateIp = async () => {
    if (!ipAddress) {
      setError("Zadejte IP adresu pro validaci");
      return;
    }

    setIsValidating(true);
    setError(null);

    try {
      // Validace formátu IP adresy
      const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
      const ipv6Regex = /^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/;

      const isIPv4 = ipv4Regex.test(ipAddress);
      const isIPv6 = ipv6Regex.test(ipAddress);

      let results: any = {
        isValid: isIPv4 || isIPv6,
        ipType: isIPv4 ? "ipv4" : isIPv6 ? "ipv6" : "unknown",
      };

      if (!results.isValid) {
        setValidationResults(results);
        onValidationComplete(results);
        return;
      }

      // Určení typu IP adresy (veřejná, privátní, rezervovaná)
      let ipNetworkType = '';
      if (isIPv4) {
        if (/^(10\.|172\.(1[6-9]|2[0-9]|3[0-1])\.|192\.168\.)/.test(ipAddress)) {
          ipNetworkType = 'Privátní';
          results.usage = "residential" as IpAddressUsage;
        } else if (/^127\./.test(ipAddress)) {
          ipNetworkType = 'Localhost';
          results.usage = "residential" as IpAddressUsage;
        } else if (/^169\.254\./.test(ipAddress)) {
          ipNetworkType = 'Link-local';
          results.usage = "residential" as IpAddressUsage;
        } else if (/^(224\.|239\.)/.test(ipAddress)) {
          ipNetworkType = 'Multicast';
          results.usage = "other" as IpAddressUsage;
        } else {
          ipNetworkType = 'Veřejná';
          results.usage = "datacenter" as IpAddressUsage;
        }
      } else {
        if (/^fe80:/i.test(ipAddress)) {
          ipNetworkType = 'Link-local';
          results.usage = "residential" as IpAddressUsage;
        } else if (/^fc00:/i.test(ipAddress) || /^fd00:/i.test(ipAddress)) {
          ipNetworkType = 'Unikátní lokální';
          results.usage = "residential" as IpAddressUsage;
        } else if (/^ff00:/i.test(ipAddress)) {
          ipNetworkType = 'Multicast';
          results.usage = "other" as IpAddressUsage;
        } else if (/^::1$/.test(ipAddress)) {
          ipNetworkType = 'Localhost';
          results.usage = "residential" as IpAddressUsage;
        } else {
          ipNetworkType = 'Veřejná';
          results.usage = "datacenter" as IpAddressUsage;
        }
      }

      results.networkType = ipNetworkType;

      // Pokud je IP adresa veřejná, získáme geolokační informace
      if (ipNetworkType === 'Veřejná') {
        // Získání geolokačních informací pomocí ipinfo.io (bez API klíče)
        const response = await fetch(`https://ipinfo.io/${ipAddress}/json`);
        const data = await response.json();

        if (data) {
          results.geoLocation = {
            country: data.country || '',
            region: data.region || '',
            city: data.city || '',
            postalCode: data.postal || '',
            timezone: data.timezone || '',
            isp: data.org || '',
            organization: data.org || '',
            asn: data.org ? data.org.split(' ')[0] : '',
            asnOrganization: data.org || ''
          };

          // Přidání souřadnic, pokud jsou k dispozici
          if (data.loc) {
            const [lat, lon] = data.loc.split(',');
            results.geoLocation.latitude = parseFloat(lat);
            results.geoLocation.longitude = parseFloat(lon);
          }

          // Detekce typu využití na základě organizace
          if (data.org) {
            const orgLower = data.org.toLowerCase();
            if (orgLower.includes('vpn') || orgLower.includes('virtual private')) {
              results.usage = "vpn" as IpAddressUsage;
            } else if (orgLower.includes('proxy') || orgLower.includes('cloudflare')) {
              results.usage = "proxy" as IpAddressUsage;
            } else if (orgLower.includes('host') || orgLower.includes('server') || orgLower.includes('data center')) {
              results.usage = "hosting" as IpAddressUsage;
            } else if (orgLower.includes('mobile') || orgLower.includes('wireless')) {
              results.usage = "mobile" as IpAddressUsage;
            } else if (orgLower.includes('business') || orgLower.includes('enterprise')) {
              results.usage = "business" as IpAddressUsage;
            }
          }

          // Přidání hostname, pokud je k dispozici
          if (data.hostname) {
            results.hostname = data.hostname;
          } else {
            results.hostname = `${ipAddress}.unknown-hostname.local`;
          }
        }
      } else {
        // Pro nepublikované IP adresy nastavíme základní informace
        results.geoLocation = {
          country: 'Lokální síť',
          region: 'Lokální síť',
          city: 'Lokální síť',
          postalCode: '',
          timezone: '',
          isp: 'Lokální síť',
          organization: 'Lokální síť',
          asn: '',
          asnOrganization: ''
        };
        results.hostname = `${ipAddress}.local`;
      }

      setValidationResults(results);
      onValidationComplete(results);
    } catch (err) {
      setError("Chyba při validaci IP adresy");
      console.error("IP validation error:", err);
    } finally {
      setIsValidating(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Validace IP adresy</CardTitle>
        <CardDescription>
          Ověřte platnost IP adresy a získejte informace o její geolokaci
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-4">
          <div className="flex space-x-2">
            <Input
              value={ipAddress}
              onChange={(e) => setIpAddress(e.target.value)}
              placeholder="Zadejte IP adresu (např. ***********)"
              disabled={isValidating}
            />
            <Button onClick={validateIp} disabled={isValidating}>
              {isValidating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Validace...
                </>
              ) : (
                "Validovat"
              )}
            </Button>
          </div>

          {error && (
            <div className="bg-destructive/10 p-3 rounded-md text-destructive flex items-center">
              <XCircle className="h-5 w-5 mr-2" />
              {error}
            </div>
          )}

          {validationResults && (
            <div className="border rounded-md p-4 space-y-4">
              <div className="flex items-center">
                <Globe className="h-5 w-5 mr-2 text-primary" />
                <h3 className="text-lg font-semibold">Výsledky validace</h3>
              </div>

              <div className="flex items-center space-x-2">
                <span className="font-medium">Stav:</span>
                {validationResults.isValid ? (
                  <div className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                    <Badge className="bg-green-500">Platná IP adresa</Badge>
                  </div>
                ) : (
                  <div className="flex items-center">
                    <XCircle className="h-4 w-4 text-destructive mr-1" />
                    <Badge variant="destructive">Neplatná IP adresa</Badge>
                  </div>
                )}
              </div>

              {validationResults.isValid && (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <span className="font-medium">Typ:</span>{" "}
                      <Badge variant="outline">{validationResults.ipType.toUpperCase()}</Badge>
                    </div>
                    <div>
                      <span className="font-medium">Síť:</span>{" "}
                      <Badge variant={validationResults.networkType === 'Veřejná' ? 'default' : 'secondary'}>
                        {validationResults.networkType}
                      </Badge>
                    </div>
                    <div>
                      <span className="font-medium">Využití:</span>{" "}
                      <Badge variant="outline">
                        {validationResults.usage === "residential" ? "Rezidenční" :
                         validationResults.usage === "business" ? "Firemní" :
                         validationResults.usage === "hosting" ? "Hosting" :
                         validationResults.usage === "vpn" ? "VPN" :
                         validationResults.usage === "proxy" ? "Proxy" :
                         validationResults.usage === "tor_exit_node" ? "Tor Exit Node" :
                         validationResults.usage === "mobile" ? "Mobilní" :
                         validationResults.usage === "datacenter" ? "Datacenter" :
                         "Jiné využití"}
                      </Badge>
                    </div>
                  </div>

                  <div>
                    <span className="font-medium">Hostname:</span> {validationResults.hostname}
                  </div>

                  <div className="border-t pt-3">
                    <h4 className="font-semibold mb-2">Geolokace</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      <div>
                        <span className="font-medium">Země:</span> {validationResults.geoLocation.country}
                      </div>
                      <div>
                        <span className="font-medium">Region:</span> {validationResults.geoLocation.region}
                      </div>
                      <div>
                        <span className="font-medium">Město:</span> {validationResults.geoLocation.city}
                      </div>
                      <div>
                        <span className="font-medium">PSČ:</span> {validationResults.geoLocation.postalCode || 'Neuvedeno'}
                      </div>
                      <div>
                        <span className="font-medium">ISP:</span> {validationResults.geoLocation.isp}
                      </div>
                      <div>
                        <span className="font-medium">Organizace:</span> {validationResults.geoLocation.organization}
                      </div>
                      <div>
                        <span className="font-medium">ASN:</span> {validationResults.geoLocation.asn || 'Neuvedeno'}
                      </div>
                      <div>
                        <span className="font-medium">Časové pásmo:</span> {validationResults.geoLocation.timezone || 'Neuvedeno'}
                      </div>
                    </div>
                  </div>

                  {validationResults.networkType === 'Veřejná' && validationResults.geoLocation.latitude && validationResults.geoLocation.longitude && (
                    <div className="border-t pt-3">
                      <h4 className="font-semibold mb-2">Mapa</h4>
                      <div className="w-full h-[200px] border rounded-md overflow-hidden">
                        <iframe
                          width="100%"
                          height="100%"
                          frameBorder="0"
                          scrolling="no"
                          marginHeight={0}
                          marginWidth={0}
                          src={`https://www.openstreetmap.org/export/embed.html?bbox=${validationResults.geoLocation.longitude-0.01},${validationResults.geoLocation.latitude-0.01},${validationResults.geoLocation.longitude+0.01},${validationResults.geoLocation.latitude+0.01}&layer=mapnik&marker=${validationResults.geoLocation.latitude},${validationResults.geoLocation.longitude}`}
                          style={{ border: '1px solid #ddd', borderRadius: '4px' }}
                        />
                      </div>
                      <div className="mt-1 text-xs text-muted-foreground text-right">
                        <a
                          href={`https://www.openstreetmap.org/?mlat=${validationResults.geoLocation.latitude}&mlon=${validationResults.geoLocation.longitude}#map=15/${validationResults.geoLocation.latitude}/${validationResults.geoLocation.longitude}`}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          Zobrazit větší mapu
                        </a>
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <p className="text-sm text-muted-foreground">
          Validace používá API ipinfo.io pro získání geolokačních dat.
        </p>
      </CardFooter>
    </Card>
  );
}
