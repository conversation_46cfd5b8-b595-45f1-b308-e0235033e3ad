import { Timestamp } from "firebase/firestore";
import { PhotoMetadata } from "."; // Assuming PhotoMetadata is in index.ts

export interface LinkedInExperience {
  id: string;
  title: string;
  companyName: string;
  companyUrl?: string;
  location?: string;
  startDate?: string | Timestamp;
  endDate?: string | Timestamp | null; // null for current
  description?: string;
}

export interface LinkedInEducation {
  id: string;
  schoolName: string;
  schoolUrl?: string;
  degree?: string;
  fieldOfStudy?: string;
  startDate?: string | Timestamp;
  endDate?: string | Timestamp | null;
  description?: string;
}

export interface LinkedInSkill {
  id: string;
  name: string;
  endorsements?: number;
}

export interface LinkedInRecommendation {
  id: string;
  recommenderName: string;
  recommenderProfileUrl?: string;
  relationship?: string;
  text: string;
  date?: string | Timestamp;
}

export interface LinkedInModuleData {
  subjectId: string;
  profileUrl?: string;
  fullName?: string;
  headline?: string;
  location?: string;
  summary?: string;
  profilePictureUrl?: string;
  profilePictureBase64?: string;
  profilePictureStoragePath?: string;
  connectionsCount?: number;
  experiences?: LinkedInExperience[];
  education?: LinkedInEducation[];
  skills?: LinkedInSkill[];
  recommendationsReceived?: LinkedInRecommendation[];
  recommendationsGiven?: LinkedInRecommendation[];
  notes?: string;
  osintNotes?: string;
  lastUpdatedAt: Timestamp | null;
  createdAt: Timestamp | null;
} 