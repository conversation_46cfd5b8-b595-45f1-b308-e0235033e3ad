import { db } from '@/lib/firebase';
import { doc, getDoc, collection, getDocs } from 'firebase/firestore';

export interface CaseData {
  id: string;
  name: string;
  description?: string;
  personalInfo?: any;
  company?: any;
  realEstate?: any[];
  training?: any;
  emailAnalysis?: any[];
  phoneNumbers?: any[];
  ipAddresses?: any[];
  mapOverlays?: any;
  facebookData?: any[];
  instagramData?: any[];
  createdAt: Date;
  updatedAt: Date;
}

export class DataCollector {
  async collectCaseData(caseId: string): Promise<CaseData> {
    try {
      // Získání základních informací o případu
      const caseDocRef = doc(db, 'cases', caseId);
      const caseDoc = await getDoc(caseDocRef);

      if (!caseDoc.exists()) {
        throw new Error(`Případ s ID ${caseId} nebyl nalezen`);
      }

      const caseInfo = { id: caseDoc.id, ...caseDoc.data() };

      // Získání subjektů případu
      const subjectsCollectionRef = collection(db, 'cases', caseId, 'subjects');
      const subjectsSnapshot = await getDocs(subjectsCollectionRef);

      const subjects: any[] = [];
      for (const subjectDoc of subjectsSnapshot.docs) {
        const subjectData = { id: subjectDoc.id, ...subjectDoc.data() };

        // Získání dat modulů pro každý subjekt
        const moduleDataCollectionRef = collection(db, 'cases', caseId, 'subjects', subjectDoc.id, 'moduleData');
        const moduleDataSnapshot = await getDocs(moduleDataCollectionRef);

        const moduleData: any = {};
        moduleDataSnapshot.forEach(moduleDoc => {
          moduleData[moduleDoc.id] = moduleDoc.data();
        });

        subjects.push({
          ...subjectData,
          moduleData
        });
      }

      // Transformace dat do jednotného formátu
      const caseData: CaseData = {
        id: caseInfo.id,
        name: caseInfo.title || caseInfo.name || 'Neznámý případ',
        description: caseInfo.description || undefined,
        createdAt: caseInfo.createdAt?.toDate() || new Date(),
        updatedAt: caseInfo.updatedAt?.toDate() || new Date(),
        personalInfo: this.extractPersonalInfo(subjects),
        company: this.extractCompany(subjects),
        realEstate: this.extractRealEstate(subjects),
        training: this.extractTraining(subjects),
        emailAnalysis: this.extractEmailAnalysis(subjects),
        phoneNumbers: this.extractPhoneNumbers(subjects),
        ipAddresses: this.extractIpAddresses(subjects),
        mapOverlays: this.extractMapOverlays(subjects),
        facebookData: this.extractFacebookData(subjects),
        instagramData: this.extractInstagramData(subjects)
      };

      return caseData;
    } catch (error) {
      console.error('Chyba při sběru dat případu:', error);
      throw new Error('Nepodařilo se získat data případu');
    }
  }

  private extractPersonalInfo(subjects: any[]) {
    // Najdeme první fyzickou osobu s daty z evidence obyvatel
    const physicalSubject = subjects.find(s => s.type === 'physical' && s.moduleData?.evidence_obyvatel);
    if (!physicalSubject) return null;

    const evidenceData = physicalSubject.moduleData.evidence_obyvatel;
    return {
      firstName: physicalSubject.firstName || evidenceData.firstName,
      lastName: physicalSubject.lastName || evidenceData.lastName,
      birthDate: physicalSubject.dateOfBirth || evidenceData.birthDate,
      birthPlace: evidenceData.birthPlace,
      nationality: evidenceData.nationality,
      idNumber: evidenceData.idNumber,
      addresses: evidenceData.addresses || [],
      contacts: evidenceData.contacts || [],
      documents: evidenceData.documents || [],
      notes: evidenceData.notes
    };
  }

  private extractCompany(subjects: any[]) {
    // Najdeme první právnickou osobu nebo subjekt s podnikatelskými aktivitami
    const legalSubject = subjects.find(s => s.type === 'legal' || s.moduleData?.business_activity);
    if (!legalSubject) return null;

    const businessData = legalSubject.moduleData?.business_activity;
    return {
      name: legalSubject.name || businessData?.companyName,
      ico: legalSubject.companyId || businessData?.ico,
      dic: businessData?.dic,
      address: businessData?.address,
      legalForm: businessData?.legalForm,
      establishedDate: businessData?.establishedDate,
      capital: businessData?.capital,
      employees: businessData?.employees,
      activities: businessData?.activities || [],
      representatives: businessData?.representatives || [],
      notes: businessData?.notes
    };
  }

  private extractRealEstate(subjects: any[]) {
    const properties: any[] = [];

    subjects.forEach(subject => {
      const cadastreData = subject.moduleData?.cadastre;
      if (cadastreData && cadastreData.properties) {
        cadastreData.properties.forEach((property: any) => {
          properties.push({
            id: property.id,
            type: property.type,
            address: property.address,
            cadastralNumber: property.cadastralNumber,
            area: property.area,
            value: property.value,
            owner: property.owner,
            coOwners: property.coOwners || [],
            encumbrances: property.encumbrances || [],
            photos: property.photos || [],
            notes: property.notes
          });
        });
      }
    });

    return properties;
  }

  private extractTraining(subjects: any[]) {
    // Kombinujeme data o výcviku ze všech subjektů
    const combinedTraining = {
      policeTraining: [] as any[],
      militaryTraining: [] as any[],
      combatSports: [] as any[],
      securityRisks: [] as any[],
      notes: ''
    };

    subjects.forEach(subject => {
      const trainingData = subject.moduleData?.training;
      if (trainingData) {
        if (trainingData.policeTraining) combinedTraining.policeTraining.push(...trainingData.policeTraining);
        if (trainingData.militaryTraining) combinedTraining.militaryTraining.push(...trainingData.militaryTraining);
        if (trainingData.combatSports) combinedTraining.combatSports.push(...trainingData.combatSports);
        if (trainingData.securityRisks) combinedTraining.securityRisks.push(...trainingData.securityRisks);
        if (trainingData.notes) combinedTraining.notes += (combinedTraining.notes ? '\n' : '') + trainingData.notes;
      }
    });

    // Vrátíme null pokud nejsou žádná data
    if (combinedTraining.policeTraining.length === 0 &&
        combinedTraining.militaryTraining.length === 0 &&
        combinedTraining.combatSports.length === 0 &&
        combinedTraining.securityRisks.length === 0 &&
        !combinedTraining.notes) {
      return null;
    }

    return combinedTraining;
  }

  private extractEmailAnalysis(subjects: any[]) {
    const emails: any[] = [];

    subjects.forEach(subject => {
      const emailData = subject.moduleData?.email_analysis;
      if (emailData && emailData.emails) {
        emailData.emails.forEach((email: any) => {
          emails.push({
            id: email.id,
            address: email.address,
            provider: email.provider,
            isValid: email.isValid,
            breaches: email.breaches || [],
            socialMedia: email.socialMedia || [],
            registrations: email.registrations || [],
            photos: email.photos || [],
            notes: email.notes
          });
        });
      }
    });

    return emails;
  }

  private extractPhoneNumbers(subjects: any[]) {
    const phones: any[] = [];

    subjects.forEach(subject => {
      const phoneData = subject.moduleData?.phone_numbers;
      if (phoneData && phoneData.phoneNumbers) {
        phoneData.phoneNumbers.forEach((phone: any) => {
          phones.push({
            id: phone.id,
            number: phone.number,
            type: phone.type,
            operator: phone.operator,
            isValid: phone.isValid,
            location: phone.location,
            registrations: phone.registrations || [],
            photos: phone.photos || [],
            notes: phone.notes
          });
        });
      }
    });

    return phones;
  }

  private extractIpAddresses(subjects: any[]) {
    const ips: any[] = [];

    subjects.forEach(subject => {
      const ipData = subject.moduleData?.network_analysis;
      if (ipData && ipData.ipAddresses) {
        ipData.ipAddresses.forEach((ip: any) => {
          ips.push({
            id: ip.id,
            address: ip.address,
            type: ip.type,
            provider: ip.provider,
            location: ip.location,
            ports: ip.ports || [],
            services: ip.services || [],
            threats: ip.threats || [],
            photos: ip.photos || [],
            notes: ip.notes
          });
        });
      }
    });

    return ips;
  }

  private extractMapOverlays(subjects: any[]) {
    // Kombinujeme mapové překryvy ze všech subjektů
    const combinedOverlays = {
      defaultCenter: [50.0755, 14.4378], // Praha jako výchozí
      defaultZoom: 10,
      defaultBasemap: 'osm',
      points: [] as any[],
      areas: [] as any[],
      routes: [] as any[]
    };

    subjects.forEach(subject => {
      const mapData = subject.moduleData?.map_overlays;
      if (mapData) {
        if (mapData.defaultCenter) combinedOverlays.defaultCenter = mapData.defaultCenter;
        if (mapData.defaultZoom) combinedOverlays.defaultZoom = mapData.defaultZoom;
        if (mapData.defaultBasemap) combinedOverlays.defaultBasemap = mapData.defaultBasemap;
        if (mapData.points) combinedOverlays.points.push(...mapData.points);
        if (mapData.areas) combinedOverlays.areas.push(...mapData.areas);
        if (mapData.routes) combinedOverlays.routes.push(...mapData.routes);
      }
    });

    // Vrátíme null pokud nejsou žádná data
    if (combinedOverlays.points.length === 0 &&
        combinedOverlays.areas.length === 0 &&
        combinedOverlays.routes.length === 0) {
      return null;
    }

    return combinedOverlays;
  }

  private extractFacebookData(subjects: any[]) {
    // Implementace extrakce dat z Facebooku
    return [];
  }

  private extractInstagramData(subjects: any[]) {
    // Implementace extrakce dat z Instagramu
    return [];
  }
}
