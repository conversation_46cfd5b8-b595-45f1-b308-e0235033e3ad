/**
 * <PERSON><PERSON><PERSON> osa - h<PERSON><PERSON><PERSON>
 */

// Načtení CSS stylů pro dialogy
function loadTimelineDialogStyles() {
    // Ko<PERSON><PERSON><PERSON>, zda již není načten
    if (!document.querySelector('link[href="timeline-dialogs.css"]')) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = 'timeline-dialogs.css';
        document.head.appendChild(link);
        console.log('CSS styly pro dialogy časové osy byly načteny');
    }
}

// Načtení stylů při inicializaci
loadTimelineDialogStyles();

// Globální proměnné
let timelineEvents = [];
let timelineSettings = {
    view: 'chronological', // chronological, hierarchical, comparison
    scale: 'months', // years, months, days, hours
    filters: {
        type: 'all',
        source: 'all',
        dateFrom: '',
        dateTo: '',
        keywords: ''
    },
    notifications: {
        email: '',
        frequency: 'daily'
    }
};

/**
 * Inicializace modulu časové osy
 */
function initTimeline() {
    console.log('Inicializace modulu časové osy');

    // Najít aktuální modul časové osy
    const timelineModule = document.querySelector('.module[id^="module-casova-osa"]');
    if (!timelineModule) {
        console.error('Modul časové osy nebyl nalezen');
        return;
    }

    console.log('Nalezen modul časové osy:', timelineModule.id);

    // Inicializace ovládacích prvků
    initTimelineControls(timelineModule);

    // Inicializace filtrů
    initTimelineFilters(timelineModule);

    // Inicializace vizualizace
    initTimelineVisualization(timelineModule);

    // Inicializace analytického panelu
    initTimelineAnalysis(timelineModule);

    // Inicializace fotogalerie
    initTimelineGallery(timelineModule);

    // Inicializace notifikací
    initTimelineNotifications(timelineModule);

    // Načtení uložených dat
    loadTimelineData();
}

/**
 * Inicializace ovládacích prvků časové osy
 * @param {HTMLElement} moduleElement - Element modulu
 */
function initTimelineControls(moduleElement) {
    // Tlačítko pro přidání události
    const addEventButton = moduleElement.querySelector('.add-event');
    if (addEventButton) {
        addEventButton.addEventListener('click', showAddEventDialog);
    }

    // Tlačítko pro import událostí
    const importButton = moduleElement.querySelector('.import-events');
    if (importButton) {
        importButton.addEventListener('click', showImportEventsDialog);
    }

    // Tlačítko pro export časové osy
    const exportButton = moduleElement.querySelector('.export-timeline');
    if (exportButton) {
        exportButton.addEventListener('click', exportTimeline);
    }

    // Přepínače režimů zobrazení
    const viewModeButtons = moduleElement.querySelectorAll('.view-mode');
    viewModeButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Odstranění aktivní třídy ze všech tlačítek
            viewModeButtons.forEach(btn => btn.classList.remove('active'));

            // Přidání aktivní třídy na kliknuté tlačítko
            this.classList.add('active');

            // Nastavení režimu zobrazení
            const viewMode = this.getAttribute('data-view');
            setTimelineView(viewMode);
        });
    });

    // Přepínače měřítka
    const scaleModeButtons = moduleElement.querySelectorAll('.scale-mode');
    scaleModeButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Odstranění aktivní třídy ze všech tlačítek
            scaleModeButtons.forEach(btn => btn.classList.remove('active'));

            // Přidání aktivní třídy na kliknuté tlačítko
            this.classList.add('active');

            // Nastavení měřítka
            const scaleMode = this.getAttribute('data-scale');
            setTimelineScale(scaleMode);
        });
    });
}

/**
 * Inicializace filtrů časové osy
 * @param {HTMLElement} moduleElement - Element modulu
 */
function initTimelineFilters(moduleElement) {
    // Filtr podle typu
    const typeFilter = moduleElement.querySelector('.filter-type');
    if (typeFilter) {
        typeFilter.addEventListener('change', function() {
            timelineSettings.filters.type = this.value;
        });
    }

    // Filtr podle zdroje
    const sourceFilter = moduleElement.querySelector('.filter-source');
    if (sourceFilter) {
        sourceFilter.addEventListener('change', function() {
            timelineSettings.filters.source = this.value;
        });
    }

    // Filtr podle data od
    const dateFromFilter = moduleElement.querySelector('.filter-date-from');
    if (dateFromFilter) {
        dateFromFilter.addEventListener('change', function() {
            timelineSettings.filters.dateFrom = this.value;
        });
    }

    // Filtr podle data do
    const dateToFilter = moduleElement.querySelector('.filter-date-to');
    if (dateToFilter) {
        dateToFilter.addEventListener('change', function() {
            timelineSettings.filters.dateTo = this.value;
        });
    }

    // Filtr podle klíčových slov
    const keywordsFilter = moduleElement.querySelector('.filter-keywords');
    if (keywordsFilter) {
        keywordsFilter.addEventListener('input', function() {
            timelineSettings.filters.keywords = this.value;
        });
    }

    // Tlačítko pro aplikaci filtrů
    const applyFiltersButton = moduleElement.querySelector('.apply-filters');
    if (applyFiltersButton) {
        applyFiltersButton.addEventListener('click', applyTimelineFilters);
    }

    // Tlačítko pro reset filtrů
    const resetFiltersButton = moduleElement.querySelector('.reset-filters');
    if (resetFiltersButton) {
        resetFiltersButton.addEventListener('click', resetTimelineFilters);
    }
}

/**
 * Inicializace vizualizace časové osy
 * @param {HTMLElement} moduleElement - Element modulu
 */
function initTimelineVisualization(moduleElement) {
    // Přidání vertikální čáry do kontejneru událostí
    const eventsContainer = moduleElement.querySelector('.timeline-events');
    if (eventsContainer) {
        const verticalLine = document.createElement('div');
        verticalLine.className = 'timeline-vertical-line';
        eventsContainer.appendChild(verticalLine);
    }

    // Inicializace časové osy
    renderTimelineScale(moduleElement);
}

/**
 * Inicializace analytického panelu
 * @param {HTMLElement} moduleElement - Element modulu
 */
function initTimelineAnalysis(moduleElement) {
    // Tlačítko pro spuštění analýzy
    const runAnalysisButton = moduleElement.querySelector('.run-analysis');
    if (runAnalysisButton) {
        runAnalysisButton.addEventListener('click', runTimelineAnalysis);
    }

    // Tlačítko pro zobrazení/skrytí panelu
    const togglePanelButton = moduleElement.querySelector('.toggle-analysis-panel');
    if (togglePanelButton) {
        togglePanelButton.addEventListener('click', function() {
            const analysisContent = moduleElement.querySelector('.analysis-content');
            if (analysisContent) {
                if (analysisContent.style.display === 'none') {
                    analysisContent.style.display = 'block';
                    this.innerHTML = '<i class="fas fa-chevron-up"></i>';
                } else {
                    analysisContent.style.display = 'none';
                    this.innerHTML = '<i class="fas fa-chevron-down"></i>';
                }
            }
        });
    }

    // Záložky analýzy
    const analysisTabs = moduleElement.querySelectorAll('.analysis-tab');
    analysisTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Odstranění aktivní třídy ze všech záložek
            analysisTabs.forEach(t => t.classList.remove('active'));

            // Přidání aktivní třídy na kliknutou záložku
            this.classList.add('active');

            // Skrytí všech obsahů záložek
            const tabContents = moduleElement.querySelectorAll('.analysis-tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Zobrazení obsahu odpovídající záložky
            const tabName = this.getAttribute('data-tab');
            const tabContent = moduleElement.querySelector(`#${tabName}-tab`);
            if (tabContent) {
                tabContent.classList.add('active');
            }
        });
    });
}

/**
 * Inicializace fotogalerie pro časovou osu
 * @param {HTMLElement} moduleElement - Element modulu
 */
function initTimelineGallery(moduleElement) {
    // Počkáme chvíli, aby se DOM stihlo načíst
    setTimeout(() => {
        const gallery = moduleElement.querySelector('.timeline-photo-gallery');
        if (!gallery) {
            console.warn('Fotogalerie nebyla nalezena v modulu časové osy, zkusíme najít podle ID');
            // Zkusíme najít galerii podle ID, které obsahuje "timeline-photo-gallery"
            const galleries = moduleElement.querySelectorAll('[id*="timeline-photo-gallery"]');
            if (galleries.length > 0) {
                const gallery = galleries[0];
                console.log('Nalezena fotogalerie podle ID:', gallery.id);

                // Inicializace galerie
                initPhotoGallery(moduleElement, gallery, gallery.querySelector('.photo-gallery-add'));

                // Přidání podpory pro Ctrl+V
                document.addEventListener('paste', function(event) {
                    handlePaste(event, gallery);
                });
            } else {
                console.error('Fotogalerie nebyla nalezena v modulu časové osy ani podle ID');
            }
            return;
        }

        // Inicializace galerie
        initPhotoGallery(moduleElement, gallery, gallery.querySelector('.photo-gallery-add'));

        // Přidání podpory pro Ctrl+V
        document.addEventListener('paste', function(event) {
            handlePaste(event, gallery);
        });
    }, 500); // Počkáme 500ms
}

/**
 * Inicializace notifikací pro časovou osu
 * @param {HTMLElement} moduleElement - Element modulu
 */
function initTimelineNotifications(moduleElement) {
    // Tlačítko pro testování emailových alertů
    const testEmailButton = moduleElement.querySelector('.test-email-alert');
    if (testEmailButton) {
        testEmailButton.addEventListener('click', testTimelineEmailAlert);
    }

    // Tlačítko pro uložení nastavení notifikací
    const saveSettingsButton = moduleElement.querySelector('.save-notification-settings');
    if (saveSettingsButton) {
        saveSettingsButton.addEventListener('click', saveTimelineNotificationSettings);
    }
}

/**
 * Načtení uložených dat časové osy
 */
function loadTimelineData() {
    console.log('Načítání uložených dat časové osy');

    // Načtení událostí z localStorage
    const savedEvents = localStorage.getItem('timelineEvents');
    if (savedEvents) {
        try {
            timelineEvents = JSON.parse(savedEvents);
            renderTimelineEvents();
        } catch (error) {
            console.error('Chyba při načítání událostí časové osy:', error);
        }
    }

    // Načtení nastavení z localStorage
    const savedSettings = localStorage.getItem('timelineSettings');
    if (savedSettings) {
        try {
            timelineSettings = JSON.parse(savedSettings);
            updateTimelineUI();
        } catch (error) {
            console.error('Chyba při načítání nastavení časové osy:', error);
        }
    }

    // Pokud nejsou žádné události, zobrazíme prázdný stav
    if (timelineEvents.length === 0) {
        showEmptyState();
    } else {
        hideEmptyState();
    }
}

/**
 * Aktualizace UI časové osy podle aktuálního nastavení
 */
function updateTimelineUI() {
    console.log('Aktualizace UI časové osy');

    const timelineModule = document.querySelector('.module[id^="module-casova-osa"]');
    if (!timelineModule) return;

    // Aktualizace režimu zobrazení
    const viewModeButtons = timelineModule.querySelectorAll('.view-mode');
    viewModeButtons.forEach(button => {
        const viewMode = button.getAttribute('data-view');
        if (viewMode === timelineSettings.view) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }
    });

    // Aktualizace měřítka
    const scaleModeButtons = timelineModule.querySelectorAll('.scale-mode');
    scaleModeButtons.forEach(button => {
        const scaleMode = button.getAttribute('data-scale');
        if (scaleMode === timelineSettings.scale) {
            button.classList.add('active');
        } else {
            button.classList.remove('active');
        }
    });

    // Aktualizace filtrů
    const typeFilter = timelineModule.querySelector('.filter-type');
    if (typeFilter) {
        typeFilter.value = timelineSettings.filters.type;
    }

    const sourceFilter = timelineModule.querySelector('.filter-source');
    if (sourceFilter) {
        sourceFilter.value = timelineSettings.filters.source;
    }

    const dateFromFilter = timelineModule.querySelector('.filter-date-from');
    if (dateFromFilter) {
        dateFromFilter.value = timelineSettings.filters.dateFrom;
    }

    const dateToFilter = timelineModule.querySelector('.filter-date-to');
    if (dateToFilter) {
        dateToFilter.value = timelineSettings.filters.dateTo;
    }

    const keywordsFilter = timelineModule.querySelector('.filter-keywords');
    if (keywordsFilter) {
        keywordsFilter.value = timelineSettings.filters.keywords;
    }

    // Aktualizace notifikací
    const emailInput = timelineModule.querySelector('#timeline-alert-email');
    if (emailInput && timelineSettings.notifications && timelineSettings.notifications.email) {
        emailInput.value = timelineSettings.notifications.email;
    }

    const frequencySelect = timelineModule.querySelector('#timeline-alert-frequency');
    if (frequencySelect && timelineSettings.notifications && timelineSettings.notifications.frequency) {
        frequencySelect.value = timelineSettings.notifications.frequency;
    }

    // Překreslení časové osy
    renderTimelineScale(timelineModule);
    renderTimelineEvents();
}

/**
 * Zobrazení prázdného stavu časové osy
 */
function showEmptyState() {
    console.log('Zobrazení prázdného stavu časové osy');

    const timelineModule = document.querySelector('.module[id^="module-casova-osa"]');
    if (!timelineModule) return;

    // Kontrola, zda již existuje prázdný stav
    let emptyState = timelineModule.querySelector('.timeline-empty-state');

    if (!emptyState) {
        // Vytvoření prázdného stavu
        emptyState = document.createElement('div');
        emptyState.className = 'timeline-empty-state';
        emptyState.innerHTML = `
            <div class="timeline-empty-icon">
                <i class="fas fa-calendar-times"></i>
            </div>
            <div class="timeline-empty-text">
                <h4>Časová osa je prázdná</h4>
                <p>Přidejte události pomocí tlačítka "Přidat událost" nebo importujte události z externích zdrojů.</p>
            </div>
            <div class="timeline-empty-actions">
                <button type="button" class="btn-primary add-event-empty">
                    <i class="fas fa-plus"></i> Přidat událost
                </button>
                <button type="button" class="btn-secondary import-events-empty">
                    <i class="fas fa-file-import"></i> Importovat události
                </button>
            </div>
        `;

        // Přidání prázdného stavu do modulu
        const eventsContainer = timelineModule.querySelector('.timeline-events');
        if (eventsContainer) {
            eventsContainer.appendChild(emptyState);
        }

        // Přidání event listenerů pro tlačítka
        const addEventButton = emptyState.querySelector('.add-event-empty');
        if (addEventButton) {
            addEventButton.addEventListener('click', showAddEventDialog);
        }

        const importEventsButton = emptyState.querySelector('.import-events-empty');
        if (importEventsButton) {
            importEventsButton.addEventListener('click', showImportEventsDialog);
        }
    }

    // Zobrazení prázdného stavu
    emptyState.style.display = 'flex';
}

/**
 * Skrytí prázdného stavu časové osy
 */
function hideEmptyState() {
    console.log('Skrytí prázdného stavu časové osy');

    const timelineModule = document.querySelector('.module[id^="module-casova-osa"]');
    if (!timelineModule) return;

    const emptyState = timelineModule.querySelector('.timeline-empty-state');
    if (emptyState) {
        emptyState.style.display = 'none';
    }
}

/**
 * Uložení dat časové osy
 */
function saveTimelineData() {
    console.log('Ukládání dat časové osy');

    // Uložení událostí do localStorage
    localStorage.setItem('timelineEvents', JSON.stringify(timelineEvents));

    // Uložení nastavení do localStorage
    localStorage.setItem('timelineSettings', JSON.stringify(timelineSettings));
}

/**
 * Spuštění analýzy časové osy
 */
function runTimelineAnalysis() {
    console.log('Spuštění analýzy časové osy');

    // Kontrola, zda existují události k analýze
    if (timelineEvents.length === 0) {
        showNotification('Časová osa neobsahuje žádné události k analýze.', 'warning');
        return;
    }

    // Zobrazení notifikace o zahájení analýzy
    showNotification('Probíhá analýza časové osy...', 'info');

    // Simulace analýzy
    setTimeout(() => {
        // Analýza vzorců
        analyzePatterns();

        // Analýza mezer
        analyzeGaps();

        // Analýza statistik
        analyzeStatistics();

        // Analýza predikcí
        analyzePredictions();

        // Zobrazení notifikace o dokončení analýzy
        showNotification('Analýza časové osy byla úspěšně dokončena.', 'success');
    }, 1000);
}
