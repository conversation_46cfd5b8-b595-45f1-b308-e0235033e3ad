/* Styly pro mapu a jej<PERSON> prvky */

/* Styly pro body na mapě */
.map-point-marker {
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.5));
  z-index: 1000 !important;
}

/* Styly pro zvýraznění bodů */
.map-point-highlight {
  filter: drop-shadow(0 0 8px rgba(0, 0, 0, 0.3));
  z-index: 900 !important;
}

/* Styly pro polygon oblasti */
.map-area-polygon {
  filter: drop-shadow(0 0 10px rgba(0, 0, 0, 0.4));
  z-index: 800 !important;
}

/* Styly pro čáry mezi body */
.map-area-line {
  filter: drop-shadow(0 0 6px rgba(0, 0, 0, 0.4));
  z-index: 850 !important;
}

/* Styly pro tlačítko dokončení kreslení */
.complete-drawing-btn {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 16px;
  font-weight: bold;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  cursor: pointer;
  transition: all 0.2s ease;
  z-index: 9999999 !important;
}

.complete-drawing-btn:hover {
  transform: translateX(-50%) scale(1.05) !important;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

/* Styly pro tooltip bodů */
.leaflet-tooltip {
  font-weight: bold;
  font-size: 14px;
  background-color: rgba(255, 255, 255, 0.9);
  border: 2px solid #333;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
  padding: 4px 8px;
  z-index: 1000 !important;
}

/* Styly pro instrukce kreslení */
.drawing-instructions {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  font-size: 14px;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 9999 !important;
  max-width: 400px;
  margin: 0 auto;
}
