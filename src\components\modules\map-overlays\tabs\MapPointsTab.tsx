"use client";

import { useState, useEffect } from "react";
import { UseFormReturn, useFieldArray } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MapOverlaysModuleFormValues, mapPointTypes } from "../schemas";
import { MapPoint } from "@/types";
import { MapPinIcon, Plus, Trash2, Search, Edit, Eye, Map, Calendar, Tag, FileText } from "lucide-react";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ScrollA<PERSON> } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { PhotoUploadSection } from "../../network-analysis/PhotoUploadSection";

interface MapPointsTabProps {
  form: UseFormReturn<MapOverlaysModuleFormValues>;
}

export function MapPointsTab({ form }: MapPointsTabProps) {
  const [selectedPointId, setSelectedPointId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("list");

  const { control, register, watch } = form;
  const { fields, append, remove, update } = useFieldArray({
    control,
    name: "points",
  });

  const points = watch("points") || [];
  const selectedPointIndex = points.findIndex(p => p.id === selectedPointId);

  // Filtrování bodů podle vyhledávacího výrazu
  const filteredPoints = points.filter(point =>
    point.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (point.description && point.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (point.address && point.address.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Poslouchání události pro otevření detailu bodu z mapy
  useEffect(() => {
    const handleOpenPointDetail = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.pointId) {
        setSelectedPointId(customEvent.detail.pointId);
        setActiveTab("detail");
      }
    };

    window.addEventListener('openPointDetail', handleOpenPointDetail);

    return () => {
      window.removeEventListener('openPointDetail', handleOpenPointDetail);
    };
  }, []);

  // Přidání nového bodu
  const handleAddPoint = () => {
    const newPoint: MapPoint = {
      id: uuidv4(),
      name: "Nový bod",
      pointType: "general",
      latitude: 50.0755, // Praha jako výchozí
      longitude: 14.4378,
      photos: [],
      relatedPoints: [],
      relatedSubjects: [],
    };
    append(newPoint);
    setSelectedPointId(newPoint.id);
    setActiveTab("detail");
  };

  // Odstranění bodu
  const handleRemovePoint = (index: number) => {
    if (window.confirm("Opravdu chcete smazat tento bod?")) {
      remove(index);
      if (selectedPointId === points[index].id) {
        setSelectedPointId(null);
        setActiveTab("list");
      }
    }
  };

  // Výběr bodu pro editaci
  const handleSelectPoint = (id: string) => {
    setSelectedPointId(id);
    setActiveTab("detail");

    // Vyvoláme událost pro výběr bodu na mapě
    window.dispatchEvent(new CustomEvent('selectPointOnMap', { detail: { pointId: id } }));
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Body na mapě</CardTitle>
              <CardDescription>
                Správa bodů zájmu a jejich vlastností
              </CardDescription>
            </div>
            <Button onClick={handleAddPoint}>
              <Plus className="mr-2 h-4 w-4" />
              Přidat bod
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="list">Seznam bodů</TabsTrigger>
              <TabsTrigger value="detail" disabled={selectedPointId === null}>
                Detail bodu
              </TabsTrigger>
            </TabsList>

            <TabsContent value="list" className="pt-4">
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Vyhledat body..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                <ScrollArea className="h-[400px] rounded-md border">
                  {filteredPoints.length === 0 ? (
                    <div className="p-4 text-center text-muted-foreground">
                      Nebyly nalezeny žádné body
                    </div>
                  ) : (
                    <div className="p-4 space-y-2">
                      {filteredPoints.map((point, index) => (
                        <div
                          key={point.id}
                          className="flex items-center justify-between p-2 rounded-md hover:bg-muted cursor-pointer"
                          onClick={() => handleSelectPoint(point.id)}
                        >
                          <div className="flex items-center space-x-2">
                            <div className={`w-2 h-2 rounded-full ${
                              point.pointType === "crime" ? "bg-red-500" :
                              point.pointType === "person" ? "bg-green-500" :
                              point.pointType === "vehicle" ? "bg-yellow-500" :
                              point.pointType === "building" ? "bg-purple-500" :
                              point.pointType === "surveillance" ? "bg-blue-500" :
                              point.pointType === "meeting" ? "bg-orange-500" :
                              point.pointType === "evidence" ? "bg-pink-500" :
                              "bg-gray-500"
                            }`}></div>
                            <div>
                              <div className="font-medium">{point.name}</div>
                              <div className="text-xs text-muted-foreground">
                                {point.address || `${point.latitude.toFixed(4)}, ${point.longitude.toFixed(4)}`}
                              </div>
                            </div>
                          </div>
                          <div className="flex space-x-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleSelectPoint(point.id);
                              }}
                            >
                              <Edit className="h-4 w-4 text-muted-foreground" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRemovePoint(index);
                              }}
                            >
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </div>
            </TabsContent>

            <TabsContent value="detail" className="pt-4">
              {selectedPointId && selectedPointIndex !== -1 && (
                <div className="space-y-4">
                  <Tabs defaultValue="basic">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="basic">Základní informace</TabsTrigger>
                      <TabsTrigger value="location">Poloha</TabsTrigger>
                      <TabsTrigger value="photos">Fotodokumentace</TabsTrigger>
                    </TabsList>

                    <TabsContent value="basic" className="space-y-4 pt-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`points.${selectedPointIndex}.name`}>Název bodu</Label>
                          <Input
                            id={`points.${selectedPointIndex}.name`}
                            {...register(`points.${selectedPointIndex}.name`)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`points.${selectedPointIndex}.pointType`}>Typ bodu</Label>
                          <Select
                            defaultValue={points[selectedPointIndex].pointType}
                            onValueChange={(value) => {
                              const updatedPoint = { ...points[selectedPointIndex], pointType: value as any };
                              update(selectedPointIndex, updatedPoint);
                            }}
                          >
                            <SelectTrigger id={`points.${selectedPointIndex}.pointType`}>
                              <SelectValue placeholder="Vyberte typ bodu" />
                            </SelectTrigger>
                            <SelectContent>
                              {mapPointTypes.map((type) => (
                                <SelectItem key={type} value={type}>
                                  {type === "general" ? "Obecný" :
                                   type === "crime" ? "Trestný čin" :
                                   type === "person" ? "Osoba" :
                                   type === "vehicle" ? "Vozidlo" :
                                   type === "building" ? "Budova" :
                                   type === "surveillance" ? "Sledování" :
                                   type === "meeting" ? "Setkání" :
                                   type === "evidence" ? "Důkaz" :
                                   type === "other" ? "Jiný" : type}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      {points[selectedPointIndex].pointType === "other" && (
                        <div className="space-y-2">
                          <Label htmlFor={`points.${selectedPointIndex}.otherPointTypeDetail`}>
                            Upřesnění typu
                          </Label>
                          <Input
                            id={`points.${selectedPointIndex}.otherPointTypeDetail`}
                            {...register(`points.${selectedPointIndex}.otherPointTypeDetail`)}
                            placeholder="Zadejte vlastní typ bodu"
                          />
                        </div>
                      )}

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`points.${selectedPointIndex}.date`}>Datum</Label>
                          <Input
                            id={`points.${selectedPointIndex}.date`}
                            type="date"
                            {...register(`points.${selectedPointIndex}.date`)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`points.${selectedPointIndex}.category`}>Kategorie</Label>
                          <Input
                            id={`points.${selectedPointIndex}.category`}
                            {...register(`points.${selectedPointIndex}.category`)}
                            placeholder="Např. Důležitý, Podezřelý, ..."
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`points.${selectedPointIndex}.description`}>Popis</Label>
                        <Textarea
                          id={`points.${selectedPointIndex}.description`}
                          {...register(`points.${selectedPointIndex}.description`)}
                          placeholder="Zadejte popis bodu..."
                          rows={4}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`points.${selectedPointIndex}.notes`}>Poznámky</Label>
                        <Textarea
                          id={`points.${selectedPointIndex}.notes`}
                          {...register(`points.${selectedPointIndex}.notes`)}
                          placeholder="Zadejte poznámky k bodu..."
                          rows={4}
                        />
                      </div>
                    </TabsContent>

                    <TabsContent value="location" className="space-y-4 pt-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`points.${selectedPointIndex}.latitude`}>Zeměpisná šířka</Label>
                          <Input
                            id={`points.${selectedPointIndex}.latitude`}
                            type="number"
                            step="0.000001"
                            {...register(`points.${selectedPointIndex}.latitude`, {
                              valueAsNumber: true,
                            })}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`points.${selectedPointIndex}.longitude`}>Zeměpisná délka</Label>
                          <Input
                            id={`points.${selectedPointIndex}.longitude`}
                            type="number"
                            step="0.000001"
                            {...register(`points.${selectedPointIndex}.longitude`, {
                              valueAsNumber: true,
                            })}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`points.${selectedPointIndex}.address`}>Adresa</Label>
                        <Input
                          id={`points.${selectedPointIndex}.address`}
                          {...register(`points.${selectedPointIndex}.address`)}
                          placeholder="Zadejte adresu bodu"
                        />
                      </div>

                      <div className="w-full h-[200px] bg-muted rounded-md relative overflow-hidden">
                        <div
                          className="w-full h-full"
                          style={{
                            backgroundImage: `url(https://api.mapbox.com/styles/v1/mapbox/streets-v11/static/${points[selectedPointIndex].longitude},${points[selectedPointIndex].latitude},15,0/800x400?access_token=pk.eyJ1IjoiZGVtb3VzZXIiLCJhIjoiY2txOHkwdWUzMDkzcjJvcWk2ZzFzZ2h6ZSJ9.TxMGHqwYLO1QnfEUL8F7_A)`,
                            backgroundSize: 'cover',
                            backgroundPosition: 'center'
                          }}
                        >
                          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                            <MapPinIcon className="h-8 w-8 text-red-500" />
                          </div>
                        </div>
                      </div>

                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={() => {
                          // Přepneme na záložku s mapou
                          const tabsElement = document.querySelector('[role="tablist"]');
                          if (tabsElement) {
                            const viewTabButton = tabsElement.querySelector('[value="view"]') as HTMLButtonElement;
                            if (viewTabButton) {
                              viewTabButton.click();
                            }
                          }
                        }}
                      >
                        <Map className="mr-2 h-4 w-4" />
                        Vybrat polohu na mapě
                      </Button>
                    </TabsContent>

                    <TabsContent value="photos" className="pt-4">
                      <PhotoUploadSection
                        control={control}
                        entityIndex={selectedPointIndex}
                        entityType="points"
                      />
                    </TabsContent>
                  </Tabs>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="border-t pt-4">
          <div className="flex justify-between w-full">
            <div className="text-sm text-muted-foreground">
              Celkem bodů: {points.length}
            </div>
            {selectedPointId && (
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedPointId(null);
                  setActiveTab("list");
                }}
              >
                Zpět na seznam
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
