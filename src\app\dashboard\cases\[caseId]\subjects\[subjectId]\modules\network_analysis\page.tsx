import { Metadata } from "next";
import { notFound } from "next/navigation";
import { db } from "@/lib/firebase";
import { doc, getDoc } from "firebase/firestore";
import { NetworkAnalysisModule } from "@/components/modules/network-analysis/NetworkAnalysisModule";
import { getNetworkAnalysisData } from "@/lib/firebase/firestore";
import { Subject } from "@/types";

export const metadata: Metadata = {
  title: "IP adresy a síťová analýza | OSINT",
  description: "Modul pro analýzu IP adres a síťové aktivity",
};

async function getSubject(caseId: string, subjectId: string) {
  try {
    const subjectRef = doc(db, "cases", caseId, "subjects", subjectId);
    const subjectSnap = await getDoc(subjectRef);

    if (!subjectSnap.exists()) {
      return null;
    }

    return {
      id: subjectSnap.id,
      ...subjectSnap.data(),
    } as Subject;
  } catch (error) {
    console.error("Error fetching subject:", error);
    return null;
  }
}

interface NetworkAnalysisPageProps {
  params: {
    caseId: string;
    subjectId: string;
  };
}

export default async function NetworkAnalysisPage({
  params,
}: NetworkAnalysisPageProps) {
  try {
    const { caseId, subjectId } = params;
    
    const subject = await getSubject(caseId, subjectId);
    
    if (!subject) {
      notFound();
    }

    const networkAnalysisData = await getNetworkAnalysisData(caseId, subjectId);

    return (
      <div className="container mx-auto py-6">
        <NetworkAnalysisModule
          caseId={caseId}
          subject={subject}
          initialData={networkAnalysisData || undefined}
        />
      </div>
    );
  } catch (error) {
    console.error("Error in NetworkAnalysisPage:", error);
    return (
      <div className="container mx-auto py-6">
        <div className="bg-destructive/10 p-4 rounded-md text-destructive">
          <h2 className="text-lg font-semibold">Chyba při načítání dat</h2>
          <p>Nepodařilo se načíst data pro síťovou analýzu. Zkuste to prosím později.</p>
        </div>
      </div>
    );
  }
}
