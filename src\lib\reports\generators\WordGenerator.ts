import { CaseData } from '../DataCollector';

interface GenerateParams {
  caseData: CaseData;
  settings: any;
  modules: string[];
}

export class WordGenerator {
  async generate(params: GenerateParams): Promise<Buffer> {
    // Pro Word generování budeme používat docx knihovnu
    // Zatím vrátíme HTML obsah jako mock
    const { caseData, settings, modules } = params;

    // Vytvoření HTML obsahu pro Word dokument
    const htmlContent = this.generateHTMLContent(caseData, settings, modules);

    // Konverze na Buffer
    return Buffer.from(htmlContent, 'utf-8');
  }

  private generateHTMLContent(caseData: CaseData, settings: any, modules: string[]): string {
    return `
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <title>OSINT Report - ${settings.subject || caseData.name}</title>
    <style>
        body { font-family: 'Times New Roman', serif; font-size: 12pt; line-height: 1.4; }
        .header { text-align: center; margin-bottom: 30px; }
        .title { font-size: 18pt; font-weight: bold; margin: 20px 0; }
        .section { margin: 20px 0; }
        .section h2 { color: #1a3c89; font-size: 14pt; margin-bottom: 10px; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        td { padding: 5px; border-bottom: 1px solid #eee; }
        .label { font-weight: bold; width: 30%; }
    </style>
</head>
<body>
    <div class="header">
        <h1>POLICIE ČESKÉ REPUBLIKY</h1>
        <div class="title">${settings.title}</div>
        <div>${settings.subject}</div>
    </div>

    ${modules.map(moduleId => this.generateModuleHTML(moduleId, caseData)).join('')}
</body>
</html>`;
  }

  private generateWordContent(caseData: CaseData, settings: any, modules: string[]): string {
    // Základní XML struktura pro Word dokument
    return `<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
  <w:body>
    <w:p>
      <w:pPr>
        <w:jc w:val="center"/>
        <w:rPr>
          <w:b/>
          <w:sz w:val="28"/>
        </w:rPr>
      </w:pPr>
      <w:r>
        <w:rPr>
          <w:b/>
          <w:sz w:val="28"/>
        </w:rPr>
        <w:t>POLICIE ČESKÉ REPUBLIKY</w:t>
      </w:r>
    </w:p>

    <w:p>
      <w:pPr>
        <w:jc w:val="center"/>
        <w:rPr>
          <w:sz w:val="24"/>
        </w:rPr>
      </w:pPr>
      <w:r>
        <w:rPr>
          <w:sz w:val="24"/>
        </w:rPr>
        <w:t>${settings.title}</w:t>
      </w:r>
    </w:p>

    <w:p>
      <w:pPr>
        <w:jc w:val="center"/>
        <w:rPr>
          <w:sz w:val="20"/>
        </w:rPr>
      </w:pPr>
      <w:r>
        <w:rPr>
          <w:sz w:val="20"/>
        </w:rPr>
        <w:t>${settings.subject}</w:t>
      </w:r>
    </w:p>

    ${this.generateModulesContent(caseData, modules)}

  </w:body>
</w:document>`;
  }

  private generateModulesContent(caseData: CaseData, modules: string[]): string {
    let content = '';

    modules.forEach(moduleId => {
      content += this.generateModuleWordContent(moduleId, caseData);
    });

    return content;
  }

  private generateModuleWordContent(moduleId: string, caseData: CaseData): string {
    const moduleName = this.getModuleName(moduleId);

    return `
    <w:p>
      <w:pPr>
        <w:rPr>
          <w:b/>
          <w:sz w:val="22"/>
        </w:rPr>
      </w:pPr>
      <w:r>
        <w:rPr>
          <w:b/>
          <w:sz w:val="22"/>
        </w:rPr>
        <w:t>${moduleName}</w:t>
      </w:r>
    </w:p>

    ${this.getModuleData(moduleId, caseData)}
    `;
  }

  private getModuleData(moduleId: string, caseData: CaseData): string {
    switch (moduleId) {
      case 'personal-info':
        return this.generatePersonalInfoWord(caseData.personalInfo);
      case 'company':
        return this.generateCompanyWord(caseData.company);
      case 'real-estate':
        return this.generateRealEstateWord(caseData.realEstate);
      case 'training':
        return this.generateTrainingWord(caseData.training);
      case 'email-analysis':
        return this.generateEmailAnalysisWord(caseData.emailAnalysis);
      case 'phone-numbers':
        return this.generatePhoneNumbersWord(caseData.phoneNumbers);
      case 'ip-addresses':
        return this.generateIpAddressesWord(caseData.ipAddresses);
      case 'map-overlays':
        return this.generateMapOverlaysWord(caseData.mapOverlays);
      case 'facebook':
        return this.generateFacebookWord(caseData.facebookData);
      case 'instagram':
        return this.generateInstagramWord(caseData.instagramData);
      default:
        return '';
    }
  }

  private generatePersonalInfoWord(data: any): string {
    if (!data) return '';

    return `
    <w:p>
      <w:r>
        <w:t>Jméno: ${data.firstName || ''} ${data.lastName || ''}</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>Datum narození: ${data.birthDate ? new Date(data.birthDate).toLocaleDateString('cs-CZ') : ''}</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>Místo narození: ${data.birthPlace || ''}</w:t>
      </w:r>
    </w:p>
    `;
  }

  private generateCompanyWord(data: any): string {
    if (!data) return '';

    return `
    <w:p>
      <w:r>
        <w:t>Název: ${data.name || ''}</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>IČO: ${data.ico || ''}</w:t>
      </w:r>
    </w:p>
    <w:p>
      <w:r>
        <w:t>DIČ: ${data.dic || ''}</w:t>
      </w:r>
    </w:p>
    `;
  }

  private generateRealEstateWord(data: any[]): string {
    if (!data || data.length === 0) return '';

    let content = '';
    data.forEach((property, index) => {
      content += `
      <w:p>
        <w:r>
          <w:t>Nemovitost ${index + 1}: ${property.type || ''}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Adresa: ${property.address || ''}</w:t>
        </w:r>
      </w:p>
      `;
    });

    return content;
  }

  private generateTrainingWord(data: any): string {
    if (!data) return '';

    let content = '';

    if (data.policeTraining && data.policeTraining.length > 0) {
      content += `
      <w:p>
        <w:r>
          <w:rPr><w:b/></w:rPr>
          <w:t>Policejní výcvik:</w:t>
        </w:r>
      </w:p>
      `;

      data.policeTraining.forEach((training: any) => {
        content += `
        <w:p>
          <w:r>
            <w:t>- ${training.rank || ''} ${training.unit ? `(${training.unit})` : ''}</w:t>
          </w:r>
        </w:p>
        `;
      });
    }

    return content;
  }

  private generateEmailAnalysisWord(data: any[]): string {
    if (!data || data.length === 0) return '';

    let content = '';
    data.forEach((email) => {
      content += `
      <w:p>
        <w:r>
          <w:t>Email: ${email.address}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Poskytovatel: ${email.provider || ''}</w:t>
        </w:r>
      </w:p>
      `;
    });

    return content;
  }

  private generatePhoneNumbersWord(data: any[]): string {
    if (!data || data.length === 0) return '';

    let content = '';
    data.forEach((phone) => {
      content += `
      <w:p>
        <w:r>
          <w:t>Telefon: ${phone.number}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Operátor: ${phone.operator || ''}</w:t>
        </w:r>
      </w:p>
      `;
    });

    return content;
  }

  private generateIpAddressesWord(data: any[]): string {
    if (!data || data.length === 0) return '';

    let content = '';
    data.forEach((ip) => {
      content += `
      <w:p>
        <w:r>
          <w:t>IP adresa: ${ip.address}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Poskytovatel: ${ip.provider || ''}</w:t>
        </w:r>
      </w:p>
      `;
    });

    return content;
  }

  private generateMapOverlaysWord(data: any): string {
    if (!data) return '';

    let content = `
    <w:p>
      <w:r>
        <w:t>Mapové překryvy</w:t>
      </w:r>
    </w:p>
    `;

    if (data.points && data.points.length > 0) {
      content += `
      <w:p>
        <w:r>
          <w:t>Počet bodů: ${data.points.length}</w:t>
        </w:r>
      </w:p>
      `;
    }

    return content;
  }

  private generateFacebookWord(data: any[]): string {
    if (!data || data.length === 0) return '';

    let content = '';

    data.forEach((profile) => {
      content += `
      <w:p>
        <w:pPr>
          <w:rPr>
            <w:b/>
          </w:rPr>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:b/>
          </w:rPr>
          <w:t>Facebook profil: ${profile.profileName || profile.username || 'Neurčený název'}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Profilové jméno: ${profile.profileName || ''}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Uživatelské jméno: ${profile.username || ''}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>URL profilu: ${profile.profileUrl || ''}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Bydliště: ${profile.currentLocation || ''}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Zaměstnání: ${profile.currentJob || ''}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Vzdělání: ${profile.education || ''}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Vztahový stav: ${profile.relationshipStatus || ''}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Počet přátel: ${profile.friendsCount || 'Neznámý'}</w:t>
        </w:r>
      </w:p>
      `;

      if (profile.aboutMe) {
        content += `
        <w:p>
          <w:pPr>
            <w:rPr>
              <w:b/>
            </w:rPr>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:b/>
            </w:rPr>
            <w:t>O osobě:</w:t>
          </w:r>
        </w:p>
        <w:p>
          <w:r>
            <w:t>${profile.aboutMe}</w:t>
          </w:r>
        </w:p>
        `;
      }

      if (profile.contactInfo) {
        content += `
        <w:p>
          <w:pPr>
            <w:rPr>
              <w:b/>
            </w:rPr>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:b/>
            </w:rPr>
            <w:t>Kontaktní údaje:</w:t>
          </w:r>
        </w:p>
        <w:p>
          <w:r>
            <w:t>${profile.contactInfo}</w:t>
          </w:r>
        </w:p>
        `;
      }

      if (profile.investigationNotes) {
        content += `
        <w:p>
          <w:pPr>
            <w:rPr>
              <w:b/>
            </w:rPr>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:b/>
            </w:rPr>
            <w:t>Poznámky k vyšetřování:</w:t>
          </w:r>
        </w:p>
        <w:p>
          <w:r>
            <w:t>${profile.investigationNotes}</w:t>
          </w:r>
        </w:p>
        `;
      }

      // Příspěvky
      if (profile.posts && profile.posts.length > 0) {
        content += `
        <w:p>
          <w:pPr>
            <w:rPr>
              <w:b/>
            </w:rPr>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:b/>
            </w:rPr>
            <w:t>Příspěvky:</w:t>
          </w:r>
        </w:p>
        `;

        profile.posts.forEach((post) => {
          content += `
          <w:p>
            <w:pPr>
              <w:rPr>
                <w:b/>
              </w:rPr>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:b/>
              </w:rPr>
              <w:t>${post.date ? `Datum: ${post.date}` : 'Datum neznámé'}</w:t>
            </w:r>
          </w:p>
          <w:p>
            <w:r>
              <w:t>${post.text || ''}</w:t>
            </w:r>
          </w:p>
          `;
        });
      }
    });

    return content;
  }

  private generateInstagramWord(data: any[]): string {
    if (!data || data.length === 0) return '';

    let content = '';

    data.forEach((profile) => {
      content += `
      <w:p>
        <w:pPr>
          <w:rPr>
            <w:b/>
          </w:rPr>
        </w:pPr>
        <w:r>
          <w:rPr>
            <w:b/>
          </w:rPr>
          <w:t>Instagram profil: ${profile.displayName || profile.username || 'Neurčený název'}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Jméno na profilu: ${profile.displayName || ''}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Uživatelské jméno: @${profile.username || ''}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>URL profilu: ${profile.profileUrl || ''}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Počet sledujících: ${profile.followersCount || 'Neznámý'}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Sleduje účtů: ${profile.followingCount || 'Neznámý'}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Počet příspěvků: ${profile.postsCount || 'Neznámý'}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Ověřený účet: ${profile.isVerified ? 'Ano' : 'Ne'}</w:t>
        </w:r>
      </w:p>
      <w:p>
        <w:r>
          <w:t>Soukromý účet: ${profile.isPrivate ? 'Ano' : 'Ne'}</w:t>
        </w:r>
      </w:p>
      `;

      if (profile.bio) {
        content += `
        <w:p>
          <w:pPr>
            <w:rPr>
              <w:b/>
            </w:rPr>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:b/>
            </w:rPr>
            <w:t>Bio:</w:t>
          </w:r>
        </w:p>
        <w:p>
          <w:r>
            <w:t>${profile.bio}</w:t>
          </w:r>
        </w:p>
        `;
      }

      if (profile.website) {
        content += `
        <w:p>
          <w:pPr>
            <w:rPr>
              <w:b/>
            </w:rPr>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:b/>
            </w:rPr>
            <w:t>Webová stránka:</w:t>
          </w:r>
        </w:p>
        <w:p>
          <w:r>
            <w:t>${profile.website}</w:t>
          </w:r>
        </w:p>
        `;
      }

      // Příspěvky
      if (profile.posts && profile.posts.length > 0) {
        content += `
        <w:p>
          <w:pPr>
            <w:rPr>
              <w:b/>
            </w:rPr>
          </w:pPr>
          <w:r>
            <w:rPr>
              <w:b/>
            </w:rPr>
            <w:t>Příspěvky:</w:t>
          </w:r>
        </w:p>
        `;

        profile.posts.forEach((post) => {
          content += `
          <w:p>
            <w:pPr>
              <w:rPr>
                <w:b/>
              </w:rPr>
            </w:pPr>
            <w:r>
              <w:rPr>
                <w:b/>
              </w:rPr>
              <w:t>${post.date ? `Datum: ${post.date}` : 'Datum neznámé'}</w:t>
            </w:r>
          </w:p>
          <w:p>
            <w:r>
              <w:t>${post.caption || ''}</w:t>
            </w:r>
          </w:p>
          `;
        });
      }
    });

    return content;
  }

  private getModuleName(moduleId: string): string {
    switch (moduleId) {
      case 'personal-info': return 'Osobní údaje';
      case 'company': return 'Informace o firmě';
      case 'real-estate': return 'Nemovitosti';
      case 'training': return 'Výcvik a bezpečnostní rizika';
      case 'email-analysis': return 'Emailové adresy';
      case 'phone-numbers': return 'Telefonní čísla';
      case 'ip-addresses': return 'IP adresy';
      case 'map-overlays': return 'Mapové překryvy';
      case 'facebook': return 'Facebook profily';
      case 'instagram': return 'Instagram profily';
      default: return `Modul ${moduleId}`;
    }
  }

  private generateModuleHTML(moduleId: string, caseData: CaseData): string {
    const moduleName = this.getModuleName(moduleId);
    const moduleData = this.getModuleData(moduleId, caseData);

    if (!moduleData) return '';

    return `
    <div class="section">
        <h2>${moduleName}</h2>
        ${moduleData}
    </div>`;
  }
}
