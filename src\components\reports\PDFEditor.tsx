"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Settings, Download, Palette, Layout, Type, Image } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { LoadingButton } from '@/components/ui/loading';

interface PDFEditorSettings {
  // Základní nastavení
  title: string;
  subject: string;
  jid: string;
  documentNumber: string;
  location: string;
  date: string;
  department: string;
  purpose: string;
  
  // Styling
  style: 'official' | 'modern';
  fontSize: number;
  lineHeight: number;
  margins: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };
  
  // Barvy
  primaryColor: string;
  secondaryColor: string;
  textColor: string;
  backgroundColor: string;
  
  // Rozložení
  headerHeight: number;
  footerHeight: number;
  showPageNumbers: boolean;
  pageNumberPosition: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  showBorders: boolean;
  borderWidth: number;
  borderColor: string;
  
  // Fotografie
  photoSize: 'small' | 'medium' | 'large';
  photoAlignment: 'left' | 'center' | 'right';
  photoSpacing: number;
}

interface PDFEditorProps {
  caseId: string;
  onGenerate?: (settings: PDFEditorSettings) => void;
}

export default function PDFEditor({ caseId, onGenerate }: PDFEditorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const { toast } = useToast();

  const [settings, setSettings] = useState<PDFEditorSettings>({
    // Základní nastavení
    title: 'OSINT (Open Source Intelligence)',
    subject: '',
    jid: '',
    documentNumber: '',
    location: 'Pardubice',
    date: new Date().toLocaleDateString('cs-CZ', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }),
    department: `Odbor analytiky a kyber. kriminality\nOddělení kybernetické kriminality\nNa Spravedlnosti 2516, 530 48 Pardubice`,
    purpose: 'Provést komplexní OSINT (Open Source Intelligence) analýzu zájmové osoby',
    
    // Styling
    style: 'official',
    fontSize: 11,
    lineHeight: 1.5,
    margins: {
      top: 20,
      bottom: 20,
      left: 20,
      right: 20
    },
    
    // Barvy
    primaryColor: '#2c3e50',
    secondaryColor: '#3498db',
    textColor: '#333333',
    backgroundColor: '#ffffff',
    
    // Rozložení
    headerHeight: 60,
    footerHeight: 40,
    showPageNumbers: true,
    pageNumberPosition: 'top-left',
    showBorders: true,
    borderWidth: 2,
    borderColor: '#000000',
    
    // Fotografie
    photoSize: 'large',
    photoAlignment: 'center',
    photoSpacing: 15
  });

  const updateSettings = (key: keyof PDFEditorSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const updateMargins = (side: keyof PDFEditorSettings['margins'], value: number) => {
    setSettings(prev => ({
      ...prev,
      margins: {
        ...prev.margins,
        [side]: value
      }
    }));
  };

  const generateCustomPDF = async () => {
    setIsGenerating(true);
    
    try {
      const response = await fetch('/api/reports/generate-custom', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          caseId,
          settings,
          reportType: 'full'
        }),
      });

      if (!response.ok) {
        throw new Error('Chyba při generování PDF');
      }

      // Stáhneme soubor
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `custom-osint-report-${caseId}-${Date.now()}.pdf`;
      
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "PDF vygenerováno",
        description: "Vlastní PDF report byl úspěšně vygenerován a stažen.",
      });

      if (onGenerate) {
        onGenerate(settings);
      }

      setIsOpen(false);
    } catch (error) {
      console.error('Chyba při generování PDF:', error);
      toast({
        title: "Chyba při generování PDF",
        description: error instanceof Error ? error.message : 'Neznámá chyba',
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="bg-purple-50 hover:bg-purple-100 border-purple-200">
          <Settings className="w-4 h-4 mr-2" />
          PDF Editor
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Palette className="w-5 h-5" />
            PDF Editor - Vlastní úprava reportu
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="basic">Základní</TabsTrigger>
            <TabsTrigger value="styling">Styling</TabsTrigger>
            <TabsTrigger value="layout">Rozložení</TabsTrigger>
            <TabsTrigger value="colors">Barvy</TabsTrigger>
            <TabsTrigger value="photos">Fotografie</TabsTrigger>
          </TabsList>

          {/* Základní nastavení */}
          <TabsContent value="basic" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Type className="w-4 h-4" />
                  Základní informace
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Název reportu</Label>
                    <Input
                      id="title"
                      value={settings.title}
                      onChange={(e) => updateSettings('title', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="subject">Subjekt analýzy</Label>
                    <Input
                      id="subject"
                      value={settings.subject}
                      onChange={(e) => updateSettings('subject', e.target.value)}
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="jid">JID</Label>
                    <Input
                      id="jid"
                      value={settings.jid}
                      onChange={(e) => updateSettings('jid', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="documentNumber">Číslo jednací</Label>
                    <Input
                      id="documentNumber"
                      value={settings.documentNumber}
                      onChange={(e) => updateSettings('documentNumber', e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="location">Místo</Label>
                    <Input
                      id="location"
                      value={settings.location}
                      onChange={(e) => updateSettings('location', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="date">Datum</Label>
                    <Input
                      id="date"
                      value={settings.date}
                      onChange={(e) => updateSettings('date', e.target.value)}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="department">Adresa oddělení</Label>
                  <Textarea
                    id="department"
                    value={settings.department}
                    onChange={(e) => updateSettings('department', e.target.value)}
                    rows={4}
                  />
                </div>

                <div>
                  <Label htmlFor="purpose">Účel analýzy</Label>
                  <Textarea
                    id="purpose"
                    value={settings.purpose}
                    onChange={(e) => updateSettings('purpose', e.target.value)}
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Styling */}
          <TabsContent value="styling" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Styl a typografie</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="style">Základní styl</Label>
                  <Select
                    value={settings.style}
                    onValueChange={(value: 'official' | 'modern') => updateSettings('style', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="official">Oficiální (Byrokratický)</SelectItem>
                      <SelectItem value="modern">Moderní (Analytický)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Velikost písma: {settings.fontSize}pt</Label>
                  <Slider
                    value={[settings.fontSize]}
                    onValueChange={(value) => updateSettings('fontSize', value[0])}
                    min={8}
                    max={16}
                    step={0.5}
                    className="mt-2"
                  />
                </div>

                <div>
                  <Label>Řádkování: {settings.lineHeight}</Label>
                  <Slider
                    value={[settings.lineHeight]}
                    onValueChange={(value) => updateSettings('lineHeight', value[0])}
                    min={1.0}
                    max={2.5}
                    step={0.1}
                    className="mt-2"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Rozložení */}
          <TabsContent value="layout" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Layout className="w-4 h-4" />
                  Rozložení stránky
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Horní okraj: {settings.margins.top}mm</Label>
                    <Slider
                      value={[settings.margins.top]}
                      onValueChange={(value) => updateMargins('top', value[0])}
                      min={10}
                      max={40}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <Label>Dolní okraj: {settings.margins.bottom}mm</Label>
                    <Slider
                      value={[settings.margins.bottom]}
                      onValueChange={(value) => updateMargins('bottom', value[0])}
                      min={10}
                      max={40}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Levý okraj: {settings.margins.left}mm</Label>
                    <Slider
                      value={[settings.margins.left]}
                      onValueChange={(value) => updateMargins('left', value[0])}
                      min={10}
                      max={40}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <Label>Pravý okraj: {settings.margins.right}mm</Label>
                    <Slider
                      value={[settings.margins.right]}
                      onValueChange={(value) => updateMargins('right', value[0])}
                      min={10}
                      max={40}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="showPageNumbers"
                    checked={settings.showPageNumbers}
                    onCheckedChange={(checked) => updateSettings('showPageNumbers', checked)}
                  />
                  <Label htmlFor="showPageNumbers">Zobrazit číslování stránek</Label>
                </div>

                {settings.showPageNumbers && (
                  <div>
                    <Label htmlFor="pageNumberPosition">Pozice číslování</Label>
                    <Select
                      value={settings.pageNumberPosition}
                      onValueChange={(value) => updateSettings('pageNumberPosition', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="top-left">Nahoře vlevo</SelectItem>
                        <SelectItem value="top-right">Nahoře vpravo</SelectItem>
                        <SelectItem value="bottom-left">Dole vlevo</SelectItem>
                        <SelectItem value="bottom-right">Dole vpravo</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Switch
                    id="showBorders"
                    checked={settings.showBorders}
                    onCheckedChange={(checked) => updateSettings('showBorders', checked)}
                  />
                  <Label htmlFor="showBorders">Zobrazit čáry a rámečky</Label>
                </div>

                {settings.showBorders && (
                  <div>
                    <Label>Tloušťka čar: {settings.borderWidth}px</Label>
                    <Slider
                      value={[settings.borderWidth]}
                      onValueChange={(value) => updateSettings('borderWidth', value[0])}
                      min={1}
                      max={5}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Barvy */}
          <TabsContent value="colors" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Barevné schéma</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="primaryColor">Primární barva</Label>
                    <div className="flex gap-2 mt-1">
                      <Input
                        id="primaryColor"
                        type="color"
                        value={settings.primaryColor}
                        onChange={(e) => updateSettings('primaryColor', e.target.value)}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={settings.primaryColor}
                        onChange={(e) => updateSettings('primaryColor', e.target.value)}
                        className="flex-1"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="secondaryColor">Sekundární barva</Label>
                    <div className="flex gap-2 mt-1">
                      <Input
                        id="secondaryColor"
                        type="color"
                        value={settings.secondaryColor}
                        onChange={(e) => updateSettings('secondaryColor', e.target.value)}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={settings.secondaryColor}
                        onChange={(e) => updateSettings('secondaryColor', e.target.value)}
                        className="flex-1"
                      />
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="textColor">Barva textu</Label>
                    <div className="flex gap-2 mt-1">
                      <Input
                        id="textColor"
                        type="color"
                        value={settings.textColor}
                        onChange={(e) => updateSettings('textColor', e.target.value)}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={settings.textColor}
                        onChange={(e) => updateSettings('textColor', e.target.value)}
                        className="flex-1"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="backgroundColor">Barva pozadí</Label>
                    <div className="flex gap-2 mt-1">
                      <Input
                        id="backgroundColor"
                        type="color"
                        value={settings.backgroundColor}
                        onChange={(e) => updateSettings('backgroundColor', e.target.value)}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={settings.backgroundColor}
                        onChange={(e) => updateSettings('backgroundColor', e.target.value)}
                        className="flex-1"
                      />
                    </div>
                  </div>
                </div>

                {settings.showBorders && (
                  <div>
                    <Label htmlFor="borderColor">Barva čar</Label>
                    <div className="flex gap-2 mt-1">
                      <Input
                        id="borderColor"
                        type="color"
                        value={settings.borderColor}
                        onChange={(e) => updateSettings('borderColor', e.target.value)}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={settings.borderColor}
                        onChange={(e) => updateSettings('borderColor', e.target.value)}
                        className="flex-1"
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Fotografie */}
          <TabsContent value="photos" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Image className="w-4 h-4" />
                  Nastavení fotografií
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="photoSize">Velikost fotografií</Label>
                  <Select
                    value={settings.photoSize}
                    onValueChange={(value: 'small' | 'medium' | 'large') => updateSettings('photoSize', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="small">Malé (200px)</SelectItem>
                      <SelectItem value="medium">Střední (300px)</SelectItem>
                      <SelectItem value="large">Velké (400px)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="photoAlignment">Zarovnání fotografií</Label>
                  <Select
                    value={settings.photoAlignment}
                    onValueChange={(value: 'left' | 'center' | 'right') => updateSettings('photoAlignment', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="left">Vlevo</SelectItem>
                      <SelectItem value="center">Na střed</SelectItem>
                      <SelectItem value="right">Vpravo</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label>Rozestup fotografií: {settings.photoSpacing}px</Label>
                  <Slider
                    value={[settings.photoSpacing]}
                    onValueChange={(value) => updateSettings('photoSpacing', value[0])}
                    min={5}
                    max={30}
                    step={1}
                    className="mt-2"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Akce */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button 
            variant="outline" 
            onClick={() => setIsOpen(false)}
            disabled={isGenerating}
          >
            Zrušit
          </Button>
          <LoadingButton
            onClick={generateCustomPDF}
            isLoading={isGenerating}
            loadingText="Generuji PDF..."
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            <Download className="w-4 h-4 mr-2" />
            Generovat vlastní PDF
          </LoadingButton>
        </div>
      </DialogContent>
    </Dialog>
  );
}
