"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Settings, Download, Palette, Layout, Type, Image, Save, Upload, Trash2, Eye } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { LoadingButton } from '@/components/ui/loading';
import PDFPreview from './PDFPreview';
import { getCustomStyles, saveCustomStyle, deleteCustomStyle, type CustomStyle } from '@/lib/customStyles';

interface PDFEditorSettings {
  // Základní nastavení
  title: string;
  subject: string;
  jid: string;
  documentNumber: string;
  location: string;
  date: string;
  department: string;
  purpose: string;

  // Styling
  style: 'official' | 'modern';
  fontSize: number;
  lineHeight: number;
  margins: {
    top: number;
    bottom: number;
    left: number;
    right: number;
  };

  // Barvy
  primaryColor: string;
  secondaryColor: string;
  textColor: string;
  backgroundColor: string;

  // Rozložení
  headerHeight: number;
  footerHeight: number;
  showPageNumbers: boolean;
  pageNumberPosition: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  showBorders: boolean;
  borderWidth: number;
  borderColor: string;

  // Fotografie
  photoSize: 'small' | 'medium' | 'large';
  photoAlignment: 'left' | 'center' | 'right';
  photoSpacing: number;

  // Pokročilé rozložení
  tableSpacing: number;
  tableBorderRadius: number;
  tableCellPadding: number;
  sectionSpacing: number;
  moduleSpacing: number;
  headerSpacing: number;
  footerSpacing: number;

  // Čáry a oddělení
  showSectionDividers: boolean;
  sectionDividerStyle: 'solid' | 'dashed' | 'dotted';
  sectionDividerColor: string;
  showModuleBorders: boolean;
  moduleBorderRadius: number;

  // Tabulky
  tableHeaderBackground: string;
  tableAlternateRows: boolean;
  tableAlternateRowColor: string;
  tableBorderStyle: 'solid' | 'dashed' | 'dotted' | 'none';
}

interface PDFEditorProps {
  caseId: string;
  onGenerate?: (settings: PDFEditorSettings) => void;
}

export default function PDFEditor({ caseId, onGenerate }: PDFEditorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showPreview, setShowPreview] = useState(true);
  const [customStyles, setCustomStyles] = useState<CustomStyle[]>([]);
  const [selectedStyleId, setSelectedStyleId] = useState<string>('');
  const [newStyleName, setNewStyleName] = useState('');
  const [newStyleDescription, setNewStyleDescription] = useState('');
  const { toast } = useToast();

  const [settings, setSettings] = useState<PDFEditorSettings>({
    // Základní nastavení
    title: 'OSINT (Open Source Intelligence)',
    subject: '',
    jid: '',
    documentNumber: '',
    location: 'Pardubice',
    date: new Date().toLocaleDateString('cs-CZ', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }),
    department: `Odbor analytiky a kyber. kriminality\nOddělení kybernetické kriminality\nNa Spravedlnosti 2516, 530 48 Pardubice`,
    purpose: 'Provést komplexní OSINT (Open Source Intelligence) analýzu zájmové osoby',

    // Styling
    style: 'official',
    fontSize: 11,
    lineHeight: 1.5,
    margins: {
      top: 20,
      bottom: 20,
      left: 20,
      right: 20
    },

    // Barvy
    primaryColor: '#2c3e50',
    secondaryColor: '#3498db',
    textColor: '#333333',
    backgroundColor: '#ffffff',

    // Rozložení
    headerHeight: 60,
    footerHeight: 40,
    showPageNumbers: true,
    pageNumberPosition: 'top-left',
    showBorders: true,
    borderWidth: 2,
    borderColor: '#000000',

    // Fotografie
    photoSize: 'large',
    photoAlignment: 'center',
    photoSpacing: 15,

    // Pokročilé rozložení
    tableSpacing: 15,
    tableBorderRadius: 4,
    tableCellPadding: 8,
    sectionSpacing: 20,
    moduleSpacing: 25,
    headerSpacing: 30,
    footerSpacing: 40,

    // Čáry a oddělení
    showSectionDividers: true,
    sectionDividerStyle: 'solid',
    sectionDividerColor: '#e0e0e0',
    showModuleBorders: true,
    moduleBorderRadius: 8,

    // Tabulky
    tableHeaderBackground: '#f8f9fa',
    tableAlternateRows: true,
    tableAlternateRowColor: '#f9f9f9',
    tableBorderStyle: 'solid'
  });

  // Načtení vlastních stylů při otevření
  React.useEffect(() => {
    if (isOpen) {
      const styles = getCustomStyles();
      setCustomStyles(styles);
    }
  }, [isOpen]);

  const updateSettings = (key: keyof PDFEditorSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const updateMargins = (side: keyof PDFEditorSettings['margins'], value: number) => {
    setSettings(prev => ({
      ...prev,
      margins: {
        ...prev.margins,
        [side]: value
      }
    }));
  };

  const loadStyle = (styleId: string) => {
    const style = customStyles.find(s => s.id === styleId);
    if (style) {
      setSettings(style.settings);
      setSelectedStyleId(styleId);
      toast({
        title: "Styl načten",
        description: `Styl "${style.name}" byl úspěšně načten.`,
      });
    }
  };

  const saveNewStyle = async () => {
    if (!newStyleName.trim()) {
      toast({
        title: "Chyba",
        description: "Zadejte název stylu.",
        variant: "destructive"
      });
      return;
    }

    setIsSaving(true);
    try {
      const newStyle = saveCustomStyle({
        name: newStyleName.trim(),
        description: newStyleDescription.trim(),
        settings: settings
      });

      const updatedStyles = getCustomStyles();
      setCustomStyles(updatedStyles);
      setNewStyleName('');
      setNewStyleDescription('');
      setSelectedStyleId(newStyle.id);

      toast({
        title: "Styl uložen",
        description: `Vlastní styl "${newStyle.name}" byl úspěšně uložen.`,
      });
    } catch (error) {
      toast({
        title: "Chyba při ukládání",
        description: error instanceof Error ? error.message : 'Neznámá chyba',
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const deleteStyle = (styleId: string) => {
    const style = customStyles.find(s => s.id === styleId);
    if (!style) return;

    if (deleteCustomStyle(styleId)) {
      const updatedStyles = getCustomStyles();
      setCustomStyles(updatedStyles);
      if (selectedStyleId === styleId) {
        setSelectedStyleId('');
      }
      toast({
        title: "Styl smazán",
        description: `Styl "${style.name}" byl smazán.`,
      });
    } else {
      toast({
        title: "Chyba",
        description: "Nepodařilo se smazat styl.",
        variant: "destructive"
      });
    }
  };

  const generateCustomPDF = async () => {
    setIsGenerating(true);

    try {
      const response = await fetch('/api/reports/generate-custom', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          caseId,
          settings,
          reportType: 'full'
        }),
      });

      if (!response.ok) {
        throw new Error('Chyba při generování PDF');
      }

      // Stáhneme soubor
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `custom-osint-report-${caseId}-${Date.now()}.pdf`;

      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast({
        title: "PDF vygenerováno",
        description: "Vlastní PDF report byl úspěšně vygenerován a stažen.",
      });

      if (onGenerate) {
        onGenerate(settings);
      }

      setIsOpen(false);
    } catch (error) {
      console.error('Chyba při generování PDF:', error);
      toast({
        title: "Chyba při generování PDF",
        description: error instanceof Error ? error.message : 'Neznámá chyba',
        variant: "destructive"
      });
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="bg-purple-50 hover:bg-purple-100 border-purple-200">
          <Settings className="w-4 h-4 mr-2" />
          PDF Editor
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Palette className="w-5 h-5" />
              PDF Editor - Vlastní úprava reportu
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowPreview(!showPreview)}
              className="flex items-center gap-2"
            >
              <Eye className="w-4 h-4" />
              {showPreview ? 'Skrýt náhled' : 'Zobrazit náhled'}
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className={`grid gap-6 ${showPreview ? 'grid-cols-2' : 'grid-cols-1'}`}>
          {/* Levá strana - Nastavení */}
          <div className="space-y-4">

        <Tabs defaultValue="styles" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="styles">Styly</TabsTrigger>
            <TabsTrigger value="basic">Základní</TabsTrigger>
            <TabsTrigger value="layout">Rozložení</TabsTrigger>
            <TabsTrigger value="advanced">Pokročilé</TabsTrigger>
          </TabsList>

          {/* Správa stylů */}
          <TabsContent value="styles" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="w-4 h-4" />
                  Načíst existující styl
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="styleSelect">Vyberte styl</Label>
                  <Select
                    value={selectedStyleId}
                    onValueChange={loadStyle}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Vyberte styl..." />
                    </SelectTrigger>
                    <SelectContent>
                      {customStyles.map((style) => (
                        <SelectItem key={style.id} value={style.id}>
                          <div className="flex items-center justify-between w-full">
                            <div>
                              <div className="font-medium">{style.name}</div>
                              {style.description && (
                                <div className="text-xs text-gray-500">{style.description}</div>
                              )}
                            </div>
                            {!style.id.startsWith('official-') && !style.id.startsWith('modern-') && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  deleteStyle(style.id);
                                }}
                                className="ml-2 h-6 w-6 p-0 text-red-500 hover:text-red-700"
                              >
                                <Trash2 className="w-3 h-3" />
                              </Button>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Save className="w-4 h-4" />
                  Uložit jako nový styl
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="styleName">Název stylu</Label>
                  <Input
                    id="styleName"
                    value={newStyleName}
                    onChange={(e) => setNewStyleName(e.target.value)}
                    placeholder="Např. Můj vlastní styl"
                  />
                </div>
                <div>
                  <Label htmlFor="styleDescription">Popis (volitelný)</Label>
                  <Textarea
                    id="styleDescription"
                    value={newStyleDescription}
                    onChange={(e) => setNewStyleDescription(e.target.value)}
                    placeholder="Krátký popis stylu..."
                    rows={2}
                  />
                </div>
                <LoadingButton
                  onClick={saveNewStyle}
                  isLoading={isSaving}
                  loadingText="Ukládám..."
                  disabled={!newStyleName.trim()}
                  className="w-full"
                >
                  <Save className="w-4 h-4 mr-2" />
                  Uložit styl
                </LoadingButton>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Základní nastavení */}
          <TabsContent value="basic" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Type className="w-4 h-4" />
                  Základní informace
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Název reportu</Label>
                    <Input
                      id="title"
                      value={settings.title}
                      onChange={(e) => updateSettings('title', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="subject">Subjekt analýzy</Label>
                    <Input
                      id="subject"
                      value={settings.subject}
                      onChange={(e) => updateSettings('subject', e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="jid">JID</Label>
                    <Input
                      id="jid"
                      value={settings.jid}
                      onChange={(e) => updateSettings('jid', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="documentNumber">Číslo jednací</Label>
                    <Input
                      id="documentNumber"
                      value={settings.documentNumber}
                      onChange={(e) => updateSettings('documentNumber', e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="location">Místo</Label>
                    <Input
                      id="location"
                      value={settings.location}
                      onChange={(e) => updateSettings('location', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="date">Datum</Label>
                    <Input
                      id="date"
                      value={settings.date}
                      onChange={(e) => updateSettings('date', e.target.value)}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="department">Adresa oddělení</Label>
                  <Textarea
                    id="department"
                    value={settings.department}
                    onChange={(e) => updateSettings('department', e.target.value)}
                    rows={4}
                  />
                </div>

                <div>
                  <Label htmlFor="purpose">Účel analýzy</Label>
                  <Textarea
                    id="purpose"
                    value={settings.purpose}
                    onChange={(e) => updateSettings('purpose', e.target.value)}
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Styl a typografie</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="style">Základní styl</Label>
                  <Select
                    value={settings.style}
                    onValueChange={(value: 'official' | 'modern') => updateSettings('style', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="official">Oficiální (Byrokratický)</SelectItem>
                      <SelectItem value="modern">Moderní (Analytický)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Velikost písma: {settings.fontSize}pt</Label>
                    <Slider
                      value={[settings.fontSize]}
                      onValueChange={(value) => updateSettings('fontSize', value[0])}
                      min={8}
                      max={16}
                      step={0.5}
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <Label>Řádkování: {settings.lineHeight}</Label>
                    <Slider
                      value={[settings.lineHeight]}
                      onValueChange={(value) => updateSettings('lineHeight', value[0])}
                      min={1.0}
                      max={2.5}
                      step={0.1}
                      className="mt-2"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Barevné schéma</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="primaryColor">Primární barva</Label>
                    <div className="flex gap-2 mt-1">
                      <Input
                        id="primaryColor"
                        type="color"
                        value={settings.primaryColor}
                        onChange={(e) => updateSettings('primaryColor', e.target.value)}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={settings.primaryColor}
                        onChange={(e) => updateSettings('primaryColor', e.target.value)}
                        className="flex-1"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="secondaryColor">Sekundární barva</Label>
                    <div className="flex gap-2 mt-1">
                      <Input
                        id="secondaryColor"
                        type="color"
                        value={settings.secondaryColor}
                        onChange={(e) => updateSettings('secondaryColor', e.target.value)}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={settings.secondaryColor}
                        onChange={(e) => updateSettings('secondaryColor', e.target.value)}
                        className="flex-1"
                      />
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="textColor">Barva textu</Label>
                    <div className="flex gap-2 mt-1">
                      <Input
                        id="textColor"
                        type="color"
                        value={settings.textColor}
                        onChange={(e) => updateSettings('textColor', e.target.value)}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={settings.textColor}
                        onChange={(e) => updateSettings('textColor', e.target.value)}
                        className="flex-1"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="backgroundColor">Barva pozadí</Label>
                    <div className="flex gap-2 mt-1">
                      <Input
                        id="backgroundColor"
                        type="color"
                        value={settings.backgroundColor}
                        onChange={(e) => updateSettings('backgroundColor', e.target.value)}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={settings.backgroundColor}
                        onChange={(e) => updateSettings('backgroundColor', e.target.value)}
                        className="flex-1"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Rozložení */}
          <TabsContent value="layout" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Layout className="w-4 h-4" />
                  Rozložení stránky
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Horní okraj: {settings.margins.top}mm</Label>
                    <Slider
                      value={[settings.margins.top]}
                      onValueChange={(value) => updateMargins('top', value[0])}
                      min={10}
                      max={40}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <Label>Dolní okraj: {settings.margins.bottom}mm</Label>
                    <Slider
                      value={[settings.margins.bottom]}
                      onValueChange={(value) => updateMargins('bottom', value[0])}
                      min={10}
                      max={40}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Levý okraj: {settings.margins.left}mm</Label>
                    <Slider
                      value={[settings.margins.left]}
                      onValueChange={(value) => updateMargins('left', value[0])}
                      min={10}
                      max={40}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <Label>Pravý okraj: {settings.margins.right}mm</Label>
                    <Slider
                      value={[settings.margins.right]}
                      onValueChange={(value) => updateMargins('right', value[0])}
                      min={10}
                      max={40}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="showPageNumbers"
                    checked={settings.showPageNumbers}
                    onCheckedChange={(checked) => updateSettings('showPageNumbers', checked)}
                  />
                  <Label htmlFor="showPageNumbers">Zobrazit číslování stránek</Label>
                </div>

                {settings.showPageNumbers && (
                  <div>
                    <Label htmlFor="pageNumberPosition">Pozice číslování</Label>
                    <Select
                      value={settings.pageNumberPosition}
                      onValueChange={(value) => updateSettings('pageNumberPosition', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="top-left">Nahoře vlevo</SelectItem>
                        <SelectItem value="top-right">Nahoře vpravo</SelectItem>
                        <SelectItem value="bottom-left">Dole vlevo</SelectItem>
                        <SelectItem value="bottom-right">Dole vpravo</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Switch
                    id="showBorders"
                    checked={settings.showBorders}
                    onCheckedChange={(checked) => updateSettings('showBorders', checked)}
                  />
                  <Label htmlFor="showBorders">Zobrazit čáry a rámečky</Label>
                </div>

                {settings.showBorders && (
                  <div>
                    <Label>Tloušťka čar: {settings.borderWidth}px</Label>
                    <Slider
                      value={[settings.borderWidth]}
                      onValueChange={(value) => updateSettings('borderWidth', value[0])}
                      min={1}
                      max={5}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Image className="w-4 h-4" />
                  Nastavení fotografií
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="photoSize">Velikost fotografií</Label>
                    <Select
                      value={settings.photoSize}
                      onValueChange={(value: 'small' | 'medium' | 'large') => updateSettings('photoSize', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="small">Malé (200px)</SelectItem>
                        <SelectItem value="medium">Střední (300px)</SelectItem>
                        <SelectItem value="large">Velké (400px)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="photoAlignment">Zarovnání fotografií</Label>
                    <Select
                      value={settings.photoAlignment}
                      onValueChange={(value: 'left' | 'center' | 'right') => updateSettings('photoAlignment', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="left">Vlevo</SelectItem>
                        <SelectItem value="center">Na střed</SelectItem>
                        <SelectItem value="right">Vpravo</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label>Rozestup fotografií: {settings.photoSpacing}px</Label>
                  <Slider
                    value={[settings.photoSpacing]}
                    onValueChange={(value) => updateSettings('photoSpacing', value[0])}
                    min={5}
                    max={30}
                    step={1}
                    className="mt-2"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Pokročilé nastavení */}
          <TabsContent value="advanced" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>🔧 Pokročilé rozložení</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Rozestup sekcí: {settings.sectionSpacing}px</Label>
                    <Slider
                      value={[settings.sectionSpacing]}
                      onValueChange={(value) => updateSettings('sectionSpacing', value[0])}
                      min={10}
                      max={50}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <Label>Rozestup modulů: {settings.moduleSpacing}px</Label>
                    <Slider
                      value={[settings.moduleSpacing]}
                      onValueChange={(value) => updateSettings('moduleSpacing', value[0])}
                      min={15}
                      max={60}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Rozestup hlavičky: {settings.headerSpacing}px</Label>
                    <Slider
                      value={[settings.headerSpacing]}
                      onValueChange={(value) => updateSettings('headerSpacing', value[0])}
                      min={20}
                      max={80}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <Label>Rozestup patičky: {settings.footerSpacing}px</Label>
                    <Slider
                      value={[settings.footerSpacing]}
                      onValueChange={(value) => updateSettings('footerSpacing', value[0])}
                      min={20}
                      max={80}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>📊 Nastavení tabulek</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Rozestup tabulek: {settings.tableSpacing}px</Label>
                    <Slider
                      value={[settings.tableSpacing]}
                      onValueChange={(value) => updateSettings('tableSpacing', value[0])}
                      min={5}
                      max={30}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <Label>Padding buněk: {settings.tableCellPadding}px</Label>
                    <Slider
                      value={[settings.tableCellPadding]}
                      onValueChange={(value) => updateSettings('tableCellPadding', value[0])}
                      min={4}
                      max={20}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Zaoblení tabulek: {settings.tableBorderRadius}px</Label>
                    <Slider
                      value={[settings.tableBorderRadius]}
                      onValueChange={(value) => updateSettings('tableBorderRadius', value[0])}
                      min={0}
                      max={15}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                  <div>
                    <Label htmlFor="tableBorderStyle">Styl ohraničení tabulek</Label>
                    <Select
                      value={settings.tableBorderStyle}
                      onValueChange={(value: 'solid' | 'dashed' | 'dotted' | 'none') => updateSettings('tableBorderStyle', value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="solid">Plná čára</SelectItem>
                        <SelectItem value="dashed">Čárkovaná</SelectItem>
                        <SelectItem value="dotted">Tečkovaná</SelectItem>
                        <SelectItem value="none">Bez ohraničení</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="tableHeaderBackground">Barva hlavičky tabulky</Label>
                  <div className="flex gap-2 mt-1">
                    <Input
                      id="tableHeaderBackground"
                      type="color"
                      value={settings.tableHeaderBackground}
                      onChange={(e) => updateSettings('tableHeaderBackground', e.target.value)}
                      className="w-16 h-10 p-1"
                    />
                    <Input
                      value={settings.tableHeaderBackground}
                      onChange={(e) => updateSettings('tableHeaderBackground', e.target.value)}
                      className="flex-1"
                    />
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="tableAlternateRows"
                    checked={settings.tableAlternateRows}
                    onCheckedChange={(checked) => updateSettings('tableAlternateRows', checked)}
                  />
                  <Label htmlFor="tableAlternateRows">Střídavé barvy řádků</Label>
                </div>

                {settings.tableAlternateRows && (
                  <div>
                    <Label htmlFor="tableAlternateRowColor">Barva střídavých řádků</Label>
                    <div className="flex gap-2 mt-1">
                      <Input
                        id="tableAlternateRowColor"
                        type="color"
                        value={settings.tableAlternateRowColor}
                        onChange={(e) => updateSettings('tableAlternateRowColor', e.target.value)}
                        className="w-16 h-10 p-1"
                      />
                      <Input
                        value={settings.tableAlternateRowColor}
                        onChange={(e) => updateSettings('tableAlternateRowColor', e.target.value)}
                        className="flex-1"
                      />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>➖ Čáry a oddělení</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="showSectionDividers"
                    checked={settings.showSectionDividers}
                    onCheckedChange={(checked) => updateSettings('showSectionDividers', checked)}
                  />
                  <Label htmlFor="showSectionDividers">Zobrazit oddělovače sekcí</Label>
                </div>

                {settings.showSectionDividers && (
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="sectionDividerStyle">Styl oddělovačů</Label>
                      <Select
                        value={settings.sectionDividerStyle}
                        onValueChange={(value: 'solid' | 'dashed' | 'dotted') => updateSettings('sectionDividerStyle', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="solid">Plná čára</SelectItem>
                          <SelectItem value="dashed">Čárkovaná</SelectItem>
                          <SelectItem value="dotted">Tečkovaná</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="sectionDividerColor">Barva oddělovačů</Label>
                      <div className="flex gap-2 mt-1">
                        <Input
                          id="sectionDividerColor"
                          type="color"
                          value={settings.sectionDividerColor}
                          onChange={(e) => updateSettings('sectionDividerColor', e.target.value)}
                          className="w-16 h-10 p-1"
                        />
                        <Input
                          value={settings.sectionDividerColor}
                          onChange={(e) => updateSettings('sectionDividerColor', e.target.value)}
                          className="flex-1"
                        />
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex items-center space-x-2">
                  <Switch
                    id="showModuleBorders"
                    checked={settings.showModuleBorders}
                    onCheckedChange={(checked) => updateSettings('showModuleBorders', checked)}
                  />
                  <Label htmlFor="showModuleBorders">Zobrazit rámečky modulů</Label>
                </div>

                {settings.showModuleBorders && (
                  <div>
                    <Label>Zaoblení rámečků modulů: {settings.moduleBorderRadius}px</Label>
                    <Slider
                      value={[settings.moduleBorderRadius]}
                      onValueChange={(value) => updateSettings('moduleBorderRadius', value[0])}
                      min={0}
                      max={20}
                      step={1}
                      className="mt-2"
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
        </div>

        {/* Pravá strana - Náhled */}
        {showPreview && (
          <div className="space-y-4">
            <PDFPreview settings={settings} />
          </div>
        )}
        </div>

        {/* Akce */}
        <div className="flex justify-end gap-3 pt-4 border-t">
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isGenerating}
          >
            Zrušit
          </Button>
          <LoadingButton
            onClick={generateCustomPDF}
            isLoading={isGenerating}
            loadingText="Generuji PDF..."
            className="bg-purple-600 hover:bg-purple-700 text-white"
          >
            <Download className="w-4 h-4 mr-2" />
            Generovat vlastní PDF
          </LoadingButton>
        </div>
      </DialogContent>
    </Dialog>
  );
}
