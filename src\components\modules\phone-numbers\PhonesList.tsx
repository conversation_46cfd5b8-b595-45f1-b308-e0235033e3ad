"use client";

import { useState } from "react";
import { Control, useFieldArray } from "react-hook-form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
  Phone,
  Search,
  Plus,
  MoreVertical,
  Edit,
  Trash2,
  Copy,
  CheckCircle2,
  XCircle,
  AlertCircle,
  Building,
  Calendar
} from "lucide-react";
import { PhoneNumbersModuleFormValues } from "./schemas";

interface PhonesListProps {
  control: Control<PhoneNumbersModuleFormValues>;
  onSelectPhone: (index: number) => void;
  onAddPhone: () => void;
}

export function PhonesList({ control, onSelectPhone, onAddPhone }: PhonesListProps) {
  const [searchQuery, setSearchQuery] = useState("");

  const { fields, remove } = useFieldArray({
    control,
    name: "phones",
  });

  const filteredPhones = fields.filter(phone => {
    const phoneData = phone as any;
    const query = searchQuery.toLowerCase();

    return (
      phoneData.phoneNumber?.toLowerCase().includes(query) ||
      phoneData.owner?.toLowerCase().includes(query) ||
      phoneData.associatedOrganization?.toLowerCase().includes(query)
    );
  });

  const getVerificationStatusBadge = (status: string) => {
    switch (status) {
      case "verified":
        return (
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle2 className="mr-1 h-3 w-3" />
            Ověřeno
          </Badge>
        );
      case "unverified":
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <AlertCircle className="mr-1 h-3 w-3" />
            Neověřeno
          </Badge>
        );
      case "invalid":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            <XCircle className="mr-1 h-3 w-3" />
            Neplatné
          </Badge>
        );
      case "disconnected":
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            <Phone className="mr-1 h-3 w-3" />
            Odpojeno
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            <AlertCircle className="mr-1 h-3 w-3" />
            Neznámý stav
          </Badge>
        );
    }
  };

  const getPhoneTypeBadge = (type: string) => {
    switch (type) {
      case "mobile":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            Mobilní
          </Badge>
        );
      case "landline":
        return (
          <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
            Pevná linka
          </Badge>
        );
      case "business":
        return (
          <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200">
            Firemní
          </Badge>
        );
      case "fax":
        return (
          <Badge variant="outline" className="bg-cyan-50 text-cyan-700 border-cyan-200">
            Fax
          </Badge>
        );
      case "voip":
        return (
          <Badge variant="outline" className="bg-teal-50 text-teal-700 border-teal-200">
            VoIP
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            Jiný
          </Badge>
        );
    }
  };

  const getCarrierBadge = (carrier: string) => {
    switch (carrier) {
      case "o2":
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            O2
          </Badge>
        );
      case "t-mobile":
        return (
          <Badge variant="outline" className="bg-pink-50 text-pink-700 border-pink-200">
            T-Mobile
          </Badge>
        );
      case "vodafone":
        return (
          <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
            Vodafone
          </Badge>
        );
      case "other":
        return (
          <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
            Jiný operátor
          </Badge>
        );
      default:
        return (
          <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">
            Neznámý operátor
          </Badge>
        );
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Phone className="mr-2 h-5 w-5 text-primary" />
          Seznam telefonních čísel
        </CardTitle>
        <CardDescription>
          Správa telefonních čísel spojených se subjektem
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Hledat telefonní čísla..."
              className="pl-8"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <Button onClick={onAddPhone}>
            <Plus className="mr-2 h-4 w-4" />
            Přidat telefonní číslo
          </Button>
        </div>

        {fields.length === 0 ? (
          <div className="text-center py-8 border rounded-md bg-muted/20">
            <Phone className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">Zatím nebyla přidána žádná telefonní čísla</p>
            <Button
              variant="outline"
              className="mt-4"
              onClick={onAddPhone}
            >
              <Plus className="mr-2 h-4 w-4" />
              Přidat telefonní číslo
            </Button>
          </div>
        ) : (
          <div className="border rounded-md">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Telefonní číslo</TableHead>
                  <TableHead>Vlastník / Organizace</TableHead>
                  <TableHead>Stav</TableHead>
                  <TableHead>Typ</TableHead>
                  <TableHead>Operátor</TableHead>
                  <TableHead className="text-right">Akce</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPhones.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                      Žádná telefonní čísla neodpovídají vašemu vyhledávání
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredPhones.map((phone, index) => {
                    const phoneData = phone as any;
                    return (
                      <TableRow key={phone.id} className="cursor-pointer hover:bg-muted/50" onClick={() => {
                        // Použijeme setTimeout, abychom zabránili aktualizaci stavu během renderování
                        setTimeout(() => {
                          onSelectPhone(fields.findIndex(f => f.id === phone.id));
                        }, 0);
                      }}>
                        <TableCell className="font-medium">
                          <div className="flex flex-col">
                            <span>{phoneData.phoneNumber}</span>
                            <span className="text-xs text-muted-foreground flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
                              {phoneData.discoveryDate || "Datum neznámé"}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-col">
                            {phoneData.owner && <span>{phoneData.owner}</span>}
                            {phoneData.associatedOrganization && (
                              <span className="text-xs text-muted-foreground flex items-center">
                                <Building className="h-3 w-3 mr-1" />
                                {phoneData.associatedOrganization}
                              </span>
                            )}
                            {!phoneData.owner && !phoneData.associatedOrganization && (
                              <span className="text-muted-foreground">Neznámý</span>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {getVerificationStatusBadge(phoneData.verificationStatus)}
                        </TableCell>
                        <TableCell>
                          {phoneData.phoneType ? getPhoneTypeBadge(phoneData.phoneType) : "-"}
                        </TableCell>
                        <TableCell>
                          {phoneData.carrier ? getCarrierBadge(phoneData.carrier) : "-"}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                              <Button variant="ghost" size="icon">
                                <MoreVertical className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation();
                                // Použijeme setTimeout, abychom zabránili aktualizaci stavu během renderování
                                setTimeout(() => {
                                  onSelectPhone(fields.findIndex(f => f.id === phone.id));
                                }, 0);
                              }}>
                                <Edit className="mr-2 h-4 w-4" />
                                Upravit
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={(e) => {
                                e.stopPropagation();
                                navigator.clipboard.writeText(phoneData.phoneNumber);
                              }}>
                                <Copy className="mr-2 h-4 w-4" />
                                Kopírovat číslo
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                className="text-destructive focus:text-destructive"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (confirm("Opravdu chcete odstranit toto telefonní číslo?")) {
                                    remove(fields.findIndex(f => f.id === phone.id));
                                  }
                                }}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Odstranit
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
