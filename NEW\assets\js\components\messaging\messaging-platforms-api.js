/**
 * Monitoring komunikačn<PERSON>ch platforem - funkce pro práci s API a daty
 */

/**
 * Zobrazení dialogu pro přidání kanálu/serveru/skupiny
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 */
function showAddChannelDialog(platform) {
    console.log('Zobrazení dialogu pro přidání kanálu/serveru/skupiny:', platform);

    // Vytvoření dialogu
    const dialog = document.createElement('div');
    dialog.className = 'add-channel-dialog';

    // Nastavení obsahu dialogu podle platformy
    let dialogTitle, dialogContent, placeholderText;
    
    switch (platform) {
        case 'telegram':
            dialogTitle = 'Přidat Telegram kanál';
            placeholderText = 'Zadejte URL nebo ID kanálu (např. https://t.me/example nebo @example)';
            break;
        case 'discord':
            dialogTitle = 'Přidat Discord server';
            placeholderText = 'Zadejte pozvánku na server (např. https://discord.gg/example)';
            break;
        case 'whatsapp':
            dialogTitle = 'Přidat WhatsApp skupinu';
            placeholderText = 'Zadejte pozvánku do skupiny nebo telefonní číslo správce';
            break;
        default:
            console.error('Neznámá platforma:', platform);
            return;
    }

    // Vytvoření obsahu dialogu
    dialogContent = `
        <div class="add-channel-dialog-content">
            <div class="add-channel-dialog-header">
                <h3>${dialogTitle}</h3>
                <button type="button" class="add-channel-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="add-channel-dialog-body">
                <div class="form-group">
                    <label>Identifikátor</label>
                    <input type="text" class="form-control" id="channel-identifier" placeholder="${placeholderText}">
                </div>
                <div class="form-group">
                    <label>Název (volitelný)</label>
                    <input type="text" class="form-control" id="channel-name" placeholder="Zadejte vlastní název pro identifikaci">
                </div>
                <div class="form-group">
                    <label>Popis (volitelný)</label>
                    <textarea class="form-control" id="channel-description" rows="2" placeholder="Zadejte popis kanálu/serveru/skupiny"></textarea>
                </div>
                <div class="form-group">
                    <label>Kategorie</label>
                    <select class="form-control" id="channel-category">
                        <option value="">-- Vyberte kategorii --</option>
                        <option value="news">Zpravodajství</option>
                        <option value="politics">Politika</option>
                        <option value="technology">Technologie</option>
                        <option value="finance">Finance</option>
                        <option value="entertainment">Zábava</option>
                        <option value="sports">Sport</option>
                        <option value="education">Vzdělávání</option>
                        <option value="other">Jiné</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Klíčová slova (oddělená čárkou)</label>
                    <input type="text" class="form-control" id="channel-keywords" placeholder="Zadejte klíčová slova oddělená čárkou">
                </div>
            </div>
            <div class="add-channel-dialog-footer">
                <button type="button" class="btn-secondary add-channel-dialog-cancel">Zrušit</button>
                <button type="button" class="btn-primary add-channel-dialog-add" data-platform="${platform}">Přidat</button>
            </div>
        </div>
    `;

    dialog.innerHTML = dialogContent;

    // Přidání dialogu do stránky
    document.body.appendChild(dialog);

    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.add-channel-dialog-close').addEventListener('click', () => dialog.remove());
    dialog.querySelector('.add-channel-dialog-cancel').addEventListener('click', () => dialog.remove());
    
    dialog.querySelector('.add-channel-dialog-add').addEventListener('click', function() {
        const identifier = dialog.querySelector('#channel-identifier').value.trim();
        const name = dialog.querySelector('#channel-name').value.trim();
        const description = dialog.querySelector('#channel-description').value.trim();
        const category = dialog.querySelector('#channel-category').value;
        const keywords = dialog.querySelector('#channel-keywords').value.trim();
        
        if (!identifier) {
            alert('Zadejte identifikátor kanálu/serveru/skupiny.');
            return;
        }
        
        // Přidání kanálu/serveru/skupiny
        addChannel(platform, identifier, name, description, category, keywords);
        
        // Zavření dialogu
        dialog.remove();
    });

    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}

/**
 * Přidání kanálu/serveru/skupiny
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 * @param {string} identifier - Identifikátor kanálu/serveru/skupiny
 * @param {string} name - Název kanálu/serveru/skupiny
 * @param {string} description - Popis kanálu/serveru/skupiny
 * @param {string} category - Kategorie kanálu/serveru/skupiny
 * @param {string} keywords - Klíčová slova oddělená čárkou
 */
function addChannel(platform, identifier, name, description, category, keywords) {
    console.log('Přidání kanálu/serveru/skupiny:', platform, identifier, name);

    // Vytvoření ID pro nový kanál/server/skupinu
    const id = `${platform}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    
    // Vytvoření objektu pro nový kanál/server/skupinu
    const newItem = {
        id: id,
        identifier: identifier,
        name: name || identifier,
        description: description || '',
        category: category || 'other',
        keywords: keywords ? keywords.split(',').map(k => k.trim()) : [],
        memberCount: 0,
        lastUpdate: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        status: 'pending'
    };
    
    // Přidání specifických vlastností podle platformy
    switch (platform) {
        case 'telegram':
            newItem.type = identifier.startsWith('@') || identifier.includes('t.me/') ? 'Kanál' : 'Skupina';
            messagingPlatformsData.telegram.channels.push(newItem);
            break;
        case 'discord':
            messagingPlatformsData.discord.servers.push(newItem);
            break;
        case 'whatsapp':
            messagingPlatformsData.whatsapp.groups.push(newItem);
            break;
    }
    
    // Uložení dat
    saveMessagingData();
    
    // Aktualizace UI
    updatePlatformsUI();
    
    // Zobrazení notifikace
    showNotification(`${getItemTypeName(platform)} byl(a) úspěšně přidán(a) do monitoringu.`, 'success');
    
    // Zahájení získávání dat pro nový kanál/server/skupinu
    fetchChannelData(platform, id);
}

/**
 * Získání dat pro kanál/server/skupinu
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 * @param {string} itemId - ID kanálu/serveru/skupiny
 */
function fetchChannelData(platform, itemId) {
    console.log('Získávání dat pro kanál/server/skupinu:', platform, itemId);
    
    // Zobrazení notifikace o zahájení získávání dat
    showNotification(`Zahajuji získávání dat pro ${getItemTypeName(platform)}...`, 'info');
    
    // Simulace získávání dat z API
    // V reálné aplikaci by zde byl API požadavek na backend
    setTimeout(() => {
        // Aktualizace stavu kanálu/serveru/skupiny
        updateItemStatus(platform, itemId, 'active');
        
        // Simulace získání počtu členů
        updateItemMemberCount(platform, itemId, Math.floor(Math.random() * 10000) + 100);
        
        // Simulace získání zpráv
        generateSimulatedMessages(platform, itemId, 20);
        
        // Uložení dat
        saveMessagingData();
        
        // Aktualizace UI
        updatePlatformsUI();
        
        // Zobrazení notifikace o dokončení získávání dat
        showNotification(`Data pro ${getItemTypeName(platform)} byla úspěšně získána.`, 'success');
    }, 2000);
}

/**
 * Aktualizace stavu kanálu/serveru/skupiny
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 * @param {string} itemId - ID kanálu/serveru/skupiny
 * @param {string} status - Nový stav (pending, active, error)
 */
function updateItemStatus(platform, itemId, status) {
    let item;
    
    switch (platform) {
        case 'telegram':
            item = messagingPlatformsData.telegram.channels.find(c => c.id === itemId);
            break;
        case 'discord':
            item = messagingPlatformsData.discord.servers.find(s => s.id === itemId);
            break;
        case 'whatsapp':
            item = messagingPlatformsData.whatsapp.groups.find(g => g.id === itemId);
            break;
    }
    
    if (item) {
        item.status = status;
        item.lastUpdate = new Date().toISOString();
    }
}

/**
 * Aktualizace počtu členů kanálu/serveru/skupiny
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 * @param {string} itemId - ID kanálu/serveru/skupiny
 * @param {number} count - Počet členů
 */
function updateItemMemberCount(platform, itemId, count) {
    let item;
    
    switch (platform) {
        case 'telegram':
            item = messagingPlatformsData.telegram.channels.find(c => c.id === itemId);
            break;
        case 'discord':
            item = messagingPlatformsData.discord.servers.find(s => s.id === itemId);
            break;
        case 'whatsapp':
            item = messagingPlatformsData.whatsapp.groups.find(g => g.id === itemId);
            break;
    }
    
    if (item) {
        item.memberCount = count;
    }
}

/**
 * Generování simulovaných zpráv pro kanál/server/skupinu
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 * @param {string} itemId - ID kanálu/serveru/skupiny
 * @param {number} count - Počet zpráv k vygenerování
 */
function generateSimulatedMessages(platform, itemId, count) {
    console.log('Generování simulovaných zpráv:', platform, itemId, count);
    
    // Vytvoření pole pro zprávy
    const messages = [];
    
    // Vytvoření pole pro uživatele
    const users = [];
    
    // Vytvoření simulovaných uživatelů
    for (let i = 0; i < 5; i++) {
        const userId = `${platform}-user-${Date.now()}-${Math.floor(Math.random() * 1000)}-${i}`;
        const user = {
            id: userId,
            username: `user${i + 1}`,
            displayName: `Uživatel ${i + 1}`,
            avatarUrl: `https://i.pravatar.cc/150?u=${userId}`
        };
        users.push(user);
    }
    
    // Vytvoření simulovaných zpráv
    const now = new Date();
    for (let i = 0; i < count; i++) {
        const messageDate = new Date(now);
        messageDate.setHours(messageDate.getHours() - i);
        
        const messageId = `${platform}-message-${Date.now()}-${Math.floor(Math.random() * 1000)}-${i}`;
        const userId = users[Math.floor(Math.random() * users.length)].id;
        
        const message = {
            id: messageId,
            channelId: itemId,
            userId: userId,
            content: getRandomMessageContent(),
            timestamp: messageDate.toISOString(),
            attachments: Math.random() > 0.7 ? generateRandomAttachments() : []
        };
        
        messages.push(message);
    }
    
    // Přidání zpráv a uživatelů do dat
    switch (platform) {
        case 'telegram':
            messagingPlatformsData.telegram.messages[itemId] = messages;
            users.forEach(user => {
                messagingPlatformsData.telegram.users[user.id] = user;
            });
            break;
        case 'discord':
            messagingPlatformsData.discord.messages[itemId] = messages;
            users.forEach(user => {
                messagingPlatformsData.discord.users[user.id] = user;
            });
            break;
        case 'whatsapp':
            messagingPlatformsData.whatsapp.messages[itemId] = messages;
            users.forEach(user => {
                messagingPlatformsData.whatsapp.users[user.id] = user;
            });
            break;
    }
}

/**
 * Získání náhodného obsahu zprávy
 * @returns {string} - Náhodný obsah zprávy
 */
function getRandomMessageContent() {
    const messages = [
        "Dobrý den všem, mám důležité informace k aktuální situaci.",
        "Dnes jsem se dozvěděl zajímavou věc ohledně našeho projektu.",
        "Má někdo zkušenosti s novým systémem, který byl nedávno zaveden?",
        "Potřebuji pomoc s řešením problému, který se objevil včera večer.",
        "Sdílím s vámi odkaz na důležitý článek, který by vás mohl zajímat.",
        "Jaký je váš názor na nedávné změny v legislativě?",
        "Dnes proběhla důležitá schůzka, zde jsou hlavní body, které byly projednány.",
        "Upozorňuji na blížící se termín pro odevzdání dokumentů.",
        "Mám nové informace o situaci, která se vyvíjí velmi rychle.",
        "Potřebujeme dobrovolníky pro nadcházející akci, kdo má zájem?"
    ];
    
    return messages[Math.floor(Math.random() * messages.length)];
}

/**
 * Generování náhodných příloh
 * @returns {Array} - Pole náhodných příloh
 */
function generateRandomAttachments() {
    const attachmentCount = Math.floor(Math.random() * 3) + 1;
    const attachments = [];
    
    for (let i = 0; i < attachmentCount; i++) {
        const attachmentType = Math.random() > 0.5 ? 'image' : 'document';
        
        if (attachmentType === 'image') {
            attachments.push({
                type: 'image',
                url: `https://picsum.photos/500/300?random=${Date.now() + i}`,
                width: 500,
                height: 300
            });
        } else {
            attachments.push({
                type: 'document',
                name: `dokument_${i + 1}.pdf`,
                size: Math.floor(Math.random() * 1000000) + 10000,
                url: '#'
            });
        }
    }
    
    return attachments;
}

/**
 * Uložení dat monitoringu komunikačních platforem
 */
function saveMessagingData() {
    console.log('Ukládání dat monitoringu komunikačních platforem');
    
    // Uložení dat do localStorage
    localStorage.setItem('messagingPlatformsData', JSON.stringify(messagingPlatformsData));
}

/**
 * Uložení nastavení alertů
 */
function saveMessagingSettings() {
    console.log('Ukládání nastavení alertů');
    
    // Získání hodnot z formuláře
    const emailInput = document.getElementById('messaging-alert-email');
    const frequencySelect = document.getElementById('messaging-alert-frequency');
    const keywordsTextarea = document.getElementById('messaging-keywords');
    
    if (!emailInput || !frequencySelect || !keywordsTextarea) {
        console.error('Formulářové prvky nebyly nalezeny');
        return;
    }
    
    // Aktualizace nastavení
    messagingAlertSettings.email = emailInput.value.trim();
    messagingAlertSettings.frequency = frequencySelect.value;
    messagingAlertSettings.keywords = keywordsTextarea.value.split(',').map(k => k.trim()).filter(k => k);
    
    // Uložení nastavení do localStorage
    localStorage.setItem('messagingAlertSettings', JSON.stringify(messagingAlertSettings));
    
    // Zobrazení notifikace
    showNotification('Nastavení bylo úspěšně uloženo.', 'success');
}
