/**
 * Monitoring komunikačních platforem - h<PERSON><PERSON><PERSON>
 */

// Globální proměnné
let messagingPlatformsData = {
    telegram: {
        channels: [],
        messages: {},
        users: {}
    },
    discord: {
        servers: [],
        channels: {},
        messages: {},
        users: {}
    },
    whatsapp: {
        groups: [],
        messages: {},
        users: {}
    }
};

let messagingAlertSettings = {
    email: '',
    frequency: 'daily',
    keywords: []
};

/**
 * Inicializace modulu monitoringu komunikačních platforem
 */
function initMessagingPlatforms() {
    console.log('Inicializace modulu monitoringu komunikačních platforem');

    // Najít aktuální modul monitoringu komunikačních platforem
    // Hledáme modul s ID, které začíná na "module-messaging-monitoring"
    const messagingModule = document.querySelector('.module[id^="module-messaging-monitoring"]');
    if (!messagingModule) {
        console.error('Modul monitoringu komunikačních platforem nebyl nalezen');
        return;
    }

    console.log('Nalezen modul monitoringu komunikačních platforem:', messagingModule.id);

    // Inicializace záložek
    initMessagingTabs(messagingModule);

    // Inicializace fotogalerie
    initMessagingGallery(messagingModule);

    // Přidání event listenerů pro tlačítka
    addMessagingEventListeners(messagingModule);

    // Načtení uložených dat
    loadMessagingData();
}

/**
 * Inicializace záložek pro platformy
 * @param {HTMLElement} moduleElement - Element modulu
 */
function initMessagingTabs(moduleElement) {
    const tabs = moduleElement.querySelectorAll('.messaging-tab');
    const contents = moduleElement.querySelectorAll('.messaging-content');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Odstranění aktivní třídy ze všech záložek a obsahů
            tabs.forEach(t => t.classList.remove('active'));
            contents.forEach(c => c.classList.remove('active'));

            // Přidání aktivní třídy na kliknutou záložku
            this.classList.add('active');

            // Zobrazení odpovídajícího obsahu
            const platform = this.getAttribute('data-platform');
            const content = moduleElement.querySelector(`#${platform}-content`);
            if (content) {
                content.classList.add('active');
            }
        });
    });
}

/**
 * Inicializace fotogalerie
 * @param {HTMLElement} moduleElement - Element modulu
 */
function initMessagingGallery(moduleElement) {
    const gallery = moduleElement.querySelector('.messaging-platforms-gallery');
    if (!gallery) {
        console.error('Fotogalerie nebyla nalezena v modulu monitoringu komunikačních platforem');
        return;
    }

    // Inicializace galerie
    initPhotoGallery(moduleElement, gallery, gallery.querySelector('.photo-gallery-add'));

    // Přidání podpory pro Ctrl+V
    document.addEventListener('paste', function(event) {
        handlePaste(event, gallery);
    });
}

/**
 * Přidání event listenerů pro tlačítka
 * @param {HTMLElement} moduleElement - Element modulu
 */
function addMessagingEventListeners(moduleElement) {
    // Event listenery pro přidání kanálu/serveru/skupiny
    moduleElement.querySelectorAll('.add-channel').forEach(button => {
        button.addEventListener('click', function() {
            const platform = this.getAttribute('data-platform');
            showAddChannelDialog(platform);
        });
    });

    // Event listenery pro aktualizaci dat
    moduleElement.querySelectorAll('.refresh-data').forEach(button => {
        button.addEventListener('click', function() {
            const platform = this.getAttribute('data-platform');
            refreshPlatformData(platform);
        });
    });

    // Event listenery pro export dat
    moduleElement.querySelectorAll('.export-data').forEach(button => {
        button.addEventListener('click', function() {
            const platform = this.getAttribute('data-platform');
            exportPlatformData(platform);
        });
    });

    // Event listener pro uložení nastavení
    const saveSettingsButton = moduleElement.querySelector('.save-settings');
    if (saveSettingsButton) {
        saveSettingsButton.addEventListener('click', saveMessagingSettings);
    }

    // Event listener pro testování emailových alertů
    const testEmailButton = moduleElement.querySelector('.test-email-alert');
    if (testEmailButton) {
        testEmailButton.addEventListener('click', testMessagingEmailAlert);
    }
}

/**
 * Načtení uložených dat
 */
function loadMessagingData() {
    console.log('Načítání uložených dat pro monitoring komunikačních platforem');

    // Simulace načtení dat z localStorage nebo API
    // V reálné aplikaci by zde byl API požadavek na backend
    setTimeout(() => {
        // Načtení nastavení alertů
        const savedAlertSettings = localStorage.getItem('messagingAlertSettings');
        if (savedAlertSettings) {
            try {
                messagingAlertSettings = JSON.parse(savedAlertSettings);
                updateSettingsUI();
            } catch (error) {
                console.error('Chyba při načítání nastavení alertů:', error);
            }
        }

        // Načtení dat platforem
        const savedPlatformsData = localStorage.getItem('messagingPlatformsData');
        if (savedPlatformsData) {
            try {
                messagingPlatformsData = JSON.parse(savedPlatformsData);
                updatePlatformsUI();
            } catch (error) {
                console.error('Chyba při načítání dat platforem:', error);
            }
        }
    }, 500);
}

/**
 * Aktualizace UI nastavení
 */
function updateSettingsUI() {
    const emailInput = document.getElementById('messaging-alert-email');
    const frequencySelect = document.getElementById('messaging-alert-frequency');
    const keywordsTextarea = document.getElementById('messaging-keywords');

    if (emailInput) {
        emailInput.value = messagingAlertSettings.email || '';
    }

    if (frequencySelect) {
        frequencySelect.value = messagingAlertSettings.frequency || 'daily';
    }

    if (keywordsTextarea) {
        keywordsTextarea.value = messagingAlertSettings.keywords.join(', ') || '';
    }
}

/**
 * Aktualizace UI platforem
 */
function updatePlatformsUI() {
    // Aktualizace Telegram UI
    updateTelegramUI();

    // Aktualizace Discord UI
    updateDiscordUI();

    // Aktualizace WhatsApp UI
    updateWhatsAppUI();
}

/**
 * Aktualizace Telegram UI
 */
function updateTelegramUI() {
    const channelsList = document.getElementById('telegram-channels');
    if (!channelsList) return;

    // Kontrola, zda existují kanály
    if (messagingPlatformsData.telegram.channels.length === 0) {
        channelsList.innerHTML = `
            <div class="no-channels-message">
                <i class="fas fa-info-circle"></i>
                <p>Zatím nesledujete žádné Telegram kanály. Přidejte kanál pomocí tlačítka výše.</p>
            </div>
        `;
        return;
    }

    // Vytvoření HTML pro kanály
    let channelsHtml = '';
    messagingPlatformsData.telegram.channels.forEach(channel => {
        channelsHtml += `
            <div class="channel-item" data-channel-id="${channel.id}" data-platform="telegram">
                <div class="channel-item-header">
                    <div class="channel-name">${channel.name}</div>
                    <div class="channel-type">${channel.type}</div>
                </div>
                <div class="channel-info">
                    <div class="channel-members">
                        <i class="fas fa-users"></i> ${channel.memberCount}
                    </div>
                    <div class="channel-last-update">
                        <i class="fas fa-clock"></i> ${formatDate(channel.lastUpdate)}
                    </div>
                </div>
            </div>
        `;
    });

    channelsList.innerHTML = channelsHtml;

    // Přidání event listenerů pro kliknutí na kanál
    channelsList.querySelectorAll('.channel-item').forEach(item => {
        item.addEventListener('click', function() {
            const channelId = this.getAttribute('data-channel-id');
            const platform = this.getAttribute('data-platform');

            // Odstranění aktivní třídy ze všech kanálů
            channelsList.querySelectorAll('.channel-item').forEach(i => i.classList.remove('active'));

            // Přidání aktivní třídy na kliknutý kanál
            this.classList.add('active');

            // Zobrazení detailu kanálu
            showChannelDetail(channelId, platform);
        });
    });
}

/**
 * Aktualizace Discord UI
 */
function updateDiscordUI() {
    const serversList = document.getElementById('discord-channels');
    if (!serversList) return;

    // Kontrola, zda existují servery
    if (messagingPlatformsData.discord.servers.length === 0) {
        serversList.innerHTML = `
            <div class="no-channels-message">
                <i class="fas fa-info-circle"></i>
                <p>Zatím nesledujete žádné Discord servery. Přidejte server pomocí tlačítka výše.</p>
            </div>
        `;
        return;
    }

    // Vytvoření HTML pro servery
    let serversHtml = '';
    messagingPlatformsData.discord.servers.forEach(server => {
        serversHtml += `
            <div class="channel-item" data-server-id="${server.id}" data-platform="discord">
                <div class="channel-item-header">
                    <div class="channel-name">${server.name}</div>
                    <div class="channel-type">Server</div>
                </div>
                <div class="channel-info">
                    <div class="channel-members">
                        <i class="fas fa-users"></i> ${server.memberCount}
                    </div>
                    <div class="channel-last-update">
                        <i class="fas fa-clock"></i> ${formatDate(server.lastUpdate)}
                    </div>
                </div>
            </div>
        `;
    });

    serversList.innerHTML = serversHtml;

    // Přidání event listenerů pro kliknutí na server
    serversList.querySelectorAll('.channel-item').forEach(item => {
        item.addEventListener('click', function() {
            const serverId = this.getAttribute('data-server-id');
            const platform = this.getAttribute('data-platform');

            // Odstranění aktivní třídy ze všech serverů
            serversList.querySelectorAll('.channel-item').forEach(i => i.classList.remove('active'));

            // Přidání aktivní třídy na kliknutý server
            this.classList.add('active');

            // Zobrazení detailu serveru
            showServerDetail(serverId, platform);
        });
    });
}

/**
 * Aktualizace WhatsApp UI
 */
function updateWhatsAppUI() {
    const groupsList = document.getElementById('whatsapp-channels');
    if (!groupsList) return;

    // Kontrola, zda existují skupiny
    if (messagingPlatformsData.whatsapp.groups.length === 0) {
        groupsList.innerHTML = `
            <div class="no-channels-message">
                <i class="fas fa-info-circle"></i>
                <p>Zatím nesledujete žádné WhatsApp skupiny. Přidejte skupinu pomocí tlačítka výše.</p>
            </div>
        `;
        return;
    }

    // Vytvoření HTML pro skupiny
    let groupsHtml = '';
    messagingPlatformsData.whatsapp.groups.forEach(group => {
        groupsHtml += `
            <div class="channel-item" data-group-id="${group.id}" data-platform="whatsapp">
                <div class="channel-item-header">
                    <div class="channel-name">${group.name}</div>
                    <div class="channel-type">Skupina</div>
                </div>
                <div class="channel-info">
                    <div class="channel-members">
                        <i class="fas fa-users"></i> ${group.memberCount}
                    </div>
                    <div class="channel-last-update">
                        <i class="fas fa-clock"></i> ${formatDate(group.lastUpdate)}
                    </div>
                </div>
            </div>
        `;
    });

    groupsList.innerHTML = groupsHtml;

    // Přidání event listenerů pro kliknutí na skupinu
    groupsList.querySelectorAll('.channel-item').forEach(item => {
        item.addEventListener('click', function() {
            const groupId = this.getAttribute('data-group-id');
            const platform = this.getAttribute('data-platform');

            // Odstranění aktivní třídy ze všech skupin
            groupsList.querySelectorAll('.channel-item').forEach(i => i.classList.remove('active'));

            // Přidání aktivní třídy na kliknutou skupinu
            this.classList.add('active');

            // Zobrazení detailu skupiny
            showGroupDetail(groupId, platform);
        });
    });
}
