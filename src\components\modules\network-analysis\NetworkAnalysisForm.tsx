"use client";

import { useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { NetworkAnalysisModuleFormValues, networkAnalysisModuleSchema } from "./schemas";
import { NetworkAnalysisModuleData, Subject, IpAddressRecord, DomainRecord, NetworkDevice } from "@/types";
import { IpAddressList } from "./IpAddressList";
import { IpAddressDetail } from "./IpAddressDetail";
import { DomainList } from "./DomainList";
import { DomainDetailForm } from "./DomainDetailForm";
import { DeviceList } from "./DeviceList";
import { DeviceDetail } from "./DeviceDetail";
import { NetworkMap } from "./NetworkMap";
import { Globe, Save, Plus, ArrowLeft, Server, Wifi, Activity } from "lucide-react";

interface NetworkAnalysisFormProps {
  caseId: string;
  subject: Subject;
  initialData?: NetworkAnalysisModuleData | null;
  existingData?: NetworkAnalysisModuleData | null;
  onSave: (data: NetworkAnalysisModuleFormValues) => Promise<void>;
}

export function NetworkAnalysisForm({
  caseId,
  subject,
  initialData,
  existingData,
  onSave
}: NetworkAnalysisFormProps) {
  // Použijeme existingData, pokud je k dispozici, jinak initialData
  const data = existingData || initialData;
  const [activeTab, setActiveTab] = useState("ip-list");
  const [selectedIpId, setSelectedIpId] = useState<string | null>(null);
  const [selectedDomainId, setSelectedDomainId] = useState<string | null>(null);
  const [selectedDeviceId, setSelectedDeviceId] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);

  const form = useForm<NetworkAnalysisModuleFormValues>({
    resolver: zodResolver(networkAnalysisModuleSchema),
    defaultValues: {
      ipAddresses: data?.ipAddresses || [],
      domains: data?.domains || [],
      devices: data?.devices || [],
      networkMap: data?.networkMap || "",
      generalNotes: data?.generalNotes || "",
    },
  });

  const ipAddresses = form.watch("ipAddresses");
  const domains = form.watch("domains");
  const devices = form.watch("devices");

  const handleSelectIp = (index: number) => {
    if (index >= 0 && index < ipAddresses.length) {
      // Použijeme setTimeout, abychom zabránili aktualizaci stavu během renderování
      setTimeout(() => {
        setSelectedIpId(ipAddresses[index].id);
        setActiveTab("ip-detail");
      }, 0);
    }
  };

  const handleAddIp = () => {
    const newIp: IpAddressRecord = {
      id: uuidv4(),
      ipAddress: "",
      ipType: "ipv4",
      source: "osint",
      verificationStatus: "unverified",
      discoveryDate: new Date().toISOString().split('T')[0],
    };

    form.setValue("ipAddresses", [...ipAddresses, newIp]);

    // Použijeme setTimeout, abychom zabránili aktualizaci stavu během renderování
    setTimeout(() => {
      setSelectedIpId(newIp.id);
      setActiveTab("ip-detail");
    }, 0);
  };

  const handleSelectDomain = (index: number) => {
    if (index >= 0 && index < domains.length) {
      setTimeout(() => {
        setSelectedDomainId(domains[index].id);
        setActiveTab("domain-detail");
      }, 0);
    }
  };

  const handleAddDomain = () => {
    const newDomain: DomainRecord = {
      id: uuidv4(),
      domainName: "",
    };

    form.setValue("domains", [...domains, newDomain]);

    setTimeout(() => {
      setSelectedDomainId(newDomain.id);
      setActiveTab("domain-detail");
    }, 0);
  };

  const handleSelectDevice = (index: number) => {
    if (index >= 0 && index < devices.length) {
      setTimeout(() => {
        setSelectedDeviceId(devices[index].id);
        setActiveTab("device-detail");
      }, 0);
    }
  };

  const handleAddDevice = () => {
    const newDevice: NetworkDevice = {
      id: uuidv4(),
      deviceType: "desktop",
      name: "",
    };

    form.setValue("devices", [...devices, newDevice]);

    setTimeout(() => {
      setSelectedDeviceId(newDevice.id);
      setActiveTab("device-detail");
    }, 0);
  };

  const handleSubmit = async (data: NetworkAnalysisModuleFormValues) => {
    setIsSaving(true);
    try {
      await onSave(data);
    } catch (error) {
      console.error("Error saving network analysis data:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const selectedIpIndex = ipAddresses.findIndex(ip => ip.id === selectedIpId);
  const selectedDomainIndex = domains.findIndex(domain => domain.id === selectedDomainId);
  const selectedDeviceIndex = devices.findIndex(device => device.id === selectedDeviceId);

  return (
    <FormProvider {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)}>
        <div className="space-y-4 pb-10">
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={isSaving || !form.formState.isDirty}
            >
              <Save className="mr-2 h-4 w-4" />
              {isSaving ? "Ukládání..." : "Uložit změny"}
            </Button>
          </div>

          <Separator />

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="ip-list">Seznam IP adres</TabsTrigger>
              <TabsTrigger value="ip-detail" disabled={!selectedIpId}>
                Detail IP adresy
              </TabsTrigger>
              <TabsTrigger value="domain-list">Seznam domén</TabsTrigger>
              <TabsTrigger value="domain-detail" disabled={!selectedDomainId}>
                Detail domény
              </TabsTrigger>
              <TabsTrigger value="device-list">Seznam zařízení</TabsTrigger>
              <TabsTrigger value="device-detail" disabled={!selectedDeviceId}>
                Detail zařízení
              </TabsTrigger>
              <TabsTrigger value="network-map">Síťová mapa</TabsTrigger>
              <TabsTrigger value="notes">Poznámky</TabsTrigger>
            </TabsList>

            <TabsContent value="ip-list" className="space-y-4">
              <div className="flex justify-end">
                <Button type="button" onClick={handleAddIp}>
                  <Plus className="mr-2 h-4 w-4" />
                  Přidat IP adresu
                </Button>
              </div>

              <IpAddressList
                control={form.control}
                onSelectIp={handleSelectIp}
                onAddIp={handleAddIp}
              />
            </TabsContent>

            <TabsContent value="ip-detail" className="space-y-4">
              {selectedIpId && selectedIpIndex !== -1 ? (
                <>
                  <div className="flex justify-between items-center">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setActiveTab("ip-list")}
                    >
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Zpět na seznam IP adres
                    </Button>
                  </div>

                  <ScrollArea className="h-[calc(100vh-250px)]">
                    <IpAddressDetail
                      control={form.control}
                      ipIndex={selectedIpIndex}
                      domains={domains}
                      devices={devices}
                    />
                  </ScrollArea>
                </>
              ) : (
                <Card>
                  <CardContent className="py-10 text-center">
                    <Globe className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      Vyberte IP adresu ze seznamu nebo přidejte novou
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      className="mt-4"
                      onClick={handleAddIp}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Přidat IP adresu
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="domain-list" className="space-y-4">
              <div className="flex justify-end">
                <Button type="button" onClick={handleAddDomain}>
                  <Plus className="mr-2 h-4 w-4" />
                  Přidat doménu
                </Button>
              </div>

              <DomainList
                control={form.control}
                onSelectDomain={handleSelectDomain}
                onAddDomain={handleAddDomain}
              />
            </TabsContent>

            <TabsContent value="domain-detail" className="space-y-4">
              {selectedDomainId && selectedDomainIndex !== -1 ? (
                <>
                  <div className="flex justify-between items-center">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setActiveTab("domain-list")}
                    >
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Zpět na seznam domén
                    </Button>
                  </div>

                  <ScrollArea className="h-[calc(100vh-250px)]">
                    <DomainDetailForm
                      domainIndex={selectedDomainIndex}
                    />
                  </ScrollArea>
                </>
              ) : (
                <Card>
                  <CardContent className="py-10 text-center">
                    <Server className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      Vyberte doménu ze seznamu nebo přidejte novou
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      className="mt-4"
                      onClick={handleAddDomain}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Přidat doménu
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="device-list" className="space-y-4">
              <div className="flex justify-end">
                <Button type="button" onClick={handleAddDevice}>
                  <Plus className="mr-2 h-4 w-4" />
                  Přidat zařízení
                </Button>
              </div>

              <DeviceList
                control={form.control}
                onSelectDevice={handleSelectDevice}
                onAddDevice={handleAddDevice}
              />
            </TabsContent>

            <TabsContent value="device-detail" className="space-y-4">
              {selectedDeviceId && selectedDeviceIndex !== -1 ? (
                <>
                  <div className="flex justify-between items-center">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setActiveTab("device-list")}
                    >
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Zpět na seznam zařízení
                    </Button>
                  </div>

                  <ScrollArea className="h-[calc(100vh-250px)]">
                    <DeviceDetail
                      control={form.control}
                      deviceIndex={selectedDeviceIndex}
                      ipAddresses={ipAddresses}
                    />
                  </ScrollArea>
                </>
              ) : (
                <Card>
                  <CardContent className="py-10 text-center">
                    <Wifi className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      Vyberte zařízení ze seznamu nebo přidejte nové
                    </p>
                    <Button
                      type="button"
                      variant="outline"
                      className="mt-4"
                      onClick={handleAddDevice}
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      Přidat zařízení
                    </Button>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="network-map" className="space-y-4">
              <NetworkMap
                control={form.control}
                ipAddresses={ipAddresses}
                domains={domains}
                devices={devices}
              />
            </TabsContent>

            <TabsContent value="notes">
              <Card>
                <CardHeader>
                  <CardTitle>Obecné poznámky</CardTitle>
                  <CardDescription>
                    Další informace o síťové analýze subjektu
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    {...form.register("generalNotes")}
                    placeholder="Zadejte obecné poznámky k modulu..."
                    className="min-h-[200px]"
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </form>
    </FormProvider>
  );
}
