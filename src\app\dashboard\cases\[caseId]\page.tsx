"use client";

import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { useEffect, useState, useCallback, ChangeEvent, useMemo } from "react"; // Přidáno useMemo
import type { Case, Subject, SubjectType, PhysicalPersonSubject, LegalEntitySubject, EvidenceObyvatelData, FamilyMembersModuleData, DriverLicenseModuleData, VehiclesModuleData, GunLicenseModuleData, LocationsModuleData, BusinessActivityModuleData, CadastreModuleData, TrainingModuleData, EmailAnalysisModuleData, PhoneNumbersModuleData, NetworkAnalysisModuleData, MapOverlaysModuleData, PhoneAnalysisModuleData, FinancialMonitoringModuleData } from "@/types";
import type { FacebookModuleData } from "@/types/facebook";
import type { InstagramModuleData } from "@/types/instagram";
import { Tooltip, TooltipProvider, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import {
  ArrowLeft, Edit, UserPlus, FilePlus, Activity, BarChart3, FileText as FileTextIcon, Info, Users, Layers,
  AlertTriangle, CheckCircle, Landmark, Fingerprint, CalendarClock, FileArchive, UserCircle as UserIconLucide, Loader2, Trash2, Edit3, Briefcase, HeartHandshake, Car, CarFront, Target, MapPin, Building, Building2, GraduationCap, Mail, Phone, Globe, Network, Map, Facebook, Instagram, Linkedin, Twitter, DollarSign
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";

import { EditCaseForm } from "@/components/cases/EditCaseForm";
import { db } from "@/lib/firebase";
import { doc, onSnapshot, updateDoc, serverTimestamp, Timestamp, collection, query, orderBy, deleteDoc, getDoc, setDoc, writeBatch, increment, getDocs } from "firebase/firestore";
import { useToast } from "@/hooks/use-toast";
import { LoadingSpinner, LoadingDots } from "@/components/ui/loading";
import { AddSubjectDialog } from "@/components/subjects/AddSubjectDialog";
import { EditSubjectDialog } from "@/components/subjects/EditSubjectDialog";
import { EvidenceObyvatelForm } from "@/components/modules/evidence-obyvatel/EvidenceObyvatelForm";
import { FamilyMembersForm } from "@/components/modules/family-members/FamilyMembersForm";
import { VehiclesForm } from "@/components/modules/vehicles/VehiclesForm";
import { DriverLicenseForm } from "@/components/modules/driver-license/DriverLicenseForm";
import { GunLicenseForm } from "@/components/modules/gun-license/GunLicenseForm";
import { LocationsForm } from "@/components/modules/locations/LocationsForm";
import { BusinessActivityForm } from "@/components/modules/business-activity/BusinessActivityForm";
import { CadastreForm } from "@/components/modules/cadastre/CadastreForm";
import { TrainingForm } from "@/components/modules/training/TrainingForm";
import { EmailAnalysisForm } from "@/components/modules/email-analysis/EmailAnalysisForm";
import { PhoneNumbersForm } from "@/components/modules/phone-numbers/PhoneNumbersForm";
import { NetworkAnalysisForm } from "@/components/modules/network-analysis/NetworkAnalysisForm";
import { MapOverlaysForm } from "@/components/modules/map-overlays/MapOverlaysForm";
import FacebookModule from "@/components/modules/facebook/FacebookModule";
import InstagramModule from "@/components/modules/instagram/InstagramModule";
import TwitterModule from "@/components/modules/twitter/TwitterModule";
import LinkedInModule from "@/components/modules/linkedin/LinkedInModule";
import ReportGenerator from "@/components/reports/ReportGenerator";
import PhoneAnalysisForm from "@/components/modules/phone-analysis/PhoneAnalysisForm";
import { FinancialMonitoringForm } from "@/components/modules/financial-monitoring/FinancialMonitoringForm";


const statusStyles: Record<Case["status"], string> = {
  active: "bg-green-100 text-green-700 border-green-200",
  pending: "bg-yellow-100 text-yellow-700 border-yellow-200",
  completed: "bg-blue-100 text-blue-700 border-blue-200",
  archived: "bg-gray-100 text-gray-700 border-gray-200",
};

const priorityStyles: Record<Case["priority"], string> = {
  high: "bg-red-100 text-red-700",
  medium: "bg-yellow-100 text-yellow-700",
  low: "bg-green-100 text-green-700",
};

interface SummaryStatCardProps {
  title: string;
  value: string | number;
  icon: React.ElementType;
  className?: string;
  action?: React.ReactNode;
  description?: string;
}

function SummaryStatCard({ title, value, icon: Icon, className, action, description }: SummaryStatCardProps) {
  return (
    <Card className={cn("shadow-lg hover:shadow-xl transition-shadow", className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">{title}</CardTitle>
        <Icon className="h-5 w-5 text-primary" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-foreground">{value}</div>
        {description && <p className="text-xs text-muted-foreground pt-1">{description}</p>}
        {action && <div className="pt-2">{action}</div>}
      </CardContent>
    </Card>
  );
}

interface CaseUpdateData {
  title: string;
  referenceNumber?: string;
  internalId?: string;
  description: string;
  deadlineInfo: string;
  priority?: Case["priority"];
  status?: Case["status"];
  investigatorName?: string;
  progress?: number;
}

type ModuleData = EvidenceObyvatelData | FamilyMembersModuleData | DriverLicenseModuleData | VehiclesModuleData | GunLicenseModuleData | LocationsModuleData | BusinessActivityModuleData | CadastreModuleData | TrainingModuleData | EmailAnalysisModuleData | PhoneNumbersModuleData | NetworkAnalysisModuleData | MapOverlaysModuleData | FacebookModuleData | InstagramModuleData | PhoneAnalysisModuleData | FinancialMonitoringModuleData | null;

const KNOWN_MODULE_IDS: Record<SubjectType, string[]> = {
  physical: ["evidence_obyvatel", "family_members", "registr_ridicske_prukazy", "gun_license", "vehicles", "locations", "business_activity", "cadastre", "training", "email_analysis", "phone_numbers", "network_analysis", "map_overlays", "facebook", "instagram", "twitter", "linkedin", "phone_analysis", "financial_monitoring"],
  legal: ["vehicles", "locations", "business_activity", "cadastre", "training", "email_analysis", "phone_numbers", "network_analysis", "map_overlays", "facebook", "instagram", "twitter", "linkedin", "phone_analysis", "financial_monitoring"],
};

export default function CaseDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const caseId = params.caseId as string;

  const [caseData, setCaseData] = useState<Case | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isSavingGlobal, setIsSavingGlobal] = useState(false); // Globální stav ukládání/mazání

  const [isAddSubjectDialogOpen, setIsAddSubjectDialogOpen] = useState(false);
  const [isEditSubjectDialogOpen, setIsEditSubjectDialogOpen] = useState(false);
  const [subjectToEdit, setSubjectToEdit] = useState<Subject | null>(null);
  const [subjectToDelete, setSubjectToDelete] = useState<Subject | null>(null);
  const [isDeleteSubjectAlertOpen, setIsDeleteSubjectAlertOpen] = useState(false);

  const [subjects, setSubjects] = useState<Subject[]>([]);
  const [subjectsLoading, setSubjectsLoading] = useState(true);

  const [selectedSubjectForModules, setSelectedSubjectForModules] = useState<Subject | null>(null);
  const [selectedModuleKey, setSelectedModuleKey] = useState<string | null>(null);
  const [currentModuleData, setCurrentModuleData] = useState<ModuleData>(null);
  const [moduleDataLoading, setModuleDataLoading] = useState(false);
  const [moduleCompletionStatus, setModuleCompletionStatus] = useState<Record<string, boolean>>({});

  // Helper function to safely get module data ID
  const getModuleDataId = (data: ModuleData): string => {
    if (!data) return 'new';
    // Try to get id from data, fallback to timestamp
    if (typeof data === 'object' && 'id' in data && data.id) {
      return String(data.id);
    }
    if (typeof data === 'object' && 'createdAt' in data && data.createdAt) {
      return String((data as any).createdAt.seconds || Date.now());
    }
    return 'existing';
  };

  useEffect(() => {
    if (caseId) {
      setLoading(true);
      const docRef = doc(db, "cases", caseId);
      const unsubscribe = onSnapshot(docRef, (docSnap) => {
        if (docSnap.exists()) {
          const data = docSnap.data();
          let formattedCreationDate = 'Neznámo';
          if (data.createdAt instanceof Timestamp) {
            formattedCreationDate = data.createdAt.toDate().toLocaleDateString('cs-CZ', { year: 'numeric', month: '2-digit', day: '2-digit' });
          } else if (typeof data.createdAt === 'string') {
             formattedCreationDate = data.createdAt;
          } else if (data.createdAt && data.createdAt.seconds) {
             formattedCreationDate = new Timestamp(data.createdAt.seconds, data.createdAt.nanoseconds).toDate().toLocaleDateString('cs-CZ', { year: 'numeric', month: '2-digit', day: '2-digit' });
          }

          setCaseData({
            id: docSnap.id,
            ...data,
            title: data.title || "Neznámý případ",
            status: data.status || "pending",
            priority: data.priority || "medium",
            subjectsCount: data.subjectsCount || 0,
            creationDate: formattedCreationDate,
            investigatorId: data.investigatorId || "",
            investigatorName: data.investigatorName || "Nepřiřazeno",
            deadlineInfo: data.deadlineInfo || "Nespecifikováno",
            description: data.description || "",
            progress: data.progress || 0,
            modulesCompleted: data.modulesCompleted || 0,
            modulesTotal: data.modulesTotal || 0,
            referenceNumber: data.referenceNumber || "",
            internalId: data.internalId || "",
            createdAt: data.createdAt as Timestamp,
            updatedAt: data.updatedAt as Timestamp,
          } as Case);
        } else {
          setCaseData(null);
          toast({
            title: "Případ nenalezen",
            description: `Případ s ID "${caseId}" nebyl nalezen.`,
            variant: "destructive",
          });
          router.push("/dashboard/cases");
        }
        setLoading(false);
      }, (error) => {
        console.error("Error fetching case details:", error);
        toast({
          title: "Chyba při načítání detailu případu",
          description: error.message || "Nepodařilo se načíst informace o případu.",
          variant: "destructive",
        });
        setLoading(false);
        setCaseData(null);
      });
      return () => unsubscribe();
    } else {
      setLoading(false);
    }
  }, [caseId, router, toast]);

  useEffect(() => {
    if (!caseId) return;
    setSubjectsLoading(true);
    const subjectsCollectionRef = collection(db, "cases", caseId, "subjects");
    const q = query(subjectsCollectionRef, orderBy("addedAt", "desc"));

    const unsubscribeSubjects = onSnapshot(q, (querySnapshot) => {
      const subjectsData: Subject[] = [];
      querySnapshot.forEach((docSub) => {
        subjectsData.push({ id: docSub.id, ...docSub.data() } as Subject);
      });
      setSubjects(subjectsData);
      setSubjectsLoading(false);

      // Aktualizace subjectsCount v případě, pokud se liší od počtu načtených subjektů
      if (caseData && (caseData.subjectsCount === undefined || caseData.subjectsCount !== subjectsData.length)) {
        const caseDocRef = doc(db, "cases", caseId);
        updateDoc(caseDocRef, { subjectsCount: subjectsData.length, updatedAt: serverTimestamp() })
          .catch(err => {
            console.error("Error updating subjectsCount: ", err);
            // toast({ title: "Chyba aktualizace počtu subjektů", description: err.message, variant: "destructive" }); // Může být příliš rušivé při každém načtení
          });
      }
    }, (error) => {
      console.error("Error fetching subjects:", error);
      toast({
        title: "Chyba při načítání subjektů",
        description: error.message,
        variant: "destructive",
      });
      setSubjectsLoading(false);
    });
    return () => unsubscribeSubjects();
  }, [caseId, caseData, toast]); // Závislost na caseData pro spuštění aktualizace subjectsCount

 useEffect(() => {
    if (selectedSubjectForModules && caseId) {
      const fetchModuleStatus = async () => {
        const status: Record<string, boolean> = {};
        const subjectType = selectedSubjectForModules.type as SubjectType;
        const moduleIdsToCheck = KNOWN_MODULE_IDS[subjectType] || [];

        for (const moduleId of moduleIdsToCheck) {
          try {
            const moduleDocRef = doc(db, "cases", caseId, "subjects", selectedSubjectForModules.id, "moduleData", moduleId);
            const docSnap = await getDoc(moduleDocRef);
             // Modul je vyplněný, pokud dokument existuje A obsahuje nějaká pole (ignorujeme prázdné dokumenty)
            status[moduleId] = docSnap.exists() && Object.keys(docSnap.data() || {}).length > 0;
          } catch (error) {
            console.error(`Error fetching status for module ${moduleId}:`, error);
            status[moduleId] = false;
          }
        }
        setModuleCompletionStatus(status);
      };
      fetchModuleStatus();
    } else {
      setModuleCompletionStatus({}); // Vyprázdnit statusy, pokud není vybraný subjekt
    }
  }, [selectedSubjectForModules, caseId]);


  const handleCaseUpdate = async (updatedData: CaseUpdateData) => {
    if (!caseData) return;
    setIsSavingGlobal(true); // Použít globální stav
    try {
      const caseDocRef = doc(db, "cases", caseData.id);
      await updateDoc(caseDocRef, { ...updatedData, updatedAt: serverTimestamp() });
      setIsEditDialogOpen(false);
      toast({ title: "Případ aktualizován" });
    } catch (error: any) {
      toast({ title: "Chyba při aktualizaci případu", description: error.message, variant: "destructive" });
    } finally {
      setIsSavingGlobal(false); // Použít globální stav
    }
  };

  const handleDeleteSubject = async () => {
    if (!subjectToDelete || !caseId) return;
    setIsSavingGlobal(true); // Použít globální stav
    try {
      const subjectDocRef = doc(db, "cases", caseId, "subjects", subjectToDelete.id);
      const moduleDataCollectionRef = collection(db, "cases", caseId, "subjects", subjectToDelete.id, "moduleData");
      const moduleDataSnapshot = await getDocs(moduleDataCollectionRef);

      const batch = writeBatch(db);
      let modulesDeletedCount = 0;
       // Smazat všechny dokumenty modulů spojené s tímto subjektem
      moduleDataSnapshot.forEach(mdDoc => {
         // Počítat jen moduly, které byly skutečně vyplněné (obsahovaly data)
        if(mdDoc.exists() && Object.keys(mdDoc.data() || {}).length > 0) {
          modulesDeletedCount++;
        }
        batch.delete(doc(db, "cases", caseId, "subjects", subjectToDelete.id, "moduleData", mdDoc.id));
      });
       // Smazat samotný dokument subjektu
      batch.delete(subjectDocRef);
      await batch.commit();

      toast({ title: "Subjekt smazán" });
      setSubjectToDelete(null);
      setIsDeleteSubjectAlertOpen(false);

      // Pokud byl smazán právě vybraný subjekt pro moduly, resetovat výběr
      if (selectedSubjectForModules?.id === subjectToDelete.id) {
        setSelectedSubjectForModules(null);
        setSelectedModuleKey(null);
        setCurrentModuleData(null);
      }

       // Aktualizovat počty subjektů a vyplněných modulů v dokumentu případu a progress
      if (caseData) {
        const caseDocRef = doc(db, "cases", caseId);
        try {
          // Nejprve získej aktuální data případu pro výpočet nového progress
          const caseDoc = await getDoc(caseDocRef);
          if (caseDoc.exists()) {
            const currentData = caseDoc.data();
            const currentCompleted = currentData.modulesCompleted || 0;
            const newCompleted = Math.max(0, currentCompleted - modulesDeletedCount);
            
            // Spočítej celkový počet modulů pro všechny zbývající subjekty (bez smazaného)
            let totalModules = 0;
            subjects.filter(s => s.id !== subjectToDelete.id).forEach(s => {
              const subjectType = s.type as SubjectType;
              totalModules += (KNOWN_MODULE_IDS[subjectType]?.length || 0);
            });
            
            // Vypočítej nový progress
            const newProgress = totalModules > 0 ? Math.round((newCompleted / totalModules) * 100) : 0;
            
            await updateDoc(caseDocRef, {
              subjectsCount: increment(-1),
              modulesCompleted: increment(-modulesDeletedCount), // Odečíst počet smazaných vyplněných modulů
              modulesTotal: totalModules,
              progress: newProgress,
              updatedAt: serverTimestamp()
            });
          }
        } catch (err: any) {
          console.error("Error updating case after subject deletion:", err);
          // toast({ title: "Chyba aktualizace statistik případu po smazání subjektu", description: err.message, variant: "destructive" }); // Může být příliš rušivé
        }
      }
    } catch (error: any) {
      toast({ title: "Chyba při mazání subjektu", description: error.message, variant: "destructive"});
    } finally {
      setIsSavingGlobal(false); // Použít globální stav
    }
  };

  const openEditSubjectDialog = (subject: Subject) => {
    setSubjectToEdit(subject);
    setIsEditSubjectDialogOpen(true);
  };

  const openDeleteSubjectConfirm = (subject: Subject) => {
    setSubjectToDelete(subject);
    setIsDeleteSubjectAlertOpen(true);
  };

  const handleSelectSubjectForModules = (subjectId: string) => {
    const subject = subjects.find(s => s.id === subjectId);
    setSelectedSubjectForModules(subject || null);
    setSelectedModuleKey(null); // Resetovat výběr modulu při změně subjektu
    setCurrentModuleData(null); // Vyprázdnit data modulu
  };

  const handleSelectModule = async (moduleKey: string) => {
    setSelectedModuleKey(moduleKey);
    if (selectedSubjectForModules && caseId) {
      setModuleDataLoading(true);
      try {
        const moduleDocRef = doc(db, "cases", caseId, "subjects", selectedSubjectForModules.id, "moduleData", moduleKey);
        const docSnap = await getDoc(moduleDocRef);
        if (docSnap.exists()) {
          setCurrentModuleData({ id: docSnap.id, ...docSnap.data() } as ModuleData);
        } else {
          setCurrentModuleData(null); // Resetovat data, pokud modul neexistuje
        }
      } catch (error: any) {
        toast({ title: "Chyba načítání dat modulu", description: error.message, variant: "destructive" });
        setCurrentModuleData(null);
      } finally {
        setModuleDataLoading(false);
      }
    } else {
      setCurrentModuleData(null); // Vyprázdnit data, pokud chybí subjekt nebo caseId
    }
  };

  // Callback funkce volaná po úspěšném uložení dat modulu
 const handleModuleDataSaved = useCallback(async (moduleId: string, wasNew: boolean) => {
    toast({ title: "Data modulu uložena", description: "Informace byly úspěšně zaznamenány." });

    // Okamžitá vizuální aktualizace stavu vyplnění
    setModuleCompletionStatus(prev => ({ ...prev, [moduleId]: true }));

    // Pokud byl modul nově vyplněn (tj. předtím neexistoval s daty), inkrementovat počet vyplněných modulů v případu a aktualizovat progress
    if (wasNew && caseId && selectedSubjectForModules) {
        const caseDocRef = doc(db, "cases", caseId);
        try {
            // Nejprve získej aktuální data případu pro výpočet nového progress
            const caseDoc = await getDoc(caseDocRef);
            if (caseDoc.exists()) {
                const currentData = caseDoc.data();
                const currentCompleted = currentData.modulesCompleted || 0;
                const newCompleted = currentCompleted + 1;
                
                // Spočítej celkový počet modulů pro všechny subjekty
                let totalModules = 0;
                subjects.forEach(s => {
                    const subjectType = s.type as SubjectType;
                    totalModules += (KNOWN_MODULE_IDS[subjectType]?.length || 0);
                });
                
                // Vypočítej nový progress
                const newProgress = totalModules > 0 ? Math.round((newCompleted / totalModules) * 100) : 0;
                
                await updateDoc(caseDocRef, {
                    modulesCompleted: increment(1),
                    modulesTotal: totalModules,
                    progress: newProgress,
                    updatedAt: serverTimestamp()
                });
            }
        } catch (error: any) {
            console.error("Chyba aktualizace statistik případu po uložení modulu:", error);
            toast({ title: "Chyba aktualizace statistik případu", description: error.message, variant: "destructive" });
        }
    }

    // Nezavíráme modul po uložení - necháme uživatele, aby se vrátil sám
    // Tím umožníme přidávat více prvků na mapu bez nutnosti znovu otevírat modul

    // Znovu načíst stavy vyplnění modulů pro aktuální subjekt, pro jistotu
     if (selectedSubjectForModules && caseId) {
        const status: Record<string, boolean> = {};
        const subjectType = selectedSubjectForModules.type as SubjectType;
        const moduleIdsToCheck = KNOWN_MODULE_IDS[subjectType] || [];
        for (const mId of moduleIdsToCheck) {
          try {
            const moduleDocRef = doc(db, "cases", caseId, "subjects", selectedSubjectForModules.id, "moduleData", mId);
            const docSnap = await getDoc(moduleDocRef);
            status[mId] = docSnap.exists() && Object.keys(docSnap.data() || {}).length > 0;
          } catch (error) {
            console.error(`Error re-fetching status for module ${mId}:`, error);
            status[mId] = false;
          }
        }
        setModuleCompletionStatus(status);
    }

}, [caseId, selectedSubjectForModules, toast]); // Závislosti useCallback

  // Použití useMemo pro výpočet celkového počtu modulů k zobrazení
  const displayModulesTotal = useMemo(() => {
      let total = 0;
      subjects.forEach(s => {
          const subjectType = s.type as SubjectType;
          total += (KNOWN_MODULE_IDS[subjectType]?.length || 0);
      });
      return total;
  }, [subjects]); // Přepočítat při změně seznamu subjektů

  // useEffect pro aktualizaci modulesTotal a progress při změně subjects
  useEffect(() => {
    if (caseId && caseData && subjects.length > 0) {
      const updateCaseProgress = async () => {
        try {
          const caseDocRef = doc(db, "cases", caseId);
          const caseDoc = await getDoc(caseDocRef);
          if (caseDoc.exists()) {
            const currentData = caseDoc.data();
            const currentCompleted = currentData.modulesCompleted || 0;
            
            // Spočítej celkový počet modulů pro všechny subjekty
            let totalModules = 0;
            subjects.forEach(s => {
              const subjectType = s.type as SubjectType;
              totalModules += (KNOWN_MODULE_IDS[subjectType]?.length || 0);
            });
            
            // Vypočítej nový progress
            const newProgress = totalModules > 0 ? Math.round((currentCompleted / totalModules) * 100) : 0;
            
            // Aktualizuj pouze pokud se hodnoty změnily
            if (currentData.modulesTotal !== totalModules || currentData.progress !== newProgress) {
              await updateDoc(caseDocRef, {
                modulesTotal: totalModules,
                progress: newProgress,
                updatedAt: serverTimestamp()
              });
            }
          }
        } catch (error: any) {
          console.error("Error updating case progress:", error);
        }
      };
      
      updateCaseProgress();
    }
  }, [subjects, caseId, caseData]); // Spustí se při změně subjects

  const physicalSubjectsCount = subjects.filter(s => s.type === 'physical').length;
  const legalSubjectsCount = subjects.length - physicalSubjectsCount;


  // --- Podmíněné renderování pro stav načítání nebo případu nenalezeného ---
  // Tyto bloky musí být na začátku komponenty a správně vracet JSX

  if (loading) {
    return (
      <div className="p-8 text-center">
        <Loader2 className="mx-auto h-12 w-12 animate-spin text-primary" />
        <p>Načítání detailu případu...</p>
      </div>
    );
  }

  if (!caseData) {
    return (
      <div className="p-8 text-center">
        <AlertTriangle className="mx-auto h-12 w-12 text-destructive" />
        <h2 className="mt-4 text-xl font-semibold text-destructive">Případ nenalezen nebo chyba načítání</h2>
        <p className="mt-2 text-muted-foreground">Je možné, že byl případ smazán nebo ID případu je chybné.</p>
        <Button onClick={() => router.push("/dashboard/cases")} className="mt-4">
             <ArrowLeft className="mr-2 h-4 w-4" /> Zpět na seznam případů
        </Button>
      </div>
    );
  }

  const displayCreationDate = caseData.creationDate || (caseData.createdAt instanceof Timestamp ? caseData.createdAt.toDate().toLocaleDateString('cs-CZ', { year: 'numeric', month: '2-digit', day: '2-digit' }) : 'Neznámo');
  const displayModulesCompleted = typeof caseData.modulesCompleted === 'number' ? caseData.modulesCompleted : 0;


  // --- Hlavní return statement s JSX ---
  // Tento blok začíná na řádku 433 a obsahuje veškeré hlavní UI komponenty

  return (
    <div className="container mx-auto px-4 py-8 md:px-6 lg:px-8">
      <div className="mb-8">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.push("/dashboard/cases")}
          className="mb-4 group"
        >
          <ArrowLeft className="mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform" />
          Zpět na seznam případů
        </Button>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex-grow min-w-0">
            <h1 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl truncate" title={caseData.title}>
              {caseData.title}
            </h1>
            <div className="mt-1 flex flex-wrap items-center gap-x-3 gap-y-1 text-xs">
              <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/30">
                ID: {caseData.id.substring(0,8)}...
              </Badge>
              {caseData.referenceNumber && (
                <Badge variant="outline">Č.J.: {caseData.referenceNumber}</Badge>
              )}
              {caseData.internalId && (
                <Badge variant="outline">JID: {caseData.internalId}</Badge>
              )}
            </div>
          </div>
          <div className="mt-4 flex flex-shrink-0 gap-2 md:mt-0">
            <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" disabled={isSavingGlobal}><Edit className="mr-2 h-4 w-4" /> Upravit případ</Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Upravit případ</DialogTitle>
                  <DialogDescription>Změňte údaje o případu.</DialogDescription>
                </DialogHeader>
                <EditCaseForm
                  caseData={caseData}
                  onSubmit={handleCaseUpdate}
                  onCancel={() => setIsEditDialogOpen(false)}
                  isSaving={isSavingGlobal} // Použít globální stav
                />
              </DialogContent>
            </Dialog>
            <ReportGenerator
              caseId={caseId}
              availableModules={
                // Vytvoříme seznam modulů pro všechny subjekty s daty
                subjects.flatMap(subject =>
                  Object.entries(moduleCompletionStatus)
                    .filter(([moduleId, hasData]) => hasData)
                    .map(([moduleId, _]) => {
                      // Vytvoříme ID ve formátu {subjectId}_{moduleId}
                      const fullModuleId = `${subject.id}_${moduleId}`;
                      let moduleName = moduleId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                      let category = 'Ostatní';
                      let icon = 'fas fa-file';

                      // Mapování názvů a kategorií
                      if (moduleId === "evidence_obyvatel") { moduleName = "Evidence obyvatel"; category = "Základní údaje"; icon = "fas fa-user"; }
                      if (moduleId === "family_members") { moduleName = "Rodinní příslušníci"; category = "Vztahy"; icon = "fas fa-users"; }
                      if (moduleId === "registr_ridicske_prukazy") { moduleName = "Registr řidičských průkazů"; category = "Doklady"; icon = "fas fa-id-card"; }
                      if (moduleId === "gun_license") { moduleName = "Zbrojní průkaz"; category = "Doklady"; icon = "fas fa-id-card"; }
                      if (moduleId === "vehicles") { moduleName = "Vozidla"; category = "Majetek"; icon = "fas fa-car"; }
                      if (moduleId === "locations") { moduleName = "Lokace"; category = "Lokace"; icon = "fas fa-map-marker"; }
                      if (moduleId === "business_activity") { moduleName = "Podnikatelské aktivity"; category = "Ekonomika"; icon = "fas fa-building"; }
                      if (moduleId === "cadastre") { moduleName = "Katastr nemovitostí"; category = "Nemovitosti"; icon = "fas fa-home"; }
                      if (moduleId === "training") { moduleName = "Školení a výcvik"; category = "Výcvik"; icon = "fas fa-graduation-cap"; }
                      if (moduleId === "email_analysis") { moduleName = "Emailová analýza"; category = "Komunikace"; icon = "fas fa-envelope"; }
                      if (moduleId === "phone_numbers") { moduleName = "Telefonní čísla"; category = "Komunikace"; icon = "fas fa-phone"; }
                      if (moduleId === "network_analysis") { moduleName = "IP adresy a síťová analýza"; category = "Síťová analýza"; icon = "fas fa-network-wired"; }
                      if (moduleId === "map_overlays") { moduleName = "Mapové overlapy"; category = "Lokace"; icon = "fas fa-map"; }
                      if (moduleId === "facebook") { moduleName = "Facebook"; category = "Sociální sítě"; icon = "fab fa-facebook"; }
                      if (moduleId === "instagram") { moduleName = "Instagram"; category = "Sociální sítě"; icon = "fab fa-instagram"; }
                      if (moduleId === "twitter") { moduleName = "Twitter"; category = "Sociální sítě"; icon = "fab fa-twitter"; }
                      if (moduleId === "linkedin") { moduleName = "LinkedIn"; category = "Sociální sítě"; icon = "fab fa-linkedin"; }
                      if (moduleId === "phone_analysis") { moduleName = "Telefonní analýza"; category = "Komunikace"; icon = "fas fa-phone"; }
                      if (moduleId === "financial_monitoring") { moduleName = "Finanční monitoring"; category = "Finanční analýza"; icon = "fas fa-dollar-sign"; }

                      return {
                        id: fullModuleId,
                        name: moduleName,
                        category: category,
                        icon: icon,
                        hasData: true
                      };
                    })
                )
              }
              caseData={{
                investigatorName: caseData?.investigatorName,
                subjects: subjects.map(s => ({
                  id: s.id,
                  type: s.type,
                  firstName: s.type === 'physical' ? (s as any).firstName : undefined,
                  lastName: s.type === 'physical' ? (s as any).lastName : undefined,
                  name: s.type === 'legal' ? (s as any).name : undefined
                }))
              }}
            />
          </div>
        </div>
      </div>

      <div className="mb-8 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <SummaryStatCard title="Počet subjektů" value={subjects.length} icon={Users} description={`${physicalSubjectsCount} fyz., ${legalSubjectsCount} práv.`}/>
        <SummaryStatCard title="Modulů vyplněno" value={`${displayModulesCompleted}/${displayModulesTotal > 0 ? displayModulesTotal : 'N/A'}`} icon={Layers} description={displayModulesTotal > 0 && displayModulesCompleted > 0 ? `Pokrok: ${Math.round((displayModulesCompleted/displayModulesTotal)*100)}%` : displayModulesTotal > 0 && displayModulesCompleted === 0 ? "Zatím žádný modul vyplněn" : "Žádné relevantní moduly"} />
        <SummaryStatCard title="Priorita" value={caseData.priority.charAt(0).toUpperCase() + caseData.priority.slice(1)} icon={AlertTriangle} className={cn(priorityStyles[caseData.priority], "border-0")} />
        <SummaryStatCard title="Stav" value={caseData.status.charAt(0).toUpperCase() + caseData.status.slice(1)} icon={CheckCircle} className={cn(statusStyles[caseData.status], "border-0")} />
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-2 md:grid-cols-3 lg:grid-cols-6 mb-6">
          <TabsTrigger value="overview"><Info className="mr-1 h-4 w-4" />Přehled</TabsTrigger>
          <TabsTrigger value="subjects"><Users className="mr-1 h-4 w-4" />Subjekty</TabsTrigger>
          <TabsTrigger value="modules"><Layers className="mr-1 h-4 w-4" />Moduly</TabsTrigger>
          <TabsTrigger value="findings"><FilePlus className="mr-1 h-4 w-4" />Nálezy</TabsTrigger>
          <TabsTrigger value="analytics"><BarChart3 className="mr-1 h-4 w-4" />Analytika</TabsTrigger>
          <TabsTrigger value="notes"><FileTextIcon className="mr-1 h-4 w-4" />Poznámky</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <Card className="shadow-lg"><CardHeader><CardTitle className="text-xl">Základní informace o případu</CardTitle></CardHeader>
            <CardContent className="space-y-6 pt-4">
              <div><h3 className="font-semibold text-muted-foreground mb-1">Popis skutku / Úvodní informace</h3><p className="text-foreground/90 whitespace-pre-wrap">{caseData.description || "Není zadán žádný popis."}</p></div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-6 gap-y-4">
                <div><h3 className="font-semibold text-muted-foreground flex items-center"><UserIconLucide className="mr-2 h-4 w-4 text-primary" />Vyšetřovatel</h3><p>{caseData.investigatorName || "Nepřiřazeno"}</p></div>
                <div><h3 className="font-semibold text-muted-foreground flex items-center"><CalendarClock className="mr-2 h-4 w-4 text-primary" />Datum vytvoření</h3><p>{displayCreationDate}</p></div>
                <div><h3 className="font-semibold text-muted-foreground flex items-center"><Landmark className="mr-2 h-4 w-4 text-primary" />Č.J.</h3><p>{caseData.referenceNumber || "Nezadáno"}</p></div>
                <div><h3 className="font-semibold text-muted-foreground flex items-center"><Fingerprint className="mr-2 h-4 w-4 text-primary" />JID</h3><p>{caseData.internalId || "Nezadáno"}</p></div>
                <div><h3 className="font-semibold text-muted-foreground flex items-center"><AlertTriangle className="mr-2 h-4 w-4 text-primary" />Termín</h3><p>{caseData.deadlineInfo || "Nezadáno"}</p></div>
                <div><h3 className="font-semibold text-muted-foreground flex items-center"><Activity className="mr-2 h-4 w-4 text-primary" />Pokrok</h3><p>{caseData.progress || 0}%</p></div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="subjects">
          <Card className="shadow-lg">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle  className="text-xl">Subjekty případu ({subjects.length})</CardTitle>
              <Button size="sm" onClick={() => { setIsAddSubjectDialogOpen(true);}} disabled={isSavingGlobal}>
                <UserPlus className="mr-2 h-4 w-4" />Přidat subjekt
              </Button>
            </CardHeader>
            <CardContent className="pt-4">
              {subjectsLoading && (
                <div className="flex flex-col items-center justify-center py-12 space-y-4">
                  <LoadingSpinner size="lg" className="text-primary" />
                  <div className="text-center">
                    <h3 className="text-lg font-medium text-gray-900">Načítání subjektů</h3>
                    <p className="text-sm text-gray-600 mt-1">Získáváme seznam osob a subjektů případu</p>
                    <LoadingDots className="mt-3 text-gray-400" />
                  </div>
                </div>
              )}
              {!subjectsLoading && subjects.length === 0 && <p className="text-muted-foreground text-center py-8">Nebyly přidány žádné subjekty. Klikněte na "Přidat subjekt" pro zahájení.</p>}
              <div className="mt-4 space-y-4">
                {subjects.map((sub) => (
                  <Card key={sub.id} className="p-4 shadow-md hover:shadow-lg transition-shadow">
                    <div className="flex flex-col sm:flex-row justify-between items-start gap-3">
                      <div className="flex-grow">
                        <h4 className="font-semibold text-lg text-primary">{sub.type === 'physical' ? `${(sub as PhysicalPersonSubject).firstName} ${(sub as PhysicalPersonSubject).lastName}` : (sub as LegalEntitySubject).name}</h4>
                        <div className="flex items-center gap-2 mt-1">
                            <Badge variant={sub.type === 'physical' ? 'secondary' : 'outline'} className={cn(sub.type === 'physical' ? "bg-blue-100 text-blue-700 border-blue-300" : "bg-purple-100 text-purple-700 border-purple-300")}>
                                {sub.type === 'physical' ? <><UserIconLucide className="mr-1 h-3 w-3"/>Fyzická osoba</> : <><Briefcase className="mr-1 h-3 w-3"/>Právnická osoba</>}
                            </Badge>
                            {sub.type === 'legal' && (sub as LegalEntitySubject).companyId && <p className="text-xs text-muted-foreground">IČO: {(sub as LegalEntitySubject).companyId}</p>}
                            {sub.type === 'physical' && (sub as PhysicalPersonSubject).dateOfBirth && <p className="text-xs text-muted-foreground">Nar.: {(sub as PhysicalPersonSubject).dateOfBirth}</p>}
                        </div>
                      </div>
                      <div className="flex gap-1 flex-shrink-0 mt-2 sm:mt-0">
                        <Button variant="ghost" size="icon" onClick={() => openEditSubjectDialog(sub)} title="Upravit subjekt" disabled={isSavingGlobal}><Edit3 className="h-5 w-5 text-muted-foreground hover:text-primary" /></Button>
                        <Button variant="ghost" size="icon" onClick={() => openDeleteSubjectConfirm(sub)} className="text-destructive hover:text-destructive/90 hover:bg-destructive/10" title="Smazat subjekt" disabled={isSavingGlobal}><Trash2 className="h-5 w-5" /></Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="modules">
          <Card className="shadow-lg">
            <CardHeader><CardTitle  className="text-xl">OSINT Moduly</CardTitle><CardDescription>Vyberte subjekt a spusťte požadovaný OSINT modul pro sběr a analýzu dat.</CardDescription></CardHeader>
            <CardContent className="space-y-6 pt-4">
              {subjectsLoading ? <div className="flex justify-center py-4"><Loader2 className="h-6 w-6 animate-spin text-primary"/></div> : subjects.length === 0 ? (
                <p className="text-muted-foreground text-center py-4">Nejprve přidejte subjekty do případu v záložce "Subjekty", poté zde budete moci spouštět OSINT moduly.</p>
              ) : (
                <div className="space-y-2">
                  <Label htmlFor="subject-select-modules" className="font-semibold">Cílový subjekt pro OSINT</Label>
                  <Select onValueChange={handleSelectSubjectForModules} value={selectedSubjectForModules?.id || ""} disabled={isSavingGlobal || moduleDataLoading}>
                    <SelectTrigger id="subject-select-modules" className="w-full md:w-[400px] text-base md:text-sm">
                      <SelectValue placeholder="Vyberte subjekt pro OSINT..." />
                    </SelectTrigger>
                    <SelectContent>
                      {subjects.map(s => (
                        <SelectItem key={s.id} value={s.id}>
                          {s.type === 'physical' ? `${(s as PhysicalPersonSubject).firstName} ${(s as PhysicalPersonSubject).lastName}` : (s as LegalEntitySubject).name}
                          <span className="ml-2 text-xs text-muted-foreground">({s.type === 'physical' ? 'FO' : 'PO'})</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              )}

              {selectedSubjectForModules && !selectedModuleKey && (
                <div className="pt-6 mt-6 border-t">
                  <h3 className="text-lg font-medium mb-4">Dostupné moduly pro: <span className="font-semibold text-primary">{selectedSubjectForModules.type === 'physical' ? `${(selectedSubjectForModules as PhysicalPersonSubject).firstName} ${(selectedSubjectForModules as PhysicalPersonSubject).lastName}` : (selectedSubjectForModules as LegalEntitySubject).name}</span></h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                    {(KNOWN_MODULE_IDS[selectedSubjectForModules.type as SubjectType] || []).map(moduleId => {
                        let IconComponent: React.ElementType = Layers;
                        let moduleName = moduleId.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                        if (moduleId === "evidence_obyvatel") { IconComponent = UserIconLucide; moduleName = "Evidence obyvatel"; }
                        if (moduleId === "family_members") { IconComponent = HeartHandshake; moduleName = "Rodinní příslušníci"; }
                        if (moduleId === "registr_ridicske_prukazy") { IconComponent = Car; moduleName = "Registr řidičských průkazů"; }
                        if (moduleId === "gun_license") { IconComponent = Target; moduleName = "Zbrojní průkaz"; }
                        if (moduleId === "vehicles") { IconComponent = CarFront; moduleName = "Vozidla"; }
                        if (moduleId === "locations") { IconComponent = MapPin; moduleName = "Lokace"; }
                        if (moduleId === "business_activity") { IconComponent = Building; moduleName = "Podnikatelské aktivity"; }
                        if (moduleId === "email_analysis") { IconComponent = Mail; moduleName = "Emailová analýza"; }
                        if (moduleId === "phone_numbers") { IconComponent = Phone; moduleName = "Telefonní čísla"; }
                        if (moduleId === "cadastre") { IconComponent = Building2; moduleName = "Katastr nemovitostí"; }
                        if (moduleId === "training") { IconComponent = GraduationCap; moduleName = "Školení a výcvik"; }
                        if (moduleId === "network_analysis") { IconComponent = Network; moduleName = "IP adresy a síťová analýza"; }
                        if (moduleId === "map_overlays") { IconComponent = Map; moduleName = "Mapové overlapy"; }
                        if (moduleId === "facebook") { IconComponent = Facebook; moduleName = "Facebook"; }
                        if (moduleId === "instagram") { IconComponent = Instagram; moduleName = "Instagram"; }
                        if (moduleId === "twitter") { IconComponent = Twitter; moduleName = "Twitter"; }
                        if (moduleId === "linkedin") { IconComponent = Linkedin; moduleName = "LinkedIn"; }
                        if (moduleId === "phone_analysis") { IconComponent = Phone; moduleName = "Telefonní analýza"; }
                        if (moduleId === "financial_monitoring") { IconComponent = DollarSign; moduleName = "Finanční monitoring"; }

                        const isCompleted = !!moduleCompletionStatus[moduleId];
                        return (
                            <Button
                              key={moduleId}
                              onClick={() => handleSelectModule(moduleId)}
                              variant={isCompleted ? "default" : "outline"}
                              className={cn(
                                "justify-start h-auto py-3 text-left hover:border-primary",
                                isCompleted ? "bg-primary/15 hover:bg-primary/25 border-primary/30" : "hover:bg-accent"
                              )}
                              disabled={moduleDataLoading || isSavingGlobal}
                            >
                              {isCompleted && <CheckCircle className="mr-2 h-5 w-5 text-green-500 shrink-0" />}
                              {!isCompleted && <IconComponent className="mr-2 h-5 w-5 shrink-0" />}
                              <span className="flex-grow">{moduleName}</span>
                            </Button>
                        );
                    })}
                    {(KNOWN_MODULE_IDS[selectedSubjectForModules.type as SubjectType] || []).length === 0 && (
                       <p className="text-muted-foreground col-span-full">Pro tento typ subjektu nejsou definovány žádné moduly.</p>
                    )}
                  </div>
                </div>
              )}

              {selectedSubjectForModules && selectedModuleKey && (
                <div className="pt-6 mt-6 border-t">
                   <Button variant="outline" size="sm" onClick={() => { setSelectedModuleKey(null); setCurrentModuleData(null); }} className="mb-4" disabled={moduleDataLoading || isSavingGlobal}>
                     <ArrowLeft className="mr-2 h-4 w-4" /> Zpět na výběr modulů
                   </Button>
                  {moduleDataLoading ? (
                     <div className="flex flex-col items-center justify-center py-16 space-y-4">
                       <LoadingSpinner size="lg" className="text-primary" />
                       <div className="text-center">
                         <h3 className="text-lg font-medium text-gray-900">Načítání dat modulu</h3>
                         <p className="text-sm text-gray-600 mt-1">Připravujeme formulář pro {selectedModuleKey?.replace('_', ' ')}</p>
                         <LoadingDots className="mt-3 text-gray-400" />
                       </div>
                     </div>
                  ) : (
                    <>
                      {selectedModuleKey === "evidence_obyvatel" && selectedSubjectForModules.type === 'physical' ? (
                          <EvidenceObyvatelForm
                            key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                            caseId={caseId}
                            subject={selectedSubjectForModules as PhysicalPersonSubject}
                            existingData={currentModuleData as EvidenceObyvatelData | null}
                            onSave={handleModuleDataSaved}
                          />
                        ) : selectedModuleKey === "family_members" && selectedSubjectForModules.type === 'physical' ? (
                          <FamilyMembersForm
                             key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                             caseId={caseId}
                             subject={selectedSubjectForModules as PhysicalPersonSubject}
                             existingData={currentModuleData as FamilyMembersModuleData | null}
                             onSave={handleModuleDataSaved}
                          />
                        ) : selectedModuleKey === "registr_ridicske_prukazy" && selectedSubjectForModules.type === 'physical' ? (
                          <DriverLicenseForm
                             key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                             caseId={caseId}
                             subject={selectedSubjectForModules as PhysicalPersonSubject}
                             existingData={currentModuleData as DriverLicenseModuleData | null}
                             onSave={handleModuleDataSaved}
                          />
                        ) : selectedModuleKey === "gun_license" && selectedSubjectForModules.type === 'physical' ? (
                          <GunLicenseForm
                             key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                             caseId={caseId}
                             subject={selectedSubjectForModules as PhysicalPersonSubject}
                             existingData={currentModuleData as GunLicenseModuleData | null}
                             onSave={handleModuleDataSaved}
                          />
                        ) : selectedModuleKey === "vehicles" ? (
                          <VehiclesForm
                             key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                             caseId={caseId}
                             subject={selectedSubjectForModules}
                             existingData={currentModuleData as VehiclesModuleData | null}
                             onSave={handleModuleDataSaved}
                          />
                        ) : selectedModuleKey === "locations" ? (
                          <LocationsForm
                             key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                             caseId={caseId}
                             subject={selectedSubjectForModules}
                             existingData={currentModuleData as LocationsModuleData | null}
                             onSave={handleModuleDataSaved}
                          />
                        ) : selectedModuleKey === "business_activity" ? (
                          <BusinessActivityForm
                             key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                             caseId={caseId}
                             subject={selectedSubjectForModules}
                             existingData={currentModuleData as BusinessActivityModuleData | null}
                             onSave={handleModuleDataSaved}
                          />
                        ) : selectedModuleKey === "cadastre" ? (
                          <CadastreForm
                             key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                             caseId={caseId}
                             subject={selectedSubjectForModules}
                             existingData={currentModuleData as CadastreModuleData | null}
                             onSave={handleModuleDataSaved}
                          />
                        ) : selectedModuleKey === "training" ? (
                          <TrainingForm
                             key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                             caseId={caseId}
                             subject={selectedSubjectForModules}
                             existingData={currentModuleData as TrainingModuleData | null}
                             onSave={handleModuleDataSaved}
                          />
                        ) : selectedModuleKey === "email_analysis" ? (
                          <EmailAnalysisForm
                             key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                             caseId={caseId}
                             subject={selectedSubjectForModules}
                             existingData={currentModuleData as EmailAnalysisModuleData | null}
                             onSave={handleModuleDataSaved}
                          />
                        ) : selectedModuleKey === "phone_numbers" ? (
                          <PhoneNumbersForm
                             key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                             caseId={caseId}
                             subject={selectedSubjectForModules}
                             initialData={currentModuleData as PhoneNumbersModuleData | undefined}
                             onSave={(data: any) => handleModuleDataSaved('phone_numbers', !currentModuleData)}
                             onBack={() => { setSelectedModuleKey(null); setCurrentModuleData(null); }}
                          />
                        ) : selectedModuleKey === "network_analysis" ? (
                          <NetworkAnalysisForm
                             key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                             caseId={caseId}
                             subject={selectedSubjectForModules}
                             initialData={currentModuleData as NetworkAnalysisModuleData | undefined}
                             onSave={(data: any) => handleModuleDataSaved('network_analysis', !currentModuleData)}
                          />
                        ) : selectedModuleKey === "map_overlays" ? (
                          <div
                            onClick={(e) => e.stopPropagation()}
                            className="w-full h-full"
                            style={{ pointerEvents: "auto" }}
                          >
                            <MapOverlaysForm
                               key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                               caseId={caseId}
                               subject={selectedSubjectForModules as any}
                               initialData={currentModuleData as MapOverlaysModuleData | undefined}
                               onSave={(data: any) => handleModuleDataSaved('map_overlays', !currentModuleData)}
                               onBack={() => { setSelectedModuleKey(null); setCurrentModuleData(null); }}
                            />
                          </div>
                        ) : selectedModuleKey === "facebook" ? (
                          <FacebookModule
                             key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                             caseId={caseId}
                             subject={selectedSubjectForModules as any}
                             moduleId={selectedModuleKey}
                             onClose={() => { setSelectedModuleKey(null); setCurrentModuleData(null); }}
                          />
                        ) : selectedModuleKey === "instagram" ? (
                          <InstagramModule
                             key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                             caseId={caseId}
                             subject={selectedSubjectForModules as any}
                             moduleId={selectedModuleKey}
                             onClose={() => { setSelectedModuleKey(null); setCurrentModuleData(null); }}
                          />
                        ) : selectedModuleKey === "twitter" ? (
                          <TwitterModule
                             key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                             caseId={caseId}
                             subject={selectedSubjectForModules as any}
                             moduleId={selectedModuleKey}
                             onClose={() => { setSelectedModuleKey(null); setCurrentModuleData(null); }}
                          />
                        ) : selectedModuleKey === "linkedin" ? (
                          <LinkedInModule
                             key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                             caseId={caseId}
                             subject={selectedSubjectForModules as any}
                             moduleId={selectedModuleKey}
                             onClose={() => { setSelectedModuleKey(null); setCurrentModuleData(null); }}
                          />
                        ) : selectedModuleKey === "phone_analysis" ? (
                          <PhoneAnalysisForm
                             key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                             caseId={caseId}
                             subject={{
                               id: selectedSubjectForModules.id,
                               firstName: selectedSubjectForModules.type === 'physical' ? selectedSubjectForModules.firstName : selectedSubjectForModules.name,
                               lastName: selectedSubjectForModules.type === 'physical' ? selectedSubjectForModules.lastName : '',
                               type: selectedSubjectForModules.type === 'physical' ? 'person' : 'company'
                             }}
                             moduleId={selectedModuleKey}
                             existingData={currentModuleData as PhoneAnalysisModuleData | null}
                             onSave={(data: PhoneAnalysisModuleData, wasNew: boolean) => handleModuleDataSaved(selectedModuleKey, wasNew)}
                             onCancel={() => { setSelectedModuleKey(null); setCurrentModuleData(null); }}
                          />
                        ) : selectedModuleKey === "financial_monitoring" ? (
                          <FinancialMonitoringForm
                             key={`${selectedSubjectForModules.id}-${selectedModuleKey}-${getModuleDataId(currentModuleData)}`}
                             caseId={caseId}
                             subject={selectedSubjectForModules}
                             existingData={currentModuleData as FinancialMonitoringModuleData | null}
                             onSave={handleModuleDataSaved}
                          />
                        ) : (
                          <p>Vybraný modul není pro tento typ subjektu dostupný nebo se nepodařilo načíst komponentu.</p>
                        )}
                    </>
                  )}
                </div>
              )}
              {!selectedSubjectForModules && subjects.length > 0 && <p className="text-muted-foreground mt-6 text-center">Vyberte subjekt pro zobrazení a spuštění dostupných OSINT modulů.</p>}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="findings"><Card className="shadow-lg"><CardHeader><CardTitle className="text-xl">Nálezy a Důkazy</CardTitle></CardHeader><CardContent className="pt-4"><p className="text-muted-foreground">Zde budou shromažďovány klíčové nálezy, důkazy a soubory relevantní k případu. (TODO)</p></CardContent></Card></TabsContent>
        <TabsContent value="analytics"><Card className="shadow-lg"><CardHeader><CardTitle className="text-xl">Analytické Nástroje</CardTitle></CardHeader><CardContent className="pt-4"><p className="text-muted-foreground">Tato sekce bude obsahovat vizualizace, grafy vztahů a další analytické nástroje. (TODO)</p></CardContent></Card></TabsContent>
        <TabsContent value="notes"><Card className="shadow-lg"><CardHeader><CardTitle className="text-xl">Poznámky k případu</CardTitle></CardHeader><CardContent className="pt-4"><Textarea placeholder="Zde si můžete psát průběžné poznámky, myšlenky a úkoly..." rows={10} className="text-base md:text-sm" /><Button className="mt-3" disabled>Uložit poznámky (TODO)</Button></CardContent></Card></TabsContent>

      </Tabs>

              {caseId && <AddSubjectDialog open={isAddSubjectDialogOpen} onOpenChange={setIsAddSubjectDialogOpen} caseId={caseId} onSubjectAdded={() => toast({ title: "Subjekt úspěšně přidán" })}/>}
      {caseId && subjectToEdit && <EditSubjectDialog open={isEditSubjectDialogOpen} onOpenChange={setIsEditSubjectDialogOpen} caseId={caseId} subject={subjectToEdit} onSubjectUpdated={() => { toast({ title: "Subjekt úspěšně aktualizován" }); setSubjectToEdit(null); }}/>}

      <AlertDialog open={isDeleteSubjectAlertOpen} onOpenChange={setIsDeleteSubjectAlertOpen}>
        <AlertDialogContent>
          <AlertDialogHeader><AlertDialogTitle>Opravdu smazat subjekt?</AlertDialogTitle>
            <AlertDialogDescription>Tato akce nelze vrátit zpět. Subjekt "{subjectToDelete?.type === 'physical' ? `${(subjectToDelete as PhysicalPersonSubject).firstName} ${(subjectToDelete as PhysicalPersonSubject).lastName}` : (subjectToDelete as LegalEntitySubject)?.name}" a všechna jeho související data (včetně vyplněných modulů) budou trvale odstraněna.</AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setSubjectToDelete(null)} disabled={isSavingGlobal}>Zrušit</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteSubject} disabled={isSavingGlobal} className="bg-destructive hover:bg-destructive/90">
              {isSavingGlobal && <Loader2 className="mr-2 h-4 w-4 animate-spin" />} Smazat
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
