"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { PhoneNumbersModuleData, Subject } from "@/types";
import { PhoneNumbersForm } from "@/components/modules/phone-numbers/PhoneNumbersForm";
import { ModulePageSkeleton } from "@/components/modules/ModulePageSkeleton";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";
import { collection, setDoc, serverTimestamp } from "firebase/firestore";

export default function PhoneNumbersModulePage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [moduleData, setModuleData] = useState<PhoneNumbersModuleData | null>(null);
  const [subject, setSubject] = useState<Subject | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const caseId = params.caseId as string;
  const subjectId = params.subjectId as string;

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Načtení subjektu
        const subjectRef = doc(db, "cases", caseId, "subjects", subjectId);
        const subjectSnap = await getDoc(subjectRef);
        
        if (!subjectSnap.exists()) {
          throw new Error("Subjekt nebyl nalezen");
        }
        
        const subjectData = subjectSnap.data() as Subject;
        setSubject(subjectData);
        
        // Načtení dat modulu
        const moduleRef = doc(
          collection(db, "cases", caseId, "subjects", subjectId, "modules"),
          "phone_numbers"
        );
        
        const moduleSnap = await getDoc(moduleRef);
        
        if (moduleSnap.exists()) {
          setModuleData(moduleSnap.data() as PhoneNumbersModuleData);
        } else {
          // Modul ještě neexistuje, vytvoříme prázdný
          setModuleData({
            subjectId,
            phones: [],
            generalNotes: "",
          });
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        toast({
          title: "Chyba při načítání dat",
          description: "Nepodařilo se načíst data modulu telefonních čísel",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchData();
  }, [caseId, subjectId, toast]);

  const handleSave = async (data: any) => {
    try {
      const moduleRef = doc(
        collection(db, "cases", caseId, "subjects", subjectId, "modules"),
        "phone_numbers"
      );
      
      await setDoc(moduleRef, {
        ...data,
        subjectId,
        lastUpdatedAt: serverTimestamp(),
        createdAt: moduleData?.createdAt || serverTimestamp(),
      });
      
      toast({
        title: "Úspěšně uloženo",
        description: "Data modulu telefonních čísel byla úspěšně uložena",
      });
      
      router.refresh();
    } catch (error) {
      console.error("Error saving data:", error);
      toast({
        title: "Chyba při ukládání dat",
        description: "Nepodařilo se uložit data modulu telefonních čísel",
        variant: "destructive",
      });
      throw error;
    }
  };

  const handleBack = () => {
    router.push(`/dashboard/cases/${caseId}`);
  };

  if (isLoading || !subject) {
    return <ModulePageSkeleton />;
  }

  return (
    <PhoneNumbersForm
      caseId={caseId}
      subject={subject}
      initialData={moduleData}
      onSave={handleSave}
      onBack={handleBack}
    />
  );
}
