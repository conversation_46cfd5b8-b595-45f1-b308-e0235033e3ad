/**
 * Radikální oprava tlačítek modulu telefonní analýzy
 * Tento skript se spustí po načtení stránky a opraví tlačítka v modulu telefonní analýzy
 */

// Funkce pro opravu tlačítek modulu telefonní analýzy
function fixPhoneModuleButtons() {
    console.log("Spouštím radikální opravu tlačítek modulu telefonní analýzy");
    
    // Najít všechny moduly telefonní analýzy
    const phoneModules = Array.from(document.querySelectorAll('.module')).filter(module => {
        const header = module.querySelector('.module-header');
        if (!header) return false;
        
        const icon = header.querySelector('.module-icon i');
        const title = header.querySelector('h3');
        
        return icon && title && 
               icon.className.includes('fa-phone') && 
               title.textContent.includes('Telefonní čísla');
    });
    
    console.log(`Nalezeno ${phoneModules.length} modulů telefonní analýzy`);
    
    // Opravit každý modul
    phoneModules.forEach((module, index) => {
        console.log(`Opravuji modul telefonní analýzy #${index + 1}`);
        fixSinglePhoneModule(module);
    });
}

// Funkce pro opravu jednoho modulu telefonní analýzy
function fixSinglePhoneModule(module) {
    // Najít hlavičku modulu
    const header = module.querySelector('.module-header');
    if (!header) {
        console.error("Modul nemá hlavičku");
        return;
    }
    
    // Odstranit všechny existující akce
    const actionsContainer = header.querySelector('.module-actions');
    if (actionsContainer) {
        actionsContainer.remove();
    }
    
    // Vytvořit nový kontejner pro akce
    const newActionsContainer = document.createElement('div');
    newActionsContainer.className = 'module-actions';
    
    // Přidat tlačítka pro posun nahoru/dolů a smazání
    newActionsContainer.innerHTML = `
        <button type="button" class="module-action-btn move-up-fixed" title="Posunout nahoru">
            <i class="fas fa-arrow-up"></i>
        </button>
        <button type="button" class="module-action-btn move-down-fixed" title="Posunout dolů">
            <i class="fas fa-arrow-down"></i>
        </button>
        <button type="button" class="module-action-btn delete-fixed" title="Odstranit modul">
            <i class="fas fa-trash"></i>
        </button>
    `;
    
    // Přidat kontejner pro akce do hlavičky
    header.appendChild(newActionsContainer);
    
    // Přidat event listenery pro tlačítka
    const moveUpBtn = newActionsContainer.querySelector('.move-up-fixed');
    const moveDownBtn = newActionsContainer.querySelector('.move-down-fixed');
    const deleteBtn = newActionsContainer.querySelector('.delete-fixed');
    
    // Event listener pro posun nahoru
    moveUpBtn.onclick = function(event) {
        event.stopPropagation();
        event.preventDefault();
        
        console.log("Kliknuto na tlačítko posun nahoru");
        
        const prevModule = module.previousElementSibling;
        if (prevModule && !prevModule.classList.contains('add-module-container')) {
            module.parentNode.insertBefore(module, prevModule);
            if (typeof updateTableOfContents === 'function') {
                updateTableOfContents();
            }
        }
        
        return false;
    };
    
    // Event listener pro posun dolů
    moveDownBtn.onclick = function(event) {
        event.stopPropagation();
        event.preventDefault();
        
        console.log("Kliknuto na tlačítko posun dolů");
        
        const nextModule = module.nextElementSibling;
        if (nextModule) {
            module.parentNode.insertBefore(nextModule, module);
            if (typeof updateTableOfContents === 'function') {
                updateTableOfContents();
            }
        }
        
        return false;
    };
    
    // Event listener pro smazání
    deleteBtn.onclick = function(event) {
        event.stopPropagation();
        event.preventDefault();
        
        console.log("Kliknuto na tlačítko smazání");
        
        if (confirm('Opravdu chcete odstranit tento modul?')) {
            module.remove();
            if (typeof updateTableOfContents === 'function') {
                updateTableOfContents();
            }
        }
        
        return false;
    };
}

// Spustit opravu po načtení stránky
document.addEventListener('DOMContentLoaded', function() {
    // Počkat na načtení všech ostatních skriptů
    setTimeout(fixPhoneModuleButtons, 1500);
    
    // Pro jistotu spustit opravu ještě jednou po delší době
    setTimeout(fixPhoneModuleButtons, 3000);
});

// Přidat opravu i při kliknutí na stránku (pro případ, že se moduly přidají dynamicky)
document.addEventListener('click', function() {
    setTimeout(fixPhoneModuleButtons, 500);
});
