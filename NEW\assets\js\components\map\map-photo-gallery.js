/**
 * Mapové overlapy - funkce pro fotodokumentaci
 */

/**
 * Inicializace fotogalerie pro mapové overlapy
 * @param {HTMLElement} moduleElement - Element modulu mapových overlapů
 */
function initMapPhotoGallery(moduleElement) {
    console.log('Inicializace fotogalerie pro mapové overlapy');
    
    if (!moduleElement) {
        console.error('Modul mapových overlapů nebyl předán do initMapPhotoGallery');
        return;
    }
    
    // Najít galerii v modulu
    const gallery = moduleElement.querySelector('.map-photo-gallery');
    if (!gallery) {
        console.error('Fotogalerie nebyla nalezena v modulu mapových overlapů');
        return;
    }
    
    // Najít input pro nahrávání fotografií
    const uploadInput = moduleElement.querySelector('#map-photo-upload');
    if (!uploadInput) {
        console.error('Input pro nahrávání fotografií nebyl nalezen');
        return;
    }
    
    // Přidání event listeneru pro tlačítko přidání fotografie
    const addButton = gallery.querySelector('.photo-gallery-add');
    if (addButton) {
        console.log('Nalezeno tlačítko pro přidání fotografie');
        
        // Odstranění existujících event listenerů
        const newAddButton = addButton.cloneNode(true);
        addButton.parentNode.replaceChild(newAddButton, addButton);
        
        // Přidání nového event listeneru
        newAddButton.addEventListener('click', function() {
            uploadInput.click();
        });
    }
    
    // Přidání event listenerů pro tlačítka nahrávání
    const uploadButtons = moduleElement.querySelectorAll('.upload-photo[data-gallery="map-photo-gallery"]');
    uploadButtons.forEach(button => {
        button.addEventListener('click', function() {
            uploadInput.click();
        });
    });
    
    // Přidání event listeneru pro změnu input[type=file]
    uploadInput.addEventListener('change', function(event) {
        handleMapPhotoUpload(event, gallery);
    });
    
    // Přidání event listeneru pro tlačítko vložení ze schránky
    const pasteButtons = moduleElement.querySelectorAll('.paste-photo[data-gallery="map-photo-gallery"]');
    pasteButtons.forEach(button => {
        button.addEventListener('click', function() {
            alert('Pro vložení obrázku ze schránky stiskněte Ctrl+V kdekoli v modulu mapových overlapů (mimo textová pole).');
        });
    });
    
    // Přidání event listeneru pro tlačítko přidání z URL
    const urlButtons = moduleElement.querySelectorAll('.add-photo-url[data-gallery="map-photo-gallery"]');
    urlButtons.forEach(button => {
        button.addEventListener('click', function() {
            addMapPhotoFromUrl(gallery);
        });
    });
    
    // Přidání event listeneru pro vložení ze schránky (Ctrl+V)
    moduleElement.addEventListener('paste', function(event) {
        const activeElement = document.activeElement;
        
        // Kontrola, zda kurzor není v textovém poli
        if (!activeElement.tagName.match(/input|textarea/i)) {
            // Zabránit výchozímu chování
            event.preventDefault();
            event.stopPropagation();
            
            handleMapPhotoPaste(event, gallery);
        }
    });
}

/**
 * Zpracování nahrání fotografie z počítače
 * @param {Event} event - Událost změny input[type=file]
 * @param {HTMLElement} gallery - Element galerie
 */
function handleMapPhotoUpload(event, gallery) {
    console.log('Zpracování nahrání fotografie z počítače pro mapové overlapy');
    
    // Resetování input[type=file] pro možnost nahrání stejného souboru znovu
    const input = event.target;
    
    const file = input.files[0];
    if (!file) {
        console.error('Nebyl vybrán žádný soubor');
        return;
    }
    
    console.log('Vybraný soubor:', file.name, file.type, file.size);
    
    // Kontrola, zda jde o obrázek
    if (!file.type.match('image.*')) {
        alert('Vybraný soubor není obrázek.');
        input.value = ''; // Reset input
        return;
    }
    
    // Vytvoření URL pro náhled
    const imageUrl = URL.createObjectURL(file);
    
    // Přidání fotografie do galerie
    addMapPhotoToGallery(gallery, file.name, imageUrl);
    
    // Kontrola, zda soubor obsahuje EXIF data s GPS souřadnicemi
    checkForGpsCoordinates(file);
    
    // Reset input pro možnost nahrání stejného souboru znovu
    input.value = '';
}

/**
 * Kontrola, zda soubor obsahuje GPS souřadnice
 * @param {File} file - Soubor fotografie
 */
function checkForGpsCoordinates(file) {
    console.log('Kontrola GPS souřadnic ve fotografii');
    
    // Kontrola, zda je načtena knihovna exif-js
    if (typeof EXIF === 'undefined') {
        console.error('Knihovna exif-js není načtena');
        return;
    }
    
    // Načtení EXIF dat
    EXIF.getData(file, function() {
        const exifData = EXIF.getAllTags(this);
        console.log('EXIF data:', exifData);
        
        // Kontrola, zda existují GPS souřadnice
        if (exifData.GPSLatitude && exifData.GPSLongitude) {
            // Převod GPS souřadnic na desetinné stupně
            const lat = convertDMSToDD(
                exifData.GPSLatitude[0],
                exifData.GPSLatitude[1],
                exifData.GPSLatitude[2],
                exifData.GPSLatitudeRef
            );
            
            const lng = convertDMSToDD(
                exifData.GPSLongitude[0],
                exifData.GPSLongitude[1],
                exifData.GPSLongitude[2],
                exifData.GPSLongitudeRef
            );
            
            console.log('GPS souřadnice:', lat, lng);
            
            // Zobrazení dialogu s dotazem na přidání bodu na mapu
            if (confirm(`Fotografie obsahuje GPS souřadnice (${lat.toFixed(6)}, ${lng.toFixed(6)}). Chcete přidat bod na mapu?`)) {
                addMarkerToMap(lat, lng, file.name);
            }
        }
    });
}

/**
 * Převod GPS souřadnic z formátu DMS (stupně, minuty, sekundy) na DD (desetinné stupně)
 * @param {number} degrees - Stupně
 * @param {number} minutes - Minuty
 * @param {number} seconds - Sekundy
 * @param {string} direction - Směr (N, S, E, W)
 * @returns {number} - Souřadnice v desetinných stupních
 */
function convertDMSToDD(degrees, minutes, seconds, direction) {
    let dd = degrees + minutes / 60 + seconds / 3600;
    
    if (direction === 'S' || direction === 'W') {
        dd = dd * -1;
    }
    
    return dd;
}

/**
 * Přidání markeru na mapu
 * @param {number} lat - Zeměpisná šířka
 * @param {number} lng - Zeměpisná délka
 * @param {string} title - Název markeru
 */
function addMarkerToMap(lat, lng, title) {
    console.log('Přidání markeru na mapu:', lat, lng, title);
    
    if (!map || !mapLayers || !mapLayers.dataLayers || !mapLayers.dataLayers.markers) {
        console.error('Mapa nebo vrstvy nejsou inicializovány');
        return;
    }
    
    // Vytvoření markeru
    const marker = L.marker([lat, lng]).addTo(mapLayers.dataLayers.markers);
    
    // Přidání popup s informacemi
    marker.bindPopup(`
        <div class="popup-content">
            <h4>Fotografie: ${title}</h4>
            <p>Souřadnice: ${lat.toFixed(6)}, ${lng.toFixed(6)}</p>
            <div class="popup-actions">
                <button type="button" class="btn-inline center-on-marker">
                    <i class="fas fa-crosshairs"></i> Centrovat mapu
                </button>
            </div>
        </div>
    `);
    
    // Přidání event listeneru pro tlačítko centrování mapy
    marker.on('popupopen', function() {
        const centerButton = document.querySelector('.center-on-marker');
        if (centerButton) {
            centerButton.addEventListener('click', function() {
                map.setView([lat, lng], 18);
            });
        }
    });
    
    // Přidání markeru do seznamu
    markers.push(marker);
    
    // Přesun mapy na marker
    map.setView([lat, lng], 16);
}

/**
 * Zpracování vložení obrázku ze schránky
 * @param {Event} event - Událost vložení
 * @param {HTMLElement} gallery - Element galerie
 */
function handleMapPhotoPaste(event, gallery) {
    console.log('Zpracování vložení obrázku ze schránky pro mapové overlapy');
    
    // Kontrola, zda schránka obsahuje obrázek
    const clipboardData = event.clipboardData || window.clipboardData;
    if (!clipboardData) {
        console.error('Clipboard data nejsou k dispozici');
        return;
    }
    
    const items = clipboardData.items;
    if (!items) {
        console.error('Clipboard items nejsou k dispozici');
        return;
    }
    
    let blob = null;
    
    // Procházení položek ve schránce
    for (let i = 0; i < items.length; i++) {
        console.log('Clipboard item:', items[i].type);
        if (items[i].type.indexOf('image') === 0) {
            blob = items[i].getAsFile();
            console.log('Nalezen obrázek ve schránce:', blob);
            break;
        }
    }
    
    if (!blob) {
        console.error('Schránka neobsahuje obrázek');
        alert('Schránka neobsahuje obrázek. Zkopírujte obrázek do schránky a zkuste to znovu.');
        return;
    }
    
    // Vytvoření URL pro náhled
    const imageUrl = URL.createObjectURL(blob);
    console.log('Vytvořeno URL pro náhled:', imageUrl);
    
    // Přidání fotografie do galerie
    addMapPhotoToGallery(gallery, 'Vložený obrázek', imageUrl);
    
    // Kontrola, zda soubor obsahuje EXIF data s GPS souřadnicemi
    checkForGpsCoordinates(blob);
}

/**
 * Přidání fotografie z URL
 * @param {HTMLElement} gallery - Element galerie
 */
function addMapPhotoFromUrl(gallery) {
    console.log('Přidání fotografie z URL pro mapové overlapy');
    
    // Zobrazení dialogu pro zadání URL
    const url = prompt('Zadejte URL obrázku:');
    if (!url) return;
    
    // Kontrola, zda URL je platná
    if (!url.match(/^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i)) {
        alert('Zadejte platnou URL obrázku (končící na .jpg, .png, .gif nebo .webp).');
        return;
    }
    
    console.log('Přidání obrázku z URL:', url);
    
    // Přidání fotografie do galerie
    addMapPhotoToGallery(gallery, 'Obrázek z URL', url);
}

/**
 * Přidání fotografie do galerie
 * @param {HTMLElement} gallery - Element galerie
 * @param {string} title - Název fotografie
 * @param {string} imageUrl - URL obrázku
 */
function addMapPhotoToGallery(gallery, title, imageUrl) {
    console.log('Přidání fotografie do galerie mapových overlapů:', title, imageUrl);
    
    if (!gallery) {
        console.error('Galerie nebyla předána do addMapPhotoToGallery');
        return;
    }
    
    // Vytvoření nové fotografie
    const photoId = `map-photo-${Date.now()}`;
    const photoHTML = `
        <div class="photo-item" id="${photoId}" data-image-url="${imageUrl}">
            <div class="photo-preview">
                <img src="${imageUrl}" alt="${title}" onerror="this.src='https://via.placeholder.com/150x150?text=Chyba+načítání'">
            </div>
            <div class="photo-info">
                <div class="photo-title">${title}</div>
                <div class="photo-actions">
                    <button type="button" class="photo-action-btn view-photo" data-photo-id="${photoId}" title="Zobrazit fotografii">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" class="photo-action-btn add-to-map" data-photo-id="${photoId}" title="Přidat na mapu">
                        <i class="fas fa-map-marker-alt"></i>
                    </button>
                    <button type="button" class="photo-action-btn delete-photo" data-photo-id="${photoId}" title="Odstranit fotografii">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;
    
    // Přidání fotografie do galerie
    const addButton = gallery.querySelector('.photo-gallery-add');
    if (addButton) {
        gallery.insertBefore(document.createRange().createContextualFragment(photoHTML), addButton);
    } else {
        gallery.insertAdjacentHTML('beforeend', photoHTML);
    }
    
    // Přidání event listenerů pro tlačítka
    const photoElement = document.getElementById(photoId);
    if (!photoElement) {
        console.error('Nově přidaná fotografie nebyla nalezena v DOM');
        return;
    }
    
    const viewButton = photoElement.querySelector('.view-photo');
    if (viewButton) {
        viewButton.addEventListener('click', function() {
            const photoId = this.getAttribute('data-photo-id');
            const photoElement = document.getElementById(photoId);
            if (photoElement) {
                const imageUrl = photoElement.getAttribute('data-image-url');
                const photoTitle = photoElement.querySelector('.photo-title').textContent;
                showMapPhotoViewer(imageUrl, photoTitle);
            }
        });
    }
    
    const addToMapButton = photoElement.querySelector('.add-to-map');
    if (addToMapButton) {
        addToMapButton.addEventListener('click', function() {
            const photoId = this.getAttribute('data-photo-id');
            const photoElement = document.getElementById(photoId);
            if (photoElement) {
                const imageUrl = photoElement.getAttribute('data-image-url');
                const photoTitle = photoElement.querySelector('.photo-title').textContent;
                addPhotoToMap(imageUrl, photoTitle);
            }
        });
    }
    
    const deleteButton = photoElement.querySelector('.delete-photo');
    if (deleteButton) {
        deleteButton.addEventListener('click', function() {
            const photoId = this.getAttribute('data-photo-id');
            const photoElement = document.getElementById(photoId);
            if (photoElement && confirm('Opravdu chcete odstranit tuto fotografii?')) {
                photoElement.remove();
            }
        });
    }
}

/**
 * Zobrazení prohlížeče fotografií
 * @param {string} imageUrl - URL obrázku
 * @param {string} title - Název fotografie
 */
function showMapPhotoViewer(imageUrl, title) {
    console.log('Zobrazení prohlížeče fotografií pro mapové overlapy:', title, imageUrl);
    
    // Vytvoření prohlížeče fotografií
    const viewer = document.createElement('div');
    viewer.className = 'photo-viewer';
    viewer.innerHTML = `
        <div class="photo-viewer-content">
            <div class="photo-viewer-header">
                <div class="photo-viewer-title">${title}</div>
                <div class="photo-viewer-actions">
                    <button type="button" class="photo-viewer-action-btn add-to-map-viewer" title="Přidat na mapu">
                        <i class="fas fa-map-marker-alt"></i>
                    </button>
                    <button type="button" class="photo-viewer-action-btn close-viewer" title="Zavřít">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="photo-viewer-body">
                <img src="${imageUrl}" alt="${title}" onerror="this.src='https://via.placeholder.com/800x600?text=Chyba+načítání'">
            </div>
        </div>
    `;
    
    // Přidání prohlížeče do stránky
    document.body.appendChild(viewer);
    
    // Přidání event listenerů pro tlačítka
    viewer.querySelector('.close-viewer').addEventListener('click', function() {
        viewer.remove();
    });
    
    viewer.querySelector('.add-to-map-viewer').addEventListener('click', function() {
        addPhotoToMap(imageUrl, title);
    });
    
    // Přidání event listeneru pro kliknutí mimo obsah prohlížeče
    viewer.addEventListener('click', function(event) {
        if (event.target === viewer) {
            viewer.remove();
        }
    });
}

/**
 * Přidání fotografie na mapu
 * @param {string} imageUrl - URL obrázku
 * @param {string} title - Název fotografie
 */
function addPhotoToMap(imageUrl, title) {
    console.log('Přidání fotografie na mapu:', title, imageUrl);
    
    if (!map) {
        console.error('Mapa není inicializována');
        return;
    }
    
    // Zobrazení dialogu pro zadání souřadnic
    const dialog = document.createElement('div');
    dialog.className = 'coordinates-dialog';
    dialog.innerHTML = `
        <div class="coordinates-dialog-content">
            <div class="coordinates-dialog-header">
                <h3>Přidání fotografie na mapu</h3>
                <button type="button" class="coordinates-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="coordinates-dialog-body">
                <p>Zadejte souřadnice pro umístění fotografie na mapu:</p>
                <div class="form-group">
                    <label>Zeměpisná šířka:</label>
                    <input type="text" id="latitude-input" class="form-control" placeholder="např. 50.0755">
                </div>
                <div class="form-group">
                    <label>Zeměpisná délka:</label>
                    <input type="text" id="longitude-input" class="form-control" placeholder="např. 14.4378">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-inline add-photo-to-map-confirm">
                        <i class="fas fa-check"></i> Přidat na mapu
                    </button>
                    <button type="button" class="btn-inline pick-coordinates-from-map">
                        <i class="fas fa-map-marked-alt"></i> Vybrat z mapy
                    </button>
                </div>
            </div>
        </div>
    `;
    
    // Přidání dialogu do stránky
    document.body.appendChild(dialog);
    
    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.coordinates-dialog-close').addEventListener('click', function() {
        dialog.remove();
    });
    
    dialog.querySelector('.add-photo-to-map-confirm').addEventListener('click', function() {
        const latInput = dialog.querySelector('#latitude-input');
        const lngInput = dialog.querySelector('#longitude-input');
        
        const lat = parseFloat(latInput.value);
        const lng = parseFloat(lngInput.value);
        
        if (isNaN(lat) || isNaN(lng)) {
            alert('Zadejte platné souřadnice.');
            return;
        }
        
        // Přidání markeru s fotografií na mapu
        addPhotoMarkerToMap(lat, lng, title, imageUrl);
        
        // Zavření dialogu
        dialog.remove();
    });
    
    dialog.querySelector('.pick-coordinates-from-map').addEventListener('click', function() {
        // Zavření dialogu
        dialog.remove();
        
        // Zobrazení informace pro uživatele
        alert('Klikněte na mapu pro výběr souřadnic.');
        
        // Nastavení režimu výběru souřadnic
        map.once('click', function(event) {
            const lat = event.latlng.lat;
            const lng = event.latlng.lng;
            
            // Přidání markeru s fotografií na mapu
            addPhotoMarkerToMap(lat, lng, title, imageUrl);
        });
    });
    
    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}

/**
 * Přidání markeru s fotografií na mapu
 * @param {number} lat - Zeměpisná šířka
 * @param {number} lng - Zeměpisná délka
 * @param {string} title - Název fotografie
 * @param {string} imageUrl - URL obrázku
 */
function addPhotoMarkerToMap(lat, lng, title, imageUrl) {
    console.log('Přidání markeru s fotografií na mapu:', lat, lng, title, imageUrl);
    
    if (!map || !mapLayers || !mapLayers.dataLayers || !mapLayers.dataLayers.markers) {
        console.error('Mapa nebo vrstvy nejsou inicializovány');
        return;
    }
    
    // Vytvoření markeru
    const marker = L.marker([lat, lng]).addTo(mapLayers.dataLayers.markers);
    
    // Přidání popup s fotografií
    marker.bindPopup(`
        <div class="popup-content">
            <h4>${title}</h4>
            <div class="popup-image">
                <img src="${imageUrl}" alt="${title}" style="max-width: 200px; max-height: 200px;">
            </div>
            <p>Souřadnice: ${lat.toFixed(6)}, ${lng.toFixed(6)}</p>
            <div class="popup-actions">
                <button type="button" class="btn-inline center-on-marker">
                    <i class="fas fa-crosshairs"></i> Centrovat mapu
                </button>
                <button type="button" class="btn-inline view-photo-from-marker">
                    <i class="fas fa-eye"></i> Zobrazit fotografii
                </button>
            </div>
        </div>
    `);
    
    // Přidání event listenerů pro tlačítka v popup
    marker.on('popupopen', function() {
        const centerButton = document.querySelector('.center-on-marker');
        if (centerButton) {
            centerButton.addEventListener('click', function() {
                map.setView([lat, lng], 18);
            });
        }
        
        const viewButton = document.querySelector('.view-photo-from-marker');
        if (viewButton) {
            viewButton.addEventListener('click', function() {
                showMapPhotoViewer(imageUrl, title);
            });
        }
    });
    
    // Přidání markeru do seznamu
    markers.push(marker);
    
    // Přesun mapy na marker
    map.setView([lat, lng], 16);
    
    // Otevření popup
    marker.openPopup();
}
