# Nastavení CORS pro Firebase Storage

Pro správné fungování nahrávání obrázků do Firebase Storage je potřeba nastavit CORS pravidla. Toto je potřeba udělat pouze jednou pro celý projekt.

## Postup nastavení CORS

1. Nainstalujte Firebase CLI, pokud ji ještě nemáte:
   ```
   npm install -g firebase-tools
   ```

2. Přihlaste se k Firebase:
   ```
   firebase login
   ```

3. Použijte soubor `cors.json` z kořenového adresáře projektu, který obsahuje CORS pravidla:
   ```json
   [
     {
       "origin": ["http://localhost:3000", "https://krypton-osint.web.app", "https://krypton-osint.firebaseapp.com"],
       "method": ["GET", "PUT", "POST", "DELETE"],
       "maxAgeSeconds": 3600
     }
   ]
   ```

4. Nastavte CORS pravidla pro Firebase Storage:
   ```
   gsutil cors set cors.json gs://krypton-osint.firebasestorage.app
   ```

5. <PERSON><PERSON><PERSON><PERSON><PERSON>, že CORS pravidla byla nastavena:
   ```
   gsutil cors get gs://krypton-osint.firebasestorage.app
   ```

## Řešení problémů

Pokud i po nastavení CORS pravidel dochází k chybám, aplikace je navržena tak, aby používala záložní řešení:

1. Při nahrávání obrázků se v případě CORS chyby použije původní base64 řetězec místo URL z Firebase Storage.
2. Při mazání obrázků se v případě CORS chyby pouze zaloguje varování a pokračuje se dál.

Toto zajišťuje, že aplikace bude fungovat i v případě problémů s CORS, i když nebude využívat všechny výhody Firebase Storage.
