"use client";

import { useState, useMemo } from "react";
import { useForm, useFieldArray, Control, FieldValues, FieldPath } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { db } from "@/lib/firebase";
import { doc, setDoc, Timestamp } from "firebase/firestore";
import { LoadingSpinner } from "@/components/ui/loading";
import {
  Phone, 
  CheckCircle, 
  Search,
  PlusCircle,
  Trash2,
  Copy,
  Shield,
  Activity,
  Smartphone,
  Globe,
  Clock,
  Users,
  MessageSquare,
  ImageIcon
} from "lucide-react";
import { PhoneAnalysisModuleData, PhoneActivity, ConnectedDevice, ReverseSearchResult } from "@/types/phoneAnalysis";
import { PhotoDocumentationSection } from "./PhotoDocumentationSection";

// FormItemRHF komponenta podle evidence obyvatel
interface FormItemRHFProps<T extends FieldValues = any> {
  label: string;
  name: FieldPath<T>;
  control: Control<T>;
  placeholder?: string;
  type?: string;
  disabled?: boolean;
  className?: string;
  as?: 'input' | 'textarea';
  rows?: number;
  smallLabel?: boolean;
}

const FormItemRHF = <T extends FieldValues>({ 
  label, 
  name, 
  control, 
  placeholder, 
  type = "text", 
  disabled = false, 
  className, 
  as = 'input', 
  rows = 3, 
  smallLabel = false 
}: FormItemRHFProps<T>) => (
  <FormField
    control={control} 
    name={name} 
    disabled={disabled}
    render={({ field, fieldState: { error } }) => (
      <FormItem className={className}>
        <FormLabel className={smallLabel ? "text-xs font-medium" : "font-semibold text-sm"}>
          {label}
        </FormLabel>
        <FormControl>
          {as === 'input' ? (
            <Input 
              {...field} 
              value={field.value || ''} 
              placeholder={placeholder} 
              type={type} 
              disabled={disabled}
              className={error ? "border-destructive" : ""} 
            />
          ) : (
            <Textarea 
              {...field} 
              value={field.value || ''} 
              placeholder={placeholder} 
              rows={rows} 
              disabled={disabled}
              className={error ? "border-destructive" : ""} 
            />
          )}
        </FormControl>
        {error && <FormMessage />}
      </FormItem>
    )}
  />
);

// Schema pro validaci
const phoneActivitySchema = z.object({
  id: z.string(),
  date: z.string(),
  activity: z.string(),
  source: z.string(),
  notes: z.string().optional(),
});

const connectedDeviceSchema = z.object({
  id: z.string(),
  deviceType: z.string(),
  deviceModel: z.string().optional(),
  lastSeen: z.string().optional(),
  notes: z.string().optional(),
});

const reverseSearchResultSchema = z.object({
  id: z.string(),
  source: z.string(),
  associatedName: z.string().optional(),
  associatedData: z.string().optional(),
  confidence: z.enum(["high", "medium", "low"]),
  notes: z.string().optional(),
});

const phoneAnalysisModuleSchema = z.object({
  phoneNumber: z.string().min(1, "Telefonní číslo je povinné").refine((value) => {
    if (!value.trim()) return false;
    
    // Základní validace formátu telefonního čísla
    const cleanPhone = value.replace(/\s+/g, '').replace(/[()-]/g, '');
    
    // Mezinárodní formát
    if (cleanPhone.startsWith('+')) {
      return /^\+[1-9]\d{1,14}$/.test(cleanPhone);
    }
    
    // České číslo bez předvolby (9 číslic)
    if (/^[67][0-9]{8}$/.test(cleanPhone)) {
      return true;
    }
    
    // České číslo s předvolbou
    if (cleanPhone.startsWith('420') && /^420[67][0-9]{8}$/.test(cleanPhone)) {
      return true;
    }
    
    return false;
  }, {
    message: "Zadejte platné telefonní číslo ve formátu +420 123 456 789"
  }),
  source: z.string(),
  otherSource: z.string().optional(),
  discoveryDate: z.string().optional(),
  activity: z.enum(["active", "inactive", "unknown"]),
  
  // Validace
  isValidated: z.boolean().optional(),
  validationResults: z.string().optional(),
  
  // Služby
  serviceWhatsapp: z.boolean(),
  serviceTelegram: z.boolean(),
  serviceSignal: z.boolean(),
  serviceViber: z.boolean(),
  serviceFacebook: z.boolean(),
  
  // Reverzní vyhledávání
  reverseSearchResults: z.array(reverseSearchResultSchema),
  reverseSearchNotes: z.string().optional(),
  
  // Historie aktivit
  phoneActivities: z.array(phoneActivitySchema),
  
  // Připojená zařízení
  connectedDevices: z.array(connectedDeviceSchema),
  
  // Fotodokumentace
  photos: z.array(z.any()).optional(),
  
  // Poznámky
  technicalNotes: z.string().optional(),
  investigationNotes: z.string().optional(),
  osintNotes: z.string().optional(),
});

type PhoneAnalysisFormValues = z.infer<typeof phoneAnalysisModuleSchema>;

interface PhoneAnalysisFormProps {
  caseId: string;
  subject: {
    id: string;
    firstName: string;
    lastName: string;
    type: "person" | "company";
  };
  moduleId: string;
  existingData: PhoneAnalysisModuleData | null;
  onSave: (data: PhoneAnalysisModuleData, wasNew: boolean) => void;
  onCancel: () => void;
}

export default function PhoneAnalysisForm({ caseId, subject, moduleId, existingData, onSave, onCancel }: PhoneAnalysisFormProps) {
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [isSearching, setIsSearching] = useState(false);

  const defaultValues = useMemo(() => ({
    phoneNumber: existingData?.phoneNumber || "",
    source: existingData?.source || "",
    otherSource: existingData?.otherSource || "",
    discoveryDate: existingData?.discoveryDate || "",
    activity: existingData?.activity || "unknown" as const,
    
    // Validace
    isValidated: false,
    validationResults: "",
    
    // Služby
    serviceWhatsapp: existingData?.serviceRegistrations?.whatsapp || false,
    serviceTelegram: existingData?.serviceRegistrations?.telegram || false,
    serviceSignal: existingData?.serviceRegistrations?.signal || false,
    serviceViber: existingData?.serviceRegistrations?.viber || false,
    serviceFacebook: existingData?.serviceRegistrations?.facebook || false,
    
    // Reverzní vyhledávání
    reverseSearchResults: existingData?.reverseSearchResults || [],
    reverseSearchNotes: existingData?.reverseSearchNotes || "",
    
    // Historie aktivit
    phoneActivities: existingData?.phoneActivities || [],
    
    // Připojená zařízení
    connectedDevices: existingData?.connectedDevices || [],
    
    // Fotodokumentace
    photos: existingData?.photos || [],
    
    // Poznámky
    technicalNotes: existingData?.technicalNotes || "",
    investigationNotes: existingData?.investigationNotes || "",
    osintNotes: existingData?.osintNotes || "",
  }), [existingData]);

  const form = useForm<PhoneAnalysisFormValues>({
    resolver: zodResolver(phoneAnalysisModuleSchema),
    defaultValues,
  });

  const { fields: activityFields, append: appendActivity, remove: removeActivity } = useFieldArray({
    control: form.control,
    name: "phoneActivities"
  });

  const { fields: deviceFields, append: appendDevice, remove: removeDevice } = useFieldArray({
    control: form.control,
    name: "connectedDevices"
  });

  const { fields: searchResultFields, append: appendSearchResult, remove: removeSearchResult } = useFieldArray({
    control: form.control,
    name: "reverseSearchResults"
  });

  // Funkce pro validaci telefonního čísla podle starého kódu z NEW
  const validatePhoneNumber = async () => {
    const phoneNumber = form.watch("phoneNumber");
    if (!phoneNumber) {
      toast({
        title: "Chyba",
        description: "Nejprve zadejte telefonní číslo",
        variant: "destructive"
      });
      return;
    }

    setIsValidating(true);
    try {
      // Zpracování podle starého kódu z NEW - používáme NumVerify API
      const cleanPhone = phoneNumber.replace(/\s+/g, '').replace(/[()-]/g, '');
      
      // Kontrola, zda číslo začíná na + (mezinárodní formát)
      let apiPhone;
      let countryCode = '';

      if (cleanPhone.startsWith('+')) {
        // Odstranění + ze začátku čísla pro API volání
        apiPhone = cleanPhone.substring(1);

        // Získání kódu země z telefonního čísla (podle starého kódu)
        if (cleanPhone.startsWith('+420')) {
          countryCode = 'CZ';
        } else if (cleanPhone.startsWith('+421')) {
          countryCode = 'SK';
        } else if (cleanPhone.startsWith('+48')) {
          countryCode = 'PL';
        } else if (cleanPhone.startsWith('+49')) {
          countryCode = 'DE';
        } else if (cleanPhone.startsWith('+43')) {
          countryCode = 'AT';
        } else if (cleanPhone.startsWith('+36')) {
          countryCode = 'HU';
        }
      } else {
        // Pokud nezačíná na +, předpokládáme české číslo (podle starého kódu)
        if (cleanPhone.startsWith('00420')) {
          apiPhone = cleanPhone.replace('00420', '');
          countryCode = 'CZ';
        } else if (cleanPhone.startsWith('420')) {
          apiPhone = cleanPhone.replace('420', '');
          countryCode = 'CZ';
        } else {
          // Předpokládáme, že jde o české číslo bez předvolby
          apiPhone = cleanPhone;
          countryCode = 'CZ';
        }
      }

      // NumVerify API konstanty z starého kódu
      const NUMVERIFY_API_KEY = '********************************';
      const NUMVERIFY_API_URL = 'http://apilayer.net/api/validate';
      
      // Sestavení URL pro API volání (podle starého kódu)
      const apiUrl = `${NUMVERIFY_API_URL}?access_key=${NUMVERIFY_API_KEY}&number=${apiPhone}&country_code=${countryCode}&format=1`;

      try {
        // Volání NumVerify API přes náš endpoint
        const response = await fetch('/api/validate-phone', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            phone: apiPhone,
            countryCode: countryCode
          })
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        if (data.error) {
          throw new Error(data.error);
        }

        let validationResults = "";
        
        // Formát čísla
        if (data.valid) {
          validationResults += "Formát čísla: ✓ Platné číslo\n";
        } else {
          validationResults += "Formát čísla: ✗ Neplatné číslo\n";
        }

        // Mezinárodní formát
        validationResults += `Mezinárodní formát: ${data.international_format || 'Neznámý'}\n`;

        // Země
        const countryInfo = data.country_name ? `${data.country_name} (${data.country_code})` : 'Neznámá';
        validationResults += `Země: ${countryInfo}\n`;

        // Operátor - SKUTEČNÝ z API!
        validationResults += `Operátor: ${data.carrier || 'Neznámý'}\n`;

        // Typ čísla podle starého kódu
        let lineType = 'Neznámý';
        if (data.line_type === 'mobile') {
          lineType = 'Mobilní';
        } else if (data.line_type === 'landline') {
          lineType = 'Pevná linka';
        } else if (data.line_type === 'voip') {
          lineType = 'VoIP';
        } else if (data.line_type === 'premium') {
          lineType = 'Prémiové číslo';
        } else if (data.line_type === 'tollfree') {
          lineType = 'Bezplatné číslo';
        }
        validationResults += `Typ čísla: ${lineType}`;

        form.setValue("validationResults", validationResults);
        form.setValue("isValidated", data.valid || false);
        
        toast({
          title: data.valid ? "Validace dokončena" : "Číslo neplatné",
          description: data.valid ? "Telefonní číslo bylo úspěšně validováno" : "Telefonní číslo není platné",
          variant: data.valid ? "default" : "destructive"
        });

      } catch (apiError) {
        console.error('Chyba při volání NumVerify API:', apiError);
        
        // Fallback na základní validaci pokud API nefunguje
        const errorMessage = apiError instanceof Error ? apiError.message : 'Neznámá chyba API';
        const basicValidationResults = `Formát čísla: ✗ API nedostupné
Mezinárodní formát: ${cleanPhone.startsWith('+') ? cleanPhone : '+420' + apiPhone}
Země: Česká republika (CZ)
Operátor: Neznámý (API nedostupné)
Typ čísla: Neznámý

Chyba: ${errorMessage}
Tip: Zkontrolujte internetové připojení`;

        form.setValue("validationResults", basicValidationResults);
        form.setValue("isValidated", false);
        
        toast({
          title: "Chyba API",
          description: "NumVerify API není dostupné. Použita základní validace.",
          variant: "destructive"
        });
      }

    } catch (error) {
      toast({
        title: "Chyba validace",
        description: "Nepodařilo se validovat telefonní číslo",
        variant: "destructive"
      });
    } finally {
      setIsValidating(false);
    }
  };

  // Funkce pro reverzní vyhledávání
  const performReverseSearch = async () => {
    const phoneNumber = form.watch("phoneNumber");
    if (!phoneNumber) {
      toast({
        title: "Chyba",
        description: "Nejprve zadejte telefonní číslo",
        variant: "destructive"
      });
      return;
    }

    setIsSearching(true);
    try {
      // Simulace vyhledávání - v reálné aplikaci by se volalo API
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Přidání mock výsledků
      const mockResults = [
        {
          id: crypto.randomUUID(),
          source: "Veřejný adresář",
          associatedName: "Jan Novák",
          associatedData: "Brno, Česká republika",
          confidence: "high" as const,
          notes: "Nalezeno ve veřejném telefonním adresáři"
        },
        {
          id: crypto.randomUUID(),
          source: "Sociální sítě",
          associatedName: "J. Novák",
          associatedData: "Facebook profil",
          confidence: "medium" as const,
          notes: "Číslo uvedeno v profilu"
        }
      ];
      
      // Přidání výsledků do formuláře
      mockResults.forEach(result => appendSearchResult(result));
      
      toast({
        title: "Vyhledávání dokončeno",
        description: `Nalezeno ${mockResults.length} výsledků`
      });
    } catch (error) {
      toast({
        title: "Chyba vyhledávání",
        description: "Nepodařilo se provést reverzní vyhledávání",
        variant: "destructive"
      });
    } finally {
      setIsSearching(false);
    }
  };

  const onSubmitHandler = async (data: PhoneAnalysisFormValues) => {
    setIsSaving(true);
    try {
      const moduleDocRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);

      // Získej validační výsledky z formuláře (už obsahují API data)
      let validationResult = undefined;
      if (data.isValidated && data.validationResults) {
        // Parsuj výsledky validace z textového řetězce
        const lines = data.validationResults.split('\n');
        let internationalFormat = '';
        let country = '';
        let carrier = '';
        let phoneType = '';
        
        lines.forEach(line => {
          if (line.startsWith('Mezinárodní formát:')) {
            internationalFormat = line.replace('Mezinárodní formát:', '').trim();
          } else if (line.startsWith('Země:')) {
            country = line.replace('Země:', '').trim();
          } else if (line.startsWith('Operátor:')) {
            carrier = line.replace('Operátor:', '').trim();
          } else if (line.startsWith('Typ čísla:')) {
            phoneType = line.replace('Typ čísla:', '').trim();
          }
        });

        validationResult = {
          isValid: true,
          internationalFormat: internationalFormat !== 'Neznámý' ? internationalFormat : undefined,
          country: country !== 'Neznámá' ? country : undefined,
          carrier: carrier !== 'Neznámý' ? carrier : undefined,
          phoneType: phoneType !== 'Neznámý' ? phoneType : undefined
        };
      }

      const saveData: PhoneAnalysisModuleData = {
        subjectId: subject.id,
        phoneNumber: data.phoneNumber,
        source: data.source,
        otherSource: data.otherSource,
        discoveryDate: data.discoveryDate,
        activity: data.activity,
        
        validationResult,
        
        serviceRegistrations: {
          whatsapp: data.serviceWhatsapp,
          telegram: data.serviceTelegram,
          signal: data.serviceSignal,
          viber: data.serviceViber,
          facebook: data.serviceFacebook,
        },
        
        reverseSearchResults: data.reverseSearchResults,
        reverseSearchNotes: data.reverseSearchNotes,
        phoneActivities: data.phoneActivities,
        connectedDevices: data.connectedDevices,
        photos: data.photos || [],
        technicalNotes: data.technicalNotes,
        investigationNotes: data.investigationNotes,
        osintNotes: data.osintNotes,
        
        lastUpdatedAt: Timestamp.now(),
        createdAt: existingData?.createdAt || Timestamp.now(),
      };

      await setDoc(moduleDocRef, saveData);
      
      toast({
        title: "Data uložena",
        description: "Telefonní analýza byla úspěšně uložena"
      });

      const wasNew = !existingData || !existingData.createdAt;
      onSave(saveData, wasNew);

    } catch (error: any) {
      console.error("Error saving phone analysis data:", error);
      toast({
        title: "Chyba ukládání",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmitHandler)} className="space-y-6">
        {/* Základní údaje o telefonu */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Phone className="mr-2 h-5 w-5 text-blue-600" />
              Základní údaje o telefonním čísle
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phoneNumber">Telefonní číslo *</Label>
                <div className="flex gap-2">
                  <Input
                    id="phoneNumber"
                    {...form.register("phoneNumber")}
                    placeholder="+420 123 456 789"
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={validatePhoneNumber}
                    disabled={isValidating}
                  >
                    {isValidating ? (
                      <LoadingSpinner size="sm" className="mr-2" />
                    ) : (
                      <CheckCircle className="mr-2 h-4 w-4" />
                    )}
                    Validovat
                  </Button>
                </div>
              </div>
              <FormItemRHF<PhoneAnalysisFormValues>
                label="Zdroj telefonního čísla"
                name="source"
                control={form.control}
                placeholder="Kde bylo číslo nalezeno"
              />
            </div>

            {form.watch("source") === "other" && (
              <FormItemRHF<PhoneAnalysisFormValues>
                label="Upřesnění zdroje"
                name="otherSource"
                control={form.control}
                placeholder="Zadejte zdroj telefonního čísla"
              />
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemRHF<PhoneAnalysisFormValues>
                label="Datum objevení"
                name="discoveryDate"
                control={form.control}
                type="date"
              />
              <div>
                <Label>Aktivita čísla</Label>
                <Select
                  value={form.watch("activity")}
                  onValueChange={(value) => form.setValue("activity", value as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Vyberte stav" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Aktivní</SelectItem>
                    <SelectItem value="inactive">Neaktivní</SelectItem>
                    <SelectItem value="unknown">Neznámý</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {form.watch("validationResults") && (
              <Card className="bg-green-50 border-green-200">
                <CardHeader>
                  <CardTitle className="text-sm text-green-800">Výsledky validace</CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="text-sm text-green-700 whitespace-pre-wrap">
                    {form.watch("validationResults")}
                  </pre>
                </CardContent>
              </Card>
            )}
          </CardContent>
        </Card>

        {/* Reverzní vyhledávání */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <Search className="mr-2 h-5 w-5 text-primary" />
                Reverzní vyhledávání
              </span>
              <Button
                type="button"
                variant="outline"
                onClick={performReverseSearch}
                disabled={isSearching}
              >
                {isSearching ? (
                  <LoadingSpinner size="sm" className="mr-2" />
                ) : (
                  <Search className="mr-2 h-4 w-4" />
                )}
                Vyhledat v databázích
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {searchResultFields.map((field, index) => (
              <Card key={field.id} className="p-4">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium">Výsledek {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeSearchResult(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF<PhoneAnalysisFormValues>
                      label="Zdroj"
                      name={`reverseSearchResults.${index}.source` as any}
                      control={form.control}
                      placeholder="Název zdroje"
                    />
                    <div>
                      <Label>Důvěryhodnost</Label>
                      <Select
                        value={form.watch(`reverseSearchResults.${index}.confidence` as any)}
                        onValueChange={(value) => form.setValue(`reverseSearchResults.${index}.confidence` as any, value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Vyberte" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="high">Vysoká</SelectItem>
                          <SelectItem value="medium">Střední</SelectItem>
                          <SelectItem value="low">Nízká</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF<PhoneAnalysisFormValues>
                      label="Spojené jméno"
                      name={`reverseSearchResults.${index}.associatedName` as any}
                      control={form.control}
                      placeholder="Nalezené jméno"
                    />
                    <FormItemRHF<PhoneAnalysisFormValues>
                      label="Spojená data"
                      name={`reverseSearchResults.${index}.associatedData` as any}
                      control={form.control}
                      placeholder="Další nalezené informace"
                    />
                  </div>
                  
                  <FormItemRHF<PhoneAnalysisFormValues>
                    label="Poznámky"
                    name={`reverseSearchResults.${index}.notes` as any}
                    control={form.control}
                    placeholder="Poznámky k výsledku"
                    as="textarea"
                    rows={2}
                  />
                </div>
              </Card>
            ))}

            <Button
              type="button"
              variant="outline"
              onClick={() => appendSearchResult({
                id: crypto.randomUUID(),
                source: "",
                confidence: "medium" as const,
              })}
            >
              <PlusCircle className="mr-2 h-4 w-4" />
              Přidat výsledek
            </Button>

            <FormItemRHF<PhoneAnalysisFormValues>
              label="Poznámky k reverznímu vyhledávání"
              name="reverseSearchNotes"
              control={form.control}
              placeholder="Poznámky k výsledkům reverzního vyhledávání"
              as="textarea"
              rows={3}
            />
          </CardContent>
        </Card>

        {/* Registrace ve službách */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MessageSquare className="mr-2 h-5 w-5 text-primary" />
              Registrace ve službách
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="serviceWhatsapp"
                  {...form.register("serviceWhatsapp")}
                />
                <Label htmlFor="serviceWhatsapp">WhatsApp</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="serviceTelegram"
                  {...form.register("serviceTelegram")}
                />
                <Label htmlFor="serviceTelegram">Telegram</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="serviceSignal"
                  {...form.register("serviceSignal")}
                />
                <Label htmlFor="serviceSignal">Signal</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="serviceViber"
                  {...form.register("serviceViber")}
                />
                <Label htmlFor="serviceViber">Viber</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="serviceFacebook"
                  {...form.register("serviceFacebook")}
                />
                <Label htmlFor="serviceFacebook">Facebook</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Historie aktivit */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <Clock className="mr-2 h-5 w-5 text-primary" />
                Historie aktivit
              </span>
              <Button
                type="button"
                variant="outline"
                onClick={() => appendActivity({
                  id: crypto.randomUUID(),
                  date: "",
                  activity: "",
                  source: "",
                  notes: ""
                })}
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Přidat aktivitu
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {activityFields.map((field, index) => (
              <Card key={field.id} className="p-4">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium">Aktivita {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeActivity(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF<PhoneAnalysisFormValues>
                      label="Datum"
                      name={`phoneActivities.${index}.date` as any}
                      control={form.control}
                      type="date"
                    />
                    <FormItemRHF<PhoneAnalysisFormValues>
                      label="Zdroj"
                      name={`phoneActivities.${index}.source` as any}
                      control={form.control}
                      placeholder="Kde byla aktivita zaznamenána"
                    />
                  </div>
                  
                  <FormItemRHF<PhoneAnalysisFormValues>
                    label="Popis aktivity"
                    name={`phoneActivities.${index}.activity` as any}
                    control={form.control}
                    placeholder="Popis aktivity"
                    as="textarea"
                    rows={2}
                  />
                  
                  <FormItemRHF<PhoneAnalysisFormValues>
                    label="Poznámky"
                    name={`phoneActivities.${index}.notes` as any}
                    control={form.control}
                    placeholder="Poznámky k aktivitě"
                    as="textarea"
                    rows={2}
                  />
                </div>
              </Card>
            ))}
          </CardContent>
        </Card>

        {/* Připojená zařízení */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <Smartphone className="mr-2 h-5 w-5 text-primary" />
                Připojená zařízení
              </span>
              <Button
                type="button"
                variant="outline"
                onClick={() => appendDevice({
                  id: crypto.randomUUID(),
                  deviceType: "",
                  deviceModel: "",
                  lastSeen: "",
                  notes: ""
                })}
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Přidat zařízení
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {deviceFields.map((field, index) => (
              <Card key={field.id} className="p-4">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium">Zařízení {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeDevice(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormItemRHF<PhoneAnalysisFormValues>
                      label="Typ zařízení"
                      name={`connectedDevices.${index}.deviceType` as any}
                      control={form.control}
                      placeholder="Smartphone, Tablet..."
                    />
                    <FormItemRHF<PhoneAnalysisFormValues>
                      label="Model"
                      name={`connectedDevices.${index}.deviceModel` as any}
                      control={form.control}
                      placeholder="iPhone 12, Samsung Galaxy..."
                    />
                    <FormItemRHF<PhoneAnalysisFormValues>
                      label="Naposledy viděn"
                      name={`connectedDevices.${index}.lastSeen` as any}
                      control={form.control}
                      type="date"
                    />
                  </div>
                  
                  <FormItemRHF<PhoneAnalysisFormValues>
                    label="Poznámky"
                    name={`connectedDevices.${index}.notes` as any}
                    control={form.control}
                    placeholder="Poznámky k zařízení"
                    as="textarea"
                    rows={2}
                  />
                </div>
              </Card>
            ))}
          </CardContent>
        </Card>

        {/* Fotodokumentace */}
        <Card className="shadow-md">
          <PhotoDocumentationSection
            form={form as any}
            namePrefix="photos"
            title="Fotodokumentace telefonní analýzy"
            description="Nahrajte a spravujte snímky obrazovky telefonních dat. Fotografie se ukládají trvale do aplikace."
            photoHint="phone analysis screenshot call log SMS contacts"
            caseId={caseId}
            subjectId={subject.id}
            moduleId={moduleId}
          />
        </Card>

        {/* Poznámky */}
        <Card>
          <CardHeader>
            <CardTitle>Poznámky a analýza</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormItemRHF<PhoneAnalysisFormValues>
              label="Technické poznámky"
              name="technicalNotes"
              control={form.control}
              placeholder="Technické poznámky k analýze telefonního čísla..."
              as="textarea"
              rows={4}
            />
            <FormItemRHF<PhoneAnalysisFormValues>
              label="OSINT poznámky"
              name="osintNotes"
              control={form.control}
              placeholder="Poznámky z OSINT vyšetřování telefonního čísla..."
              as="textarea"
              rows={4}
            />
            <FormItemRHF<PhoneAnalysisFormValues>
              label="Vyšetřovací poznámky"
              name="investigationNotes"
              control={form.control}
              placeholder="Interní poznámky vyšetřovatele..."
              as="textarea"
              rows={3}
            />
          </CardContent>
        </Card>

        {/* Tlačítko pro uložení */}
        <div className="flex justify-end pt-6">
          <Button type="submit" disabled={isSaving}>
            {isSaving ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Ukládání...
              </>
            ) : (
              "Uložit telefonní analýzu"
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
} 