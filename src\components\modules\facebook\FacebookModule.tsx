import { useState, useEffect } from "react";
import { doc, getDoc, deleteDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, Edit, Save, Plus, Trash2, Facebook, Image, Users, ThumbsUp, MessageSquare } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { FacebookModuleData } from "@/types/facebook";
import FacebookForm from "./FacebookForm";
import FacebookProfile from "./FacebookProfile";
import FacebookPosts from "./FacebookPosts";
import FacebookFriends from "./FacebookFriends";
import FacebookPages from "./FacebookPages";
import FacebookGroups from "./FacebookGroups";

interface FacebookModuleProps {
  caseId: string;
  subject: {
    id: string;
    firstName: string;
    lastName: string;
    type: "person" | "company";
  };
  moduleId: string;
  onClose: () => void;
}

export default function FacebookModule({ caseId, subject, moduleId, onClose }: FacebookModuleProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [data, setData] = useState<FacebookModuleData | null>(null);
  const [activeTab, setActiveTab] = useState("profile");
  const { toast } = useToast();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const docRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);
        const docSnap = await getDoc(docRef);
        
        if (docSnap.exists()) {
          setData(docSnap.data() as FacebookModuleData);
        } else {
          // Pokud data neexistují, vytvoříme prázdný objekt
          setData({
            subjectId: subject.id,
            lastUpdatedAt: null,
            createdAt: null
          });
          setIsEditing(true); // Automaticky přepneme do režimu editace
        }
      } catch (error) {
        console.error("Error fetching Facebook data:", error);
        toast({
          title: "Chyba při načítání dat",
          description: "Nepodařilo se načíst data Facebook modulu.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [caseId, subject.id, moduleId, toast]);

  const handleSave = (savedData: FacebookModuleData, wasNew: boolean) => {
    setData(savedData);
    setIsEditing(false);
    toast({
      title: wasNew ? "Modul vytvořen" : "Modul aktualizován",
      description: `Facebook modul byl úspěšně ${wasNew ? "vytvořen" : "aktualizován"}.`,
    });
  };

  const handleDelete = async () => {
    if (!confirm("Opravdu chcete smazat tento Facebook profil?")) {
      return;
    }
    
    setIsLoading(true);
    try {
      const docRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);
      await deleteDoc(docRef);
      
      toast({
        title: "Profil smazán",
        description: "Facebook profil byl úspěšně smazán.",
      });
      
      onClose();
    } catch (error) {
      console.error("Error deleting Facebook profile:", error);
      toast({
        title: "Chyba při mazání",
        description: "Nepodařilo se smazat Facebook profil.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Facebook className="mr-2 h-5 w-5 text-blue-600" />
            Facebook
          </CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center items-center min-h-[300px]">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  if (isEditing) {
    return (
      <FacebookForm
        caseId={caseId}
        subject={subject}
        moduleId={moduleId}
        existingData={data}
        onSave={handleSave}
        onCancel={() => setIsEditing(false)}
      />
    );
  }

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center">
          <Facebook className="mr-2 h-5 w-5 text-blue-600" />
          Facebook
        </CardTitle>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
            <Edit className="h-4 w-4 mr-1" /> Upravit
          </Button>
          <Button variant="destructive" size="sm" onClick={handleDelete}>
            <Trash2 className="h-4 w-4 mr-1" /> Smazat
          </Button>
          <Button variant="ghost" size="sm" onClick={onClose}>
            Zavřít
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="profile">Profil</TabsTrigger>
            <TabsTrigger value="posts">Příspěvky</TabsTrigger>
            <TabsTrigger value="friends">Přátelé</TabsTrigger>
            <TabsTrigger value="pages">Stránky</TabsTrigger>
            <TabsTrigger value="groups">Skupiny</TabsTrigger>
          </TabsList>
          
          <TabsContent value="profile">
            <FacebookProfile data={data} />
          </TabsContent>
          
          <TabsContent value="posts">
            <FacebookPosts data={data} />
          </TabsContent>
          
          <TabsContent value="friends">
            <FacebookFriends data={data} />
          </TabsContent>
          
          <TabsContent value="pages">
            <FacebookPages data={data} />
          </TabsContent>
          
          <TabsContent value="groups">
            <FacebookGroups data={data} />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
