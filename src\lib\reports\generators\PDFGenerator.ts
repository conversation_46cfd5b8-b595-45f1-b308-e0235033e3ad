import puppeteer from 'puppeteer';
import type { CaseData } from '../DataCollector';
import { generatePhoneNumbersPDF, generateSocialMediaPDF } from './SocialMediaPDF';

interface GenerateParams {
  caseData: CaseData;
  settings: any;
  modules: string[];
}

export class PDFGenerator {
  async generate(params: GenerateParams): Promise<Buffer> {
    const { caseData, settings, modules } = params;

    let browser;
    try {
      // Vytvoření HTML obsahu pro konverzi na PDF
      const htmlContent = this.generateHTMLForPDF(caseData, settings, modules);

      return await this.generateFromHTML(htmlContent);

    } catch (error) {
      console.error('Chyba při generování PDF:', error);
      throw new Error(`Nepodařilo se vygenerat PDF: ${error instanceof Error ? error.message : 'Neznámá chyba'}`);
    }
  }

  async generateFromHTML(htmlContent: string): Promise<Buffer> {
    let browser;
    try {
      // Spuštění puppeteeru pro generování PDF
      browser = await puppeteer.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      const page = await browser.newPage();

      // Nastavení obsahu stránky s časovým limitem
      await page.setContent(htmlContent, {
        waitUntil: 'networkidle0',
        timeout: 30000
      });

      // Nastavení viewport pro A4
      await page.setViewport({
        width: 794,
        height: 1123,
        deviceScaleFactor: 1,
      });

      // Generování PDF s optimalizací pro A4
      const pdfBuffer = await page.pdf({
        format: 'A4',
        printBackground: true,
        margin: {
          top: '20mm',
          right: '20mm',
          bottom: '20mm',
          left: '20mm'
        },
        preferCSSPageSize: true,
        displayHeaderFooter: false,
        timeout: 30000
      });

      await browser.close();
      return Buffer.from(pdfBuffer);

    } catch (error) {
      if (browser) {
        await browser.close();
      }
      console.error('Chyba při generování PDF z HTML:', error);
      throw new Error(`Nepodařilo se vygenerat PDF z HTML: ${error instanceof Error ? error.message : 'Neznámá chyba'}`);
    }
  }

  private generateHTMLForPDF(caseData: CaseData, settings: any, modules: string[]): string {
    return `
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSINT Report - ${settings.subject || caseData.name}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Times New Roman', serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #000000;
            background: #ffffff;
            margin: 0;
            padding: 0;
        }

        .document-container {
            width: 100%;
            background: white;
        }

        /* Zachovaná úvodní stránka */
        .header {
            border-bottom: 2px solid #333333;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }

        .police-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .police-left {
            font-size: 12pt;
        }

        .police-right {
            text-align: right;
        }

        .department-info {
            font-size: 10pt;
            margin-bottom: 15px;
            line-height: 1.3;
        }

        .document-info {
            display: flex;
            justify-content: space-between;
            font-size: 10pt;
        }

        .document-title {
            font-size: 18pt;
            font-weight: bold;
            text-align: center;
            margin: 40px 0;
            color: #000000;
        }

        .subject-name {
            font-size: 14pt;
            text-align: center;
            margin-bottom: 30px;
            font-weight: bold;
            color: #000000;
        }

        .protocol-section {
            margin: 30px 0;
        }

        .protocol-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #000000;
        }

        .protocol-text {
            text-align: justify;
            line-height: 1.5;
            color: #000000;
        }

        .warning-section {
            margin: 30px 0;
            padding: 15px;
            border: 1px solid #666666;
            background-color: #f5f5f5;
        }

        .warning-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #000000;
        }

        ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        li {
            margin-bottom: 5px;
            color: #000000;
        }

        /* Čisté stránky pro tisk */
        .page-break {
            page-break-before: always;
        }

        .page-header {
            margin-bottom: 30px;
        }

        .page-number {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 11pt;
            color: #000000;
        }

        .page-divider {
            border-bottom: 1px solid #333333;
            margin-bottom: 20px;
        }

        /* Sekce */
        .section {
            margin: 30px 0;
            page-break-inside: avoid;
        }

        .section-title {
            font-size: 13pt;
            font-weight: bold;
            color: #000000;
            margin-bottom: 20px;
            border-bottom: 1px solid #666666;
            padding-bottom: 3px;
        }

        /* Datové bloky - čisté, bez rámečků */
        .data-block {
            margin: 25px 0;
            page-break-inside: avoid;
        }

        .data-title {
            font-size: 12pt;
            font-weight: bold;
            color: #000000;
            margin-bottom: 15px;
        }

        .data-grid {
            margin: 15px 0;
        }

        .data-row {
            display: table;
            width: 100%;
            margin-bottom: 8px;
        }

        .data-label {
            display: table-cell;
            width: 140px;
            font-size: 10pt;
            color: #666666;
            font-weight: normal;
            vertical-align: top;
            padding-right: 15px;
        }

        .data-value {
            display: table-cell;
            font-size: 11pt;
            color: #000000;
            font-weight: normal;
            line-height: 1.3;
        }

        .data-value.emphasized {
            font-weight: bold;
        }

        /* Seznamy */
        .data-list {
            margin: 15px 0;
        }

        .data-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-item:last-child {
            border-bottom: none;
        }

        .item-primary {
            font-size: 11pt;
            color: #000000;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .item-secondary {
            font-size: 10pt;
            color: #666666;
            margin-bottom: 2px;
        }

        .item-status {
            font-size: 10pt;
            color: #000000;
            font-weight: bold;
        }

        /* Poznámky */
        .note-block {
            margin: 20px 0;
            padding: 15px 0;
            border-top: 1px solid #666666;
            border-bottom: 1px solid #666666;
        }

        .note-title {
            font-size: 11pt;
            font-weight: bold;
            color: #000000;
            margin-bottom: 8px;
        }

        .note-text {
            font-size: 11pt;
            color: #333333;
            line-height: 1.4;
        }

        /* Sub-bloky */
        .sub-block {
            margin: 20px 0;
            page-break-inside: avoid;
        }

        .sub-title {
            font-size: 11pt;
            font-weight: bold;
            color: #000000;
            margin-bottom: 10px;
            border-bottom: 1px solid #cccccc;
            padding-bottom: 3px;
        }

        /* Podpis */
        .signature-block {
            margin-top: 40px;
            text-align: right;
            page-break-inside: avoid;
        }

        .signature-content {
            display: inline-block;
            text-align: left;
            border-top: 1px solid #333333;
            padding-top: 15px;
            min-width: 200px;
        }

        .signature-title {
            font-size: 10pt;
            color: #666666;
            margin-bottom: 25px;
        }

        .signature-name {
            font-size: 11pt;
            font-weight: bold;
            color: #000000;
        }

        /* Tisk optimalizace */
        @media print {
            body {
                margin: 0;
                padding: 0;
                background: white;
            }

            .page-break {
                page-break-before: always;
            }

            .section, .data-block {
                page-break-inside: avoid;
            }
        }

        /* Kompatibilita pro starší moduly */
        .module {
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .module h3 {
            color: #000000;
            font-size: 12pt;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .info-table td {
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: top;
        }

        .info-table .label {
            font-weight: bold;
            width: 140px;
            color: #666666;
            font-size: 10pt;
            padding-right: 15px;
        }

        .info-table .value {
            color: #000000;
            font-size: 11pt;
        }

        .subsection {
            margin-top: 15px;
        }

        .subsection h4 {
            color: #000000;
            font-size: 11pt;
            margin-bottom: 8px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="document-container">
        <!-- Zachovaná úvodní stránka -->
        <div class="header">
            <div class="police-header">
                <div class="police-left">
                    POLICIE ČESKÉ REPUBLIKY<br>
                    Krajské ředitelství policie Pardubického kraje
                </div>
                <div class="police-right">
                    JID:
                </div>
            </div>
            <div class="department-info">
                ${(settings.department || '').replace(/\n/g, '<br>')}
            </div>
            <div class="document-info">
                <div class="left-info">
                    <div>Č. j. ${settings.documentNumber || ''}</div>
                </div>
                <div class="right-info">
                    <div>${settings.location} ${settings.date}</div>
                    <div>Počet stran: ${this.calculatePages(modules)}</div>
                </div>
            </div>
        </div>

        <div class="document-title">
            ${settings.title}
        </div>

        <div class="subject-name">
            ${settings.subject || 'Jméno, příjmení, datum narození'}
        </div>

        <div class="protocol-section">
            <div class="protocol-title">Cíl protokolu:</div>
            <div class="protocol-text">
                ${settings.purpose}
            </div>
        </div>

        <div class="warning-section" style="margin-top: 60px;">
            <div class="warning-title">Důležité upozornění:</div>
            <div class="protocol-text">
                Tento OSINT průzkum využívá kombinovaný přístup zahrnující:
                <ul>
                    <li>Systematické vyhledávání v interních informačních systémech Policie ČR</li>
                    <li>Komplexní prověření veškerých dostupných zdrojů v otevřeném prostoru Internet</li>
                </ul>
                Průzkum klade důraz na získání maximálního množství relevantních informací z otevřených zdrojů při současném zachování operativní bezpečnosti a eliminaci možnosti odhalení zájmu o sledovanou osobu.
            </div>
        </div>

        <!-- Dynamicky generované moduly -->
        ${this.generateModulesForPDF(caseData, modules)}

        <!-- Podpis -->
        <div class="signature-block">
            <div class="signature-content">
                <div class="signature-title">Zpracoval:</div>
                <div class="signature-name">${this.extractInvestigatorName(settings.department)}</div>
            </div>
        </div>
    </div>
</body>
</html>`;
  }

  private generateModulesForPDF(caseData: CaseData, modules: string[]): string {
    let html = '';
    let pageNumber = 2; // Začínáme od 2. stránky

    // Základní údaje
    if (caseData.personalInfo) {
      html += `
      <div class="page-break">
          <div class="page-header">
              <div class="page-number">${pageNumber}. strana</div>
              <div class="page-divider"></div>
          </div>

          <section class="section">
              <h2 class="section-title">Základní údaje</h2>
              ${this.generatePersonalInfoPDF(caseData.personalInfo)}
              ${this.generateAddressesPDF(caseData.personalInfo?.addresses)}
          </section>
      </div>`;
      pageNumber++;
    }

    // Komunikační prostředky
    if (caseData.phoneNumbers || caseData.emailAnalysis) {
      html += `
      <div class="page-break">
          <div class="page-header">
              <div class="page-number">${pageNumber}. strana</div>
              <div class="page-divider"></div>
          </div>

                      <section class="section">
                <h2 class="section-title">Komunikační prostředky</h2>
                ${caseData.phoneNumbers ? this.generatePhoneNumbersPDF(caseData.phoneNumbers) : ''}
                ${caseData.emailAnalysis ? this.generateEmailAnalysisPDF(caseData.emailAnalysis) : ''}
                ${this.generateSocialMediaPDF([])}
            </section>
      </div>`;
      pageNumber++;
    }

    // Další moduly
    for (const moduleId of modules) {
      if (!['personal-info', 'addresses', 'phone-numbers', 'emails', 'social-media'].includes(moduleId)) {
        html += `
        <div class="page-break">
            <div class="page-header">
                <div class="page-number">${pageNumber}. strana</div>
                <div class="page-divider"></div>
            </div>

            <section class="section">
                ${this.generateModuleContentForPDF(moduleId, caseData)}
            </section>
        </div>`;
        pageNumber++;
      }
    }

    return html;
  }

  private generateModuleContentForPDF(moduleId: string, caseData: CaseData): string {
    switch (moduleId) {
      case 'personal-info':
      case 'evidence_obyvatel':
        return `<h2 class="section-title">Evidence obyvatel</h2>${this.generateEvidenceObyvatelPDF(caseData.evidenceObyvatel)}`;
      case 'family-members':
      case 'family_members':
        return `<h2 class="section-title">Rodinní příslušníci</h2>${this.generateFamilyMembersPDF(caseData.familyMembers || [])}`;
      case 'driver-license':
      case 'registr_ridicske_prukazy':
        return `<h2 class="section-title">Řidičský průkaz</h2>${this.generateDriverLicensePDF(caseData.driverLicense)}`;
      case 'gun-license':
      case 'gun_license':
        return `<h2 class="section-title">Zbrojní průkaz</h2>${this.generateGunLicensePDF(caseData.gunLicense)}`;
      case 'vehicles':
        return `<h2 class="section-title">Vozidla</h2>${this.generateVehiclesPDF(caseData.vehicles || [])}`;
      case 'locations':
        return `<h2 class="section-title">Lokace</h2>${this.generateLocationsPDF(caseData.locations || [])}`;
      case 'business-activity':
      case 'business_activity':
        return `<h2 class="section-title">Podnikatelská činnost</h2>${this.generateBusinessActivityPDF(caseData.businessActivity)}`;
      case 'cadastre':
        return `<h2 class="section-title">Katastr nemovitostí</h2>${this.generateCadastrePDF(caseData.cadastre)}`;
      case 'training':
        return `<h2 class="section-title">Výcvik a školení</h2>${this.generateTrainingPDF(caseData.training)}`;
      case 'email-analysis':
      case 'email_analysis':
        return `<h2 class="section-title">Emailová analýza</h2>${this.generateEmailAnalysisPDF(caseData.emailAnalysis || [])}`;
      case 'phone-numbers':
      case 'phone_numbers':
        return `<h2 class="section-title">Telefonní čísla</h2>${this.generatePhoneNumbersPDF(caseData.phoneNumbers || [])}`;
      case 'network-analysis':
      case 'network_analysis':
        return `<h2 class="section-title">Síťová analýza</h2>${this.generateNetworkAnalysisPDF(caseData.networkAnalysis)}`;
      case 'map-overlays':
      case 'map_overlays':
        return `<h2 class="section-title">Mapové překryvy</h2>${this.generateMapOverlaysPDF(caseData.mapOverlays)}`;
      case 'facebook':
        return `<h2 class="section-title">Facebook profily</h2>${this.generateFacebookPDF(caseData.facebookData || [])}`;
      case 'instagram':
        return `<h2 class="section-title">Instagram profily</h2>${this.generateInstagramPDF(caseData.instagramData || [])}`;
      case 'twitter':
        return `<h2 class="section-title">Twitter profily</h2>${this.generateTwitterPDF(caseData.twitterData || [])}`;
      case 'linkedin':
        return `<h2 class="section-title">LinkedIn profily</h2>${this.generateLinkedInPDF(caseData.linkedinData || [])}`;
      case 'phone-analysis':
      case 'phone_analysis':
        return `<h2 class="section-title">Analýza telefonu</h2>${this.generatePhoneAnalysisPDF(caseData.phoneAnalysis)}`;
      case 'financial-monitoring':
      case 'financial_monitoring':
        return `<h2 class="section-title">Finanční monitoring</h2>${this.generateFinancialMonitoringPDF(caseData.financialMonitoring)}`;
      default:
        return `<h2 class="section-title">Neznámý modul</h2><div class="data-block"><p>Modul ${moduleId} není podporován.</p></div>`;
    }
  }

  private generatePersonalInfoPDF(data: any): string {
    if (!data) return '';

    return `
    <div class="data-block">
        <div class="data-title">Osobní údaje</div>
        <div class="data-grid">
            <div class="data-row">
                <div class="data-label">Jméno a příjmení:</div>
                <div class="data-value emphasized">${data.firstName || ''} ${data.lastName || ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Datum narození:</div>
                <div class="data-value">${data.birthDate ? new Date(data.birthDate).toLocaleDateString('cs-CZ') : ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Místo narození:</div>
                <div class="data-value">${data.birthPlace || ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Státní příslušnost:</div>
                <div class="data-value">${data.nationality || ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Číslo OP:</div>
                <div class="data-value">${data.idNumber || ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Stav ověření:</div>
                <div class="data-value emphasized">Ověřeno z úředních zdrojů</div>
            </div>
        </div>

        ${data.notes ? `
        <div class="note-block">
            <div class="note-title">Poznámky:</div>
            <div class="note-text">${data.notes}</div>
        </div>
        ` : ''}
    </div>`;
  }

  private generateCompanyPDF(data: any): string {
    if (!data) return '';

    return `
    <div class="data-block">
        <div class="data-title">Informace o firmě</div>
        <div class="data-grid">
            <div class="data-row">
                <div class="data-label">Název:</div>
                <div class="data-value emphasized">${data.name || ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">IČO:</div>
                <div class="data-value">${data.ico || ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">DIČ:</div>
                <div class="data-value">${data.dic || ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Právní forma:</div>
                <div class="data-value">${data.legalForm || ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Stav:</div>
                <div class="data-value emphasized">${data.status || 'Aktivní'}</div>
            </div>
        </div>
    </div>`;
  }

  private generateAddressesPDF(data: any[]): string {
    if (!data || data.length === 0) return '';

    return `
    <div class="data-block">
        <div class="data-title">Adresy</div>
        <div class="data-list">
            ${data.map((address, index) => `
            <div class="data-item">
                <div class="item-primary">${address.street || ''} ${address.number || ''}</div>
                <div class="item-secondary">${address.city || ''} ${address.zip || ''}</div>
                <div class="item-status">${address.type || 'Trvalá'}</div>
            </div>
            `).join('')}
        </div>
    </div>`;
  }

  private generatePhoneNumbersPDF(data: any[]): string {
    if (!data || data.length === 0) return '';

    return `
    <div class="data-block">
        <div class="data-title">Telefonní čísla</div>
        <div class="data-list">
            ${data.map((phone, index) => `
            <div class="data-item">
                <div class="item-primary">${phone.number || ''}</div>
                <div class="item-secondary">${phone.carrier || ''}</div>
                <div class="item-status">Ověřeno</div>
            </div>
            `).join('')}
        </div>
    </div>`;
  }

  private generateEmailAnalysisPDF(data: any[]): string {
    if (!data || data.length === 0) return '';

    return `
    <div class="data-block">
        <div class="data-title">Emailové adresy</div>
        <div class="data-list">
            ${data.map((email, index) => `
            <div class="data-item">
                <div class="item-primary">${email.address || ''}</div>
                <div class="item-secondary">${email.provider || ''}</div>
                <div class="item-status">Ověřeno</div>
            </div>
            `).join('')}
        </div>
    </div>`;
  }

  private generateSocialMediaPDF(data: any[]): string {
    if (!data || data.length === 0) return '';

    return `
    <div class="data-block">
        <div class="data-title">Sociální sítě</div>
        <div class="data-list">
            ${data.map((social, index) => `
            <div class="data-item">
                <div class="item-primary">${social.platform || ''}</div>
                <div class="item-secondary">${social.username || ''}</div>
                <div class="item-status">Aktivní</div>
            </div>
            `).join('')}
        </div>
    </div>`;
  }

  private generateRealEstatePDF(data: any[]): string {
    if (!data || data.length === 0) return '';

    return `
    <div class="data-block">
        <div class="data-title">Nemovitosti</div>
        <div class="data-list">
            ${data.map((property, index) => `
            <div class="data-item">
                <div class="item-primary">${property.address || ''}</div>
                <div class="item-secondary">${property.type || ''} - ${property.ownership || ''}</div>
                <div class="item-status">Ověřeno</div>
            </div>
            `).join('')}
        </div>
    </div>`;
  }

  private generateTrainingPDF(data: any): string {
    if (!data) return '';

    return `
    <div class="data-block">
        <div class="data-title">Výcvik a bezpečnostní rizika</div>
        ${data.policeTraining && data.policeTraining.length > 0 ? `
        <div class="data-list">
            ${data.policeTraining.map((training: any, index: number) => `
            <div class="data-item">
                <div class="item-primary">${training.rank || 'Nespecifikováno'}</div>
                <div class="item-secondary">${training.unit || ''}</div>
                <div class="item-status">Ověřeno</div>
            </div>
            `).join('')}
        </div>
        ` : `
        <div class="note-block">
            <div class="note-title">Poznámka:</div>
            <div class="note-text">Nebyly nalezeny žádné záznamy o výcviku.</div>
        </div>
        `}
    </div>`;
  }

  private generateIpAddressesPDF(data: any[]): string {
    if (!data || data.length === 0) return '';

    return `
    <div class="data-block">
        <div class="data-title">IP adresy</div>
        <div class="data-list">
            ${data.map((ip, index) => `
            <div class="data-item">
                <div class="item-primary">${ip.address || ''}</div>
                <div class="item-secondary">${ip.location || ''} - ${ip.provider || ''}</div>
                <div class="item-status">Ověřeno</div>
            </div>
            `).join('')}
        </div>
    </div>`;
  }

  private generateMapOverlaysPDF(data: any): string {
    if (!data) return '';

    return `
    <div class="data-block">
        <div class="data-title">Mapové překryvy</div>
        <div class="data-grid">
            <div class="data-row">
                <div class="data-label">Počet bodů:</div>
                <div class="data-value">${data.points ? data.points.length : 0}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Počet oblastí:</div>
                <div class="data-value">${data.areas ? data.areas.length : 0}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Počet tras:</div>
                <div class="data-value">${data.routes ? data.routes.length : 0}</div>
            </div>
        </div>
    </div>`;
  }

  private calculatePages(modules: string[]): number {
    return Math.max(modules.length + 1, 3); // Minimálně 3 stránky
  }

  private extractInvestigatorName(department: string): string {
    // Extrahuje jméno ze sekce department
    const lines = department.split('\n');
    const nameLine = lines.find(line => line.includes('Vyšetřovatel:') || line.includes('Zpracoval:'));
    if (nameLine) {
      return nameLine.split(':')[1]?.trim() || 'Jméno vyšetřovatele';
    }
    return 'Jméno vyšetřovatele';
  }

  private generateFacebookPDF(data: any[]): string {
    if (!data || data.length === 0) return '';

    let html = '';

    data.forEach((profile, index) => {
      html += `
      <div class="data-block">
        <div class="data-title">Facebook profil: ${profile.profileName || profile.username || 'Neurčený název'}</div>

        <div class="data-grid">
          <div class="data-row">
            <div class="data-label">Profilové jméno:</div>
            <div class="data-value emphasized">${profile.profileName || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Uživatelské jméno:</div>
            <div class="data-value">${profile.username || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">URL profilu:</div>
            <div class="data-value">${profile.profileUrl || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Současné bydliště:</div>
            <div class="data-value">${profile.currentLocation || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Současné zaměstnání:</div>
            <div class="data-value">${profile.currentJob || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Vzdělání:</div>
            <div class="data-value">${profile.education || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Vztahový stav:</div>
            <div class="data-value">${profile.relationshipStatus || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Počet přátel:</div>
            <div class="data-value">${profile.friendsCount || 'Neznámý'}</div>
          </div>
        </div>

        ${profile.aboutMe ? `
        <div class="note-block">
          <div class="note-title">O osobě:</div>
          <div class="note-text">${profile.aboutMe}</div>
        </div>
        ` : ''}

        ${profile.contactInfo ? `
        <div class="note-block">
          <div class="note-title">Kontaktní údaje:</div>
          <div class="note-text">${profile.contactInfo}</div>
        </div>
        ` : ''}

        ${profile.investigationNotes ? `
        <div class="note-block">
          <div class="note-title">Poznámky k vyšetřování:</div>
          <div class="note-text">${profile.investigationNotes}</div>
        </div>
        ` : ''}

        ${this.generateFacebookPostsPDF(profile.posts || [])}
        ${this.generateFacebookPhotosPDF(profile.photos || [])}
      </div>
      `;
    });

    return html;
  }

  private generateFacebookPostsPDF(posts: any[]): string {
    if (!posts || posts.length === 0) return '';

    let html = `
    <div class="sub-block">
      <div class="sub-title">Příspěvky</div>
      <div class="data-list">
    `;

    posts.forEach((post, index) => {
      html += `
      <div class="data-item">
        <div class="item-primary">${post.date ? `Datum: ${post.date}` : 'Datum neznámé'}</div>
        <div class="item-text">${post.text || ''}</div>
        ${post.likesCount ? `<div class="item-secondary">Počet reakcí: ${post.likesCount}</div>` : ''}
        ${post.sharesCount ? `<div class="item-secondary">Počet sdílení: ${post.sharesCount}</div>` : ''}
        ${post.notes ? `<div class="item-secondary">Poznámky: ${post.notes}</div>` : ''}
      </div>
      `;
    });

    html += `
      </div>
    </div>
    `;

    return html;
  }

  private generateFacebookPhotosPDF(photos: any[]): string {
    if (!photos || photos.length === 0) return '';

    let html = `
    <div class="sub-block">
      <div class="sub-title">Fotografie</div>
      <div class="photo-gallery">
    `;

    photos.forEach((photo, index) => {
      html += `
      <div class="photo-item" style="text-align: center; margin-bottom: 20px;">
        ${photo.downloadURL ? `<img src="${photo.downloadURL}" alt="Fotografie ${index + 1}" style="max-width: 100%; max-height: 400px; margin: 0 auto; display: block;">` : ''}
        ${photo.description ? `<div class="photo-description" style="text-align: center; margin-top: 10px;">${photo.description}</div>` : ''}
        ${photo.dateTaken ? `<div class="photo-date" style="text-align: center; margin-top: 5px;">Datum pořízení: ${photo.dateTaken}</div>` : ''}
      </div>
      `;
    });

    html += `
      </div>
    </div>
    `;

    return html;
  }

  private generateInstagramPDF(data: any[]): string {
    if (!data || data.length === 0) return '';

    let html = '';

    data.forEach((profile, index) => {
      html += `
      <div class="data-block">
        <div class="data-title">Instagram profil: ${profile.displayName || profile.username || 'Neurčený název'}</div>

        <div class="data-grid">
          <div class="data-row">
            <div class="data-label">Jméno na profilu:</div>
            <div class="data-value emphasized">${profile.displayName || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Uživatelské jméno:</div>
            <div class="data-value">@${profile.username || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">URL profilu:</div>
            <div class="data-value">${profile.profileUrl || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Počet sledujících:</div>
            <div class="data-value">${profile.followersCount || 'Neznámý'}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Sleduje účtů:</div>
            <div class="data-value">${profile.followingCount || 'Neznámý'}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Počet příspěvků:</div>
            <div class="data-value">${profile.postsCount || 'Neznámý'}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Ověřený účet:</div>
            <div class="data-value">${profile.isVerified ? 'Ano' : 'Ne'}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Soukromý účet:</div>
            <div class="data-value">${profile.isPrivate ? 'Ano' : 'Ne'}</div>
          </div>
        </div>

        ${profile.bio ? `
        <div class="note-block">
          <div class="note-title">Bio:</div>
          <div class="note-text">${profile.bio}</div>
        </div>
        ` : ''}

        ${profile.website ? `
        <div class="note-block">
          <div class="note-title">Webová stránka:</div>
          <div class="note-text">${profile.website}</div>
        </div>
        ` : ''}

        ${profile.notes ? `
        <div class="note-block">
          <div class="note-title">Poznámky:</div>
          <div class="note-text">${profile.notes}</div>
        </div>
        ` : ''}

        ${this.generateInstagramPostsPDF(profile.posts || [])}
        ${this.generateInstagramReelsPDF(profile.reels || [])}
        ${this.generateInstagramStoriesPDF(profile.stories || [])}
        ${this.generateInstagramPhotosPDF(profile.photos || [])}
      </div>
      `;
    });

    return html;
  }

  private generateInstagramPostsPDF(posts: any[]): string {
    if (!posts || posts.length === 0) return '';

    let html = `
    <div class="sub-block">
      <div class="sub-title">Příspěvky</div>
      <div class="data-list">
    `;

    posts.forEach((post, index) => {
      html += `
      <div class="data-item">
        <div class="item-primary">${post.date ? `Datum: ${post.date}` : 'Datum neznámé'}</div>
        <div class="item-text">${post.caption || ''}</div>
        ${post.hashtags ? `<div class="item-secondary">Hashtagy: ${Array.isArray(post.hashtags) ? post.hashtags.join(', ') : post.hashtags}</div>` : ''}
        ${post.likesCount ? `<div class="item-secondary">Počet reakcí: ${post.likesCount}</div>` : ''}
        ${post.commentsCount ? `<div class="item-secondary">Počet komentářů: ${post.commentsCount}</div>` : ''}
        ${post.location ? `<div class="item-secondary">Lokace: ${post.location}</div>` : ''}
      </div>
      `;
    });

    html += `
      </div>
    </div>
    `;

    return html;
  }

  private generateInstagramReelsPDF(reels: any[]): string {
    if (!reels || reels.length === 0) return '';

    let html = `
    <div class="sub-block">
      <div class="sub-title">Reels</div>
      <div class="data-list">
    `;

    reels.forEach((reel, index) => {
      html += `
      <div class="data-item">
        <div class="item-primary">${reel.date ? `Datum: ${reel.date}` : 'Datum neznámé'}</div>
        <div class="item-text">${reel.caption || ''}</div>
        ${reel.hashtags ? `<div class="item-secondary">Hashtagy: ${Array.isArray(reel.hashtags) ? reel.hashtags.join(', ') : reel.hashtags}</div>` : ''}
        ${reel.likesCount ? `<div class="item-secondary">Počet reakcí: ${reel.likesCount}</div>` : ''}
        ${reel.commentsCount ? `<div class="item-secondary">Počet komentářů: ${reel.commentsCount}</div>` : ''}
        ${reel.duration ? `<div class="item-secondary">Délka: ${reel.duration} sekund</div>` : ''}
      </div>
      `;
    });

    html += `
      </div>
    </div>
    `;

    return html;
  }

  private generateInstagramStoriesPDF(stories: any[]): string {
    if (!stories || stories.length === 0) return '';

    let html = `
    <div class="sub-block">
      <div class="sub-title">Stories</div>
      <div class="data-list">
    `;

    stories.forEach((story, index) => {
      html += `
      <div class="data-item">
        <div class="item-primary">${story.date ? `Datum: ${story.date}` : 'Datum neznámé'}</div>
        <div class="item-text">${story.content || ''}</div>
        ${story.type ? `<div class="item-secondary">Typ: ${story.type}</div>` : ''}
        ${story.location ? `<div class="item-secondary">Lokace: ${story.location}</div>` : ''}
      </div>
      `;
    });

    html += `
      </div>
    </div>
    `;

    return html;
  }

  private generateInstagramPhotosPDF(photos: any[]): string {
    if (!photos || photos.length === 0) return '';

    let html = `
    <div class="sub-block">
      <div class="sub-title">Fotografie</div>
      <div class="photo-gallery">
    `;

    photos.forEach((photo, index) => {
      html += `
      <div class="photo-item" style="text-align: center; margin-bottom: 20px;">
        ${photo.downloadURL ? `<img src="${photo.downloadURL}" alt="Fotografie ${index + 1}" style="max-width: 100%; max-height: 400px; margin: 0 auto; display: block;">` : ''}
        ${photo.description ? `<div class="photo-description" style="text-align: center; margin-top: 10px;">${photo.description}</div>` : ''}
        ${photo.dateTaken ? `<div class="photo-date" style="text-align: center; margin-top: 5px;">Datum pořízení: ${photo.dateTaken}</div>` : ''}
      </div>
      `;
    });

    html += `
      </div>
    </div>
    `;

    return html;
  }

  private generateEvidenceObyvatelPDF(data: any): string {
    if (!data) return '<div class="data-block"><p>Žádná data z evidence obyvatel nejsou k dispozici.</p></div>';

    return `
    <div class="data-block">
      <div class="data-title">Evidence obyvatel</div>
      <div class="data-grid">
        <div class="data-row">
          <div class="data-label">Jméno a příjmení:</div>
          <div class="data-value emphasized">${data.firstName || ''} ${data.lastName || ''}</div>
        </div>
        <div class="data-row">
          <div class="data-label">Datum narození:</div>
          <div class="data-value">${data.birthDate ? new Date(data.birthDate).toLocaleDateString('cs-CZ') : ''}</div>
        </div>
        <div class="data-row">
          <div class="data-label">Místo narození:</div>
          <div class="data-value">${data.birthPlace || ''}</div>
        </div>
        <div class="data-row">
          <div class="data-label">Státní příslušnost:</div>
          <div class="data-value">${data.nationality || ''}</div>
        </div>
        <div class="data-row">
          <div class="data-label">Číslo OP:</div>
          <div class="data-value">${data.idNumber || ''}</div>
        </div>
        <div class="data-row">
          <div class="data-label">Stav ověření:</div>
          <div class="data-value emphasized">Ověřeno z úředních zdrojů</div>
        </div>
      </div>

      ${data.addresses && data.addresses.length > 0 ? `
      <div class="sub-block">
        <div class="sub-title">Adresy</div>
        <div class="data-list">
          ${data.addresses.map((address: any) => `
          <div class="data-item">
            <div class="item-primary">${address.street || ''} ${address.number || ''}</div>
            <div class="item-secondary">${address.city || ''} ${address.zip || ''}</div>
            <div class="item-status">${address.type || 'Trvalá'}</div>
          </div>
          `).join('')}
        </div>
      </div>
      ` : ''}

      ${data.notes ? `
      <div class="note-block">
        <div class="note-title">Poznámky:</div>
        <div class="note-text">${data.notes}</div>
      </div>
      ` : ''}
    </div>`;
  }

  private generateFamilyMembersPDF(data: any[]): string {
    if (!data || data.length === 0) return '<div class="data-block"><p>Žádní rodinní příslušníci nejsou evidováni.</p></div>';

    return `
    <div class="data-block">
      <div class="data-title">Rodinní příslušníci</div>
      <div class="data-list">
        ${data.map((member: any, index: number) => `
        <div class="data-item">
          <div class="item-primary">${member.firstName || ''} ${member.lastName || ''}</div>
          <div class="item-secondary">${member.relationship || ''}</div>
          <div class="item-status">${member.birthDate ? new Date(member.birthDate).toLocaleDateString('cs-CZ') : ''}</div>
          ${member.notes ? `<div class="item-text">${member.notes}</div>` : ''}
        </div>
        `).join('')}
      </div>
    </div>`;
  }

  private generateDriverLicensePDF(data: any): string {
    if (!data) return '<div class="data-block"><p>Žádné informace o řidičském průkazu nejsou k dispozici.</p></div>';

    return `
    <div class="data-block">
      <div class="data-title">Řidičský průkaz</div>
      <div class="data-grid">
        ${data.licenseNumber ? `
        <div class="data-row">
          <div class="data-label">Číslo ŘP:</div>
          <div class="data-value">${data.licenseNumber}</div>
        </div>
        ` : ''}
        ${data.validity ? `
        <div class="data-row">
          <div class="data-label">Platnost:</div>
          <div class="data-value">${data.validity}</div>
        </div>
        ` : ''}
        ${data.categories ? `
        <div class="data-row">
          <div class="data-label">Skupiny oprávnění:</div>
          <div class="data-value">${data.categories}</div>
        </div>
        ` : ''}
        ${data.pointsBalance !== undefined ? `
        <div class="data-row">
          <div class="data-label">Bodové konto:</div>
          <div class="data-value">${data.pointsBalance} bodů</div>
        </div>
        ` : ''}
      </div>

      ${data.offenses && data.offenses.length > 0 ? `
      <div class="sub-block">
        <div class="sub-title">Přestupky v dopravě (${data.offenses.length})</div>
        <div class="data-list">
          ${data.offenses.map((offense: any) => `
          <div class="data-item">
            <div class="item-primary">${offense.date || 'Neznámé datum'}</div>
            <div class="item-text">${offense.description || ''}</div>
            <div class="item-secondary">${offense.location || ''}</div>
            ${offense.penaltyAmount ? `<div class="item-status">Pokuta: ${offense.penaltyAmount}</div>` : ''}
          </div>
          `).join('')}
        </div>
      </div>
      ` : ''}

      ${this.generatePhotosPDF(data.photos || [], 'Fotodokumentace ŘP')}

      ${data.notes ? `
      <div class="note-block">
        <div class="note-title">Poznámky:</div>
        <div class="note-text">${data.notes}</div>
      </div>
      ` : ''}
    </div>`;
  }

  private generateGunLicensePDF(data: any): string {
    if (!data) return '<div class="data-block"><p>Žádné informace o zbrojním průkazu nejsou k dispozici.</p></div>';

    return `
    <div class="data-block">
      <div class="data-title">Zbrojní průkaz</div>
      <div class="data-grid">
        <div class="data-row">
          <div class="data-label">Stav vlastnictví ZP:</div>
          <div class="data-value">${this.translateGunOwnershipStatus(data.ownershipStatus || 'unknown')}</div>
        </div>
        ${data.licenseNumber ? `
        <div class="data-row">
          <div class="data-label">Číslo ZP:</div>
          <div class="data-value">${data.licenseNumber}</div>
        </div>
        ` : ''}
        ${data.groups && data.groups.length > 0 ? `
        <div class="data-row">
          <div class="data-label">Skupiny ZP:</div>
          <div class="data-value">${data.groups.map((g: string) => this.translateGunLicenseGroup(g)).join(', ')}</div>
        </div>
        ` : ''}
        ${data.validUntil ? `
        <div class="data-row">
          <div class="data-label">Platnost do:</div>
          <div class="data-value">${data.validUntil}</div>
        </div>
        ` : ''}
      </div>

      ${data.registeredWeapons && data.registeredWeapons.length > 0 ? `
      <div class="sub-block">
        <div class="sub-title">Registrované zbraně (${data.registeredWeapons.length})</div>
        <div class="data-list">
          ${data.registeredWeapons.map((weapon: any) => `
          <div class="data-item">
            <div class="item-primary">${weapon.brand || ''} ${weapon.model || ''}</div>
            <div class="item-secondary">${weapon.serialNumber || ''} - ${weapon.caliber || ''}</div>
            <div class="item-status">${this.translateWeaponLegalStatus(weapon.legalStatus || 'unknown')}</div>
          </div>
          `).join('')}
        </div>
      </div>
      ` : ''}

      ${this.generatePhotosPDF(data.photos || [], 'Fotodokumentace ZP')}

      ${data.notes ? `
      <div class="note-block">
        <div class="note-title">Poznámky:</div>
        <div class="note-text">${data.notes}</div>
      </div>
      ` : ''}
    </div>`;
  }

  private generatePhotosPDF(photos: any[], title: string = 'Fotodokumentace'): string {
    if (!photos || photos.length === 0) return '';

    return `
    <div class="sub-block">
      <div class="sub-title">${title}</div>
      <div class="photo-gallery">
        ${photos.map((photo: any, index: number) => `
        <div class="photo-item" style="text-align: center; margin-bottom: 20px;">
          ${photo.downloadURL ? `<img src="${photo.downloadURL}" alt="Fotografie ${index + 1}" style="max-width: 100%; max-height: 400px; margin: 0 auto; display: block;">` : ''}
          ${photo.description ? `<div class="photo-description" style="text-align: center; margin-top: 10px;">${photo.description}</div>` : ''}
          ${photo.dateTaken ? `<div class="photo-date" style="text-align: center; margin-top: 5px;">Datum pořízení: ${photo.dateTaken}</div>` : ''}
        </div>
        `).join('')}
      </div>
    </div>`;
  }

  private translateGunOwnershipStatus(status: string): string {
    const translations: { [key: string]: string } = {
      'yes': 'Ano, vlastní platný ZP',
      'no': 'Ne, nevlastní ZP',
      'expired': 'Neplatný / Odňatý ZP',
      'in-process': 'V řízení o vydání ZP',
      'unknown': 'Nezjištěno'
    };
    return translations[status] || status;
  }

  private translateGunLicenseGroup(group: string): string {
    const translations: { [key: string]: string } = {
      'A': 'A - Sběratelské',
      'B': 'B - Sportovní',
      'C': 'C - Lovecké',
      'D': 'D - Výkon zaměstnání',
      'E': 'E - Ochrana života, zdraví a majetku',
      'F': 'F - Pyrotechnika'
    };
    return translations[group] || group;
  }

  private translateWeaponLegalStatus(status: string): string {
    const translations: { [key: string]: string } = {
      'legal_registered': 'Legální registrovaná',
      'legal_unregistered': 'Legální neregistrovaná',
      'illegal_unregistered': 'Nelegální neregistrovaná',
      'stolen': 'Kradená',
      'converted': 'Přestavěná',
      'deactivated': 'Deaktivovaná',
      'unknown': 'Nezjištěno'
    };
    return translations[status] || status;
  }

  private generateVehiclesPDF(data: any[]): string {
    if (!data || data.length === 0) return '<div class="data-block"><p>Žádná vozidla nejsou evidována.</p></div>';

    return `
    <div class="data-block">
      <div class="data-title">Vozidla</div>
      ${data.map((vehicle: any, index: number) => `
      <div class="sub-block">
        <div class="sub-title">Vozidlo ${index + 1}</div>
        <div class="data-grid">
          ${vehicle.brand || vehicle.model ? `
          <div class="data-row">
            <div class="data-label">Značka a model:</div>
            <div class="data-value emphasized">${vehicle.brand || ''} ${vehicle.model || ''}</div>
          </div>
          ` : ''}
          ${vehicle.licensePlate ? `
          <div class="data-row">
            <div class="data-label">SPZ/RZ:</div>
            <div class="data-value">${vehicle.licensePlate}</div>
          </div>
          ` : ''}
          ${vehicle.vin ? `
          <div class="data-row">
            <div class="data-label">VIN:</div>
            <div class="data-value">${vehicle.vin}</div>
          </div>
          ` : ''}
          ${vehicle.color ? `
          <div class="data-row">
            <div class="data-label">Barva:</div>
            <div class="data-value">${vehicle.color}</div>
          </div>
          ` : ''}
          ${vehicle.yearManufactured ? `
          <div class="data-row">
            <div class="data-label">Rok výroby:</div>
            <div class="data-value">${vehicle.yearManufactured}</div>
          </div>
          ` : ''}
          ${vehicle.relationshipType ? `
          <div class="data-row">
            <div class="data-label">Vztah k vozidlu:</div>
            <div class="data-value">${this.translateRelationshipType(vehicle.relationshipType)}</div>
          </div>
          ` : ''}
          ${vehicle.otherRelationshipDetail ? `
          <div class="data-row">
            <div class="data-label">Upřesnění vztahu:</div>
            <div class="data-value">${vehicle.otherRelationshipDetail}</div>
          </div>
          ` : ''}
          ${vehicle.stkValidUntil ? `
          <div class="data-row">
            <div class="data-label">Platnost STK do:</div>
            <div class="data-value">${vehicle.stkValidUntil}</div>
          </div>
          ` : ''}
          ${vehicle.firstRegistered ? `
          <div class="data-row">
            <div class="data-label">Datum první registrace:</div>
            <div class="data-value">${vehicle.firstRegistered}</div>
          </div>
          ` : ''}
        </div>

        ${vehicle.notes ? `
        <div class="note-block">
          <div class="note-title">Poznámky:</div>
          <div class="note-text">${vehicle.notes}</div>
        </div>
        ` : ''}

        ${this.generatePhotosPDF(vehicle.photos || [], `Fotodokumentace vozidla ${index + 1}`)}
      </div>
      `).join('')}
    </div>`;
  }

  private translateRelationshipType(type: string): string {
    const translations: { [key: string]: string } = {
      'owner': 'Vlastník',
      'user': 'Uživatel',
      'driver': 'Řidič',
      'lessee': 'Nájemce',
      'other': 'Jiný'
    };
    return translations[type] || type;
  }

  private generateLocationsPDF(data: any[]): string {
    if (!data || data.length === 0) return '<div class="data-block"><p>Žádné lokace nejsou evidovány.</p></div>';

    return `
    <div class="data-block">
      <div class="data-title">Lokace</div>
      <div class="data-list">
        ${data.map((location: any, index: number) => `
        <div class="data-item">
          <div class="item-primary">${location.name || `Lokace ${index + 1}`}</div>
          <div class="item-secondary">${location.address || ''}</div>
          <div class="item-status">${location.type || 'Nespecifikováno'}</div>
          ${location.coordinates ? `<div class="item-text">GPS: ${location.coordinates}</div>` : ''}
          ${location.description ? `<div class="item-text">${location.description}</div>` : ''}
        </div>
        `).join('')}
      </div>
    </div>`;
  }

  private generateBusinessActivityPDF(data: any): string {
    if (!data) return '<div class="data-block"><p>Žádné informace o podnikatelské činnosti nejsou k dispozici.</p></div>';

    return `
    <div class="data-block">
      <div class="data-title">Podnikatelská činnost</div>
      <div class="data-grid">
        ${data.companyName ? `
        <div class="data-row">
          <div class="data-label">Název společnosti:</div>
          <div class="data-value emphasized">${data.companyName}</div>
        </div>
        ` : ''}
        ${data.ico ? `
        <div class="data-row">
          <div class="data-label">IČO:</div>
          <div class="data-value">${data.ico}</div>
        </div>
        ` : ''}
        ${data.dic ? `
        <div class="data-row">
          <div class="data-label">DIČ:</div>
          <div class="data-value">${data.dic}</div>
        </div>
        ` : ''}
        ${data.legalForm ? `
        <div class="data-row">
          <div class="data-label">Právní forma:</div>
          <div class="data-value">${data.legalForm}</div>
        </div>
        ` : ''}
        ${data.status ? `
        <div class="data-row">
          <div class="data-label">Stav:</div>
          <div class="data-value">${data.status}</div>
        </div>
        ` : ''}
      </div>

      ${data.businessActivities && data.businessActivities.length > 0 ? `
      <div class="sub-block">
        <div class="sub-title">Předměty podnikání</div>
        <div class="data-list">
          ${data.businessActivities.map((activity: any) => `
          <div class="data-item">
            <div class="item-primary">${activity.name || activity.description || ''}</div>
            <div class="item-secondary">${activity.code || ''}</div>
          </div>
          `).join('')}
        </div>
      </div>
      ` : ''}

      ${this.generatePhotosPDF(data.photos || [], 'Fotodokumentace podnikání')}

      ${data.notes ? `
      <div class="note-block">
        <div class="note-title">Poznámky:</div>
        <div class="note-text">${data.notes}</div>
      </div>
      ` : ''}
    </div>`;
  }

  private generateCadastrePDF(data: any): string {
    if (!data) return '<div class="data-block"><p>Žádné informace z katastru nemovitostí nejsou k dispozici.</p></div>';

    return `
    <div class="data-block">
      <div class="data-title">Katastr nemovitostí</div>

      ${data.properties && data.properties.length > 0 ? `
      <div class="sub-block">
        <div class="sub-title">Nemovitosti (${data.properties.length})</div>
        <div class="data-list">
          ${data.properties.map((property: any) => `
          <div class="data-item">
            <div class="item-primary">${property.address || property.description || 'Nemovitost'}</div>
            <div class="item-secondary">${property.type || ''} - ${property.ownership || ''}</div>
            <div class="item-status">${property.parcelNumber || ''}</div>
            ${property.area ? `<div class="item-text">Výměra: ${property.area} m²</div>` : ''}
          </div>
          `).join('')}
        </div>
      </div>
      ` : ''}

      ${this.generatePhotosPDF(data.photos || [], 'Fotodokumentace nemovitostí')}

      ${data.notes ? `
      <div class="note-block">
        <div class="note-title">Poznámky:</div>
        <div class="note-text">${data.notes}</div>
      </div>
      ` : ''}
    </div>`;
  }

  private generateNetworkAnalysisPDF(data: any): string {
    if (!data) return '<div class="data-block"><p>Žádné informace ze síťové analýzy nejsou k dispozici.</p></div>';

    return `
    <div class="data-block">
      <div class="data-title">Síťová analýza</div>

      ${data.ipAddresses && data.ipAddresses.length > 0 ? `
      <div class="sub-block">
        <div class="sub-title">IP adresy (${data.ipAddresses.length})</div>
        <div class="data-list">
          ${data.ipAddresses.map((ip: any) => `
          <div class="data-item">
            <div class="item-primary">${ip.address || ''}</div>
            <div class="item-secondary">${ip.location || ''} - ${ip.provider || ''}</div>
            <div class="item-status">${ip.type || 'Neznámo'}</div>
          </div>
          `).join('')}
        </div>
      </div>
      ` : ''}

      ${data.domains && data.domains.length > 0 ? `
      <div class="sub-block">
        <div class="sub-title">Domény (${data.domains.length})</div>
        <div class="data-list">
          ${data.domains.map((domain: any) => `
          <div class="data-item">
            <div class="item-primary">${domain.name || ''}</div>
            <div class="item-secondary">${domain.registrar || ''}</div>
            <div class="item-status">${domain.status || 'Aktivní'}</div>
          </div>
          `).join('')}
        </div>
      </div>
      ` : ''}

      ${this.generatePhotosPDF(data.photos || [], 'Fotodokumentace síťové analýzy')}

      ${data.notes ? `
      <div class="note-block">
        <div class="note-title">Poznámky:</div>
        <div class="note-text">${data.notes}</div>
      </div>
      ` : ''}
    </div>`;
  }

  private generateTwitterPDF(data: any[]): string {
    if (!data || data.length === 0) return '<div class="data-block"><p>Žádné Twitter profily nejsou evidovány.</p></div>';

    let html = '';

    data.forEach((profile, index) => {
      html += `
      <div class="data-block">
        <div class="data-title">Twitter profil: ${profile.displayName || profile.username || 'Neurčený název'}</div>

        <div class="data-grid">
          <div class="data-row">
            <div class="data-label">Jméno na profilu:</div>
            <div class="data-value emphasized">${profile.displayName || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Uživatelské jméno:</div>
            <div class="data-value">@${profile.username || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">URL profilu:</div>
            <div class="data-value">${profile.profileUrl || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Počet sledujících:</div>
            <div class="data-value">${profile.followersCount || 'Neznámý'}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Sleduje účtů:</div>
            <div class="data-value">${profile.followingCount || 'Neznámý'}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Počet tweetů:</div>
            <div class="data-value">${profile.tweetsCount || 'Neznámý'}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Ověřený účet:</div>
            <div class="data-value">${profile.isVerified ? 'Ano' : 'Ne'}</div>
          </div>
        </div>

        ${profile.bio ? `
        <div class="note-block">
          <div class="note-title">Bio:</div>
          <div class="note-text">${profile.bio}</div>
        </div>
        ` : ''}

        ${this.generateTwitterTweetsPDF(profile.tweets || [])}
        ${this.generatePhotosPDF(profile.photos || [], 'Fotografie z profilu')}
      </div>
      `;
    });

    return html;
  }

  private generateTwitterTweetsPDF(tweets: any[]): string {
    if (!tweets || tweets.length === 0) return '';

    return `
    <div class="sub-block">
      <div class="sub-title">Tweety (${tweets.length})</div>
      <div class="data-list">
        ${tweets.map((tweet: any) => `
        <div class="data-item">
          <div class="item-primary">${tweet.date ? `Datum: ${tweet.date}` : 'Datum neznámé'}</div>
          <div class="item-text">${tweet.text || ''}</div>
          ${tweet.retweetsCount ? `<div class="item-secondary">Retweety: ${tweet.retweetsCount}</div>` : ''}
          ${tweet.likesCount ? `<div class="item-secondary">Lajky: ${tweet.likesCount}</div>` : ''}
        </div>
        `).join('')}
      </div>
    </div>`;
  }

  private generateLinkedInPDF(data: any[]): string {
    if (!data || data.length === 0) return '<div class="data-block"><p>Žádné LinkedIn profily nejsou evidovány.</p></div>';

    let html = '';

    data.forEach((profile, index) => {
      html += `
      <div class="data-block">
        <div class="data-title">LinkedIn profil: ${profile.fullName || profile.headline || 'Neurčený název'}</div>

        <div class="data-grid">
          <div class="data-row">
            <div class="data-label">Celé jméno:</div>
            <div class="data-value emphasized">${profile.fullName || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Pozice:</div>
            <div class="data-value">${profile.headline || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">URL profilu:</div>
            <div class="data-value">${profile.profileUrl || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Lokace:</div>
            <div class="data-value">${profile.location || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Počet spojení:</div>
            <div class="data-value">${profile.connectionsCount || 'Neznámý'}</div>
          </div>
        </div>

        ${profile.summary ? `
        <div class="note-block">
          <div class="note-title">Shrnutí:</div>
          <div class="note-text">${profile.summary}</div>
        </div>
        ` : ''}

        ${profile.experiences && profile.experiences.length > 0 ? `
        <div class="sub-block">
          <div class="sub-title">Pracovní zkušenosti (${profile.experiences.length})</div>
          <div class="data-list">
            ${profile.experiences.map((exp: any) => `
            <div class="data-item">
              <div class="item-primary">${exp.position || ''}</div>
              <div class="item-secondary">${exp.company || ''}</div>
              <div class="item-status">${exp.duration || ''}</div>
              ${exp.description ? `<div class="item-text">${exp.description}</div>` : ''}
            </div>
            `).join('')}
          </div>
        </div>
        ` : ''}

        ${profile.education && profile.education.length > 0 ? `
        <div class="sub-block">
          <div class="sub-title">Vzdělání (${profile.education.length})</div>
          <div class="data-list">
            ${profile.education.map((edu: any) => `
            <div class="data-item">
              <div class="item-primary">${edu.school || ''}</div>
              <div class="item-secondary">${edu.degree || ''}</div>
              <div class="item-status">${edu.duration || ''}</div>
            </div>
            `).join('')}
          </div>
        </div>
        ` : ''}

        ${this.generatePhotosPDF(profile.photos || [], 'Fotografie z profilu')}
      </div>
      `;
    });

    return html;
  }

  private generatePhoneAnalysisPDF(data: any): string {
    if (!data) return '<div class="data-block"><p>Žádné informace z analýzy telefonu nejsou k dispozici.</p></div>';

    return `
    <div class="data-block">
      <div class="data-title">Analýza telefonu</div>

      ${data.deviceInfo ? `
      <div class="sub-block">
        <div class="sub-title">Informace o zařízení</div>
        <div class="data-grid">
          ${data.deviceInfo.brand ? `
          <div class="data-row">
            <div class="data-label">Značka:</div>
            <div class="data-value">${data.deviceInfo.brand}</div>
          </div>
          ` : ''}
          ${data.deviceInfo.model ? `
          <div class="data-row">
            <div class="data-label">Model:</div>
            <div class="data-value">${data.deviceInfo.model}</div>
          </div>
          ` : ''}
          ${data.deviceInfo.imei ? `
          <div class="data-row">
            <div class="data-label">IMEI:</div>
            <div class="data-value">${data.deviceInfo.imei}</div>
          </div>
          ` : ''}
          ${data.deviceInfo.phoneNumber ? `
          <div class="data-row">
            <div class="data-label">Telefonní číslo:</div>
            <div class="data-value">${data.deviceInfo.phoneNumber}</div>
          </div>
          ` : ''}
        </div>
      </div>
      ` : ''}

      ${data.contacts && data.contacts.length > 0 ? `
      <div class="sub-block">
        <div class="sub-title">Kontakty (${data.contacts.length})</div>
        <div class="data-list">
          ${data.contacts.map((contact: any) => `
          <div class="data-item">
            <div class="item-primary">${contact.name || 'Bez jména'}</div>
            <div class="item-secondary">${contact.phoneNumber || ''}</div>
            <div class="item-status">${contact.type || 'Kontakt'}</div>
          </div>
          `).join('')}
        </div>
      </div>
      ` : ''}

      ${data.messages && data.messages.length > 0 ? `
      <div class="sub-block">
        <div class="sub-title">Zprávy (${data.messages.length})</div>
        <div class="data-list">
          ${data.messages.map((message: any) => `
          <div class="data-item">
            <div class="item-primary">${message.date || 'Neznámé datum'}</div>
            <div class="item-secondary">${message.sender || ''} → ${message.recipient || ''}</div>
            <div class="item-text">${message.content || ''}</div>
          </div>
          `).join('')}
        </div>
      </div>
      ` : ''}

      ${this.generatePhotosPDF(data.photos || [], 'Fotodokumentace analýzy')}

      ${data.notes ? `
      <div class="note-block">
        <div class="note-title">Poznámky:</div>
        <div class="note-text">${data.notes}</div>
      </div>
      ` : ''}
    </div>`;
  }

  private generateFinancialMonitoringPDF(data: any): string {
    if (!data) return '<div class="data-block"><p>Žádné informace z finančního monitoringu nejsou k dispozici.</p></div>';

    return `
    <div class="data-block">
      <div class="data-title">Finanční monitoring</div>

      ${data.registryRecords && data.registryRecords.length > 0 ? `
      <div class="sub-block">
        <div class="sub-title">Záznamy v registrech (${data.registryRecords.length})</div>
        <div class="data-list">
          ${data.registryRecords.map((record: any) => `
          <div class="data-item">
            <div class="item-primary">${record.registryType || 'Neznámý registr'}</div>
            <div class="item-secondary">${record.description || ''}</div>
            <div class="item-status">${record.status || 'Aktivní'}</div>
            ${record.amount ? `<div class="item-text">Částka: ${record.amount}</div>` : ''}
          </div>
          `).join('')}
        </div>
      </div>
      ` : ''}

      ${data.bankAccounts && data.bankAccounts.length > 0 ? `
      <div class="sub-block">
        <div class="sub-title">Bankovní účty (${data.bankAccounts.length})</div>
        <div class="data-list">
          ${data.bankAccounts.map((account: any) => `
          <div class="data-item">
            <div class="item-primary">${account.bankName || 'Neznámá banka'}</div>
            <div class="item-secondary">${account.accountNumber || ''}</div>
            <div class="item-status">${account.status || 'Aktivní'}</div>
          </div>
          `).join('')}
        </div>
      </div>
      ` : ''}

      ${data.transactions && data.transactions.length > 0 ? `
      <div class="sub-block">
        <div class="sub-title">Transakce (${data.transactions.length})</div>
        <div class="data-list">
          ${data.transactions.map((transaction: any) => `
          <div class="data-item">
            <div class="item-primary">${transaction.date || 'Neznámé datum'}</div>
            <div class="item-secondary">${transaction.description || ''}</div>
            <div class="item-status">${transaction.amount || ''}</div>
          </div>
          `).join('')}
        </div>
      </div>
      ` : ''}

      ${this.generatePhotosPDF(data.photos || [], 'Fotodokumentace finančního monitoringu')}

      ${data.notes ? `
      <div class="note-block">
        <div class="note-title">Poznámky:</div>
        <div class="note-text">${data.notes}</div>
      </div>
      ` : ''}
    </div>`;
  }
}
