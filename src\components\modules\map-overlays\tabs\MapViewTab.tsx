"use client";

import { useEffect, useRef, useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { UseFormReturn } from "react-hook-form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MapOverlaysModuleFormValues, mapBasemapTypes } from "../schemas";
import { Download, Share2, Printer, Maximize2, Square, Route } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import dynamic from "next/dynamic";

// Dynamicky importujeme mapu, aby se vyhnula problémům s SSR
const DynamicMap = dynamic(() => import("../components/LeafletMap").catch(err => {
  console.error("Chyba při importu LeafletMap:", err);
  return () => <div>Nepodařilo se načíst mapu</div>;
}), {
  ssr: false,
  loading: () => (
    <div className="w-full h-[500px] flex items-center justify-center bg-muted rounded-md">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
    </div>
  ),
});

interface MapViewTabProps {
  form: UseFormReturn<MapOverlaysModuleFormValues>;
}

export function MapViewTab({ form }: MapViewTabProps) {
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [visibleLayers, setVisibleLayers] = useState<Record<string, boolean>>({
    points: true,
    areas: true,
    routes: true,
    heatmap: false,
    clusters: false,
    external: true,
  });
  // Unikátní klíč pro mapu, který se změní při každém renderování
  const [mapKey, setMapKey] = useState(`map-${Date.now()}`);
  // Vybrané prvky na mapě
  const [selectedPointId, setSelectedPointId] = useState<string | null>(null);
  const [selectedAreaId, setSelectedAreaId] = useState<string | null>(null);
  const [selectedRouteId, setSelectedRouteId] = useState<string | null>(null);

  // Režim kreslení na mapě
  const [drawingMode, setDrawingMode] = useState<"point" | "area" | "route" | null>(null);

  const { watch, setValue } = form;
  const formValues = watch();

  // Aktualizace klíče mapy při změně záložky a poslouchání událostí
  useEffect(() => {
    // Generujeme nový klíč pro mapu při prvním renderování komponenty
    setMapKey(`map-${Date.now()}`);
    console.log("Map key updated:", `map-${Date.now()}`);

    // Poslouchání události pro výběr bodu na mapě
    const handleSelectPointOnMap = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.pointId) {
        setSelectedPointId(customEvent.detail.pointId);
        setSelectedAreaId(null);
        setSelectedRouteId(null);
      }
    };

    // Poslouchání události pro výběr oblasti na mapě
    const handleSelectAreaOnMap = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.areaId) {
        setSelectedAreaId(customEvent.detail.areaId);
        setSelectedPointId(null);
        setSelectedRouteId(null);
      }
    };

    // Poslouchání události pro výběr trasy na mapě
    const handleSelectRouteOnMap = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.routeId) {
        setSelectedRouteId(customEvent.detail.routeId);
        setSelectedPointId(null);
        setSelectedAreaId(null);
      }
    };

    // Poslouchání události pro spuštění kreslení oblasti
    const handleStartDrawingArea = () => {
      setDrawingMode("area");
    };

    // Poslouchání události pro spuštění kreslení trasy
    const handleStartDrawingRoute = () => {
      setDrawingMode("route");
    };

    window.addEventListener('selectPointOnMap', handleSelectPointOnMap);
    window.addEventListener('selectAreaOnMap', handleSelectAreaOnMap);
    window.addEventListener('selectRouteOnMap', handleSelectRouteOnMap);
    window.addEventListener('startDrawingArea', handleStartDrawingArea);
    window.addEventListener('startDrawingRoute', handleStartDrawingRoute);

    return () => {
      window.removeEventListener('selectPointOnMap', handleSelectPointOnMap);
      window.removeEventListener('selectAreaOnMap', handleSelectAreaOnMap);
      window.removeEventListener('selectRouteOnMap', handleSelectRouteOnMap);
      window.removeEventListener('startDrawingArea', handleStartDrawingArea);
      window.removeEventListener('startDrawingRoute', handleStartDrawingRoute);
    };
  }, []);

  // Přepínání fullscreen režimu
  const toggleFullscreen = (e: React.MouseEvent) => {
    // Zastavíme propagaci události, aby se formulář neodeslal
    e.preventDefault();
    e.stopPropagation();

    if (typeof document !== 'undefined') {
      if (!document.fullscreenElement) {
        mapContainerRef.current?.requestFullscreen().catch(err => {
          console.error(`Chyba při přepnutí do režimu celé obrazovky: ${err.message}`);
        });
        setIsFullscreen(true);
      } else {
        document.exitFullscreen().catch(err => {
          console.error(`Chyba při ukončení režimu celé obrazovky: ${err.message}`);
        });
        setIsFullscreen(false);
      }
    }
  };

  // Přepínání viditelnosti vrstev
  const toggleLayer = (layer: string) => {
    // Aktualizujeme stav viditelnosti vrstev
    const newVisibleLayers = {
      ...visibleLayers,
      [layer]: !visibleLayers[layer],
    };
    setVisibleLayers(newVisibleLayers);

    // Aktualizace hodnoty ve formuláři - ale neukládáme automaticky
    const currentLayers = formValues.visibleLayers || [];
    if (visibleLayers[layer]) {
      setValue("visibleLayers", currentLayers.filter(l => l !== layer as any), { shouldDirty: true });
    } else {
      setValue("visibleLayers", [...currentLayers, layer as any], { shouldDirty: true });
    }

    // Aktualizujeme mapu
    setMapKey(`map-${Date.now()}`);
  };

  // Změna podkladové mapy
  const changeBasemap = (basemap: string) => {
    // Aktualizujeme hodnotu ve formuláři - ale neukládáme automaticky
    setValue("defaultBasemap", basemap as any, { shouldDirty: true });

    // Aktualizujeme mapu
    setMapKey(`map-${Date.now()}`);
  };

  // Zastavení propagace události kliknutí
  const handleMapClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  // Funkce pro výběr bodu na mapě
  const handlePointSelect = (pointId: string) => {
    setSelectedPointId(pointId);
    setSelectedAreaId(null);
    setSelectedRouteId(null);

    // Přepneme na záložku s body
    const tabsElement = document.querySelector('[role="tablist"]');
    if (tabsElement) {
      const pointsTabButton = tabsElement.querySelector('[value="points"]') as HTMLButtonElement;
      if (pointsTabButton) {
        pointsTabButton.click();

        // Otevřeme dialog pro úpravu bodu
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('openPointDetail', { detail: { pointId } }));
        }, 100);
      }
    }
  };

  // Funkce pro výběr oblasti na mapě
  const handleAreaSelect = (areaId: string) => {
    setSelectedAreaId(areaId);
    setSelectedPointId(null);
    setSelectedRouteId(null);

    // Přepneme na záložku s oblastmi
    const tabsElement = document.querySelector('[role="tablist"]');
    if (tabsElement) {
      const areasTabButton = tabsElement.querySelector('[value="areas"]') as HTMLButtonElement;
      if (areasTabButton) {
        areasTabButton.click();

        // Otevřeme dialog pro úpravu oblasti
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('openAreaDetail', { detail: { areaId } }));
        }, 100);
      }
    }
  };

  // Funkce pro výběr trasy na mapě
  const handleRouteSelect = (routeId: string) => {
    setSelectedRouteId(routeId);
    setSelectedPointId(null);
    setSelectedAreaId(null);

    // Přepneme na záložku s trasami
    const tabsElement = document.querySelector('[role="tablist"]');
    if (tabsElement) {
      const routesTabButton = tabsElement.querySelector('[value="routes"]') as HTMLButtonElement;
      if (routesTabButton) {
        routesTabButton.click();

        // Otevřeme dialog pro úpravu trasy
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('openRouteDetail', { detail: { routeId } }));
        }, 100);
      }
    }
  };

  // Funkce pro přidání bodu na mapě
  const handleMapPointAdd = (lat: number, lng: number) => {
    console.log("handleMapPointAdd called with coordinates:", lat, lng);

    // Zastavíme propagaci události, aby se formulář neodeslal
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    const points = form.getValues().points || [];
    const newPoint = {
      id: uuidv4(),
      name: `Nový bod ${points.length + 1}`,
      pointType: "general",
      latitude: lat,
      longitude: lng,
      photos: [],
      relatedPoints: [],
      relatedSubjects: [],
    };

    // Přidáme nový bod do formuláře
    setValue("points", [...points, newPoint]);

    // Vybereme nový bod
    setSelectedPointId(newPoint.id);

    // Zobrazíme potvrzení o přidání bodu
    alert(`Bod "${newPoint.name}" byl přidán na mapu na souřadnicích [${lat.toFixed(4)}, ${lng.toFixed(4)}]`);

    // Aktualizujeme mapu
    setMapKey(`map-${Date.now()}`);

    // Přepneme na záložku s body
    const tabsElement = document.querySelector('[role="tablist"]');
    if (tabsElement) {
      const pointsTabButton = tabsElement.querySelector('[value="points"]') as HTMLButtonElement;
      if (pointsTabButton) {
        setTimeout(() => {
          pointsTabButton.click();

          // Otevřeme dialog pro úpravu bodu
          setTimeout(() => {
            window.dispatchEvent(new CustomEvent('openPointDetail', {
              detail: { pointId: newPoint.id }
            }));
          }, 100);
        }, 100);
      }
    }
  };

  // Funkce pro přidání oblasti na mapě
  const handleAreaDraw = (coordinates: Array<{lat: number, lng: number}>) => {
    console.log("handleAreaDraw called with coordinates:", coordinates);

    if (coordinates.length < 3) {
      console.error("Oblast musí mít alespoň 3 body");
      return;
    }

    const areas = form.getValues().areas || [];
    const newArea = {
      id: uuidv4(),
      name: `Nová oblast ${areas.length + 1}`,
      areaType: "general",
      coordinates: coordinates,
      photos: [],
      relatedAreas: [],
      relatedSubjects: [],
    };

    // Přidáme novou oblast do formuláře
    const updatedAreas = [...areas, newArea];
    setValue("areas", updatedAreas);

    // Vybereme novou oblast
    setSelectedAreaId(newArea.id);

    // Zobrazíme potvrzení o přidání oblasti
    console.log(`Oblast "${newArea.name}" byla přidána na mapu s ${coordinates.length} body`);

    // Aktualizujeme mapu - důležité pro zobrazení nové oblasti
    setMapKey(`map-${Date.now()}`);

    // Resetujeme režim kreslení
    setDrawingMode(null);

    // Přepneme na záložku s oblastmi
    const tabsElement = document.querySelector('[role="tablist"]');
    if (tabsElement) {
      const areasTabButton = tabsElement.querySelector('[value="areas"]') as HTMLButtonElement;
      if (areasTabButton) {
        setTimeout(() => {
          areasTabButton.click();

          // Otevřeme dialog pro úpravu oblasti
          setTimeout(() => {
            window.dispatchEvent(new CustomEvent('openAreaDetail', {
              detail: { areaId: newArea.id }
            }));
          }, 100);
        }, 100);
      }
    }

    // Explicitně aktualizujeme formulář, aby se změny projevily
    form.trigger();
  };

  // Funkce pro přidání trasy na mapě
  const handleRouteDraw = (coordinates: Array<{lat: number, lng: number}>) => {
    console.log("handleRouteDraw called with coordinates:", coordinates);

    if (coordinates.length < 2) {
      console.error("Trasa musí mít alespoň 2 body");
      return;
    }

    const routes = form.getValues().routes || [];
    const newRoute = {
      id: uuidv4(),
      name: `Nová trasa ${routes.length + 1}`,
      routeType: "general",
      coordinates: coordinates,
      photos: [],
      relatedRoutes: [],
      relatedSubjects: [],
    };

    // Přidáme novou trasu do formuláře
    const updatedRoutes = [...routes, newRoute];
    setValue("routes", updatedRoutes);

    // Vybereme novou trasu
    setSelectedRouteId(newRoute.id);

    // Zobrazíme potvrzení o přidání trasy
    console.log(`Trasa "${newRoute.name}" byla přidána na mapu s ${coordinates.length} body`);

    // Aktualizujeme mapu - důležité pro zobrazení nové trasy
    setMapKey(`map-${Date.now()}`);

    // Resetujeme režim kreslení
    setDrawingMode(null);

    // Přepneme na záložku s trasami
    const tabsElement = document.querySelector('[role="tablist"]');
    if (tabsElement) {
      const routesTabButton = tabsElement.querySelector('[value="routes"]') as HTMLButtonElement;
      if (routesTabButton) {
        setTimeout(() => {
          routesTabButton.click();

          // Otevřeme dialog pro úpravu trasy
          setTimeout(() => {
            window.dispatchEvent(new CustomEvent('openRouteDetail', {
              detail: { routeId: newRoute.id }
            }));
          }, 100);
        }, 100);
      }
    }

    // Explicitně aktualizujeme formulář, aby se změny projevily
    form.trigger();
  };

  // Exportovat mapu jako obrázek
  const exportMap = () => {
    // Přepneme na záložku s mapou
    const mapContainer = document.querySelector('.leaflet-container');
    if (!mapContainer) {
      alert("Mapa není k dispozici pro export.");
      return;
    }

    try {
      // Použijeme html2canvas pro vytvoření obrázku z mapy
      // Toto je zjednodušená implementace, v reálném prostředí by bylo potřeba použít knihovnu html2canvas
      const mapElement = mapContainer as HTMLElement;

      // Vytvoříme odkaz pro stažení
      const link = document.createElement('a');
      link.download = `mapa-export-${new Date().toISOString().slice(0, 10)}.png`;

      // Otevřeme nové okno s mapou pro tisk
      const newWindow = window.open('', '_blank');
      if (newWindow) {
        newWindow.document.write(`
          <html>
            <head>
              <title>Export mapy</title>
              <style>
                body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
                .map-container { border: 1px solid #ccc; margin-bottom: 20px; }
                .map-image { max-width: 100%; height: auto; }
                .export-info { margin-top: 20px; }
                button { padding: 10px 15px; background: #007bff; color: white; border: none; cursor: pointer; }
              </style>
            </head>
            <body>
              <h1>Export mapy</h1>
              <div class="map-container">
                <img src="${mapContainer.innerHTML}" class="map-image" alt="Exportovaná mapa" />
              </div>
              <div class="export-info">
                <p>Pro uložení mapy jako obrázek klikněte pravým tlačítkem na mapu a zvolte "Uložit obrázek jako..."</p>
                <button onclick="window.print()">Vytisknout mapu</button>
              </div>
            </body>
          </html>
        `);
        newWindow.document.close();
      } else {
        alert("Nepodařilo se otevřít nové okno pro export mapy. Zkontrolujte nastavení blokování vyskakovacích oken.");
      }
    } catch (error) {
      console.error("Chyba při exportu mapy:", error);
      alert("Při exportu mapy došlo k chybě. Zkuste to prosím znovu.");
    }
  };

  // Sdílet mapu
  const shareMap = () => {
    try {
      // Vytvoříme URL pro sdílení
      const url = window.location.href;

      // Pokud je k dispozici Web Share API, použijeme ho
      if (navigator.share) {
        navigator.share({
          title: 'Sdílená mapa',
          text: 'Podívejte se na tuto mapu',
          url: url,
        })
        .then(() => console.log('Mapa byla úspěšně sdílena'))
        .catch((error) => console.log('Chyba při sdílení mapy:', error));
      } else {
        // Jinak zkopírujeme URL do schránky
        navigator.clipboard.writeText(url)
          .then(() => alert("URL mapy byla zkopírována do schránky"))
          .catch(() => {
            // Pokud selže i to, zobrazíme URL v dialogu
            prompt("Zkopírujte tuto URL pro sdílení mapy:", url);
          });
      }
    } catch (error) {
      console.error("Chyba při sdílení mapy:", error);
      alert("Při sdílení mapy došlo k chybě. Zkuste to prosím znovu.");
    }
  };

  // Tisknout mapu
  const printMap = () => {
    try {
      // Vytvoříme nové okno pro tisk
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        alert("Nepodařilo se otevřít okno pro tisk. Zkontrolujte nastavení blokování vyskakovacích oken.");
        return;
      }

      // Získáme mapu
      const mapContainer = document.querySelector('.leaflet-container');
      if (!mapContainer) {
        alert("Mapa není k dispozici pro tisk.");
        return;
      }

      // Připravíme obsah pro tisk
      printWindow.document.write(`
        <html>
          <head>
            <title>Tisk mapy</title>
            <style>
              body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
              .map-container { border: 1px solid #ccc; margin-bottom: 20px; }
              .map-image { max-width: 100%; height: auto; }
              .print-info { margin-top: 20px; }
              @media print {
                .print-info, button { display: none; }
              }
            </style>
          </head>
          <body>
            <h1>Tisk mapy</h1>
            <div class="map-container">
              ${mapContainer.outerHTML}
            </div>
            <div class="print-info">
              <p>Klikněte na tlačítko níže nebo použijte funkci tisku prohlížeče (Ctrl+P / Cmd+P)</p>
              <button onclick="window.print(); return false;">Vytisknout</button>
            </div>
            <script>
              // Automaticky spustíme tisk po načtení stránky
              window.onload = function() {
                setTimeout(function() {
                  window.print();
                }, 1000);
              };
            </script>
          </body>
        </html>
      `);
      printWindow.document.close();
    } catch (error) {
      console.error("Chyba při tisku mapy:", error);
      alert("Při tisku mapy došlo k chybě. Zkuste to prosím znovu.");
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Interaktivní mapa</CardTitle>
              <CardDescription>
                Vizualizace bodů, oblastí a tras na mapě
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="icon" onClick={exportMap}>
                      <Download className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Exportovat mapu</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="icon" onClick={shareMap}>
                      <Share2 className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Sdílet mapu</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="icon" onClick={printMap}>
                      <Printer className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Tisknout mapu</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="outline" size="icon" onClick={toggleFullscreen}>
                      <Maximize2 className="h-4 w-4" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Celá obrazovka</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="md:col-span-3">
              <div
                ref={mapContainerRef}
                className="w-full h-[500px] bg-muted rounded-md relative overflow-hidden"
                onClick={handleMapClick}
              >
                <DynamicMap
                  key={mapKey}
                  formValues={formValues}
                  visibleLayers={visibleLayers}
                  onPointSelect={handlePointSelect}
                  onAreaClick={handleAreaSelect}
                  onRouteClick={handleRouteSelect}
                  onMapClick={handleMapPointAdd}
                  onAreaDraw={handleAreaDraw}
                  onRouteDraw={handleRouteDraw}
                  selectedPointId={selectedPointId}
                  selectedAreaId={selectedAreaId}
                  selectedRouteId={selectedRouteId}
                  drawingMode={drawingMode}
                />

                {/* Statistiky mapy */}
                {/* Nástroje pro kreslení */}
                <div className="absolute top-4 left-4 bg-background/80 p-2 rounded-md z-[1000]">
                  <div className="flex space-x-2">
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant={drawingMode === "point" ? "default" : "outline"}
                            size="icon"
                            onClick={() => {
                              console.log("Přepínám na režim přidávání bodů");
                              setDrawingMode(drawingMode === "point" ? null : "point");
                              // Aktualizujeme mapu
                              setMapKey(`map-${Date.now()}`);
                            }}
                          >
                            <div className="w-2 h-2 rounded-full bg-blue-500 border-2 border-white"></div>
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Přidat bod</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant={drawingMode === "area" ? "default" : "outline"}
                            size="icon"
                            onClick={() => {
                              console.log("Přepínám na režim kreslení oblasti");
                              setDrawingMode(drawingMode === "area" ? null : "area");
                              // Aktualizujeme mapu
                              setMapKey(`map-${Date.now()}`);
                            }}
                          >
                            <Square className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Nakreslit oblast</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>

                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Button
                            variant={drawingMode === "route" ? "default" : "outline"}
                            size="icon"
                            onClick={() => {
                              console.log("Přepínám na režim kreslení trasy");
                              setDrawingMode(drawingMode === "route" ? null : "route");
                              // Aktualizujeme mapu
                              setMapKey(`map-${Date.now()}`);
                            }}
                          >
                            <Route className="h-4 w-4" />
                          </Button>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>Nakreslit trasu</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                </div>

                {/* Statistiky mapy */}
                <div className="absolute bottom-4 left-4 bg-background/80 p-2 rounded-md text-xs z-[1000]">
                  <div className="flex space-x-2">
                    <Badge variant="outline">Body: {formValues.points?.length || 0}</Badge>
                    <Badge variant="outline">Oblasti: {formValues.areas?.length || 0}</Badge>
                    <Badge variant="outline">Trasy: {formValues.routes?.length || 0}</Badge>
                  </div>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className="text-sm font-medium mb-2">Podkladová mapa</h3>
                <Select
                  value={formValues.defaultBasemap || "streets"}
                  onValueChange={changeBasemap}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Vyberte podkladovou mapu" />
                  </SelectTrigger>
                  <SelectContent>
                    {mapBasemapTypes.map((type) => (
                      <SelectItem key={type} value={type}>
                        {type === "streets" ? "Ulice" :
                         type === "satellite" ? "Satelitní" :
                         type === "terrain" ? "Terén" :
                         type === "dark" ? "Tmavá" :
                         type === "hybrid" ? "Hybridní" :
                         type === "topo" ? "Topografická" : type}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <Separator />

              <div>
                <h3 className="text-sm font-medium mb-2">Viditelné vrstvy</h3>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={visibleLayers.points}
                      onCheckedChange={() => toggleLayer("points")}
                      id="points-layer"
                    />
                    <Label htmlFor="points-layer">Body</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={visibleLayers.areas}
                      onCheckedChange={() => toggleLayer("areas")}
                      id="areas-layer"
                    />
                    <Label htmlFor="areas-layer">Oblasti</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={visibleLayers.routes}
                      onCheckedChange={() => toggleLayer("routes")}
                      id="routes-layer"
                    />
                    <Label htmlFor="routes-layer">Trasy</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={visibleLayers.heatmap}
                      onCheckedChange={() => toggleLayer("heatmap")}
                      id="heatmap-layer"
                    />
                    <Label htmlFor="heatmap-layer">Teplotní mapa</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={visibleLayers.clusters}
                      onCheckedChange={() => toggleLayer("clusters")}
                      id="clusters-layer"
                    />
                    <Label htmlFor="clusters-layer">Clustery</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={visibleLayers.external}
                      onCheckedChange={() => toggleLayer("external")}
                      id="external-layer"
                    />
                    <Label htmlFor="external-layer">Externí vrstvy</Label>
                  </div>
                </div>
              </div>

              <Separator />

              <div>
                <h3 className="text-sm font-medium mb-2">Legenda</h3>
                <div className="space-y-1 text-xs">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                    <span>Obecný bod</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 rounded-full bg-red-500"></div>
                    <span>Trestný čin</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 rounded-full bg-green-500"></div>
                    <span>Osoba</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                    <span>Vozidlo</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 rounded-full bg-purple-500"></div>
                    <span>Budova</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
