import { NextRequest, NextResponse } from 'next/server';
import { saveImageToPublicFolder } from '@/lib/file-storage';

export async function POST(request: NextRequest) {
  try {
    const { base64Data, caseId, subjectId, moduleId, fileName } = await request.json();
    
    if (!base64Data || !caseId || !subjectId || !moduleId || !fileName) {
      return NextResponse.json(
        { error: 'Chybí povinné parametry' },
        { status: 400 }
      );
    }
    
    const imagePath = await saveImageToPublicFolder(
      base64Data,
      caseId,
      subjectId,
      moduleId,
      fileName
    );
    
    return NextResponse.json({ imagePath });
    
  } catch (error) {
    console.error('Chyba API upload-image:', error);
    return NextResponse.json(
      { error: 'Nepodařilo se uložit obrázek' },
      { status: 500 }
    );
  }
} 