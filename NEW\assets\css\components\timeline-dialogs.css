/* Styly pro dialogy časové osy */

/* Základní styl dialogu */
.add-event-dialog,
.import-events-dialog,
.email-sent-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.add-event-dialog-content,
.import-events-dialog-content,
.email-sent-dialog-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Hlavička dialogu */
.add-event-dialog-header,
.import-events-dialog-header,
.email-sent-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    background-color: #f9f9f9;
}

.add-event-dialog-header h3,
.import-events-dialog-header h3,
.email-sent-dialog-header h3 {
    margin: 0;
    font-size: 18px;
    color: var(--primary-color);
}

.add-event-dialog-close,
.import-events-dialog-close,
.email-sent-dialog-close {
    background: none;
    border: none;
    font-size: 16px;
    cursor: pointer;
    color: var(--muted-text);
    padding: 5px;
}

.add-event-dialog-close:hover,
.import-events-dialog-close:hover,
.email-sent-dialog-close:hover {
    color: var(--dark-text);
}

/* Tělo dialogu */
.add-event-dialog-body,
.import-events-dialog-body,
.email-sent-dialog-body {
    padding: 20px;
    overflow-y: auto;
    max-height: calc(90vh - 130px);
}

/* Patička dialogu */
.add-event-dialog-footer,
.import-events-dialog-footer,
.email-sent-dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    background-color: #f9f9f9;
}

/* Formulářové prvky */
.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

/* Instrukce pro import */
.import-instructions {
    margin-bottom: 20px;
}

.import-instructions p {
    margin-bottom: 10px;
}

.import-note {
    display: flex;
    align-items: flex-start;
    padding: 10px;
    background-color: #f8f9fa;
    border-left: 4px solid #17a2b8;
    border-radius: 4px;
    margin-bottom: 15px;
}

.import-note i {
    color: #17a2b8;
    font-size: 16px;
    margin-right: 10px;
    margin-top: 2px;
}

.import-note span {
    font-size: 14px;
    color: #495057;
    line-height: 1.4;
}

.import-warning {
    display: flex;
    align-items: flex-start;
    padding: 10px;
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
    border-radius: 4px;
    margin-bottom: 15px;
}

.import-warning i {
    color: #ffc107;
    font-size: 16px;
    margin-right: 10px;
    margin-top: 2px;
}

.import-warning span {
    font-size: 14px;
    color: #856404;
    line-height: 1.4;
}

/* Možnosti importu */
.import-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.import-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.import-option i {
    font-size: 24px;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.import-option span {
    font-size: 14px;
}

.import-option:hover {
    background-color: #f0f0f0;
}

.import-option.active {
    background-color: var(--primary-color-light);
    border-color: var(--primary-color);
}

.import-option.active i {
    color: var(--primary-color-dark);
}

/* Checkboxy pro import */
.import-checkboxes {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
    margin-bottom: 15px;
}

.import-checkboxes label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: normal;
    cursor: pointer;
}

.import-date-range {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.import-date-range input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

/* Dialog pro odeslání emailu */
.email-sent-icon {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
}

.email-sent-icon i {
    font-size: 48px;
    color: #4CAF50;
}

.email-sent-details {
    text-align: center;
    margin-bottom: 20px;
}

.email-address {
    font-weight: bold;
    font-size: 16px;
    margin: 10px 0;
}

.email-sent-note {
    color: var(--muted-text);
    font-size: 14px;
    margin-top: 10px;
}

/* Responzivní design */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 15px;
    }

    .import-options {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }
}
