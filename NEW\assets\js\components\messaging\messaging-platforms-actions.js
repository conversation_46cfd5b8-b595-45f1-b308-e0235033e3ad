/**
 * Monitoring komunikačních platforem - funkce pro akce
 */

/**
 * Aktualizace dat platformy
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 */
function refreshPlatformData(platform) {
    console.log('Aktualizace dat platformy:', platform);
    
    // Zobrazení notifikace o zahájení aktualizace
    showNotification(`Zahajuji aktualizaci dat pro platformu ${getPlatformName(platform)}...`, 'info');
    
    // Získání všech kanálů/serverů/skupin pro danou platformu
    let items = [];
    
    switch (platform) {
        case 'telegram':
            items = messagingPlatformsData.telegram.channels;
            break;
        case 'discord':
            items = messagingPlatformsData.discord.servers;
            break;
        case 'whatsapp':
            items = messagingPlatformsData.whatsapp.groups;
            break;
    }
    
    // Kontrola, zda existují položky k aktualizaci
    if (items.length === 0) {
        showNotification(`Nebyly nalezeny žádné položky k aktualizaci pro platformu ${getPlatformName(platform)}.`, 'warning');
        return;
    }
    
    // Simulace aktualizace dat
    // V reálné aplikaci by zde byl API požadavek na backend
    setTimeout(() => {
        // Aktualizace dat pro každou položku
        items.forEach(item => {
            // Aktualizace času poslední aktualizace
            item.lastUpdate = new Date().toISOString();
            
            // Simulace aktualizace počtu členů
            item.memberCount = Math.floor(Math.random() * 10000) + 100;
            
            // Simulace aktualizace zpráv
            generateSimulatedMessages(platform, item.id, 5);
        });
        
        // Uložení dat
        saveMessagingData();
        
        // Aktualizace UI
        updatePlatformsUI();
        
        // Zobrazení notifikace o dokončení aktualizace
        showNotification(`Data pro platformu ${getPlatformName(platform)} byla úspěšně aktualizována.`, 'success');
    }, 2000);
}

/**
 * Aktualizace dat kanálu/serveru/skupiny
 * @param {string} itemId - ID kanálu/serveru/skupiny
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 */
function refreshChannelData(itemId, platform) {
    console.log('Aktualizace dat kanálu/serveru/skupiny:', itemId, platform);
    
    // Zobrazení notifikace o zahájení aktualizace
    showNotification(`Zahajuji aktualizaci dat pro ${getItemTypeName(platform)}...`, 'info');
    
    // Simulace aktualizace dat
    // V reálné aplikaci by zde byl API požadavek na backend
    setTimeout(() => {
        // Aktualizace stavu kanálu/serveru/skupiny
        updateItemStatus(platform, itemId, 'active');
        
        // Simulace aktualizace počtu členů
        updateItemMemberCount(platform, itemId, Math.floor(Math.random() * 10000) + 100);
        
        // Simulace aktualizace zpráv
        generateSimulatedMessages(platform, itemId, 5);
        
        // Uložení dat
        saveMessagingData();
        
        // Aktualizace UI
        updatePlatformsUI();
        
        // Aktualizace detailu kanálu/serveru/skupiny
        switch (platform) {
            case 'telegram':
                showChannelDetail(itemId, platform);
                break;
            case 'discord':
                showServerDetail(itemId, platform);
                break;
            case 'whatsapp':
                showGroupDetail(itemId, platform);
                break;
        }
        
        // Zobrazení notifikace o dokončení aktualizace
        showNotification(`Data pro ${getItemTypeName(platform)} byla úspěšně aktualizována.`, 'success');
    }, 2000);
}

/**
 * Export dat platformy
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 */
function exportPlatformData(platform) {
    console.log('Export dat platformy:', platform);
    
    // Zobrazení notifikace o zahájení exportu
    showNotification(`Zahajuji export dat pro platformu ${getPlatformName(platform)}...`, 'info');
    
    // Získání dat pro export
    let exportData;
    
    switch (platform) {
        case 'telegram':
            exportData = {
                platform: 'Telegram',
                channels: messagingPlatformsData.telegram.channels,
                messages: messagingPlatformsData.telegram.messages,
                users: messagingPlatformsData.telegram.users,
                exportDate: new Date().toISOString()
            };
            break;
        case 'discord':
            exportData = {
                platform: 'Discord',
                servers: messagingPlatformsData.discord.servers,
                messages: messagingPlatformsData.discord.messages,
                users: messagingPlatformsData.discord.users,
                exportDate: new Date().toISOString()
            };
            break;
        case 'whatsapp':
            exportData = {
                platform: 'WhatsApp',
                groups: messagingPlatformsData.whatsapp.groups,
                messages: messagingPlatformsData.whatsapp.messages,
                users: messagingPlatformsData.whatsapp.users,
                exportDate: new Date().toISOString()
            };
            break;
        default:
            showNotification(`Neznámá platforma: ${platform}`, 'error');
            return;
    }
    
    // Vytvoření CSV dat
    const csvData = convertToCSV(exportData);
    
    // Vytvoření Blob a odkazu pro stažení
    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    
    // Nastavení atributů odkazu
    link.setAttribute('href', url);
    link.setAttribute('download', `${platform}_export_${formatDateForFilename(new Date())}.csv`);
    link.style.visibility = 'hidden';
    
    // Přidání odkazu do stránky a kliknutí na něj
    document.body.appendChild(link);
    link.click();
    
    // Odstranění odkazu
    document.body.removeChild(link);
    
    // Zobrazení notifikace o dokončení exportu
    showNotification(`Data pro platformu ${getPlatformName(platform)} byla úspěšně exportována.`, 'success');
}

/**
 * Export dat kanálu/serveru/skupiny
 * @param {string} itemId - ID kanálu/serveru/skupiny
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 */
function exportChannelData(itemId, platform) {
    console.log('Export dat kanálu/serveru/skupiny:', itemId, platform);
    
    // Zobrazení notifikace o zahájení exportu
    showNotification(`Zahajuji export dat pro ${getItemTypeName(platform)}...`, 'info');
    
    // Získání dat pro export
    let item, messages, users;
    
    switch (platform) {
        case 'telegram':
            item = messagingPlatformsData.telegram.channels.find(c => c.id === itemId);
            messages = messagingPlatformsData.telegram.messages[itemId] || [];
            users = messagingPlatformsData.telegram.users;
            break;
        case 'discord':
            item = messagingPlatformsData.discord.servers.find(s => s.id === itemId);
            messages = messagingPlatformsData.discord.messages[itemId] || [];
            users = messagingPlatformsData.discord.users;
            break;
        case 'whatsapp':
            item = messagingPlatformsData.whatsapp.groups.find(g => g.id === itemId);
            messages = messagingPlatformsData.whatsapp.messages[itemId] || [];
            users = messagingPlatformsData.whatsapp.users;
            break;
        default:
            showNotification(`Neznámá platforma: ${platform}`, 'error');
            return;
    }
    
    if (!item) {
        showNotification(`Položka nebyla nalezena: ${itemId}`, 'error');
        return;
    }
    
    // Vytvoření dat pro export
    const exportData = {
        platform: getPlatformName(platform),
        item: item,
        messages: messages,
        users: Object.values(users).filter(user => messages.some(message => message.userId === user.id)),
        exportDate: new Date().toISOString()
    };
    
    // Vytvoření CSV dat
    const csvData = convertToCSV(exportData);
    
    // Vytvoření Blob a odkazu pro stažení
    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    
    // Nastavení atributů odkazu
    link.setAttribute('href', url);
    link.setAttribute('download', `${platform}_${item.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_${formatDateForFilename(new Date())}.csv`);
    link.style.visibility = 'hidden';
    
    // Přidání odkazu do stránky a kliknutí na něj
    document.body.appendChild(link);
    link.click();
    
    // Odstranění odkazu
    document.body.removeChild(link);
    
    // Zobrazení notifikace o dokončení exportu
    showNotification(`Data pro ${getItemTypeName(platform)} byla úspěšně exportována.`, 'success');
}

/**
 * Export zprávy
 * @param {string} messageId - ID zprávy
 * @param {string} itemId - ID kanálu/serveru/skupiny
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 */
function exportMessage(messageId, itemId, platform) {
    console.log('Export zprávy:', messageId, itemId, platform);
    
    // Získání zprávy a uživatele
    let message, user;
    
    switch (platform) {
        case 'telegram':
            message = (messagingPlatformsData.telegram.messages[itemId] || []).find(m => m.id === messageId);
            if (message) {
                user = messagingPlatformsData.telegram.users[message.userId];
            }
            break;
        case 'discord':
            message = (messagingPlatformsData.discord.messages[itemId] || []).find(m => m.id === messageId);
            if (message) {
                user = messagingPlatformsData.discord.users[message.userId];
            }
            break;
        case 'whatsapp':
            message = (messagingPlatformsData.whatsapp.messages[itemId] || []).find(m => m.id === messageId);
            if (message) {
                user = messagingPlatformsData.whatsapp.users[message.userId];
            }
            break;
        default:
            showNotification(`Neznámá platforma: ${platform}`, 'error');
            return;
    }
    
    if (!message) {
        showNotification('Zpráva nebyla nalezena.', 'error');
        return;
    }
    
    // Vytvoření dat pro export
    const exportData = {
        platform: getPlatformName(platform),
        message: message,
        user: user || { displayName: 'Neznámý uživatel', username: 'unknown' },
        exportDate: new Date().toISOString()
    };
    
    // Vytvoření CSV dat
    const csvData = convertToCSV(exportData);
    
    // Vytvoření Blob a odkazu pro stažení
    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    
    // Nastavení atributů odkazu
    link.setAttribute('href', url);
    link.setAttribute('download', `${platform}_message_${messageId.substring(0, 8)}_${formatDateForFilename(new Date())}.csv`);
    link.style.visibility = 'hidden';
    
    // Přidání odkazu do stránky a kliknutí na něj
    document.body.appendChild(link);
    link.click();
    
    // Odstranění odkazu
    document.body.removeChild(link);
    
    // Zobrazení notifikace o dokončení exportu
    showNotification('Zpráva byla úspěšně exportována.', 'success');
}

/**
 * Odstranění kanálu/serveru/skupiny
 * @param {string} itemId - ID kanálu/serveru/skupiny
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 */
function removeChannel(itemId, platform) {
    console.log('Odstranění kanálu/serveru/skupiny:', itemId, platform);
    
    // Potvrzení odstranění
    if (!confirm(`Opravdu chcete odstranit tento ${getItemTypeName(platform).toLowerCase()} z monitoringu?`)) {
        return;
    }
    
    // Odstranění položky podle platformy
    switch (platform) {
        case 'telegram':
            messagingPlatformsData.telegram.channels = messagingPlatformsData.telegram.channels.filter(c => c.id !== itemId);
            delete messagingPlatformsData.telegram.messages[itemId];
            break;
        case 'discord':
            messagingPlatformsData.discord.servers = messagingPlatformsData.discord.servers.filter(s => s.id !== itemId);
            delete messagingPlatformsData.discord.messages[itemId];
            break;
        case 'whatsapp':
            messagingPlatformsData.whatsapp.groups = messagingPlatformsData.whatsapp.groups.filter(g => g.id !== itemId);
            delete messagingPlatformsData.whatsapp.messages[itemId];
            break;
        default:
            showNotification(`Neznámá platforma: ${platform}`, 'error');
            return;
    }
    
    // Uložení dat
    saveMessagingData();
    
    // Aktualizace UI
    updatePlatformsUI();
    
    // Zobrazení notifikace
    showNotification(`${getItemTypeName(platform)} byl(a) úspěšně odstraněn(a) z monitoringu.`, 'success');
}

/**
 * Přidání zprávy do monitoringu
 * @param {string} messageId - ID zprávy
 * @param {string} itemId - ID kanálu/serveru/skupiny
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 */
function addMessageToMonitoring(messageId, itemId, platform) {
    console.log('Přidání zprávy do monitoringu:', messageId, itemId, platform);
    
    // Získání zprávy
    let message;
    
    switch (platform) {
        case 'telegram':
            message = (messagingPlatformsData.telegram.messages[itemId] || []).find(m => m.id === messageId);
            break;
        case 'discord':
            message = (messagingPlatformsData.discord.messages[itemId] || []).find(m => m.id === messageId);
            break;
        case 'whatsapp':
            message = (messagingPlatformsData.whatsapp.messages[itemId] || []).find(m => m.id === messageId);
            break;
        default:
            showNotification(`Neznámá platforma: ${platform}`, 'error');
            return;
    }
    
    if (!message) {
        showNotification('Zpráva nebyla nalezena.', 'error');
        return;
    }
    
    // Simulace přidání zprávy do monitoringu
    // V reálné aplikaci by zde byl API požadavek na backend
    
    // Zobrazení notifikace
    showNotification('Zpráva byla úspěšně přidána do monitoringu.', 'success');
}

/**
 * Přidání uživatele do monitoringu
 * @param {string} userId - ID uživatele
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 */
function addUserToMonitoring(userId, platform) {
    console.log('Přidání uživatele do monitoringu:', userId, platform);
    
    // Získání uživatele
    let user;
    
    switch (platform) {
        case 'telegram':
            user = messagingPlatformsData.telegram.users[userId];
            break;
        case 'discord':
            user = messagingPlatformsData.discord.users[userId];
            break;
        case 'whatsapp':
            user = messagingPlatformsData.whatsapp.users[userId];
            break;
        default:
            showNotification(`Neznámá platforma: ${platform}`, 'error');
            return;
    }
    
    if (!user) {
        showNotification('Uživatel nebyl nalezen.', 'error');
        return;
    }
    
    // Simulace přidání uživatele do monitoringu
    // V reálné aplikaci by zde byl API požadavek na backend
    
    // Zobrazení notifikace
    showNotification(`Uživatel ${user.displayName} byl úspěšně přidán do monitoringu.`, 'success');
}

/**
 * Zobrazení zpráv uživatele
 * @param {string} userId - ID uživatele
 * @param {string} itemId - ID kanálu/serveru/skupiny
 * @param {string} platform - Název platformy (telegram, discord, whatsapp)
 */
function viewUserMessages(userId, itemId, platform) {
    console.log('Zobrazení zpráv uživatele:', userId, itemId, platform);
    
    // Získání uživatele a jeho zpráv
    let user, messages;
    
    switch (platform) {
        case 'telegram':
            user = messagingPlatformsData.telegram.users[userId];
            messages = (messagingPlatformsData.telegram.messages[itemId] || []).filter(m => m.userId === userId);
            break;
        case 'discord':
            user = messagingPlatformsData.discord.users[userId];
            messages = (messagingPlatformsData.discord.messages[itemId] || []).filter(m => m.userId === userId);
            break;
        case 'whatsapp':
            user = messagingPlatformsData.whatsapp.users[userId];
            messages = (messagingPlatformsData.whatsapp.messages[itemId] || []).filter(m => m.userId === userId);
            break;
        default:
            showNotification(`Neznámá platforma: ${platform}`, 'error');
            return;
    }
    
    if (!user) {
        showNotification('Uživatel nebyl nalezen.', 'error');
        return;
    }
    
    if (messages.length === 0) {
        showNotification('Uživatel nemá žádné zprávy.', 'warning');
        return;
    }
    
    // Vytvoření dialogu pro zobrazení zpráv uživatele
    const dialog = document.createElement('div');
    dialog.className = 'user-messages-dialog';
    
    // Vytvoření obsahu dialogu
    dialog.innerHTML = `
        <div class="user-messages-dialog-content">
            <div class="user-messages-dialog-header">
                <h3>Zprávy uživatele ${user.displayName}</h3>
                <button type="button" class="user-messages-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="user-messages-dialog-body">
                <div class="user-info">
                    <div class="user-avatar">
                        ${user.avatarUrl ? `<img src="${user.avatarUrl}" alt="${user.displayName}">` : user.displayName.charAt(0)}
                    </div>
                    <div class="user-details">
                        <div class="user-name">${user.displayName}</div>
                        <div class="user-username">@${user.username}</div>
                        <div class="user-platform">${getPlatformName(platform)}</div>
                    </div>
                </div>
                <div class="user-messages-list">
                    ${messages.map(message => `
                        <div class="user-message-item">
                            <div class="user-message-time">${formatDate(message.timestamp)}</div>
                            <div class="user-message-content">${message.content}</div>
                            ${message.attachments && message.attachments.length > 0 ? `
                                <div class="user-message-attachments">
                                    ${message.attachments.map(attachment => {
                                        if (attachment.type === 'image') {
                                            return `
                                                <div class="user-message-attachment">
                                                    <img src="${attachment.url}" alt="Příloha">
                                                </div>
                                            `;
                                        } else {
                                            return `
                                                <div class="user-message-attachment document">
                                                    <i class="fas fa-file-alt"></i>
                                                    <span>${attachment.name}</span>
                                                </div>
                                            `;
                                        }
                                    }).join('')}
                                </div>
                            ` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
            <div class="user-messages-dialog-footer">
                <button type="button" class="btn-primary user-messages-dialog-close-btn">Zavřít</button>
            </div>
        </div>
    `;
    
    // Přidání dialogu do stránky
    document.body.appendChild(dialog);
    
    // Přidání event listenerů pro zavření dialogu
    dialog.querySelector('.user-messages-dialog-close').addEventListener('click', () => dialog.remove());
    dialog.querySelector('.user-messages-dialog-close-btn').addEventListener('click', () => dialog.remove());
    
    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}
