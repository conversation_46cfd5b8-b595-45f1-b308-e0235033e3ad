/* OSINT dokument styly */
:root {
    --primary-color: #1a3c89;
    --secondary-color: #e74c3c;
    --light-bg: #f5f7fa;
    --dark-text: #333;
    --light-text: #777;
    --border-color: #ddd;
    --highlight: #3498db;
    --success: #27ae60;

    /* Nové barvy pro lepší vizuální hierarchii */
    --primary-light: rgba(26, 60, 137, 0.1);
    --primary-medium: rgba(26, 60, 137, 0.3);
    --secondary-light: rgba(231, 76, 60, 0.1);
    --hover-bg: #f1f3f5;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --card-shadow-hover: 0 6px 12px rgba(0, 0, 0, 0.15);
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: var(--dark-text);
    background-color: #f0f2f5;
    padding: 0;
    margin: 0;
}

.pdf-toolbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: var(--primary-color);
    color: white;
    padding: 10px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 100;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.pdf-toolbar h1 {
    font-size: 18px;
    margin: 0;
}

.pdf-toolbar-actions {
    display: flex;
    gap: 10px;
}

.pdf-button {
    background-color: white;
    color: var(--primary-color);
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.2s ease;
}

.pdf-button:hover {
    background-color: #f5f5f5;
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.pdf-button i {
    font-size: 16px;
}

.document-container {
    padding-top: 60px;
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 280px;
    background-color: white;
    height: calc(100vh - 60px);
    position: fixed;
    left: 0;
    top: 60px;
    padding: 20px;
    overflow-y: auto;
    box-shadow: 2px 0 5px rgba(0,0,0,0.05);
}

.sidebar-title {
    font-size: 18px;
    color: var(--primary-color);
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.toc-list {
    list-style: none;
}

.toc-list li {
    margin-bottom: 10px;
}

.toc-section {
    font-weight: bold;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 0;
    cursor: pointer;
}

.toc-section i {
    transition: transform 0.3s ease;
}

.toc-section.expanded i {
    transform: rotate(90deg);
}

.toc-modules {
    padding-left: 25px;
    margin-top: 5px;
    display: none;
}

.toc-modules.expanded {
    display: block;
}

.toc-module {
    color: var(--dark-text);
    padding: 5px 0;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.toc-module:hover {
    color: var(--primary-color);
}

.toc-module i {
    font-size: 14px;
    opacity: 0.7;
}

.content-wrapper {
    margin-left: 280px;
    flex: 1;
    padding: 20px;
    max-width: 210mm;
    margin-right: auto;
    margin-left: 300px;
}

@media (min-width: 1500px) {
    .content-wrapper {
        margin-left: 350px;
    }
}

@media (max-width: 1200px) {
    .content-wrapper {
        margin-left: 290px;
    }
}

.page {
    width: 100%;
    min-height: auto;
    margin: 0 auto 40px;
    background: white;
    position: relative;
    page-break-after: always;
    padding: 20mm;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    border-radius: 5px;
}

.page:first-of-type {
    min-height: 297mm;
}

.continuous-page {
    width: 100%;
    background: white;
    position: relative;
    padding: 20px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    border-radius: 5px;
    margin-bottom: 40px;
}

.continuous-page .section-title {
    padding-top: 20px;
}

/* První stránka */
.header {
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 15px;
    margin-bottom: 30px;
}

.police-header {
    text-transform: uppercase;
    color: var(--primary-color);
    font-weight: bold;
    font-size: 14px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    width: 100%;
}

.police-left {
    flex: 1;
}

.police-right {
    text-align: right;
    font-weight: normal;
    color: var(--dark-text);
}

#document-jid {
    min-width: 100px;
    display: inline-block;
    font-weight: normal;
}

#document-date {
    min-width: 120px;
    display: inline-block;
}

.department-info {
    font-size: 12px;
    color: var(--light-text);
    margin-bottom: 10px;
}

.document-info {
    display: flex;
    justify-content: space-between;
    margin-top: 40px;
    font-size: 12px;
}

.title-section {
    text-align: center;
    margin-top: 80px;
    margin-bottom: 60px;
}

.document-title {
    font-size: 24px;
    font-weight: bold;
    color: black;
    margin-bottom: 40px;
}

.intel-word {
    color: black;
    text-decoration: none;
}

.subject-name {
    font-size: 20px;
    margin-top: 20px;
}

.protocol-section {
    margin-top: 60px;
}

.protocol-title {
    font-weight: bold;
    margin-bottom: 10px;
}

.protocol-text {
    margin-bottom: 20px;
    text-align: justify;
}

.warning-section {
    margin-top: 30px;
}

.warning-title {
    font-weight: bold;
    margin-bottom: 10px;
}

ul {
    padding-left: 20px;
    margin-top: 10px;
}

li {
    margin-bottom: 8px;
}

/* Druhá stránka - obsah */
.page-number {
    position: absolute;
    top: 15mm;
    right: 20mm;
    font-size: 12px;
    color: var(--light-text);
}

.content-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.toc {
    margin-top: 20px;
}

.toc-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    border-bottom: 1px dotted var(--border-color);
}

.toc-item.level-1 {
    font-weight: bold;
    margin-top: 15px;
}

.toc-item.level-2 {
    padding-left: 20px;
}

.toc-item.level-3 {
    padding-left: 40px;
    font-size: 0.9em;
}

.toc-dots {
    flex: 1;
    margin: 0 5px;
    border-bottom: 1px dotted var(--border-color);
}

/* Moduly a sekce */
.section-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 8px;
}

.section-number {
    background-color: var(--primary-color);
    color: white;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 10px;
}

.section-title h2 {
    font-size: 20px;
    margin: 0;
}

.section-content {
    margin-bottom: 30px;
}

.module {
    background-color: white;
    border-radius: 10px;
    box-shadow: var(--card-shadow);
    margin-bottom: 25px;
    overflow: hidden;
    position: relative;
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.module:hover {
    box-shadow: var(--card-shadow-hover);
    transform: translateY(-2px);
}

.module-header {
    background-color: var(--primary-color);
    padding: 12px 15px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.module-actions {
    position: absolute;
    top: 8px;
    right: 10px;
    display: flex;
    gap: 8px;
    z-index: 5;
}

.module-action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s;
    backdrop-filter: blur(2px);
}

.module-action-btn:hover {
    background-color: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.module-action-btn:active {
    transform: scale(0.95);
}

.module-action-btn.delete:hover {
    background-color: var(--secondary-color);
    color: white;
}

.module-icon {
    width: 24px;
    margin-right: 12px;
    color: white;
    opacity: 0.9;
}

.module-header h3 {
    font-size: 16px;
    margin: 0;
    color: white;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.module-content {
    padding: 20px;
    background-color: white;
}

/* Formuláře a datové prvky */
.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.mt-1 {
    margin-top: 5px;
}

.mt-2 {
    margin-top: 10px;
}

.mt-3 {
    margin-top: 15px;
}

.form-group {
    flex: 1;
    position: relative;
}

.form-group.long {
    flex: 2;
}

label {
    display: block;
    font-size: 12px;
    color: var(--light-text);
    margin-bottom: 5px;
}

.form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #fff;
}

.form-control:hover {
    border-color: #b0b0b0;
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(26, 60, 137, 0.15);
}

textarea.form-control {
    min-height: 100px;
    resize: vertical;
    line-height: 1.5;
}

select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23777' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
    padding-right: 30px;
}

/* Přidávání nových modulů */
.add-module-container {
    margin-bottom: 30px;
}

.add-module-btn, .add-btn {
    background-color: var(--primary-light);
    border: 2px dashed var(--primary-color);
    width: 100%;
    padding: 15px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    cursor: pointer;
    color: var(--primary-color);
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: 15px;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.add-module-btn:hover, .add-btn:hover {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--card-shadow);
}

.add-module-btn:active, .add-btn:active {
    transform: translateY(0);
}

.add-module-btn i, .add-btn i {
    font-size: 22px;
    transition: transform 0.3s ease;
}

.add-module-btn:hover i, .add-btn:hover i {
    transform: rotate(90deg);
}

.add-module-btn::after, .add-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

.add-module-btn:focus:not(:active)::after, .add-btn:focus:not(:active)::after {
    animation: ripple 1s ease-out;
}

/* Tlačítka pro odstranění položek */
.btn-outline-danger {
    background-color: transparent;
    color: var(--secondary-color);
    border: 1px solid var(--secondary-color);
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-outline-danger:hover {
    background-color: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(231, 76, 60, 0.2);
}

.btn-outline-danger:active {
    transform: translateY(0);
}

.btn-outline-danger:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.3);
}

.remove-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 38px;
    height: 38px;
}

.remove-item i {
    transition: transform 0.3s ease;
}

.remove-item:hover i {
    transform: rotate(90deg);
}

/* Modul výběr */
.module-picker {
    display: none;
    background-color: white;
    border-radius: 12px;
    box-shadow: var(--card-shadow-hover);
    margin-top: 15px;
    padding: 20px;
    border: 1px solid var(--border-color);
    animation: fadeIn 0.3s ease-out;
}

.module-picker.active {
    display: block;
}

.module-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.module-option {
    background-color: var(--light-bg);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 18px 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.module-option:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: var(--card-shadow);
    border-color: var(--primary-color);
}

.module-option:active {
    transform: translateY(-1px);
}

.module-option i {
    font-size: 28px;
    margin-bottom: 12px;
    display: block;
    transition: transform 0.3s ease;
}

.module-option:hover i {
    transform: scale(1.1);
}

.module-option::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
    pointer-events: none;
}

/* Předpřipravené moduly */
.social-profile {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.profile-image {
    width: 100px;
    height: 100px;
    border-radius: 6px;
    overflow: hidden;
    background-color: var(--light-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--light-text);
    font-size: 40px;
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-details {
    flex: 1;
}

.profile-name {
    font-weight: bold;
    font-size: 18px;
    margin-bottom: 5px;
}

.profile-username {
    color: var(--light-text);
    margin-bottom: 10px;
}

.photo-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 10px;
    margin-top: 15px;
}

.photo-item {
    height: 100px;
    border-radius: 6px;
    overflow: hidden;
    background-color: var(--light-bg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--light-text);
}

.photo-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-upload {
    border: 2px dashed var(--border-color);
    height: 100px;
    border-radius: 6px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--light-text);
    cursor: pointer;
    transition: all 0.2s;
}

.photo-upload:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    background-color: rgba(26, 60, 137, 0.05);
}

/* Editable content */
[contenteditable] {
    border: 1px solid transparent;
    border-radius: 4px;
    padding: 5px;
    min-height: 20px;
    transition: all 0.2s;
}

[contenteditable]:hover {
    border-color: var(--border-color);
    background-color: rgba(0,0,0,0.02);
}

[contenteditable]:focus {
    outline: none;
    border-color: var(--primary-color);
    background-color: white;
    box-shadow: 0 0 0 2px rgba(26, 60, 137, 0.1);
}

[contenteditable="true"].placeholder:empty:before {
    content: attr(data-placeholder);
    color: var(--light-text);
    font-style: italic;
}

/* Přidané styly pro tisk */
[data-print-only] {
    display: none;
}

.print-addresses,
.print-phones,
.print-documents {
    margin-top: 15px;
    margin-bottom: 15px;
}

.print-addresses h4,
.print-phones h4,
.print-documents h4 {
    font-size: 16px;
    color: var(--primary-color);
    margin-bottom: 10px;
    font-weight: 600;
}

.print-addresses ul,
.print-phones ul,
.print-documents ul {
    margin-left: 20px;
    padding-left: 0;
}

.print-addresses li,
.print-phones li,
.print-documents li {
    margin-bottom: 5px;
}

/* Responzivní nastavení */
@media print {
    body {
        background: white;
    }

    .pdf-toolbar,
    .sidebar {
        display: none !important;
    }

    .content-wrapper {
        margin-left: 0;
        padding: 0;
        max-width: none;
    }

    .page {
        box-shadow: none;
        margin: 0;
        padding: 20mm;
        page-break-after: always;
    }

    .continuous-page {
        box-shadow: none;
        margin: 0;
        padding: 20mm;
        page-break-after: auto;
    }

    /* Skrytí formulářových prvků při tisku */
    .module-actions,
    .add-module-btn,
    .module-picker,
    .form-control,
    label,
    input,
    select,
    textarea,
    .btn,
    .add-btn,
    .remove-item {
        display: none !important;
    }

    [data-print-only] {
        display: block !important;
    }

    /* Výpis pouze výsledků bez editačních prvků */
    .module-content {
        padding: 0;
    }

    .module {
        box-shadow: none;
        border: none;
        page-break-inside: avoid;
    }

    .document-item {
        background-color: transparent;
        border: none;
        box-shadow: none;
        padding: 0;
        page-break-inside: avoid;
    }

    /* Vytvoření pěkného formátovaného výstupu pro tisk */
    .form-group {
        margin-bottom: 10px;
        page-break-inside: avoid;
    }

    .form-group:before {
        content: attr(data-label);
        font-weight: bold;
        display: block;
        margin-bottom: 5px;
    }

    .form-group:after {
        content: attr(data-value);
        display: block;
    }

    /* Speciální nastavení pro údaje v dokumentech */
    .document-header h4 {
        margin-top: 10px;
        border-bottom: 1px solid #eee;
        padding-bottom: 5px;
    }

    /* Zobrazit contenteditable prvky jen jako text */
    [contenteditable] {
        border: none;
        display: inline;
    }

    [contenteditable]:after {
        content: attr(data-print-content);
    }
}

body.print-mode label,
body.print-mode input,
body.print-mode select,
body.print-mode textarea,
body.print-mode .btn,
body.print-mode .remove-item,
body.print-mode .add-btn,
body.print-mode .add-module-btn,
body.print-mode .module-actions {
    opacity: 0.3;
}

/* Dokument a doklad */
.document-item {
    background-color: var(--light-bg);
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid var(--border-color);
}

.document-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 5px;
}

.document-header h4 {
    margin: 0;
    font-size: 16px;
    color: var(--primary-color);
}

.left-info {
    font-weight: bold;
}

/* Vylepšení stylů pro fotografie */
.photo-dropzone {
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    background-color: var(--light-bg);
    min-height: 200px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.photo-dropzone:hover, .photo-dropzone.dragover {
    border-color: var(--primary-color);
    background-color: rgba(26, 60, 137, 0.05);
    box-shadow: 0 0 10px rgba(26, 60, 137, 0.2);
}

.photo-dropzone:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(26, 60, 137, 0.3);
}

.photo-dropzone.dragover::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(26, 60, 137, 0.1);
    z-index: 1;
    animation: pulse 1.5s infinite;
}

.photo-dropzone-active .dropzone-placeholder i {
    color: var(--primary-color);
    opacity: 0.8;
    transform: scale(1.1);
    transition: all 0.3s ease;
}

.photo-dropzone-active:hover .dropzone-placeholder i {
    transform: scale(1.2);
}

@keyframes pulse {
    0% { opacity: 0.1; }
    50% { opacity: 0.3; }
    100% { opacity: 0.1; }
}

.dropzone-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    color: var(--secondary-color);
}

.dropzone-placeholder i {
    font-size: 40px;
    margin-bottom: 10px;
    color: var(--primary-color);
    opacity: 0.6;
}

.dropzone-instructions {
    font-size: 13px;
    color: var(--light-text);
    margin-top: 5px;
}

.photo-preview {
    position: relative;
    margin-top: 10px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    max-width: 100%;
    transition: all 0.3s ease;
    animation: fadeIn 0.5s ease-in-out;
}

.photo-preview img {
    max-width: 100%;
    border-radius: 8px;
    display: block;
    transition: all 0.3s ease;
}

.photo-preview:hover img {
    transform: scale(1.02);
}

.photo-preview.hidden {
    display: none;
}

.remove-photo {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: rgba(255, 255, 255, 0.9);
    border: none;
    color: var(--secondary-color);
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 10;
    opacity: 0.8;
}

.photo-preview:hover .remove-photo {
    opacity: 1;
}

.remove-photo:hover {
    background-color: var(--secondary-color);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3);
}

.remove-photo:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.3);
}

/* Nové styly pro galerii fotografií */
.photo-gallery-container {
    margin-top: 15px;
}

.photo-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.photo-gallery-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    animation: fadeIn 0.5s ease-in-out;
    background-color: white;
}

.photo-gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.photo-gallery-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    display: block;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

.photo-caption-container {
    padding: 10px;
    border-top: 1px solid var(--border-color);
}

.photo-caption {
    width: 100%;
    border: none;
    padding: 5px;
    font-size: 14px;
    resize: none;
    min-height: 40px;
    background-color: #f9f9f9;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.photo-caption:focus {
    outline: none;
    background-color: white;
    box-shadow: 0 0 0 2px rgba(26, 60, 137, 0.1);
}

.photo-gallery-add {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    border: 2px dashed var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: var(--light-bg);
}

.photo-gallery-add:hover {
    border-color: var(--primary-color);
    background-color: var(--primary-light);
}

.photo-gallery-add i {
    font-size: 30px;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.photo-gallery-controls {
    position: absolute;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 5px;
    z-index: 10;
}

.photo-gallery-btn {
    background-color: rgba(255, 255, 255, 0.9);
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    opacity: 0.8;
}

.photo-gallery-btn:hover {
    opacity: 1;
    transform: scale(1.1);
}

.photo-gallery-btn.remove {
    color: var(--secondary-color);
}

.photo-gallery-btn.remove:hover {
    background-color: var(--secondary-color);
    color: white;
}

.photo-gallery-btn.edit {
    color: var(--primary-color);
}

.photo-gallery-btn.edit:hover {
    background-color: var(--primary-color);
    color: white;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Checkbox styly */
.checkbox-group {
    display: flex;
    align-items: center;
    margin-top: 25px; /* Aby byl checkbox ve stejné výšce jako input pole */
}

.checkbox-label {
    margin-left: 8px;
    margin-bottom: 0;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
}

input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin-right: 5px;
    cursor: pointer;
    accent-color: var(--primary-color);
}

/* Styly pro existující moduly ve výběru */
.module-option.module-exists {
    background-color: rgba(26, 60, 137, 0.1);
    border-color: var(--primary-color);
    position: relative;
}

.module-option.module-exists:hover {
    background-color: rgba(26, 60, 137, 0.2);
}

.exists-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: var(--primary-color);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
}

/* Zvýraznění existujícího modulu při scrollování */
@keyframes highlight-pulse {
    0% { box-shadow: 0 0 0 0 rgba(26, 60, 137, 0.7); }
    70% { box-shadow: 0 0 0 15px rgba(26, 60, 137, 0); }
    100% { box-shadow: 0 0 0 0 rgba(26, 60, 137, 0); }
}

.highlight-module {
    animation: highlight-pulse 1.5s ease-out;
    border: 2px solid var(--primary-color);
    background-color: rgba(26, 60, 137, 0.05);
}

/* Facebook modul styly */
.facebook-profile-image {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100px;
    height: 100px;
}

.facebook-posts-container {
    margin-top: 15px;
}

.facebook-post {
    border: 1px solid #ddd;
    border-radius: 5px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f9f9f9;
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.post-header h5 {
    margin: 0;
    color: #3b5998;
}

.remove-post {
    padding: 2px 5px;
    font-size: 12px;
    color: #dc3545;
    border-color: #dc3545;
    background: none;
}

.facebook-friend-item, .facebook-page-item, .facebook-group-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #eee;
}

.facebook-friend-item:hover {
    background-color: #f5f5f5;
}

.friend-of-interest {
    background-color: #fff8e9;
}

.friend-info, .page-info, .group-info {
    display: flex;
    align-items: center;
}

.friend-avatar, .page-icon, .group-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #e4e6eb;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 10px;
    overflow: hidden;
}

.friend-avatar img, .page-icon img, .group-icon img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.friend-name, .page-name, .group-name {
    font-weight: 500;
}

.friend-data {
    display: flex;
    flex-direction: column;
}

.friend-details {
    font-size: 0.85em;
    color: #65676b;
    margin-top: 3px;
}

.page-category, .group-type {
    color: #65676b;
    font-size: 0.85em;
    margin-left: 10px;
}

.mark-interest-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: #65676b;
}

.mark-interest-btn:hover {
    color: #ff9900;
}

.toggle-friends-list, .toggle-pages-list, .toggle-groups-list, .toggle-reactions {
    background: none;
    border: none;
    color: #3b5998;
    cursor: pointer;
    padding: 5px 10px;
    font-size: 0.9em;
}

.toggle-friends-list:hover, .toggle-pages-list:hover, .toggle-groups-list:hover, .toggle-reactions:hover {
    text-decoration: underline;
}

.facebook-buttons {
    display: flex;
    gap: 10px;
    margin-top: 5px;
}

.clear-friends, .clear-pages, .clear-groups {
    background-color: #f8d7da;
    border-color: #f5c2c7;
    color: #842029;
}

.clear-friends:hover, .clear-pages:hover, .clear-groups:hover {
    background-color: #f1aeb5;
    border-color: #f1aeb5;
}
/* Styly pro emailovou analýzu */
.input-with-button {
    display: flex;
    gap: 10px;
}

.btn-inline {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.btn-inline:hover {
    background-color: #0f2a6b;
    transform: translateY(-2px);
}

.btn-block {
    width: 100%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 12px 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-block:hover {
    background-color: #0f2a6b;
    transform: translateY(-2px);
}

.validation-results, .data-breach-results, .headers-analysis-results {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    animation: fadeIn 0.5s ease;
}

.validation-header, .data-breach-header {
    margin-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 10px;
}

.validation-header h4, .data-breach-header h4 {
    margin: 0;
    color: var(--primary-color);
    font-size: 16px;
}

.validation-item, .analysis-item {
    display: flex;
    margin-bottom: 10px;
    padding: 8px;
    border-bottom: 1px solid #f1f3f5;
}

.validation-label, .analysis-label {
    width: 180px;
    font-weight: 600;
    color: var(--dark-text);
}

.validation-value, .analysis-value {
    flex: 1;
}

.validation-value .valid {
    color: #28a745;
    display: flex;
    align-items: center;
    gap: 5px;
}

.validation-value .invalid {
    color: #dc3545;
    display: flex;
    align-items: center;
    gap: 5px;
}

.validation-value .pending {
    color: #6c757d;
    display: flex;
    align-items: center;
    gap: 5px;
}

.data-breach-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.breach-count-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.breach-count {
    font-size: 36px;
    font-weight: bold;
    color: var(--primary-color);
}

.breach-label {
    font-size: 14px;
    color: var(--light-text);
}

.breach-severity {
    display: flex;
    align-items: center;
    gap: 10px;
}

.severity-label {
    font-weight: 600;
}

.severity-indicator {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.severity-low {
    background-color: #d4edda;
    color: #155724;
}

.severity-medium {
    background-color: #fff3cd;
    color: #856404;
}

.severity-high {
    background-color: #f8d7da;
    color: #721c24;
}

.severity-critical {
    background-color: #dc3545;
    color: white;
}

.severity-unknown {
    background-color: #e9ecef;
    color: #6c757d;
}

.data-breach-item {
    background-color: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.data-breach-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.breach-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    border-bottom: 1px solid #f1f3f5;
    padding-bottom: 10px;
}

.breach-title {
    font-weight: 600;
    font-size: 16px;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.breach-date {
    color: var(--light-text);
    font-size: 14px;
}

.breach-details {
    margin-top: 10px;
}

.breach-detail-item {
    display: flex;
    margin-bottom: 5px;
}

.breach-detail-label {
    width: 150px;
    font-weight: 600;
    color: var(--dark-text);
}

.breach-detail-value {
    flex: 1;
}

.compromised-data {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 10px;
}

.data-tag {
    background-color: #e9ecef;
    color: #495057;
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.data-tag.sensitive {
    background-color: #f8d7da;
    color: #721c24;
}

.code-area {
    font-family: 'Courier New', monospace;
    font-size: 13px;
    background-color: #f8f9fa;
    white-space: pre;
    overflow-x: auto;
}

.visualization-placeholder {
    background-color: #f8f9fa;
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    padding: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.visualization-message {
    text-align: center;
    color: var(--light-text);
}

.visualization-message i {
    font-size: 40px;
    margin-bottom: 15px;
    color: var(--primary-color);
    opacity: 0.5;
}

/* Styly pro cestu emailu */
.email-route-container {
    margin-top: 10px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
}

.route-step {
    display: flex;
    align-items: center;
    padding: 10px;
    background-color: white;
    border-radius: 6px;
    margin-bottom: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.step-number {
    width: 24px;
    height: 24px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    margin-right: 10px;
}

.step-server {
    flex: 1;
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

.step-time {
    color: var(--light-text);
    font-size: 12px;
    margin-left: 10px;
}

.route-arrow {
    display: flex;
    justify-content: center;
    padding: 5px 0;
    color: var(--light-text);
}

/* Styly pro spojené profily */
.connected-profile {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    position: relative;
}

.connected-profile .remove-profile {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: transparent;
    color: #dc3545;
    border: 1px solid #dc3545;
    padding: 5px 10px;
    font-size: 12px;
}

.connected-profile .remove-profile:hover {
    background-color: #dc3545;
    color: white;
}

/* Styly pro telefonní analýzu */
.service-registration-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
}

.service-check {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.service-checkbox {
    margin-right: 10px;
}

.btn-check-service {
    margin-left: auto;
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.btn-check-service:hover {
    background-color: var(--primary-light);
    transform: rotate(180deg);
}

.activity-history-container {
    margin-top: 10px;
}

.activity-timeline {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    min-height: 150px;
    margin-bottom: 10px;
}

.timeline-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: var(--light-text);
    text-align: center;
}

.timeline-placeholder i {
    font-size: 30px;
    margin-bottom: 10px;
    opacity: 0.5;
}

.timeline-item {
    display: flex;
    margin-bottom: 15px;
    position: relative;
}

.timeline-date {
    width: 100px;
    font-weight: 600;
    color: var(--primary-color);
}

.timeline-content {
    flex: 1;
    background-color: white;
    padding: 10px;
    border-radius: 6px;
    border-left: 3px solid var(--primary-color);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.timeline-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.timeline-description {
    color: var(--light-text);
    font-size: 14px;
}

.timeline-actions {
    position: absolute;
    top: 5px;
    right: 5px;
}

.timeline-delete {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    font-size: 12px;
    opacity: 0.7;
}

.timeline-delete:hover {
    opacity: 1;
}

.reverse-lookup-results {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    animation: fadeIn 0.5s ease;
}

.lookup-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.lookup-count-container {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.lookup-count {
    font-size: 36px;
    font-weight: bold;
    color: var(--primary-color);
}

.lookup-label {
    font-size: 14px;
    color: var(--light-text);
}

.lookup-sources {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.sources-label {
    font-weight: 600;
    font-size: 14px;
}

.sources-list {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.source-tag {
    background-color: var(--primary-light);
    color: var(--primary-color);
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 12px;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.lookup-result-item {
    background-color: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.lookup-result-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.lookup-result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    border-bottom: 1px solid #f1f3f5;
    padding-bottom: 10px;
}

.lookup-result-source {
    font-weight: 600;
    font-size: 16px;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 8px;
}

.lookup-result-date {
    color: var(--light-text);
    font-size: 14px;
}

.lookup-result-content {
    margin-top: 10px;
}

.lookup-result-item-row {
    display: flex;
    margin-bottom: 5px;
}

.lookup-result-label {
    width: 150px;
    font-weight: 600;
    color: var(--dark-text);
}

.lookup-result-value {
    flex: 1;
}

.network-analysis-placeholder, .geo-map-placeholder {
    background-color: #f8f9fa;
    border: 2px dashed #e9ecef;
    border-radius: 8px;
    padding: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}

.network-message, .geo-map-placeholder {
    text-align: center;
    color: var(--light-text);
}

.network-message i, .geo-map-placeholder i {
    font-size: 40px;
    margin-bottom: 15px;
    color: var(--primary-color);
    opacity: 0.5;
}

/* Styly pro dialog aktivit */
.activity-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.activity-dialog-content {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    width: 500px;
    max-width: 90%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.activity-dialog-content h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.dialog-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.cancel-activity {
    background-color: #6c757d;
}

.cancel-activity:hover {
    background-color: #5a6268;
}

/* Styly pro připojená zařízení, kontakty a lokace */
.connected-device, .phone-contact, .phone-location {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    position: relative;
}

.connected-device .remove-device,
.phone-contact .remove-contact,
.phone-location .remove-location {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: transparent;
    color: #dc3545;
    border: 1px solid #dc3545;
    padding: 5px 10px;
    font-size: 12px;
}

.connected-device .remove-device:hover,
.phone-contact .remove-contact:hover,
.phone-location .remove-location:hover {
    background-color: #dc3545;
    color: white;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-bottom: 0;
    cursor: pointer;
}

.service-checkbox {
    margin-right: 5px;
}

.no-results {
    text-align: center;
    padding: 20px;
    color: var(--light-text);
    font-style: italic;
}

.info-message {
    background-color: #e7f3fe;
    border-left: 4px solid #2196F3;
    padding: 12px 15px;
    margin: 15px 0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 10px;
    color: #0c5460;
    font-size: 14px;
}

.info-message i {
    color: #2196F3;
    font-size: 18px;
}

/* Styly pro modul aktuálních událostí */
.news-search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
    width: 100%;
}

.news-search-form .form-group {
    flex: 1;
    min-width: 200px;
}

.news-search-form .form-actions {
    display: flex;
    align-items: flex-end;
    gap: 10px;
}

.news-filters {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    width: 100%;
}

.news-filters-section {
    width: 100%;
}

.news-filters-section h5 {
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--primary-color);
    font-weight: 600;
}

.news-filters .form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    width: 100%;
}

.news-filters .form-row .form-group {
    flex: 1;
    min-width: 150px;
}

.news-filters .form-group h5 {
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--primary-color);
    font-weight: 600;
}

.news-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.news-header h4 {
    margin: 0;
    font-size: 18px;
    color: var(--primary-color);
}

.news-actions {
    display: flex;
    gap: 10px;
}

.news-list {
    margin-bottom: 20px;
}

.news-item {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.2s ease;
}

.news-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.news-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.news-item-source {
    font-weight: 600;
    color: var(--primary-color);
}

.news-item-date {
    color: #6c757d;
    font-size: 14px;
}

.news-item-sentiment {
    font-size: 18px;
}

.sentiment-positive {
    color: #28a745;
}

.sentiment-neutral {
    color: #6c757d;
}

.sentiment-negative {
    color: #dc3545;
}

.news-item-content {
    margin-bottom: 10px;
}

.news-item-title {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 5px;
}

.news-item-description {
    color: #212529;
    font-size: 14px;
    line-height: 1.5;
}

.news-item-image {
    margin: 10px 0;
    text-align: center;
}

.news-item-image img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 4px;
}

.news-item-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
}

.news-item-entities {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.entity {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    color: #fff;
}

.entity-person {
    background-color: #007bff;
}

.entity-organization {
    background-color: #6f42c1;
}

.entity-location {
    background-color: #28a745;
}

.entity-other {
    background-color: #6c757d;
}

.news-item-actions {
    display: flex;
    gap: 5px;
}

.news-analysis {
    margin-top: 30px;
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.news-analysis-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.news-analysis-header h4 {
    margin: 0;
    font-size: 18px;
    color: var(--primary-color);
}

.news-analysis-section {
    margin-bottom: 20px;
}

.news-analysis-section h5 {
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--primary-color);
}

.entity-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 10px;
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.entity-cloud-item {
    display: inline-block;
    margin: 5px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.entity-cloud-item:hover {
    transform: scale(1.05);
}

.entity-count {
    font-size: 0.8em;
    color: #6c757d;
}

.sentiment-bars {
    padding: 10px;
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.sentiment-bar {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.sentiment-bar:last-child {
    margin-bottom: 0;
}

.sentiment-bar-label {
    width: 100px;
    font-weight: 600;
}

.sentiment-bar-value {
    height: 24px;
    background-color: var(--primary-color);
    border-radius: 4px;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 10px;
    min-width: 40px;
    transition: width 0.5s ease;
}

.sentiment-bar:nth-child(1) .sentiment-bar-value {
    background-color: #28a745;
}

.sentiment-bar:nth-child(2) .sentiment-bar-value {
    background-color: #6c757d;
}

.sentiment-bar:nth-child(3) .sentiment-bar-value {
    background-color: #dc3545;
}

.timeline-bars {
    display: flex;
    align-items: flex-end;
    gap: 5px;
    height: 150px;
    padding: 10px;
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    overflow-x: auto;
}

.timeline-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 40px;
}

.timeline-bar-value {
    width: 30px;
    background-color: var(--primary-color);
    border-radius: 4px 4px 0 0;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    transition: height 0.5s ease;
}

.timeline-bar-label {
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
    text-align: center;
}

.news-detail-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.news-detail-content {
    background-color: #fff;
    border-radius: 4px;
    width: 800px;
    max-width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.news-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
}

.news-detail-header h3 {
    margin: 0;
    font-size: 20px;
}

.news-detail-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.news-detail-close:hover {
    color: #dc3545;
    background-color: #f8f9fa;
}

.news-detail-body {
    padding: 15px;
}

.news-detail-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.news-detail-image {
    margin: 15px 0;
    text-align: center;
}

.news-detail-image img {
    max-width: 100%;
    max-height: 400px;
    border-radius: 4px;
}

.news-detail-description {
    margin-bottom: 15px;
    line-height: 1.6;
}

.news-detail-entities {
    margin-bottom: 15px;
}

.news-detail-entities h4 {
    margin-bottom: 10px;
    font-size: 18px;
    color: var(--primary-color);
}

.news-detail-entities-section {
    margin-bottom: 15px;
}

.news-detail-entities-section h5 {
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--primary-color);
}

.news-detail-entities-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.news-detail-language {
    display: flex;
    align-items: center;
    gap: 10px;
}

.news-detail-language .btn-translate {
    padding: 2px 8px;
    font-size: 12px;
    margin-left: 5px;
}

.news-detail-translation {
    margin: 15px 0;
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    border-left: 4px solid #007bff;
}

.news-detail-translation h4 {
    margin-bottom: 10px;
    font-size: 18px;
    color: var(--primary-color);
}

.news-detail-translation-content {
    line-height: 1.6;
}

.news-translation-title {
    margin-bottom: 10px;
    font-size: 16px;
}

.news-translation-description {
    font-size: 14px;
}

.notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1100;
    max-width: 350px;
    animation: notification-slide-in 0.3s ease;
}

.notification-hiding {
    animation: notification-slide-out 0.3s ease forwards;
}

.notification-icon {
    margin-right: 15px;
    font-size: 24px;
}

.notification-message {
    flex: 1;
    font-size: 14px;
}

.notification-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    margin-left: 10px;
}

.notification-success {
    border-left: 4px solid #28a745;
}

.notification-success .notification-icon {
    color: #28a745;
}

.notification-error {
    border-left: 4px solid #dc3545;
}

.notification-error .notification-icon {
    color: #dc3545;
}

.notification-warning {
    border-left: 4px solid #ffc107;
}

.notification-warning .notification-icon {
    color: #ffc107;
}

.notification-info {
    border-left: 4px solid #17a2b8;
}

.notification-info .notification-icon {
    color: #17a2b8;
}

@keyframes notification-slide-in {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes notification-slide-out {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Styly pro dialog s informacemi o odeslaném emailu */
.email-sent-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1100;
    animation: fade-in 0.3s ease;
}

.email-sent-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.email-sent-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
}

.email-sent-header h3 {
    margin: 0;
    font-size: 20px;
    color: var(--primary-color);
}

.email-sent-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    font-size: 18px;
    padding: 5px;
}

.email-sent-body {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.email-sent-icon {
    text-align: center;
    font-size: 48px;
    color: #28a745;
    margin-bottom: 10px;
}

.email-sent-message {
    text-align: center;
    margin-bottom: 10px;
}

.email-sent-message p {
    margin: 5px 0;
}

.email-sent-message .email-address {
    font-weight: bold;
    font-size: 18px;
    color: var(--primary-color);
    word-break: break-all;
}

.email-sent-details {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
}

.email-sent-details h4 {
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--primary-color);
}

.email-sent-details ul {
    margin: 0;
    padding-left: 20px;
}

.email-sent-details li {
    margin-bottom: 8px;
}

.email-sent-note {
    font-size: 14px;
    color: #6c757d;
    font-style: italic;
}

.email-sent-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
}

.email-sent-ok {
    padding: 8px 20px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
}

.email-sent-ok:hover {
    background-color: #0056b3;
}

@keyframes fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.entity-add {
    background: none;
    border: none;
    color: #fff;
    cursor: pointer;
    margin-left: 5px;
    padding: 0 3px;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.entity-add:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.news-detail-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.monitoring-container {
    margin-top: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.monitoring-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.monitoring-header h4 {
    margin: 0;
    font-size: 18px;
    color: var(--primary-color);
}

.monitoring-actions {
    display: flex;
    gap: 10px;
}

.monitoring-section {
    margin-bottom: 15px;
}

.monitoring-section h5 {
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--primary-color);
}

.monitoring-items {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.monitoring-item {
    display: flex;
    align-items: center;
    padding: 5px 10px;
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.monitoring-item-name {
    margin-right: 10px;
}

.monitoring-item-remove {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    padding: 2px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.monitoring-item-remove:hover {
    background-color: #f8d7da;
}

.monitoring-setup-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.monitoring-setup-content {
    background-color: #fff;
    border-radius: 4px;
    width: 600px;
    max-width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.monitoring-setup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
}

.monitoring-setup-header h3 {
    margin: 0;
    font-size: 20px;
}

.monitoring-setup-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.monitoring-setup-close:hover {
    color: #dc3545;
    background-color: #f8f9fa;
}

.monitoring-setup-body {
    padding: 15px;
}

.monitoring-setup-section {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.monitoring-setup-section h4 {
    margin-bottom: 15px;
    font-size: 18px;
    color: var(--primary-color);
}

.monitoring-setup-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 15px;
}

.no-results {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    color: #6c757d;
    text-align: center;
}

.no-results i {
    font-size: 48px;
    margin-bottom: 15px;
    color: #adb5bd;
}

/* Styly pro modul mapových overlapů */
.map-container {
    width: 100%;
    height: 750px; /* Zvětšeno o 50% z původních 500px */
    border: 1px solid #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 15px;
}

.map-controls {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.map-controls-section {
    flex: 1;
    min-width: 200px;
}

.map-controls-section h5 {
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--primary-color);
}

.basemap-options {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 10px;
}

.basemap-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 60px;
    height: 60px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    cursor: pointer;
    overflow: hidden;
    position: relative;
    transition: all 0.2s ease;
}

.basemap-selector:hover {
    border-color: var(--primary-color);
}

.basemap-selector.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-color);
}

.basemap-selector img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.basemap-selector span {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    font-size: 10px;
    padding: 2px;
    text-align: center;
}

.drawing-tools {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 10px;
}

.drawing-tool {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.drawing-tool:hover {
    border-color: var(--primary-color);
    background-color: #f8f9fa;
}

.drawing-tool i {
    font-size: 16px;
    color: #6c757d;
}

.layer-controls {
    margin-bottom: 15px;
}

.layer-item {
    display: flex;
    align-items: center;
    padding: 5px 0;
}

.layer-toggle {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.layer-toggle input[type="checkbox"] {
    margin-right: 10px;
}

.layer-name {
    font-size: 14px;
}

.search-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.search-controls .form-group {
    flex: 1;
}

.analysis-tools {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.map-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 20px;
    background-color: #f8f9fa;
    color: #6c757d;
    text-align: center;
}

.map-placeholder i {
    font-size: 48px;
    margin-bottom: 10px;
    color: #adb5bd;
}

.map-placeholder pre {
    margin-top: 10px;
    padding: 10px;
    background-color: #e9ecef;
    border-radius: 4px;
    font-size: 12px;
    overflow-x: auto;
    max-width: 100%;
}

.popup-content {
    padding: 5px;
}

.popup-content h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: var(--primary-color);
}

.popup-form {
    margin-top: 10px;
}

.popup-form .form-group {
    margin-bottom: 10px;
}

.popup-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    font-size: 14px;
}

.popup-input {
    width: 100%;
    padding: 5px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    font-size: 14px;
}

.popup-form textarea.popup-input {
    height: 60px;
    resize: vertical;
}

.popup-form .form-actions {
    display: flex;
    gap: 5px;
    margin-top: 10px;
}

.popup-properties {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 10px;
}

.popup-properties td {
    padding: 3px;
    border-bottom: 1px solid #e9ecef;
    font-size: 12px;
}

.popup-properties td:first-child {
    font-weight: 600;
    width: 40%;
}

.popup-image {
    margin: 10px 0;
    text-align: center;
}

.popup-actions {
    display: flex;
    gap: 5px;
    margin-top: 10px;
}

.analysis-results {
    margin-top: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.analysis-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.analysis-header h4 {
    margin: 0;
    font-size: 18px;
    color: var(--primary-color);
}

.analysis-section {
    margin-bottom: 15px;
}

.analysis-section h5 {
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--primary-color);
}

.analysis-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 10px;
}

.analysis-table td {
    padding: 5px;
    border-bottom: 1px solid #e9ecef;
}

.analysis-table td:first-child {
    font-weight: 600;
    width: 40%;
}

.export-dialog,
.external-data-dialog,
.coordinates-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.export-dialog-content,
.external-data-dialog-content,
.coordinates-dialog-content {
    background-color: #fff;
    border-radius: 4px;
    padding: 20px;
    width: 600px;
    max-width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.export-dialog-header,
.external-data-dialog-header,
.coordinates-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.export-dialog-header h3,
.external-data-dialog-header h3,
.coordinates-dialog-header h3 {
    margin: 0;
    font-size: 20px;
}

.export-dialog-close,
.external-data-dialog-close,
.coordinates-dialog-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.export-dialog-close:hover,
.external-data-dialog-close:hover,
.coordinates-dialog-close:hover {
    color: #dc3545;
    background-color: #f8f9fa;
}

.export-options,
.external-data-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.export-option,
.external-data-option {
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.export-option h4,
.external-data-option h4 {
    margin: 0 0 10px 0;
    font-size: 16px;
    color: var(--primary-color);
}

.export-option p,
.external-data-option p {
    margin: 0 0 15px 0;
    font-size: 14px;
    color: #6c757d;
}

/* Styly pro modul finančního monitoringu */
.financial-monitoring-container {
    margin-bottom: 20px;
}

.financial-search-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.financial-search-form .form-group {
    flex: 1;
    min-width: 200px;
}

.financial-search-form .form-actions {
    display: flex;
    align-items: flex-end;
    gap: 10px;
}

.company-list {
    margin-top: 15px;
}

.company-list-item {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.2s ease;
}

.company-list-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.company-list-name {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 5px;
}

.company-list-ico {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 5px;
}

.company-list-address {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 10px;
}

.company-list-actions {
    display: flex;
    gap: 10px;
}

.company-detail {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
}

.company-detail-header {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.company-detail-name {
    font-weight: 600;
    font-size: 18px;
    margin-bottom: 5px;
}

.company-detail-ico {
    color: #6c757d;
    font-size: 14px;
}

.company-detail-section {
    margin-bottom: 20px;
}

.company-detail-section h5 {
    margin-bottom: 10px;
    color: var(--primary-color);
    font-size: 16px;
}

.company-detail-table {
    width: 100%;
    border-collapse: collapse;
}

.company-detail-table th,
.company-detail-table td {
    padding: 8px;
    border-bottom: 1px solid #e9ecef;
}

.company-detail-table th {
    font-weight: 600;
    text-align: left;
}

.company-detail-table td:first-child {
    font-weight: 600;
    width: 30%;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.results-header h4 {
    margin: 0;
    font-size: 18px;
    color: var(--primary-color);
}

.results-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.external-links {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: var(--primary-color);
}

.loading-spinner i {
    font-size: 24px;
    margin-right: 10px;
}

.error-message {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    margin-bottom: 15px;
}

.error-message i {
    font-size: 18px;
    margin-right: 10px;
}

.risk-data-section {
    margin-bottom: 20px;
}

.risk-data-section h5 {
    margin-bottom: 10px;
    color: var(--primary-color);
    font-size: 16px;
}

.risk-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    margin-bottom: 10px;
}

.risk-status {
    display: flex;
    align-items: center;
}

.risk-status i {
    font-size: 18px;
    margin-right: 10px;
}

.risk-high {
    color: #dc3545;
}

.risk-medium {
    color: #ffc107;
}

.risk-low {
    color: #28a745;
}

.risk-actions {
    display: flex;
    gap: 10px;
}

.property-data-section,
.databox-data-section,
.public-contracts-data-section,
.financial-data-section {
    margin-bottom: 20px;
}

.property-data-section h5,
.databox-data-section h5,
.public-contracts-data-section h5,
.financial-data-section h5 {
    margin-bottom: 10px;
    color: var(--primary-color);
    font-size: 16px;
}

.relationship-graph-container {
    height: 400px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 15px;
}

.graph-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #6c757d;
    text-align: center;
    padding: 20px;
}

.graph-placeholder i {
    font-size: 48px;
    margin-bottom: 10px;
    color: #adb5bd;
}

.monitoring-container {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.monitoring-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.monitoring-header h4 {
    margin: 0;
    font-size: 18px;
    color: var(--primary-color);
}

.monitoring-list {
    margin-top: 15px;
}

.monitoring-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    margin-bottom: 10px;
}

.monitoring-item-name {
    font-weight: 600;
    margin-right: 15px;
}

.monitoring-item-ico {
    color: #6c757d;
    font-size: 14px;
    margin-right: auto;
}

.monitoring-item-actions {
    display: flex;
    gap: 10px;
}

/* Styly pro modul fotografické analýzy */
.module-description {
    margin-bottom: 20px;
    color: #6c757d;
}

.photo-gallery-analysis {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.photo-gallery-add-analysis {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 150px;
    border: 2px dashed #ccc;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.photo-gallery-add-analysis:hover {
    border-color: var(--primary-color);
    background-color: #f8f9fa;
}

.photo-gallery-add-analysis i {
    font-size: 24px;
    color: #adb5bd;
    margin-bottom: 10px;
}

.photo-gallery-add-analysis p {
    color: #6c757d;
    text-align: center;
    margin: 0;
    padding: 0 10px;
}

.analysis-tools {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.analysis-tool-row {
    display: flex;
    gap: 15px;
}

.analysis-tool {
    display: flex;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    width: 100%;
    transition: all 0.2s ease;
}

.analysis-tool:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.analysis-tool-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    margin-right: 15px;
}

.analysis-tool-icon i {
    font-size: 24px;
}

.analysis-tool-content {
    flex: 1;
}

.analysis-tool-title {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 5px;
}

.analysis-tool-description {
    color: #6c757d;
    font-size: 14px;
    margin-bottom: 10px;
}

.photo-analysis-dialog,
.exif-dialog,
.reverse-search-dialog,
.face-detection-dialog,
.object-detection-dialog,
.geolocation-dialog,
.ocr-dialog,
.loading-dialog,
.error-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.photo-analysis-dialog-content,
.exif-dialog-content,
.reverse-search-dialog-content,
.face-detection-dialog-content,
.object-detection-dialog-content,
.geolocation-dialog-content,
.ocr-dialog-content,
.loading-dialog-content,
.error-dialog-content {
    background-color: #fff;
    border-radius: 4px;
    padding: 20px;
    width: 800px;
    max-width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.photo-analysis-dialog-header,
.exif-dialog-header,
.reverse-search-dialog-header,
.face-detection-dialog-header,
.object-detection-dialog-header,
.geolocation-dialog-header,
.ocr-dialog-header,
.error-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.photo-analysis-dialog-header h3,
.exif-dialog-header h3,
.reverse-search-dialog-header h3,
.face-detection-dialog-header h3,
.object-detection-dialog-header h3,
.geolocation-dialog-header h3,
.ocr-dialog-header h3,
.error-dialog-header h3 {
    margin: 0;
    font-size: 20px;
}

.photo-analysis-dialog-close,
.exif-dialog-close,
.reverse-search-dialog-close,
.face-detection-dialog-close,
.object-detection-dialog-close,
.geolocation-dialog-close,
.ocr-dialog-close,
.error-dialog-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.photo-analysis-dialog-close:hover,
.exif-dialog-close:hover,
.reverse-search-dialog-close:hover,
.face-detection-dialog-close:hover,
.object-detection-dialog-close:hover,
.geolocation-dialog-close:hover,
.ocr-dialog-close:hover,
.error-dialog-close:hover {
    color: #dc3545;
    background-color: #f8f9fa;
}

.photo-analysis-preview,
.exif-preview,
.reverse-search-preview,
.face-detection-preview,
.object-detection-preview,
.geolocation-preview,
.ocr-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
}

.photo-analysis-preview img,
.exif-preview img,
.reverse-search-preview img,
.face-detection-preview img,
.object-detection-preview img,
.geolocation-preview img,
.ocr-preview img {
    max-width: 100%;
    max-height: 300px;
    object-fit: contain;
}

.photo-analysis-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.photo-analysis-option {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.photo-analysis-option:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.photo-analysis-option-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    margin: 0 auto 10px;
}

.photo-analysis-option-icon i {
    font-size: 24px;
}

.photo-analysis-option-title {
    font-weight: 600;
    font-size: 16px;
    text-align: center;
    margin-bottom: 5px;
}

.photo-analysis-option-description {
    color: #6c757d;
    font-size: 14px;
    text-align: center;
}

.exif-section {
    margin-bottom: 20px;
}

.exif-section h4 {
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--primary-color);
}

.exif-table {
    width: 100%;
    border-collapse: collapse;
}

.exif-table td {
    padding: 8px;
    border-bottom: 1px solid #e9ecef;
}

.exif-table td:first-child {
    font-weight: 600;
    width: 40%;
}

.exif-gps {
    margin-bottom: 20px;
}

.exif-gps h4 {
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--primary-color);
}

.exif-gps-coordinates {
    display: flex;
    margin-bottom: 10px;
}

.exif-gps-label {
    font-weight: 600;
    width: 40%;
}

.exif-gps-map {
    height: 300px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.exif-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px;
    color: #6c757d;
    text-align: center;
}

.exif-empty i {
    font-size: 48px;
    margin-bottom: 10px;
    color: #adb5bd;
}

.reverse-search-engines h4 {
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--primary-color);
}

.reverse-search-engine-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.reverse-search-engine {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
}

.reverse-search-engine:hover {
    border-color: var(--primary-color);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.reverse-search-engine-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50%;
    margin: 0 auto 10px;
}

.reverse-search-engine-icon i {
    font-size: 24px;
}

.reverse-search-engine-name {
    font-weight: 600;
    font-size: 14px;
}

.reverse-search-note,
.face-detection-note,
.object-detection-note,
.geolocation-note,
.ocr-note {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    margin-top: 15px;
    color: #6c757d;
    font-size: 14px;
}

.reverse-search-note i,
.face-detection-note i,
.object-detection-note i,
.geolocation-note i,
.ocr-note i {
    margin-right: 5px;
    color: var(--primary-color);
}

.external-links {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 15px 0;
}

.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
}

.loading-spinner i {
    font-size: 36px;
    color: var(--primary-color);
}

.loading-message {
    text-align: center;
    color: #6c757d;
}

.geolocation-techniques h4 {
    margin: 15px 0 10px;
    font-size: 16px;
    color: var(--primary-color);
}

.geolocation-techniques ul {
    margin: 0;
    padding-left: 20px;
}

.geolocation-techniques li {
    margin-bottom: 5px;
}

/* Styly pro modul IP adresy a síťová analýza */
.module-section {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.module-section:last-child {
    border-bottom: none;
}

.map-container {
    margin-top: 15px;
    height: 300px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.map-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    background-color: #f8f9fa;
    color: #6c757d;
    text-align: center;
    padding: 20px;
}

.map-placeholder i {
    font-size: 48px;
    margin-bottom: 10px;
    color: #adb5bd;
}

.whois-data {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    font-family: monospace;
    white-space: pre-wrap;
    max-height: 400px;
    overflow-y: auto;
}

.loading-spinner {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #6c757d;
}

.loading-spinner i {
    margin-right: 10px;
}

.external-links {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.dns-records {
    margin-top: 15px;
}

.dns-record-type {
    margin-bottom: 15px;
}

.dns-record-type h4 {
    margin-bottom: 10px;
    font-size: 16px;
    color: var(--primary-color);
}

.dns-record-list {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
}

.dns-record-item {
    display: flex;
    padding: 10px;
    border-bottom: 1px solid #e9ecef;
}

.dns-record-item:last-child {
    border-bottom: none;
}

.dns-record-name {
    flex: 2;
    font-weight: 600;
}

.dns-record-value {
    flex: 3;
    word-break: break-all;
}

.dns-record-ttl {
    flex: 1;
    text-align: right;
    color: #6c757d;
}

.ports-container, .subdomains-container {
    margin-top: 15px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.ports-header, .subdomains-header {
    display: flex;
    background-color: #f8f9fa;
    padding: 10px;
    font-weight: 600;
    border-bottom: 1px solid #e9ecef;
}

.port-header-number, .port-item .port-number {
    flex: 1;
}

.port-header-service, .port-item .port-service {
    flex: 2;
}

.port-header-status, .port-item .port-status {
    flex: 1;
}

.port-header-actions, .port-item .port-actions,
.subdomain-header-actions, .subdomain-item .subdomain-actions {
    flex: 1;
    text-align: right;
}

.subdomain-header-name, .subdomain-item .subdomain-name {
    flex: 2;
}

.subdomain-header-ip, .subdomain-item .subdomain-ip {
    flex: 2;
}

.ports-list, .subdomains-list {
    max-height: 300px;
    overflow-y: auto;
}

.port-item, .subdomain-item {
    display: flex;
    padding: 10px;
    border-bottom: 1px solid #e9ecef;
    align-items: center;
}

.port-item:last-child, .subdomain-item:last-child {
    border-bottom: none;
}

.port-item .port-status {
    display: flex;
    align-items: center;
}

.port-item .port-status::before {
    content: '';
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.port-item .port-status.open::before {
    background-color: #28a745;
}

.port-item .port-status.closed::before {
    background-color: #dc3545;
}

.port-item .port-status.filtered::before {
    background-color: #ffc107;
}

.btn-icon {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.btn-icon:hover {
    background-color: #f8f9fa;
    color: #dc3545;
}

.error-message {
    padding: 15px;
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    margin-bottom: 15px;
}

.info-message {
    padding: 15px;
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
    border-radius: 4px;
    margin-bottom: 15px;
}

/* Styly pro fotogalerii v modulu IP analýzy */
.photo-gallery {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.photo-gallery-add {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 150px;
    height: 150px;
    border: 2px dashed #ccc;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.photo-gallery-add:hover {
    border-color: var(--primary-color);
    background-color: #f8f9fa;
}

.photo-gallery-add i {
    font-size: 24px;
    color: #adb5bd;
    margin-bottom: 10px;
}

.photo-gallery-add p {
    color: #6c757d;
    text-align: center;
    margin: 0;
    padding: 0 10px;
}

.photo-item {
    width: 150px;
    height: 200px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.photo-item.selected {
    border: 2px solid var(--primary-color);
    box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
}

.photo-preview {
    width: 100%;
    height: 100px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
}

.photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-info {
    padding: 8px;
    height: 70px;
    overflow: hidden;
}

.photo-title {
    font-weight: 600;
    font-size: 12px;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.photo-date {
    font-size: 11px;
    color: #6c757d;
    margin-bottom: 4px;
}

.photo-description {
    font-size: 11px;
    color: #6c757d;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.photo-actions {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    display: flex;
    justify-content: space-between;
    padding: 5px;
    background-color: rgba(255, 255, 255, 0.9);
    border-top: 1px solid #e9ecef;
}

.photo-view, .photo-delete {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 3px 5px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.photo-view:hover {
    color: var(--primary-color);
    background-color: #f8f9fa;
}

.photo-delete:hover {
    color: #dc3545;
    background-color: #f8f9fa;
}

.upload-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.photo-dialog {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.photo-dialog-content {
    background-color: #fff;
    border-radius: 4px;
    padding: 20px;
    width: 500px;
    max-width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.photo-preview-dialog {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
}

.dialog-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 15px;
}

.photo-viewer {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.photo-viewer-content {
    background-color: #fff;
    border-radius: 4px;
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.photo-viewer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #e9ecef;
}

.photo-viewer-title {
    font-weight: 600;
    font-size: 16px;
}

.photo-viewer-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    padding: 5px;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.photo-viewer-close:hover {
    color: #dc3545;
    background-color: #f8f9fa;
}

.photo-viewer-body {
    padding: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: auto;
    max-height: calc(90vh - 60px);
}

.photo-viewer-body img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

/* Styly pro fotogalerii v modulu telefonní analýzy */
.photo-gallery-container {
    margin-top: 15px;
}

.photo-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.upload-buttons {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.upload-photo, .paste-photo, .add-photo-url {
    flex: 1;
    min-width: 150px;
}

.photo-item {
    background-color: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    position: relative;
}

.photo-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.photo-preview {
    height: 150px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
}

.photo-preview img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-info {
    padding: 10px;
}

.photo-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--primary-color);
}

.photo-date {
    font-size: 12px;
    color: var(--light-text);
    margin-bottom: 5px;
}

.photo-description {
    font-size: 13px;
    color: var(--dark-text);
    max-height: 60px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
}

.photo-actions {
    position: absolute;
    top: 5px;
    right: 5px;
    display: flex;
    gap: 5px;
}

.photo-view, .photo-delete {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.photo-view:hover {
    background-color: var(--primary-color);
    color: white;
}

.photo-delete:hover {
    background-color: #dc3545;
    color: white;
}

.photo-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.photo-dialog-content {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    width: 500px;
    max-width: 90%;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.photo-dialog-content h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.photo-preview-dialog {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 15px;
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 10px;
    border: 1px solid #e9ecef;
}

.photo-viewer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1100;
}

.photo-viewer-content {
    background-color: white;
    border-radius: 8px;
    max-width: 90%;
    max-height: 90%;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
}

.photo-viewer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background-color: var(--primary-color);
    color: white;
}

.photo-viewer-title {
    font-weight: 600;
    font-size: 16px;
}

.photo-viewer-close {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.photo-viewer-close:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.photo-viewer-body {
    padding: 15px;
    overflow: auto;
    display: flex;
    align-items: center;
    justify-content: center;
    max-height: 80vh;
}

.photo-viewer-body img {
    max-width: 100%;
    max-height: 70vh;
    object-fit: contain;
}

/* TikTok styly */
.tiktok-profile-image {
    background-color: #000;
    border-radius: 50%;
}

.tiktok-categories-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin-top: 10px;
}

.category-item {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.category-item input[type="checkbox"] {
    margin-right: 8px;
}

.category-item label {
    margin-bottom: 0;
    font-size: 14px;
    cursor: pointer;
}

.tiktok-video {
    border-left: 4px solid #ee1d52;
}

.tiktok-trend {
    border-left: 4px solid #69c9d0;
}

.tiktok-collab {
    border-left: 4px solid #25f4ee;
}

.tiktok-follower, .tiktok-following {
    border-left: 4px solid #fe2c55;
}

.tiktok-sound {
    border-left: 4px solid #000000;
}

/* Statistiky videí */
.video-stats {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
}

.stat-item {
    text-align: center;
    flex: 1;
}

.stat-value {
    font-weight: bold;
    font-size: 18px;
    color: #fe2c55;
}

.stat-label {
    font-size: 12px;
    color: #666;
}