
'use server';
/**
 * @fileOverview A Genkit flow to simulate ARES company lookup.
 *
 * - lookupCompanyInAres - Simulates fetching company data from ARES based on IČO.
 * - AresLookupInput - The input type for the lookupCompanyInAres function.
 * - AresLookupOutput - The return type for the lookupCompanyInAres function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const AresLookupInputSchema = z.object({
  companyId: z.string().min(8, "IČO musí mít alespoň 8 znaků.").describe('The IČO (company identification number) of the company to look up.'),
});
export type AresLookupInput = z.infer<typeof AresLookupInputSchema>;

const AresLookupOutputSchema = z.object({
  name: z.string().describe('The official name of the company.'),
  address: z.string().describe('The registered address of the company.'),
  legalForm: z.string().optional().describe('The legal form of the company (e.g., s.r.o., a.s.).'),
  establishmentDate: z.string().optional().describe('The date of establishment of the company.'),
});
export type AresLookupOutput = z.infer<typeof AresLookupOutputSchema>;

export async function lookupCompanyInAres(input: AresLookupInput): Promise<AresLookupOutput> {
  return aresLookupFlow(input);
}

const prompt = ai.definePrompt({
  name: 'aresLookupPrompt',
  input: {schema: AresLookupInputSchema},
  output: {schema: AresLookupOutputSchema},
  prompt: `Jsi simulátor API databáze ARES. Pro zadané IČO "{{companyId}}" vygeneruj realistická, ale fiktivní data české společnosti.
Vrať název společnosti, její adresu, právní formu a datum založení.
Formátuj odpověď jako JSON objekt s klíči "name", "address", "legalForm", a "establishmentDate".
Například pro IČO "12345678" můžeš vrátit:
{
  "name": "Fiktivní Firma XYZ s.r.o.",
  "address": "Ukázková 123/45, 110 00 Praha 1, Česká republika",
  "legalForm": "Společnost s ručením omezeným",
  "establishmentDate": "01.01.2020"
}
Ujisti se, že vygenerovaná adresa je platně vypadající česká adresa.
Prioritizuj generování smysluplného názvu a adresy. Ostatní pole jsou volitelná, ale pokud je generuješ, ujisti se, že jsou také realistická.
Pro IČO: {{{companyId}}}`,
});

const aresLookupFlow = ai.defineFlow(
  {
    name: 'aresLookupFlow',
    inputSchema: AresLookupInputSchema,
    outputSchema: AresLookupOutputSchema,
  },
  async (input: AresLookupInput) => {
    // Simulate some delay as if calling an external API
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const {output} = await prompt(input);
    if (!output) {
      throw new Error('Simulace ARES selhala: LLM nevrátil žádný výstup.');
    }
    return output;
  }
);
