import { getStorage, ref, uploadString, getDownloadURL, deleteObject } from "firebase/storage";
import { app } from "@/lib/firebase";
import { doc, setDoc, getDoc, deleteField } from "firebase/firestore";
import { db } from "@/lib/firebase";

const storage = getStorage(app);

// Příznak, zda používat Firebase Storage nebo Firestore pro ukládání obrázků
// Nastavíme na false kvůli CORS problémům s Firebase Storage
const USE_FIREBASE_STORAGE = false;

/**
 * Nahraje base64 obrázek do Firebase Storage nebo uloží do Firestore
 * @param base64 Base64 řetězec obrázku
 * @param path Cesta v úložišti (např. "cases/123/subjects/456/facebook/profile.jpg")
 * @returns URL pro stažení obrázku nebo původní base64 v případě chyby
 */
export const uploadBase64Image = async (base64: string, path: string): Promise<string> => {
  try {
    if (!base64 || !path) {
      console.warn("Chybí base64 data nebo cesta");
      return base64; // Vrátíme původní base64 jako záložní řešení
    }

    // Kontrola, zda base64 začíná prefixem
    if (!base64.startsWith('data:image/')) {
      console.warn("Neplatný formát base64 dat");
      return base64; // Vrátíme původní base64 jako záložní řešení
    }

    // Pokud nepoužíváme Firebase Storage, uložíme obrázek do Firestore
    if (!USE_FIREBASE_STORAGE) {
      console.log("Ukládám obrázek do Firestore místo Firebase Storage");
      try {
        await storeImageInFirestore(base64, path);
        return base64; // Vrátíme base64 pro použití jako data URL
      } catch (firestoreError) {
        console.error("Chyba při ukládání do Firestore:", firestoreError);
        return base64; // Vrátíme původní base64 i při chybě
      }
    }

    try {
      // Odstranění prefixu "data:image/jpeg;base64," pokud existuje
      const base64Data = base64.includes("base64,")
        ? base64.split("base64,")[1]
        : base64;

      // Vytvoření reference na soubor
      const storageRef = ref(storage, path);

      // Nahrání base64 dat
      const snapshot = await uploadString(storageRef, base64Data, "base64");

      // Získání URL pro stažení
      const downloadURL = await getDownloadURL(snapshot.ref);

      console.log("Obrázek úspěšně nahrán do Firebase Storage:", downloadURL);
      return downloadURL;
    } catch (storageError) {
      console.error("Chyba při nahrávání do Firebase Storage, zkouším Firestore:", storageError);

      // Fallback - zkusíme uložit do Firestore
      try {
        await storeImageInFirestore(base64, path);
        console.log("Obrázek úspěšně uložen do Firestore jako fallback");
        return base64; // Vrátíme base64 pro použití jako data URL
      } catch (firestoreError) {
        console.error("Chyba i při ukládání do Firestore:", firestoreError);
        return base64; // Vrátíme původní base64
      }
    }
  } catch (error) {
    console.error("Chyba při nahrávání obrázku:", error);
    // Vrátíme původní base64 jako záložní řešení
    return base64;
  }
};

/**
 * Uloží base64 obrázek do Firestore
 * @param base64 Base64 řetězec obrázku
 * @param path Cesta v úložišti (např. "cases/123/subjects/456/facebook/profile.jpg")
 * @returns Původní base64 řetězec
 */
async function storeImageInFirestore(base64: string, path: string): Promise<string> {
  try {
    // Vytvoříme dokument v Firestore pro uložení obrázku
    const imageDocRef = doc(db, "images", path.replace(/\//g, "_"));

    // Uložíme base64 data do Firestore
    await setDoc(imageDocRef, {
      base64,
      path,
      createdAt: new Date().toISOString()
    });

    console.log("Obrázek byl úspěšně uložen do Firestore:", path);

    // Vrátíme původní base64 řetězec
    return base64;
  } catch (error) {
    console.error("Chyba při ukládání obrázku do Firestore:", error);
    // Vrátíme původní base64 řetězec
    return base64;
  }
}

/**
 * Smaže obrázek z Firebase Storage nebo Firestore
 * @param path Cesta v úložišti (např. "cases/123/subjects/456/facebook/profile.jpg")
 */
export const deleteImage = async (path: string): Promise<void> => {
  try {
    if (!path) {
      console.warn("Chybí cesta k obrázku");
      return; // Tiše skončíme, pokud není cesta
    }

    // Pokud nepoužíváme Firebase Storage, není potřeba nic mazat
    if (!USE_FIREBASE_STORAGE) {
      return;
    }

    // Vytvoření reference na soubor
    const storageRef = ref(storage, path);

    try {
      // Smazání souboru
      await deleteObject(storageRef);
    } catch (storageError) {
      // Pokud se nepodaří smazat soubor, pouze zalogujeme chybu
      console.warn("Nepodařilo se smazat soubor z Firebase Storage:", storageError);
    }
  } catch (error) {
    console.error("Chyba při mazání obrázku:", error);
    // Nešíříme chybu dál, aby se nezastavil proces mazání modulu
  }
};

/**
 * Smaže obrázek z Firestore
 * @param path Cesta v úložišti (např. "cases/123/subjects/456/facebook/profile.jpg")
 */
async function deleteImageFromFirestore(path: string): Promise<void> {
  try {
    // Vytvoříme referenci na dokument v Firestore
    const imageDocRef = doc(db, "images", path.replace(/\//g, "_"));

    // Smažeme dokument
    await setDoc(imageDocRef, {
      base64: deleteField(),
      path: deleteField(),
      createdAt: deleteField(),
      deleted: true,
      deletedAt: new Date().toISOString()
    }, { merge: true });

    console.log("Obrázek byl úspěšně smazán z Firestore:", path);
  } catch (error) {
    console.error("Chyba při mazání obrázku z Firestore:", error);
  }
}

/**
 * Generuje unikátní cestu pro obrázek v úložišti
 * @param caseId ID případu
 * @param subjectId ID subjektu
 * @param moduleId ID modulu (např. "facebook", "instagram")
 * @param type Typ obrázku (např. "profile", "cover", "post")
 * @param index Volitelný index (např. pro příspěvky)
 * @returns Cesta v úložišti
 */
export const generateImagePath = (
  caseId: string,
  subjectId: string,
  moduleId: string,
  type: string,
  index?: number | string
): string => {
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(2, 10);

  if (index !== undefined) {
    return `cases/${caseId}/subjects/${subjectId}/${moduleId}/${type}_${index}_${timestamp}_${randomId}.jpg`;
  }

  return `cases/${caseId}/subjects/${subjectId}/${moduleId}/${type}_${timestamp}_${randomId}.jpg`;
};
