import { Metadata } from "next";
import { notFound } from "next/navigation";
import { db } from "@/lib/firebase";
import { doc, getDoc } from "firebase/firestore";
import { MapOverlaysModule } from "@/components/modules/map-overlays/MapOverlaysModule";
import { getMapOverlaysData } from "@/lib/firebase/firestore";
import { Subject } from "@/types";

export const metadata: Metadata = {
  title: "Mapové overlapy | OSINT",
  description: "Modul pro práci s mapovými vrstvami a jejich analýzu",
};

async function getSubject(caseId: string, subjectId: string) {
  try {
    const subjectRef = doc(db, "cases", caseId, "subjects", subjectId);
    const subjectSnap = await getDoc(subjectRef);

    if (!subjectSnap.exists()) {
      return null;
    }

    return {
      id: subjectSnap.id,
      ...subjectSnap.data(),
    } as Subject;
  } catch (error) {
    console.error("Error fetching subject:", error);
    return null;
  }
}

interface MapOverlaysPageProps {
  params: {
    caseId: string;
    subjectId: string;
  };
}

export default async function MapOverlaysPage({
  params,
}: MapOverlaysPageProps) {
  try {
    const { caseId, subjectId } = params;
    
    const subject = await getSubject(caseId, subjectId);
    
    if (!subject) {
      notFound();
    }

    const mapOverlaysData = await getMapOverlaysData(caseId, subjectId);

    return (
      <div className="container mx-auto py-6">
        <MapOverlaysModule
          caseId={caseId}
          subject={subject}
          initialData={mapOverlaysData || undefined}
        />
      </div>
    );
  } catch (error) {
    console.error("Error in MapOverlaysPage:", error);
    return (
      <div className="container mx-auto py-6">
        <div className="bg-destructive/10 p-4 rounded-md text-destructive">
          <h2 className="text-lg font-semibold">Chyba při načítání dat</h2>
          <p>Nepodařilo se načíst data pro mapové overlapy. Zkuste to prosím později.</p>
        </div>
      </div>
    );
  }
}
