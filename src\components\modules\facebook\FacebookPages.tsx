import { FacebookModuleData, FacebookPage } from "@/types/facebook";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ThumbsUp, ExternalLink, CheckCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Image from "next/image";

interface FacebookPagesProps {
  data: FacebookModuleData | null;
}

export default function FacebookPages({ data }: FacebookPagesProps) {
  if (!data || !data.likedPages || data.likedPages.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="text-muted-foreground mb-2"><PERSON><PERSON><PERSON><PERSON> stránky k zobrazení</div>
        <p className="text-sm text-muted-foreground">
          Přidejte oblíbené stránky v režimu úprav.
        </p>
      </div>
    );
  }

  return (
    <div>
      <h3 className="text-lg font-semibold mb-4"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> stránky ({data.likedPages.length})</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {data.likedPages.map((page) => (
          <PageCard key={page.id} page={page} />
        ))}
      </div>
    </div>
  );
}

function PageCard({ page }: { page: FacebookPage }) {
  return (
    <Card>
      <CardContent className="p-4 flex items-center gap-3">
        {/* Page Image */}
        <div className="relative w-12 h-12 rounded-md overflow-hidden flex-shrink-0">
          {page.imageUrl ? (
            <Image 
              src={page.imageUrl} 
              alt={page.name} 
              fill 
              className="object-cover"
              unoptimized
            />
          ) : (
            <div className="w-full h-full bg-blue-100 flex items-center justify-center">
              <span className="text-blue-600 font-bold">
                {page.name.charAt(0)}
              </span>
            </div>
          )}
        </div>
        
        {/* Page Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <h4 className="font-medium truncate">{page.name}</h4>
            {page.isVerified && (
              <Badge variant="outline" className="bg-blue-50">
                <CheckCircle className="h-3 w-3 text-blue-600" />
              </Badge>
            )}
          </div>
          
          <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
            {page.category && (
              <span>{page.category}</span>
            )}
            
            {page.likes !== undefined && (
              <span className="flex items-center">
                <ThumbsUp className="h-3 w-3 inline mr-1" /> 
                {page.likes.toLocaleString()} To se mi líbí
              </span>
            )}
          </div>
        </div>
        
        {/* External Link */}
        <Button variant="ghost" size="icon" asChild className="flex-shrink-0">
          <a href={page.pageUrl} target="_blank" rel="noopener noreferrer">
            <ExternalLink className="h-4 w-4" />
          </a>
        </Button>
      </CardContent>
    </Card>
  );
}
