"use client";

import { useState, useCallback, useEffect } from "react";
import { useFieldArray, Control, useWatch, UseFormReturn } from "react-hook-form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ImageUp, Trash2, ChevronUp, ChevronDown } from "lucide-react";
import { FormItemRHF } from "./FormComponents";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import type { LocationsFormValues } from "./FormComponents";
import { LoadingSpinner } from "@/components/ui/loading";

interface PhotoDocumentationSectionProps {
  form: UseFormReturn<LocationsFormValues>;
  title?: string;
  description?: string;
  namePrefix?: string;
  photoHint?: string;
  caseId?: string;
  subjectId?: string;
  moduleId?: string;
}

export function PhotoDocumentationSection({ 
  form, 
  title = "Fotodokumentace",
  description = "Nahrajte a prohlédněte si fotografie. Fotografie se ukládají trvale do aplikace.",
  namePrefix = "photos",
  photoHint = "location place building",
  caseId = 'locations',
  subjectId = 'locations', 
  moduleId = 'locations'
}: PhotoDocumentationSectionProps) {
  const { toast } = useToast();
  const [uploadingPhotos, setUploadingPhotos] = useState<Record<number, boolean>>({});
  const [photoUrls, setPhotoUrls] = useState<Record<number, string>>({});

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: namePrefix as any,
  });

  // Sledování aktuálních hodnot fotek z formuláře
  const watchedPhotos = useWatch({
    control: form.control,
    name: namePrefix as any,
  }) || [];

  // Efekt pro synchronizaci photoUrls s form data
  useEffect(() => {
    const newPhotoUrls: Record<number, string> = {};
    
    if (Array.isArray(watchedPhotos)) {
      watchedPhotos.forEach((photo: any, index: number) => {
        if (photo?.downloadURL) {
          newPhotoUrls[index] = photo.downloadURL;
        }
      });
    }
    
    setPhotoUrls(newPhotoUrls);
  }, [watchedPhotos]);

  // Placeholder obrázky
  const getPlaceholderImage = (index: number) => 
    `https://placehold.co/450x450/e2e8f0/64748b?text=Foto+${index + 1}`;

  // Funkce pro konverzi File na Base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  };

  // Funkce pro přesunutí fotografie
  const movePhoto = useCallback((fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= fields.length) return;
    
    const photos = [...watchedPhotos];
    const [movedPhoto] = photos.splice(fromIndex, 1);
    photos.splice(toIndex, 0, movedPhoto);
    
    form.setValue(namePrefix as any, photos);
    
    // Aktualizujeme také photoUrls
    const newPhotoUrls = { ...photoUrls };
    const movedUrl = newPhotoUrls[fromIndex];
    delete newPhotoUrls[fromIndex];
    
    // Přesuneme URL
    Object.keys(newPhotoUrls).forEach(key => {
      const numKey = parseInt(key);
      if (numKey > fromIndex && numKey <= toIndex) {
        newPhotoUrls[numKey - 1] = newPhotoUrls[numKey];
        delete newPhotoUrls[numKey];
      } else if (numKey < fromIndex && numKey >= toIndex) {
        newPhotoUrls[numKey + 1] = newPhotoUrls[numKey];
        delete newPhotoUrls[numKey];
      }
    });
    
    newPhotoUrls[toIndex] = movedUrl;
    setPhotoUrls(newPhotoUrls);
  }, [fields.length, form, photoUrls, namePrefix, watchedPhotos]);

  // Funkce pro zpracování obrázku
  const processImageFile = useCallback(async (file: File, index: number) => {
    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: "Soubor je příliš velký",
        description: "Maximální velikost fotografie je 10MB.",
        variant: "destructive"
      });
      return;
    }

    if (!file.type.startsWith('image/')) {
      toast({
        title: "Neplatný formát souboru",
        description: "Podporované jsou pouze obrázky (JPEG, PNG, GIF, WEBP).",
        variant: "destructive"
      });
      return;
    }

    setUploadingPhotos(prev => ({ ...prev, [index]: true }));

    try {
      const base64 = await fileToBase64(file);

      const response = await fetch('/api/upload-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          base64Data: base64,
          caseId: caseId,
          subjectId: subjectId,
          moduleId: moduleId,
          fileName: file.name
        })
      });

      if (!response.ok) {
        throw new Error('Nepodařilo se uložit fotografii na server');
      }

      const { imagePath } = await response.json();

      setPhotoUrls(prev => ({
        ...prev,
        [index]: imagePath
      }));
      
      form.setValue(`${namePrefix}.${index}.downloadURL` as any, imagePath);
      form.setValue(`${namePrefix}.${index}.fileName` as any, file.name);
      form.setValue(`${namePrefix}.${index}.description` as any, `Nahraná fotografie: ${file.name}`);
      form.setValue(`${namePrefix}.${index}.id` as any, crypto.randomUUID());

      toast({
        title: "Fotografie uložena",
        description: `Fotografie byla úspěšně uložena do aplikace: ${imagePath}`,
      });
    } catch (error) {
      console.error('Chyba při načítání fotografie:', error);
      toast({
        title: "Chyba při načítání fotografie",
        description: "Nepodařilo se načíst fotografii. Zkuste to prosím znovu.",
        variant: "destructive"
      });
    } finally {
      setUploadingPhotos(prev => ({ ...prev, [index]: false }));
    }
  }, [form, namePrefix, toast, caseId, subjectId, moduleId]);

  // Funkce pro paste obrázku ze schránky
  const handlePaste = useCallback(async (event: React.ClipboardEvent, index: number) => {
    event.preventDefault();
    const items = event.clipboardData?.items;
    
    if (!items) return;
    
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.type.startsWith('image/')) {
        const file = item.getAsFile();
        if (file) {
          await processImageFile(file, index);
        }
        break;
      }
    }
  }, [processImageFile]);

  // Funkce pro zpracování nahraného obrázku
  const handlePhotoUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const file = event.target.files?.[0];
    if (file) {
      await processImageFile(file, index);
    }
  }, [processImageFile]);

  const handleAddPhoto = () => {
    append({
      id: crypto.randomUUID(),
      downloadURL: '',
      description: '',
      dateTaken: '',
      fileName: '',
      sourceURL: '',
      storagePath: '',
    });
  };

  const handleRemovePhoto = (index: number) => {
    remove(index);
    // Odstraníme také URL z lokálního stavu
    setPhotoUrls(prev => {
      const newUrls = { ...prev };
      delete newUrls[index];
      return newUrls;
    });
    toast({
      title: "Fotografie odstraněna",
      description: "Záznam o fotografii byl úspěšně odstraněn.",
    });
  };

  return (
    <Card className="shadow-md">
      <CardHeader>
        <CardTitle className="text-xl flex items-center">
          <ImageUp className="mr-3 h-6 w-6 text-primary" />
          {title}
        </CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {fields.map((field, index) => (
          <Card key={field.id} className="p-4 space-y-4 shadow-sm bg-card-foreground/5">
            <div className="flex justify-between items-center mb-2">
              <p className="font-semibold text-md">Záznam o fotografii {index + 1}</p>
              <div className="flex items-center gap-1">
                <Button 
                  type="button" 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => movePhoto(index, index - 1)}
                  disabled={index === 0}
                  className="h-8 w-8"
                  title="Posunout nahoru"
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
                <Button 
                  type="button" 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => movePhoto(index, index + 1)}
                  disabled={index === fields.length - 1}
                  className="h-8 w-8"
                  title="Posunout dolů"
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
                <Button 
                  type="button" 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => handleRemovePhoto(index)} 
                  className="text-destructive hover:bg-destructive/10 h-8 w-8" 
                  title="Smazat fotografii"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="flex flex-col md:flex-row items-start gap-6">
              <div className="relative">
                <img
                  src={photoUrls[index] || getPlaceholderImage(index)}
                  alt={`Náhled fotografie ${index + 1}`}
                  data-ai-hint={photoHint}
                  className="w-[450px] h-[450px] object-cover rounded-lg border-2 border-muted shadow-md bg-background mx-auto"
                  onError={(e) => (e.currentTarget.src = getPlaceholderImage(index))}
                />
                {uploadingPhotos[index] && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg">
                    <LoadingSpinner size="md" className="text-white" />
                  </div>
                )}
              </div>
              
              <div className="flex-grow space-y-4 w-full">
                <FormItemRHF 
                  label="Popis fotografie" 
                  name={`${namePrefix}.${index}.description`} 
                  control={form.control} 
                  placeholder="Např. Pohled na budovu, Vchod do objektu, Parkoviště..." 
                  as="textarea" 
                  rows={3}
                />
                <FormItemRHF 
                  label="Cesta k souboru (automaticky vyplněno)" 
                  name={`${namePrefix}.${index}.downloadURL`} 
                  control={form.control} 
                  disabled={true} 
                  placeholder="Automaticky se vyplní po nahrání fotografie"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <FormItemRHF 
                label="Datum pořízení (cca)" 
                name={`${namePrefix}.${index}.dateTaken`} 
                control={form.control} 
                type="text" 
                placeholder="Např. Leden 2023, 15.03.2022" 
              />
              <FormItemRHF 
                label="Název souboru (původní)" 
                name={`${namePrefix}.${index}.fileName`} 
                control={form.control} 
                placeholder="např. lokace_foto.jpg, screenshot.png" 
              />
            </div>

            <FormItemRHF 
              label="URL zdroje (kde byla nalezena)" 
              name={`${namePrefix}.${index}.sourceURL`} 
              control={form.control} 
              placeholder="https://mapy.cz/..."
            />

            <div className="mt-4">
              <Label 
                htmlFor={`photo-upload-${index}`} 
                className={cn(
                  "flex items-center justify-center h-32 w-full border-2 border-dashed rounded-md transition-colors",
                  uploadingPhotos[index] 
                    ? "cursor-not-allowed border-muted bg-muted/30" 
                    : "cursor-pointer hover:border-primary/50 hover:bg-accent/30"
                )}
                onPaste={(e) => handlePaste(e, index)}
                tabIndex={0}
              >
                <div className="flex flex-col items-center text-muted-foreground">
                  {uploadingPhotos[index] ? (
                    <>
                      <LoadingSpinner size="md" className="text-primary" />
                      <p className="text-sm mt-1">Ukládání fotografie na server...</p>
                    </>
                  ) : (
                    <>
                      <ImageUp className="h-8 w-8"/>
                      <p className="text-sm mt-1">Klikněte pro nahrání nebo stiskněte Ctrl+V</p>
                      <p className="text-xs text-muted-foreground/70 mt-1">Max. 10MB, formáty: JPEG, PNG, GIF, WEBP</p>
                    </>
                  )}
                </div>
              </Label>
              <Input
                id={`photo-upload-${index}`}
                type="file"
                accept="image/*"
                className="sr-only"
                onChange={(e) => handlePhotoUpload(e, index)}
                disabled={uploadingPhotos[index]}
              />
            </div>
          </Card>
        ))}
        <Button 
          type="button" 
          variant="outline" 
          onClick={handleAddPhoto} 
          size="sm"
        >
          <ImageUp className="mr-2 h-4 w-4" />
          Přidat záznam o fotografii
        </Button>
      </CardContent>
    </Card>
  );
} 