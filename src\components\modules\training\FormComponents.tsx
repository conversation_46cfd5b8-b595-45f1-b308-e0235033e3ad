"use client";

import { Control } from "react-hook-form";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface FormItemRHFProps {
  control: Control<any>;
  name: string;
  label: string;
  placeholder?: string;
  type?: string;
  as?: "input" | "textarea";
  rows?: number;
  disabled?: boolean;
}

export function FormItemRHF({
  control,
  name,
  label,
  placeholder,
  type = "text",
  as = "input",
  rows = 3,
  disabled = false,
}: FormItemRHFProps) {
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            {as === "input" ? (
              <Input
                {...field}
                type={type}
                placeholder={placeholder}
                disabled={disabled}
                value={field.value || ""}
              />
            ) : (
              <Textarea
                {...field}
                placeholder={placeholder}
                rows={rows}
                disabled={disabled}
                value={field.value || ""}
              />
            )}
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
