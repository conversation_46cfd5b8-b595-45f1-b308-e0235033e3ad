"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { FileText, Download, Settings, ArrowUp, ArrowDown, Trash2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { LoadingOverlay, LoadingButton, useLoadingState } from '@/components/ui/loading';
import PDFEditor from './PDFEditor';

interface ModuleInfo {
  id: string;
  name: string;
  category: string;
  icon: string;
  hasData: boolean;
}

interface ReportSettings {
  title: string;
  subject: string;
  jid: string;
  documentNumber: string;
  location: string;
  date: string;
  department: string;
  purpose: string;
  selectedModules: string[];
  moduleOrder: string[];
  format: 'html' | 'word' | 'pdf';
  style: 'official' | 'modern';
}

interface ReportGeneratorProps {
  caseId: string;
  availableModules: ModuleInfo[];
  caseData?: {
    investigatorName?: string;
    subjects?: Array<{
      id: string;
      type: string;
      firstName?: string;
      lastName?: string;
      name?: string;
    }>;
  };
}

export default function ReportGenerator({ caseId, availableModules, caseData }: ReportGeneratorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [reportType, setReportType] = useState<'full' | 'partial'>('full');
  const { toast } = useToast();

  // Loading state management
  const {
    isLoading,
    progress,
    currentStep,
    startLoading,
    updateProgress,
    nextStep,
    stopLoading,
    setCurrentStep
  } = useLoadingState();

  // Automatické doplnění jména subjektu
  const getDefaultSubject = () => {
    if (!caseData?.subjects || caseData.subjects.length === 0) return '';

    if (caseData.subjects.length === 1) {
      const subject = caseData.subjects[0];
      return subject.type === 'physical'
        ? `${subject.firstName || ''} ${subject.lastName || ''}`.trim()
        : subject.name || '';
    }

    return ''; // Pokud je více subjektů, necháme prázdné pro výběr
  };

  const [settings, setSettings] = useState<ReportSettings>({
    title: 'OSINT (Open Source Intelligence)',
    subject: getDefaultSubject(),
    jid: '',
    documentNumber: '',
    location: 'Pardubice',
    date: new Date().toLocaleDateString('cs-CZ', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }),
    department: `Odbor analytiky a kyber. kriminality\nOddělení kybernetické kriminality\nNa Spravedlnosti 2516, 530 48 Pardubice\n\nVyšetřovatel: ${caseData?.investigatorName || 'Nepřiřazeno'}`,
    purpose: 'Provést komplexní OSINT (Open Source Intelligence) analýzu zájmové osoby',
    selectedModules: [] as string[], // Explicitně definujeme jako pole stringů
    moduleOrder: [] as string[], // Explicitně definujeme jako pole stringů
    format: 'html',
    style: 'official'
  });

  const handleModuleToggle = (moduleId: string) => {
    setSettings(prev => {
      // Zajistíme, že selectedModules a moduleOrder jsou vždy pole
      const currentSelectedModules = Array.isArray(prev.selectedModules) ? prev.selectedModules : [];
      const currentModuleOrder = Array.isArray(prev.moduleOrder) ? prev.moduleOrder : [];

      const isSelected = currentSelectedModules.includes(moduleId);

      // Pokud modul byl vybrán, odstraníme ho ze seznamu
      // Pokud nebyl vybrán, přidáme ho do seznamu
      const newSelectedModules = isSelected
        ? currentSelectedModules.filter(id => id !== moduleId)
        : [...currentSelectedModules, moduleId];

      // Stejně upravíme pořadí modulů
      const newModuleOrder = isSelected
        ? currentModuleOrder.filter(id => id !== moduleId)
        : [...currentModuleOrder, moduleId];

      console.log(`Modul ${moduleId} byl ${isSelected ? 'odstraněn' : 'přidán'} do výběru. Celkem vybráno: ${newSelectedModules.length}`);
      console.log('Nové pořadí modulů:', newModuleOrder);

      return {
        ...prev,
        selectedModules: newSelectedModules,
        moduleOrder: newModuleOrder
      };
    });
  };

  const moveModule = (moduleId: string, direction: 'up' | 'down') => {
    setSettings(prev => {
      // Zajistíme, že moduleOrder je vždy pole
      const currentModuleOrder = Array.isArray(prev.moduleOrder) ? prev.moduleOrder : [];

      const currentIndex = currentModuleOrder.indexOf(moduleId);
      if (currentIndex === -1) return prev;

      const newOrder = [...currentModuleOrder];
      const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

      if (newIndex >= 0 && newIndex < newOrder.length) {
        [newOrder[currentIndex], newOrder[newIndex]] = [newOrder[newIndex], newOrder[currentIndex]];
      }

      console.log('Přesunuto pořadí modulu:', moduleId, 'směr:', direction, 'nové pořadí:', newOrder);
      return { ...prev, moduleOrder: newOrder };
    });
  };

  const generateReport = async () => {
    // Validace povinných polí
    if (!settings.subject.trim()) {
      toast({
        title: "Chybějící údaje",
        description: "Prosím vyplňte předmět analýzy",
        variant: "destructive"
      });
      return;
    }

    if (!settings.jid.trim()) {
      toast({
        title: "Chybějící údaje",
        description: "Prosím vyplňte JID",
        variant: "destructive"
      });
      return;
    }

    if (!settings.documentNumber.trim()) {
      toast({
        title: "Chybějící údaje",
        description: "Prosím vyplňte číslo jednací",
        variant: "destructive"
      });
      return;
    }

    if (reportType === 'partial' && settings.selectedModules.length === 0) {
      toast({
        title: "Chybějící údaje",
        description: "Prosím vyberte alespoň jeden modul pro částečný report",
        variant: "destructive"
      });
      return;
    }

    // Definujeme kroky procesu
    const steps = [
      'Validace dat',
      'Připojení k serveru',
      'Načítání modulů',
      'Generování obsahu',
      settings.format === 'pdf' ? 'Vytváření PDF' : 'Formátování dokumentu',
      'Dokončování reportu'
    ];

    try {
      // Spuštění loading state
      startLoading();

      console.log(`Generuji report typu: ${reportType}`);
      console.log(`Vybrané moduly (${settings.selectedModules.length}):`, settings.selectedModules);
      console.log(`Pořadí modulů (${settings.moduleOrder.length}):`, settings.moduleOrder);

      // Krok 1: Validace dat
      setCurrentStep(0);
      updateProgress(10);
      await new Promise(resolve => setTimeout(resolve, 300));

      // Připravíme data pro API
      const requestData = {
        caseId,
        reportType,
        settings: {
          ...settings,
          moduleOrder: Array.isArray(settings.moduleOrder) ? settings.moduleOrder : [],
          selectedModules: Array.isArray(settings.selectedModules) ? settings.selectedModules : []
        },
      };

      console.log("Odesílaná data:", {
        caseId,
        reportType,
        selectedModules: requestData.settings.selectedModules,
        moduleOrder: requestData.settings.moduleOrder
      });

      // Krok 2: Připojení k serveru
      nextStep();
      updateProgress(25);
      await new Promise(resolve => setTimeout(resolve, 200));

      console.log("Odesílám požadavek na API:", JSON.stringify(requestData));

      const response = await fetch('/api/reports/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        let errorMessage = 'Chyba při generování reportu';
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          // Pokud nelze parsovat JSON, použijeme výchozí chybovou zprávu
        }
        throw new Error(errorMessage);
      }

      // Krok 3: Načítání modulů
      nextStep();
      updateProgress(50);
      await new Promise(resolve => setTimeout(resolve, 300));

      // Krok 4: Generování obsahu
      nextStep();
      updateProgress(70);

      // Simulace postupu generování obsahu
      for (let i = 70; i <= 85; i += 5) {
        updateProgress(i);
        await new Promise(resolve => setTimeout(resolve, 200));
      }

      // Krok 5: Vytváření finálního souboru
      nextStep();
      updateProgress(90);

      // Stáhneme soubor pro všechny formáty
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;

      // Nastavíme správnou příponu podle formátu
      const extension = settings.format === 'word' ? 'doc' : settings.format;
      a.download = `osint-report-${caseId}-${Date.now()}.${extension}`;

      // Krok 6: Dokončování
      nextStep();
      updateProgress(100);

      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      // Počkáme chvíli pro vizuální efekt dokončení
      await new Promise(resolve => setTimeout(resolve, 500));

      // Ukončíme loading
      stopLoading();

      // Zobrazíme toast s informací o úspěšném generování reportu
      toast({
        title: "Report vygenerován",
        description: `Report ve formátu ${settings.format.toUpperCase()} byl úspěšně vygenerován a stažen.`,
      });

      setIsOpen(false);
    } catch (error) {
      console.error('Chyba při generování reportu:', error);

      // Ukončíme loading
      stopLoading();

      // Zobrazíme toast s chybovou zprávou
      toast({
        title: "Chyba při generování reportu",
        description: error instanceof Error ? error.message : 'Neznámá chyba',
        variant: "destructive"
      });
    }
  };

  const modulesWithData = availableModules.filter(module => module.hasData);

  return (
    <div className="flex gap-2">
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogTrigger asChild>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <FileText className="w-4 h-4 mr-2" />
            Generovat Report
          </Button>
        </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="w-5 h-5" />
            Generování OSINT Reportu
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Typ reportu */}
          <Card>
            <CardHeader>
              <CardTitle>Typ reportu</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-4">
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="reportType"
                    value="full"
                    checked={reportType === 'full'}
                    onChange={(e) => setReportType(e.target.value as 'full' | 'partial')}
                    className="w-4 h-4"
                  />
                  <span>Celkový report (všechny moduly s daty)</span>
                </label>
                <label className="flex items-center space-x-2 cursor-pointer">
                  <input
                    type="radio"
                    name="reportType"
                    value="partial"
                    checked={reportType === 'partial'}
                    onChange={(e) => setReportType(e.target.value as 'full' | 'partial')}
                    className="w-4 h-4"
                  />
                  <span>Částečný report (vybrané moduly)</span>
                </label>
              </div>
            </CardContent>
          </Card>

          {/* Základní informace */}
          <Card>
            <CardHeader>
              <CardTitle>Základní informace reportu</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-gray-600 mb-4">
                <span className="text-red-500">*</span> Povinná pole
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="subject">Předmět analýzy <span className="text-red-500">*</span></Label>
                  {caseData?.subjects && caseData.subjects.length > 1 ? (
                    <Select
                      value={settings.subject}
                      onValueChange={(value) => setSettings(prev => ({ ...prev, subject: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Vyberte subjekt analýzy" />
                      </SelectTrigger>
                      <SelectContent>
                        {caseData.subjects.map((subject) => (
                          <SelectItem
                            key={subject.id}
                            value={subject.type === 'physical'
                              ? `${subject.firstName || ''} ${subject.lastName || ''}`.trim()
                              : subject.name || ''
                            }
                          >
                            {subject.type === 'physical'
                              ? `${subject.firstName || ''} ${subject.lastName || ''}`.trim()
                              : subject.name || ''
                            } ({subject.type === 'physical' ? 'FO' : 'PO'})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <Input
                      id="subject"
                      value={settings.subject}
                      onChange={(e) => setSettings(prev => ({ ...prev, subject: e.target.value }))}
                      placeholder="Jméno, příjmení, datum narození"
                    />
                  )}
                </div>
                <div>
                  <Label htmlFor="jid">JID <span className="text-red-500">*</span></Label>
                  <Input
                    id="jid"
                    value={settings.jid}
                    onChange={(e) => setSettings(prev => ({ ...prev, jid: e.target.value }))}
                    placeholder="Vyplňte JID"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="documentNumber">Číslo jednací <span className="text-red-500">*</span></Label>
                  <Input
                    id="documentNumber"
                    value={settings.documentNumber}
                    onChange={(e) => setSettings(prev => ({ ...prev, documentNumber: e.target.value }))}
                    placeholder="Doplňte č.j."
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="location">Místo</Label>
                  <Input
                    id="location"
                    value={settings.location}
                    onChange={(e) => setSettings(prev => ({ ...prev, location: e.target.value }))}
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="purpose">Cíl protokolu</Label>
                <Textarea
                  id="purpose"
                  value={settings.purpose}
                  onChange={(e) => setSettings(prev => ({ ...prev, purpose: e.target.value }))}
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Výběr modulů (pouze pro částečný report) */}
          {reportType === 'partial' && (
            <Card>
              <CardHeader>
                <CardTitle>Výběr modulů</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-sm text-gray-600">
                    Dostupné moduly s daty: {modulesWithData.length}
                  </div>
                  <div className="grid grid-cols-2 gap-2">
                    {modulesWithData.map((module) => (
                      <div key={module.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={module.id}
                          checked={settings.selectedModules.includes(module.id)}
                          onCheckedChange={() => handleModuleToggle(module.id)}
                        />
                        <Label htmlFor={module.id} className="flex items-center gap-2 cursor-pointer">
                          <span className={module.icon} />
                          {module.name}
                          <Badge variant="secondary">{module.category}</Badge>
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Pořadí modulů (pouze pro částečný report) */}
          {reportType === 'partial' && settings.selectedModules.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Pořadí modulů v reportu</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {settings.moduleOrder.map((moduleId, index) => {
                    const module = availableModules.find(m => m.id === moduleId);
                    if (!module) return null;

                    return (
                      <div key={moduleId} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          <span className="text-sm font-medium text-gray-500">
                            {index + 1}.
                          </span>
                          <span className={module.icon} />
                          <span>{module.name}</span>
                          <Badge variant="outline">{module.category}</Badge>
                        </div>
                        <div className="flex items-center gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => moveModule(moduleId, 'up')}
                            disabled={index === 0}
                          >
                            <ArrowUp className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => moveModule(moduleId, 'down')}
                            disabled={index === settings.moduleOrder.length - 1}
                          >
                            <ArrowDown className="w-4 h-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleModuleToggle(moduleId)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Formát výstupu */}
          <Card>
            <CardHeader>
              <CardTitle>Formát a vzhled výstupu</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="format">Formát souboru</Label>
                <Select
                  value={settings.format}
                  onValueChange={(value: 'html' | 'word' | 'pdf') =>
                    setSettings(prev => ({ ...prev, format: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="html">HTML (webová stránka)</SelectItem>
                    <SelectItem value="word">Word dokument (.docx)</SelectItem>
                    <SelectItem value="pdf">PDF dokument</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="style">Styl reportu</Label>
                <Select
                  value={settings.style}
                  onValueChange={(value: 'official' | 'modern') =>
                    setSettings(prev => ({ ...prev, style: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="official">
                      <div className="flex flex-col items-start">
                        <span className="font-medium">Oficiální (Byrokratický)</span>
                        <span className="text-sm text-gray-600">Formální styl pro úřední použití</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="modern">
                      <div className="flex flex-col items-start">
                        <span className="font-medium">Moderní (Analytický)</span>
                        <span className="text-sm text-gray-600">Moderní design s vizualizacemi</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Akce */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => setIsOpen(false)}
              disabled={isLoading}
            >
              Zrušit
            </Button>
            <LoadingButton
              onClick={generateReport}
              isLoading={isLoading}
              loadingText="Generuji..."
              disabled={reportType === 'partial' && settings.selectedModules.length === 0}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Download className="w-4 h-4 mr-2" />
              Generovat Report
            </LoadingButton>
          </div>
        </div>
      </DialogContent>

      {/* Loading Overlay */}
      <LoadingOverlay
        isVisible={isLoading}
        title="Generování OSINT reportu"
        description={`Probíhá vytváření ${settings.format.toUpperCase()} reportu pro případ ${caseId}`}
        progress={progress}
        steps={[
          'Validace dat',
          'Připojení k serveru',
          'Načítání modulů',
          'Generování obsahu',
          settings.format === 'pdf' ? 'Vytváření PDF' : 'Formátování dokumentu',
          'Dokončování reportu'
        ]}
        currentStep={currentStep}
      />
    </Dialog>

    <PDFEditor caseId={caseId} />
    </div>
  );
}
