/**
 * Finanční monitoring - funkce pro vyhledávání majetku a datov<PERSON>ch schr<PERSON>
 */

/**
 * <PERSON>yhledání majetku
 */
function searchProperty() {
    console.log('Vyhledání majetku');
    
    // Najít aktuální modul finančního monitoringu
    const financialModule = document.querySelector('.module[id^="module-financni-monitoring"]');
    if (!financialModule) {
        console.error('Modul finančního monitoringu nebyl nalezen');
        return;
    }
    
    // Získání IČO z tlačítka
    const button = financialModule.querySelector('.search-property');
    if (!button) {
        console.error('Tlačítko pro vyhledání majetku nebylo nalezeno');
        return;
    }
    
    const ico = button.getAttribute('data-ico');
    if (!ico) {
        alert('Nebylo nalezeno IČO firmy.');
        return;
    }
    
    // Zobrazení načítání
    const resultsContainer = financialModule.querySelector('#property-search-results');
    if (!resultsContainer) {
        console.error('Kontejner pro výsledky vyhledávání majetku nebyl nalezen');
        return;
    }
    
    resultsContainer.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Vyhledávání majetku...</span>
        </div>
    `;
    resultsContainer.style.display = 'block';
    
    // Vyhledání majetku
    fetchPropertyData(ico)
        .then(data => {
            displayPropertyData(data);
        })
        .catch(error => {
            console.error('Chyba při vyhledávání majetku:', error);
            resultsContainer.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>Nepodařilo se vyhledat majetek. Zkuste to prosím znovu.</span>
                </div>
            `;
        });
}

/**
 * Získání dat o majetku
 * @param {string} ico - IČO firmy
 * @returns {Promise<Object>} - Data o majetku
 */
function fetchPropertyData(ico) {
    console.log('Získání dat o majetku pro IČO:', ico);
    
    // Vyhledání v katastru nemovitostí
    const cadastrePromise = fetch(`https://nahlizenidokn.cuzk.cz/VyberParcelu/Vyhledat?searchType=Subjekt&subjekt=${ico}`)
        .then(response => {
            // Poznámka: ČÚZK nemá veřejné API, toto je pouze ukázka
            // V reálné implementaci by bylo potřeba použít web scraping nebo placené API
            return { cadastre: true };
        })
        .catch(() => {
            return { cadastre: false };
        });
    
    // Vyhledání vozidel
    const vehiclePromise = fetch(`https://www.mvcr.cz/clanek/centralni-registr-vozidel.aspx?q=${ico}`)
        .then(response => {
            // Poznámka: Centrální registr vozidel nemá veřejné API, toto je pouze ukázka
            // V reálné implementaci by bylo potřeba použít web scraping nebo placené API
            return { vehicles: false };
        })
        .catch(() => {
            return { vehicles: false };
        });
    
    // Vyhledání ochranných známek
    const trademarkPromise = fetch(`https://isdv.upv.cz/webapp/webapp.ozs.lst?pozk=${ico}`)
        .then(response => {
            // Poznámka: ÚPV nemá veřejné API, toto je pouze ukázka
            // V reálné implementaci by bylo potřeba použít web scraping nebo placené API
            return { trademarks: true };
        })
        .catch(() => {
            return { trademarks: false };
        });
    
    // Vrácení všech dat o majetku
    return Promise.all([cadastrePromise, vehiclePromise, trademarkPromise])
        .then(([cadastreData, vehicleData, trademarkData]) => {
            return {
                ico: ico,
                cadastre: cadastreData.cadastre,
                vehicles: vehicleData.vehicles,
                trademarks: trademarkData.trademarks,
                // Odkazy na externí služby
                cadastreUrl: `https://nahlizenidokn.cuzk.cz/VyberParcelu/Vyhledat?searchType=Subjekt&subjekt=${ico}`,
                vehicleUrl: `https://www.mvcr.cz/clanek/centralni-registr-vozidel.aspx`,
                trademarkUrl: `https://isdv.upv.cz/webapp/webapp.ozs.lst?pozk=${ico}`
            };
        });
}

/**
 * Zobrazení dat o majetku
 * @param {Object} data - Data o majetku
 */
function displayPropertyData(data) {
    console.log('Zobrazení dat o majetku:', data);
    
    // Najít aktuální modul finančního monitoringu
    const financialModule = document.querySelector('.module[id^="module-financni-monitoring"]');
    if (!financialModule) {
        console.error('Modul finančního monitoringu nebyl nalezen');
        return;
    }
    
    const resultsContainer = financialModule.querySelector('#property-search-results');
    if (!resultsContainer) {
        console.error('Kontejner pro výsledky vyhledávání majetku nebyl nalezen');
        return;
    }
    
    // Zobrazení výsledků
    let html = `
        <div class="results-header">
            <h4>Majetek - IČO: ${data.ico}</h4>
        </div>
        <div class="property-data">
            <div class="property-data-section">
                <h5>Nemovitosti</h5>
                <p>Pro vyhledání nemovitostí v katastru nemovitostí použijte následující odkaz:</p>
                <div class="external-links">
                    <a href="${data.cadastreUrl}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Katastr nemovitostí
                    </a>
                </div>
            </div>
            
            <div class="property-data-section">
                <h5>Vozidla</h5>
                <p>Informace o vozidlech nejsou veřejně dostupné. Pro více informací kontaktujte:</p>
                <div class="external-links">
                    <a href="${data.vehicleUrl}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Centrální registr vozidel
                    </a>
                </div>
            </div>
            
            <div class="property-data-section">
                <h5>Ochranné známky a patenty</h5>
                <p>Pro vyhledání ochranných známek a patentů použijte následující odkaz:</p>
                <div class="external-links">
                    <a href="${data.trademarkUrl}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Úřad průmyslového vlastnictví
                    </a>
                </div>
            </div>
            
            <div class="property-data-section">
                <h5>Další majetek</h5>
                <p>Pro vyhledání dalšího majetku můžete použít následující služby:</p>
                <div class="external-links">
                    <a href="https://www.centralniadresa.cz/cadr/cadr02003Prepare.do?znacka=${data.ico}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Centrální adresa (dražby)
                    </a>
                    <a href="https://www.justice.cz/web/msp/vysledky-vyhledavani?query=${data.ico}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Rejstřík zástav
                    </a>
                </div>
            </div>
        </div>
    `;
    
    resultsContainer.innerHTML = html;
}

/**
 * Vyhledání datové schránky
 */
function searchDatabox() {
    console.log('Vyhledání datové schránky');
    
    // Najít aktuální modul finančního monitoringu
    const financialModule = document.querySelector('.module[id^="module-financni-monitoring"]');
    if (!financialModule) {
        console.error('Modul finančního monitoringu nebyl nalezen');
        return;
    }
    
    // Získání IČO z tlačítka
    const button = financialModule.querySelector('.search-databox');
    if (!button) {
        console.error('Tlačítko pro vyhledání datové schránky nebylo nalezeno');
        return;
    }
    
    const ico = button.getAttribute('data-ico');
    if (!ico) {
        alert('Nebylo nalezeno IČO firmy.');
        return;
    }
    
    // Zobrazení načítání
    const resultsContainer = financialModule.querySelector('#databox-search-results');
    if (!resultsContainer) {
        console.error('Kontejner pro výsledky vyhledávání datové schránky nebyl nalezen');
        return;
    }
    
    resultsContainer.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Vyhledávání datové schránky...</span>
        </div>
    `;
    resultsContainer.style.display = 'block';
    
    // Vyhledání datové schránky
    fetchDataboxData(ico)
        .then(data => {
            displayDataboxData(data);
        })
        .catch(error => {
            console.error('Chyba při vyhledávání datové schránky:', error);
            resultsContainer.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>Nepodařilo se vyhledat datovou schránku. Zkuste to prosím znovu.</span>
                </div>
            `;
        });
}

/**
 * Získání dat o datové schránce
 * @param {string} ico - IČO firmy
 * @returns {Promise<Object>} - Data o datové schránce
 */
function fetchDataboxData(ico) {
    console.log('Získání dat o datové schránce pro IČO:', ico);
    
    // Vyhledání datové schránky
    return fetch(`https://www.mojedatovaschranka.cz/sds/searchForm.do?ico=${ico}`)
        .then(response => {
            // Poznámka: ISDS nemá veřejné API, toto je pouze ukázka
            // V reálné implementaci by bylo potřeba použít web scraping nebo placené API
            return {
                ico: ico,
                databoxId: 'Datová schránka nalezena',
                databoxUrl: `https://www.mojedatovaschranka.cz/sds/searchForm.do?ico=${ico}`
            };
        })
        .catch(error => {
            console.error('Chyba při získávání dat o datové schránce:', error);
            return {
                ico: ico,
                databoxId: 'Datová schránka nenalezena',
                databoxUrl: `https://www.mojedatovaschranka.cz/sds/searchForm.do?ico=${ico}`
            };
        });
}

/**
 * Zobrazení dat o datové schránce
 * @param {Object} data - Data o datové schránce
 */
function displayDataboxData(data) {
    console.log('Zobrazení dat o datové schránce:', data);
    
    // Najít aktuální modul finančního monitoringu
    const financialModule = document.querySelector('.module[id^="module-financni-monitoring"]');
    if (!financialModule) {
        console.error('Modul finančního monitoringu nebyl nalezen');
        return;
    }
    
    const resultsContainer = financialModule.querySelector('#databox-search-results');
    if (!resultsContainer) {
        console.error('Kontejner pro výsledky vyhledávání datové schránky nebyl nalezen');
        return;
    }
    
    // Zobrazení výsledků
    let html = `
        <div class="results-header">
            <h4>Datová schránka - IČO: ${data.ico}</h4>
        </div>
        <div class="databox-data">
            <div class="databox-data-section">
                <h5>Informace o datové schránce</h5>
                <p>Pro vyhledání datové schránky použijte následující odkaz:</p>
                <div class="external-links">
                    <a href="${data.databoxUrl}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Vyhledat datovou schránku
                    </a>
                </div>
            </div>
        </div>
    `;
    
    resultsContainer.innerHTML = html;
}

/**
 * Vyhledání veřejných zakázek
 */
function searchPublicContracts() {
    console.log('Vyhledání veřejných zakázek');
    
    // Najít aktuální modul finančního monitoringu
    const financialModule = document.querySelector('.module[id^="module-financni-monitoring"]');
    if (!financialModule) {
        console.error('Modul finančního monitoringu nebyl nalezen');
        return;
    }
    
    // Získání IČO z tlačítka
    const button = financialModule.querySelector('.search-public-contracts');
    if (!button) {
        console.error('Tlačítko pro vyhledání veřejných zakázek nebylo nalezeno');
        return;
    }
    
    const ico = button.getAttribute('data-ico');
    if (!ico) {
        alert('Nebylo nalezeno IČO firmy.');
        return;
    }
    
    // Zobrazení načítání
    const resultsContainer = financialModule.querySelector('#public-contracts-results');
    if (!resultsContainer) {
        console.error('Kontejner pro výsledky vyhledávání veřejných zakázek nebyl nalezen');
        return;
    }
    
    resultsContainer.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Vyhledávání veřejných zakázek...</span>
        </div>
    `;
    resultsContainer.style.display = 'block';
    
    // Vyhledání veřejných zakázek
    fetchPublicContractsData(ico)
        .then(data => {
            displayPublicContractsData(data);
        })
        .catch(error => {
            console.error('Chyba při vyhledávání veřejných zakázek:', error);
            resultsContainer.innerHTML = `
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>Nepodařilo se vyhledat veřejné zakázky. Zkuste to prosím znovu.</span>
                </div>
            `;
        });
}

/**
 * Získání dat o veřejných zakázkách
 * @param {string} ico - IČO firmy
 * @returns {Promise<Object>} - Data o veřejných zakázkách
 */
function fetchPublicContractsData(ico) {
    console.log('Získání dat o veřejných zakázkách pro IČO:', ico);
    
    // Vyhledání veřejných zakázek
    return fetch(`https://www.vestnikverejnychzakazek.cz/SearchForm/SearchContract?contractNumber=&contractName=&contractType=&cpvCode=&dateFrom=&dateTo=&price=&deadline=&documentType=&submitterName=&submitterIco=${ico}&sortBy=0`)
        .then(response => {
            // Poznámka: Věstník veřejných zakázek nemá veřejné API, toto je pouze ukázka
            // V reálné implementaci by bylo potřeba použít web scraping nebo placené API
            return {
                ico: ico,
                contractsUrl: `https://www.vestnikverejnychzakazek.cz/SearchForm/SearchContract?contractNumber=&contractName=&contractType=&cpvCode=&dateFrom=&dateTo=&price=&deadline=&documentType=&submitterName=&submitterIco=${ico}&sortBy=0`,
                registryUrl: `https://smlouvy.gov.cz/vyhledavani?q=${ico}`
            };
        })
        .catch(error => {
            console.error('Chyba při získávání dat o veřejných zakázkách:', error);
            return {
                ico: ico,
                contractsUrl: `https://www.vestnikverejnychzakazek.cz/SearchForm/SearchContract?contractNumber=&contractName=&contractType=&cpvCode=&dateFrom=&dateTo=&price=&deadline=&documentType=&submitterName=&submitterIco=${ico}&sortBy=0`,
                registryUrl: `https://smlouvy.gov.cz/vyhledavani?q=${ico}`
            };
        });
}

/**
 * Zobrazení dat o veřejných zakázkách
 * @param {Object} data - Data o veřejných zakázkách
 */
function displayPublicContractsData(data) {
    console.log('Zobrazení dat o veřejných zakázkách:', data);
    
    // Najít aktuální modul finančního monitoringu
    const financialModule = document.querySelector('.module[id^="module-financni-monitoring"]');
    if (!financialModule) {
        console.error('Modul finančního monitoringu nebyl nalezen');
        return;
    }
    
    const resultsContainer = financialModule.querySelector('#public-contracts-results');
    if (!resultsContainer) {
        console.error('Kontejner pro výsledky vyhledávání veřejných zakázek nebyl nalezen');
        return;
    }
    
    // Zobrazení výsledků
    let html = `
        <div class="results-header">
            <h4>Veřejné zakázky - IČO: ${data.ico}</h4>
        </div>
        <div class="public-contracts-data">
            <div class="public-contracts-data-section">
                <h5>Věstník veřejných zakázek</h5>
                <p>Pro vyhledání veřejných zakázek použijte následující odkaz:</p>
                <div class="external-links">
                    <a href="${data.contractsUrl}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Věstník veřejných zakázek
                    </a>
                </div>
            </div>
            
            <div class="public-contracts-data-section">
                <h5>Registr smluv</h5>
                <p>Pro vyhledání smluv v registru smluv použijte následující odkaz:</p>
                <div class="external-links">
                    <a href="${data.registryUrl}" target="_blank" class="btn-inline">
                        <i class="fas fa-external-link-alt"></i> Registr smluv
                    </a>
                </div>
            </div>
        </div>
    `;
    
    resultsContainer.innerHTML = html;
}

/**
 * Přidání subjektu do monitoringu
 */
function addToMonitoring() {
    console.log('Přidání subjektu do monitoringu');
    
    // Najít aktuální modul finančního monitoringu
    const financialModule = document.querySelector('.module[id^="module-financni-monitoring"]');
    if (!financialModule) {
        console.error('Modul finančního monitoringu nebyl nalezen');
        return;
    }
    
    // Získání IČO z tlačítka
    const button = financialModule.querySelector('.add-to-monitoring');
    if (!button) {
        console.error('Tlačítko pro přidání do monitoringu nebylo nalezeno');
        return;
    }
    
    const ico = button.getAttribute('data-ico');
    if (!ico) {
        alert('Nebylo nalezeno IČO firmy.');
        return;
    }
    
    // Přidání subjektu do monitoringu
    const monitoringList = financialModule.querySelector('#monitoring-list');
    if (!monitoringList) {
        console.error('Seznam monitorovaných subjektů nebyl nalezen');
        return;
    }
    
    // Kontrola, zda subjekt již není v monitoringu
    const existingItem = monitoringList.querySelector(`.monitoring-item[data-ico="${ico}"]`);
    if (existingItem) {
        alert('Subjekt je již v monitoringu.');
        return;
    }
    
    // Získání názvu firmy
    const companyName = financialModule.querySelector('.company-detail-name');
    if (!companyName) {
        console.error('Název firmy nebyl nalezen');
        return;
    }
    
    // Přidání subjektu do monitoringu
    const monitoringItem = document.createElement('div');
    monitoringItem.className = 'monitoring-item';
    monitoringItem.setAttribute('data-ico', ico);
    monitoringItem.innerHTML = `
        <div class="monitoring-item-name">${companyName.textContent}</div>
        <div class="monitoring-item-ico">IČO: ${ico}</div>
        <div class="monitoring-item-actions">
            <button type="button" class="btn-inline monitoring-item-remove" data-ico="${ico}">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    
    monitoringList.appendChild(monitoringItem);
    
    // Přidání event listeneru pro tlačítko odstranění
    monitoringItem.querySelector('.monitoring-item-remove').addEventListener('click', function() {
        monitoringItem.remove();
    });
    
    // Zobrazení seznamu monitorovaných subjektů
    const monitoringContainer = financialModule.querySelector('#monitoring-container');
    if (monitoringContainer) {
        monitoringContainer.style.display = 'block';
    }
    
    alert('Subjekt byl přidán do monitoringu.');
}
