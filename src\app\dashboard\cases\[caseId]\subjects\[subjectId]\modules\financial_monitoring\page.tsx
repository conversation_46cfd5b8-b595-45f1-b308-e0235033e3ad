"use client";

import { useEffect, useState } from 'react';
import { FinancialMonitoringForm } from '@/components/modules/financial-monitoring/FinancialMonitoringForm';
import { Button } from '@/components/ui/button';
import { ChevronLeft, DollarSign } from 'lucide-react';
import Link from 'next/link';
import { doc, getDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { PhysicalPersonSubject, LegalEntitySubject, FinancialMonitoringModuleData } from '@/types';
import { useRouter, useParams } from 'next/navigation';
import { toast } from '@/hooks/use-toast';

export default function FinancialMonitoringPage() {
  const params = useParams();
  const caseId = params.caseId as string;
  const subjectId = params.subjectId as string;

  const router = useRouter();
  const [subject, setSubject] = useState<PhysicalPersonSubject | LegalEntitySubject | null>(null);
  const [existingData, setExistingData] = useState<FinancialMonitoringModuleData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        // Načtení informací o subjektu
        const subjectDoc = await getDoc(doc(db, 'cases', caseId, 'subjects', subjectId));
        if (subjectDoc.exists()) {
          setSubject(subjectDoc.data() as PhysicalPersonSubject | LegalEntitySubject);
        }

        // Načtení existujících dat modulu
        const moduleDoc = await getDoc(doc(db, 'cases', caseId, 'subjects', subjectId, 'modules', 'financial_monitoring'));
        if (moduleDoc.exists()) {
          setExistingData(moduleDoc.data() as FinancialMonitoringModuleData);
        }

      } catch (error) {
        console.error('Chyba při načítání dat:', error);
        toast({
          title: "Chyba",
          description: "Nepodařilo se načíst data. Zkuste to prosím znovu.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [caseId, subjectId]);

  const handleSave = (moduleId: string, wasNew: boolean) => {
    toast({
      title: wasNew ? "Modul vytvořen" : "Modul aktualizován",
      description: `Finanční monitoring byl úspěšně ${wasNew ? 'vytvořen' : 'aktualizován'}.`,
    });
    
    // Přesměrování zpět na detail subjektu
    router.push(`/dashboard/cases/${caseId}/subjects/${subjectId}`);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <DollarSign className="mx-auto h-8 w-8 animate-spin text-primary" />
          <p className="mt-2 text-sm text-muted-foreground">Načítám data modulu...</p>
        </div>
      </div>
    );
  }

  if (!subject) {
    return (
      <div className="text-center py-8">
        <p className="text-destructive">Subjekt nebyl nalezen.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4 mb-6">
        <Button variant="ghost" size="sm" asChild>
          <Link href={`/dashboard/cases/${caseId}/subjects/${subjectId}`}>
            <ChevronLeft className="h-4 w-4 mr-1" />
            Zpět na detail subjektu
          </Link>
        </Button>
        <div className="flex items-center space-x-2">
          <DollarSign className="h-5 w-5 text-primary" />
          <h1 className="text-2xl font-bold">Finanční monitoring</h1>
        </div>
      </div>

      <FinancialMonitoringForm
        caseId={caseId}
        subject={subject}
        existingData={existingData}
        onSave={handleSave}
      />
    </div>
  );
} 