"use client";

import { cn } from '@/lib/utils';
import { FormField, FormControl, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import * as z from 'zod';

const photoMetadataSchema = z.object({
  id: z.string().optional(),
  fileName: z.string().optional().default(''),
  downloadURL: z.string().optional().default(''),
  description: z.string().optional().default(''),
  dateTaken: z.string().optional().default(''),
  sourceURL: z.string().optional().default(''),
  storagePath: z.string().optional().default(''),
});

const vehicleRecordSchema = z.object({
  id: z.string(),
  relationshipType: z.enum(["owner", "user", "former", "other"], {
    required_error: "Typ vztahu k vozidlu je povinný.",
  }),
  otherRelationshipDetail: z.string().optional(),
  make: z.string().optional(),
  model: z.string().optional(),
  licensePlate: z.string().optional(),
  vin: z.string().optional(),
  color: z.string().optional(),
  yearManufactured: z.number().nullable().optional().default(null).transform(val => val === 0 ? null : val),
  stkValidUntil: z.string().optional(),
  firstRegistered: z.string().optional(),
  notes: z.string().optional(),
  photos: z.array(photoMetadataSchema).optional().default([]),
}).superRefine((data, ctx) => {
  if (data.relationshipType === 'other' && !data.otherRelationshipDetail?.trim()) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Upřesnění vztahu je povinné, pokud je vybrán typ 'Jiný'.",
      path: ['otherRelationshipDetail'],
    });
  }
});

const vehiclesModuleSchema = z.object({
  vehicles: z.array(vehicleRecordSchema).optional().default([]),
});

export { vehiclesModuleSchema };
export type VehiclesFormValues = z.infer<typeof vehiclesModuleSchema>;

// Pomocné komponenty pro formulář
interface FormItemRHFProps {
  label: string; 
  name: string; 
  control: any; 
  placeholder?: string;
  type?: string; 
  disabled?: boolean; 
  className?: string;
  as?: 'input' | 'textarea'; 
  rows?: number; 
  smallLabel?: boolean;
}

export const FormItemRHF = ({ 
  label, 
  name, 
  control, 
  placeholder, 
  type = "text", 
  disabled = false, 
  className, 
  as = 'input', 
  rows, 
  smallLabel 
}: FormItemRHFProps) => (
  <FormField
    control={control} 
    name={name as any} 
    disabled={disabled}
    render={({ field, fieldState: { error } }) => (
      <FormItem className={cn("w-full", className)}>
        <FormLabel className={cn("font-semibold", smallLabel ? "text-xs" : "text-sm")}>
          {label}
        </FormLabel>
        <FormControl>
          {as === 'input' ? (
            <Input 
              {...field} 
              value={field.value || ''} 
              placeholder={placeholder} 
              type={type} 
              className={cn(
                smallLabel ? "text-xs h-8 py-1" : "text-sm h-10", 
                error ? "border-destructive" : ""
              )} 
            />
          ) : (
            <Textarea 
              {...field} 
              value={field.value || ''} 
              placeholder={placeholder} 
              rows={rows} 
              className={cn(
                smallLabel ? "text-xs py-1" : "text-sm", 
                error ? "border-destructive" : ""
              )} 
            />
          )}
        </FormControl>
        {error && <FormMessage className="text-xs" />}
      </FormItem>
    )}
  />
);

interface FormItemSelectRHFProps {
  label: string; 
  name: string; 
  control: any; 
  placeholder?: string;
  options: {value: string; label: string}[];
  disabled?: boolean; 
  className?: string; 
  smallLabel?: boolean;
}

export const FormItemSelectRHF = ({ 
  label, 
  name, 
  control, 
  placeholder, 
  options, 
  disabled = false, 
  className, 
  smallLabel 
}: FormItemSelectRHFProps) => (
  <FormField
    control={control} 
    name={name as any} 
    disabled={disabled}
    render={({ field, fieldState: { error } }) => (
      <FormItem className={cn("w-full", className)}>
        <FormLabel className={cn("font-semibold", smallLabel ? "text-xs" : "text-sm")}>
          {label}
        </FormLabel>
        <Select onValueChange={field.onChange} value={field.value || undefined}>
          <FormControl>
            <SelectTrigger className={cn(
              smallLabel ? "text-xs h-8 py-1" : "text-sm h-10", 
              error ? "border-destructive" : ""
            )}>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
          </FormControl>
          <SelectContent>
            {options.map(opt => (
              <SelectItem key={opt.value} value={opt.value}>
                {opt.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {error && <FormMessage className="text-xs" />}
      </FormItem>
    )}
  />
); 