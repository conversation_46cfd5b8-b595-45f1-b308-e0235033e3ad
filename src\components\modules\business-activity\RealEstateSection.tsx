"use client";

import { useState } from "react";
import { useFieldArray, Control } from "react-hook-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Building2, PlusCircle, Trash2, Home, MapPin, Calendar } from "lucide-react";
import { FormItemRHF, FormItemSelectRHF } from "./FormComponents";
import { RealEstateRecord } from "./schemas";
import { cn } from "@/lib/utils";
import type { BusinessActivityFormValues } from "./BusinessActivityForm";

interface RealEstateSectionProps {
  control: Control<BusinessActivityFormValues>;
}

const realEstateTypeOptions = [
  { value: "residential", label: "Obytná nemovitost" },
  { value: "commercial", label: "Komerční nemovitost" },
  { value: "industrial", label: "Průmyslová nemovitost" },
  { value: "land", label: "Pozemek" },
  { value: "agricultural", label: "Zemědělsk<PERSON> půda" },
  { value: "other", label: "Jiné" },
];

export function RealEstateSection({ control }: RealEstateSectionProps) {
  const [expanded, setExpanded] = useState(false);

  const { fields, append, remove } = useFieldArray({
    control,
    name: "realEstateRecords",
  });

  const handleAddRealEstate = () => {
    append({
      id: crypto.randomUUID(),
      type: "residential",
      currency: "CZK",
    });
  };

  return (
    <Card className={cn(expanded ? "" : "hover:border-primary/50 cursor-pointer transition-all")}>
      <CardHeader 
        className="flex flex-row items-center justify-between"
        onClick={() => setExpanded(!expanded)}
      >
        <CardTitle className="text-lg flex items-center">
          <Building2 className="mr-2 h-5 w-5" />
          Nemovitosti
        </CardTitle>
        <Badge variant={fields.length > 0 ? "default" : "outline"}>
          {fields.length} nemovitostí
        </Badge>
      </CardHeader>
      {expanded && (
        <CardContent className="space-y-6 pt-0">
          {fields.map((item, index) => (
            <Card key={item.id} className="p-4 shadow-sm bg-card-foreground/5 relative">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => remove(index)}
                className="absolute top-2 right-2 text-destructive hover:bg-destructive/10"
              >
                <Trash2 className="h-5 w-5" />
              </Button>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormItemSelectRHF
                  label="Typ nemovitosti"
                  name={`realEstateRecords.${index}.type`}
                  control={control}
                  options={realEstateTypeOptions}
                  placeholder="-- Vyberte typ --"
                />
                {item.type === "other" && (
                  <FormItemRHF
                    label="Upřesnění typu"
                    name={`realEstateRecords.${index}.otherTypeDetail`}
                    control={control}
                    placeholder="Upřesněte typ nemovitosti"
                  />
                )}
              </div>

              <FormItemRHF
                label="Adresa"
                name={`realEstateRecords.${index}.address`}
                control={control}
                placeholder="Adresa nemovitosti"
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <FormItemRHF
                  label="Katastrální území"
                  name={`realEstateRecords.${index}.cadastralArea`}
                  control={control}
                  placeholder="Katastrální území"
                />
                <FormItemRHF
                  label="Parcelní číslo"
                  name={`realEstateRecords.${index}.parcelNumber`}
                  control={control}
                  placeholder="Parcelní číslo"
                />
                <FormItemRHF
                  label="Číslo budovy"
                  name={`realEstateRecords.${index}.buildingNumber`}
                  control={control}
                  placeholder="Číslo budovy"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormItemRHF
                  label="Vlastnictví"
                  name={`realEstateRecords.${index}.ownership`}
                  control={control}
                  placeholder="Např. plné, podílové (50%)"
                />
                <FormItemRHF
                  label="Datum nabytí"
                  name={`realEstateRecords.${index}.acquisitionDate`}
                  control={control}
                  type="date"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <FormItemRHF
                  label="Tržní hodnota"
                  name={`realEstateRecords.${index}.marketValue`}
                  control={control}
                  type="number"
                  placeholder="Odhadovaná tržní hodnota"
                  className="md:col-span-2"
                />
                <FormItemRHF
                  label="Měna"
                  name={`realEstateRecords.${index}.currency`}
                  control={control}
                  placeholder="CZK"
                />
              </div>

              <FormItemRHF
                label="Informace o hypotéce"
                name={`realEstateRecords.${index}.mortgageInfo`}
                control={control}
                as="textarea"
                rows={2}
                placeholder="Informace o případné hypotéce nebo jiném zatížení..."
              />

              <FormItemRHF
                label="Popis využití"
                name={`realEstateRecords.${index}.usageDescription`}
                control={control}
                as="textarea"
                rows={2}
                placeholder="Popis využití nemovitosti..."
              />

              <FormItemRHF
                label="Poznámky"
                name={`realEstateRecords.${index}.notes`}
                control={control}
                as="textarea"
                rows={2}
                placeholder="Další poznámky k nemovitosti..."
              />
            </Card>
          ))}

          <Button
            type="button"
            variant="outline"
            onClick={handleAddRealEstate}
            className="w-full"
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Přidat nemovitost
          </Button>
        </CardContent>
      )}
    </Card>
  );
}
