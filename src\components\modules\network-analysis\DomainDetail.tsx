"use client";

import { Control } from "react-hook-form";
import { NetworkAnalysisModuleFormValues } from "./schemas";
import { IpAddressRecord } from "@/types";
import { Card, CardContent } from "@/components/ui/card";
import { Server } from "lucide-react";

interface DomainDetailProps {
  control: Control<NetworkAnalysisModuleFormValues>;
  domainIndex: number;
  ipAddresses: IpAddressRecord[];
}

export function DomainDetail({ control, domainIndex, ipAddresses }: DomainDetailProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center mb-4">
          <Server className="mr-2 h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold">Detail domény (Stub)</h3>
        </div>
        <p>Tato komponenta je zatím ve vývoji.</p>
      </CardContent>
    </Card>
  );
}
