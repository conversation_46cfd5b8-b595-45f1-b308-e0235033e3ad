"use client";

import { EmailRecord } from "@/types";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Edit, Trash2, Mail, Calendar, CheckCircle, XCircle, AlertCircle, 
  Shield, AlertTriangle, User, Building, Database
} from "lucide-react";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useState } from "react";

interface EmailsListProps {
  emails: EmailRecord[];
  onEdit: (id: string) => void;
  onDelete: (id: string) => void;
}

export function EmailsList({ emails, onEdit, onDelete }: EmailsListProps) {
  const [emailToDelete, setEmailToDelete] = useState<string | null>(null);
  
  const handleDeleteClick = (id: string) => {
    setEmailToDelete(id);
  };
  
  const handleConfirmDelete = () => {
    if (emailToDelete) {
      onDelete(emailToDelete);
      setEmailToDelete(null);
    }
  };
  
  const handleCancelDelete = () => {
    setEmailToDelete(null);
  };
  
  const getVerificationIcon = (status: string) => {
    switch (status) {
      case "verified":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "unverified":
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case "invalid":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "disposable":
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case "unknown":
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
      default:
        return null;
    }
  };
  
  const getVerificationText = (status: string) => {
    switch (status) {
      case "verified":
        return "Ověřeno";
      case "unverified":
        return "Neověřeno";
      case "invalid":
        return "Neplatné";
      case "disposable":
        return "Jednorázové";
      case "unknown":
        return "Neznámé";
      default:
        return status;
    }
  };
  
  const getSourceText = (source: string) => {
    switch (source) {
      case "osint":
        return "OSINT";
      case "investigation":
        return "Vyšetřování";
      case "public_data":
        return "Veřejná data";
      case "social_media":
        return "Sociální sítě";
      case "data_breach":
        return "Únik dat";
      case "direct_communication":
        return "Přímá komunikace";
      case "other":
        return "Jiný";
      default:
        return source;
    }
  };
  
  const getDataBreachSeverity = (email: EmailRecord) => {
    if (!email.dataBreaches || email.dataBreaches.length === 0) {
      return "none";
    }
    
    const count = email.dataBreaches.length;
    
    if (count >= 4) {
      return "critical";
    } else if (count >= 3) {
      return "high";
    } else if (count >= 2) {
      return "medium";
    } else {
      return "low";
    }
  };
  
  const getDataBreachIcon = (severity: string) => {
    switch (severity) {
      case "none":
        return <Shield className="h-4 w-4 text-green-500" />;
      case "low":
        return <Shield className="h-4 w-4 text-blue-500" />;
      case "medium":
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case "high":
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      case "critical":
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };
  
  const getDataBreachText = (severity: string) => {
    switch (severity) {
      case "none":
        return "Žádné";
      case "low":
        return "Nízké";
      case "medium":
        return "Střední";
      case "high":
        return "Vysoké";
      case "critical":
        return "Kritické";
      default:
        return "Neznámé";
    }
  };
  
  if (emails.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <Mail className="h-12 w-12 text-muted-foreground mb-4" />
        <h3 className="text-lg font-medium">Žádné emailové adresy</h3>
        <p className="text-sm text-muted-foreground mt-2">
          Zatím nebyly přidány žádné emailové adresy k analýze.
        </p>
      </div>
    );
  }
  
  return (
    <>
      <ScrollArea className="h-[500px]">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Emailová adresa</TableHead>
              <TableHead>Zdroj</TableHead>
              <TableHead>Jméno/Organizace</TableHead>
              <TableHead>Stav ověření</TableHead>
              <TableHead>Úniky dat</TableHead>
              <TableHead>Propojené profily</TableHead>
              <TableHead className="text-right">Akce</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {emails.map((email) => (
              <TableRow key={email.id}>
                <TableCell className="font-medium">{email.emailAddress}</TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {getSourceText(email.source)}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex flex-col">
                    {email.probableName && (
                      <div className="flex items-center">
                        <User className="h-4 w-4 mr-1" />
                        {email.probableName}
                      </div>
                    )}
                    {email.associatedOrganization && (
                      <div className="flex items-center text-muted-foreground text-xs mt-1">
                        <Building className="h-3 w-3 mr-1" />
                        {email.associatedOrganization}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    {getVerificationIcon(email.verificationStatus)}
                    <span className="ml-1">{getVerificationText(email.verificationStatus)}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {email.dataBreaches && email.dataBreaches.length > 0 ? (
                    <div className="flex items-center">
                      {getDataBreachIcon(getDataBreachSeverity(email))}
                      <span className="ml-1">{email.dataBreaches.length} ({getDataBreachText(getDataBreachSeverity(email))})</span>
                    </div>
                  ) : (
                    <span className="text-muted-foreground">Žádné</span>
                  )}
                </TableCell>
                <TableCell>
                  {email.connectedProfiles && email.connectedProfiles.length > 0 ? (
                    <div className="flex items-center">
                      <Database className="h-4 w-4 mr-1 text-blue-500" />
                      <span>{email.connectedProfiles.length}</span>
                    </div>
                  ) : (
                    <span className="text-muted-foreground">Žádné</span>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end space-x-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => onEdit(email.id)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => handleDeleteClick(email.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </ScrollArea>
      
      <AlertDialog open={!!emailToDelete} onOpenChange={() => setEmailToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Opravdu chcete smazat tento email?</AlertDialogTitle>
            <AlertDialogDescription>
              Tato akce je nevratná. Záznam o emailu bude trvale odstraněn.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={handleCancelDelete}>Zrušit</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmDelete} className="bg-destructive text-destructive-foreground">
              Smazat
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
