"use client";

import React, { useState } from "react";
import { UseFormReturn, useFieldArray } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MapOverlaysModuleFormValues, mapAreaTypes } from "../schemas";
import { MapArea } from "@/types";
import { Plus, Trash2, Search, Edit, Map, Calendar, Tag, FileText, Square, Pencil } from "lucide-react";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { PhotoUploadSection } from "../../network-analysis/PhotoUploadSection";

interface MapAreasTabProps {
  form: UseFormReturn<MapOverlaysModuleFormValues>;
}

export function MapAreasTab({ form }: MapAreasTabProps) {
  const [selectedAreaId, setSelectedAreaId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("list");

  const { control, register, watch } = form;
  const { fields, append, remove, update } = useFieldArray({
    control,
    name: "areas",
  });

  const areas = watch("areas") || [];
  const selectedAreaIndex = areas.findIndex(a => a.id === selectedAreaId);

  // Filtrování oblastí podle vyhledávacího výrazu
  const filteredAreas = areas.filter(area =>
    area.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (area.description && area.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (area.address && area.address.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Přidání nové oblasti
  const handleAddArea = () => {
    const newArea: MapArea = {
      id: uuidv4(),
      name: "Nová oblast",
      areaType: "general",
      coordinates: [
        { lat: 50.0755, lng: 14.4378 }, // Praha jako výchozí
        { lat: 50.0855, lng: 14.4378 },
        { lat: 50.0855, lng: 14.4478 },
        { lat: 50.0755, lng: 14.4478 },
      ],
      photos: [],
      relatedAreas: [],
      relatedSubjects: [],
    };
    append(newArea);
    setSelectedAreaId(newArea.id);
    setActiveTab("detail");
  };

  // Odstranění oblasti
  const handleRemoveArea = (index: number) => {
    if (window.confirm("Opravdu chcete smazat tuto oblast?")) {
      remove(index);
      if (selectedAreaId === areas[index].id) {
        setSelectedAreaId(null);
        setActiveTab("list");
      }
    }
  };

  // Výběr oblasti pro editaci
  const handleSelectArea = (id: string) => {
    setSelectedAreaId(id);
    setActiveTab("detail");
  };

  // Poslouchání události pro otevření detailu oblasti
  React.useEffect(() => {
    const handleOpenAreaDetail = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.areaId) {
        setSelectedAreaId(customEvent.detail.areaId);
        setActiveTab("detail");
      }
    };

    window.addEventListener('openAreaDetail', handleOpenAreaDetail);

    return () => {
      window.removeEventListener('openAreaDetail', handleOpenAreaDetail);
    };
  }, []);

  // Výpočet plochy oblasti v km²
  const calculateArea = (coordinates: Array<{lat: number, lng: number}>) => {
    if (coordinates.length < 3) return 0;

    // Jednoduchý výpočet plochy pomocí Gaussova vzorce
    let area = 0;
    for (let i = 0; i < coordinates.length; i++) {
      const j = (i + 1) % coordinates.length;
      area += coordinates[i].lng * coordinates[j].lat;
      area -= coordinates[j].lng * coordinates[i].lat;
    }
    area = Math.abs(area) / 2;

    // Převod na km² (velmi přibližný, nezohledňuje zakřivení Země)
    // Pro přesnější výpočet by bylo potřeba použít složitější vzorce
    const factor = 111.32 * 111.32; // přibližně 1 stupeň = 111.32 km na rovníku
    return (area * factor).toFixed(2);
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Oblasti na mapě</CardTitle>
              <CardDescription>
                Správa oblastí zájmu a jejich vlastností
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button onClick={handleAddArea}>
                <Plus className="mr-2 h-4 w-4" />
                Přidat oblast
              </Button>

              <Button
                variant="outline"
                onClick={() => {
                  // Přepneme na záložku s mapou
                  const tabsElement = document.querySelector('[role="tablist"]');
                  if (tabsElement) {
                    const viewTabButton = tabsElement.querySelector('[value="view"]') as HTMLButtonElement;
                    if (viewTabButton) {
                      viewTabButton.click();

                      // Spustíme režim kreslení oblasti s malým zpožděním, aby se mapa stihla načíst
                      setTimeout(() => {
                        // Zobrazíme uživateli instrukce
                        alert("Nyní můžete nakreslit oblast na mapě. Klikněte na mapu pro přidání bodů oblasti (min. 3 body). Po dokončení klikněte na tlačítko DOKONČIT OBLAST.");

                        // Spustíme režim kreslení oblasti
                        window.dispatchEvent(new CustomEvent('startDrawingArea'));
                      }, 300);
                    }
                  }
                }}
              >
                <Pencil className="mr-2 h-4 w-4" />
                Nakreslit oblast na mapě
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="list">Seznam oblastí</TabsTrigger>
              <TabsTrigger value="detail" disabled={selectedAreaId === null}>
                Detail oblasti
              </TabsTrigger>
            </TabsList>

            <TabsContent value="list" className="pt-4">
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Vyhledat oblasti..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                <ScrollArea className="h-[400px] rounded-md border">
                  {filteredAreas.length === 0 ? (
                    <div className="p-4 text-center text-muted-foreground">
                      Nebyly nalezeny žádné oblasti
                    </div>
                  ) : (
                    <div className="p-4 space-y-2">
                      {filteredAreas.map((area, index) => (
                        <div
                          key={area.id}
                          className="flex items-center justify-between p-2 rounded-md hover:bg-muted cursor-pointer"
                          onClick={() => handleSelectArea(area.id)}
                        >
                          <div className="flex items-center space-x-2">
                            <div className={`w-4 h-4 rounded-sm ${
                              area.areaType === "crime_area" ? "bg-red-500/30 border border-red-500" :
                              area.areaType === "surveillance" ? "bg-blue-500/30 border border-blue-500" :
                              area.areaType === "restricted" ? "bg-yellow-500/30 border border-yellow-500" :
                              area.areaType === "search" ? "bg-green-500/30 border border-green-500" :
                              area.areaType === "operation" ? "bg-purple-500/30 border border-purple-500" :
                              "bg-gray-500/30 border border-gray-500"
                            }`}></div>
                            <div>
                              <div className="font-medium">{area.name}</div>
                              <div className="text-xs text-muted-foreground">
                                {area.address || `${area.coordinates.length} bodů, ${calculateArea(area.coordinates)} km²`}
                              </div>
                            </div>
                          </div>
                          <div className="flex space-x-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleSelectArea(area.id);
                              }}
                            >
                              <Edit className="h-4 w-4 text-muted-foreground" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRemoveArea(index);
                              }}
                            >
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </div>
            </TabsContent>

            <TabsContent value="detail" className="pt-4">
              {selectedAreaId && selectedAreaIndex !== -1 && (
                <div className="space-y-4">
                  <Tabs defaultValue="basic">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="basic">Základní informace</TabsTrigger>
                      <TabsTrigger value="coordinates">Souřadnice</TabsTrigger>
                      <TabsTrigger value="photos">Fotodokumentace</TabsTrigger>
                    </TabsList>

                    <TabsContent value="basic" className="space-y-4 pt-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`areas.${selectedAreaIndex}.name`}>Název oblasti</Label>
                          <Input
                            id={`areas.${selectedAreaIndex}.name`}
                            {...register(`areas.${selectedAreaIndex}.name`)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`areas.${selectedAreaIndex}.areaType`}>Typ oblasti</Label>
                          <Select
                            defaultValue={areas[selectedAreaIndex].areaType}
                            onValueChange={(value) => {
                              const updatedArea = { ...areas[selectedAreaIndex], areaType: value as any };
                              update(selectedAreaIndex, updatedArea);
                            }}
                          >
                            <SelectTrigger id={`areas.${selectedAreaIndex}.areaType`}>
                              <SelectValue placeholder="Vyberte typ oblasti" />
                            </SelectTrigger>
                            <SelectContent>
                              {mapAreaTypes.map((type) => (
                                <SelectItem key={type} value={type}>
                                  {type === "general" ? "Obecná" :
                                   type === "crime_area" ? "Oblast trestné činnosti" :
                                   type === "surveillance" ? "Oblast sledování" :
                                   type === "restricted" ? "Zakázaná oblast" :
                                   type === "search" ? "Oblast pátrání" :
                                   type === "operation" ? "Operační oblast" :
                                   type === "other" ? "Jiná" : type}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      {areas[selectedAreaIndex].areaType === "other" && (
                        <div className="space-y-2">
                          <Label htmlFor={`areas.${selectedAreaIndex}.otherAreaTypeDetail`}>
                            Upřesnění typu
                          </Label>
                          <Input
                            id={`areas.${selectedAreaIndex}.otherAreaTypeDetail`}
                            {...register(`areas.${selectedAreaIndex}.otherAreaTypeDetail`)}
                            placeholder="Zadejte vlastní typ oblasti"
                          />
                        </div>
                      )}

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`areas.${selectedAreaIndex}.date`}>Datum</Label>
                          <Input
                            id={`areas.${selectedAreaIndex}.date`}
                            type="date"
                            {...register(`areas.${selectedAreaIndex}.date`)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`areas.${selectedAreaIndex}.category`}>Kategorie</Label>
                          <Input
                            id={`areas.${selectedAreaIndex}.category`}
                            {...register(`areas.${selectedAreaIndex}.category`)}
                            placeholder="Např. Důležitá, Podezřelá, ..."
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`areas.${selectedAreaIndex}.address`}>Adresa</Label>
                        <Input
                          id={`areas.${selectedAreaIndex}.address`}
                          {...register(`areas.${selectedAreaIndex}.address`)}
                          placeholder="Zadejte adresu oblasti"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`areas.${selectedAreaIndex}.description`}>Popis</Label>
                        <Textarea
                          id={`areas.${selectedAreaIndex}.description`}
                          {...register(`areas.${selectedAreaIndex}.description`)}
                          placeholder="Zadejte popis oblasti..."
                          rows={4}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`areas.${selectedAreaIndex}.notes`}>Poznámky</Label>
                        <Textarea
                          id={`areas.${selectedAreaIndex}.notes`}
                          {...register(`areas.${selectedAreaIndex}.notes`)}
                          placeholder="Zadejte poznámky k oblasti..."
                          rows={4}
                        />
                      </div>

                      <div className="p-4 bg-muted rounded-md">
                        <div className="flex items-center justify-between">
                          <div className="text-sm font-medium">Statistiky oblasti</div>
                          <Badge variant="outline">
                            {calculateArea(areas[selectedAreaIndex].coordinates)} km²
                          </Badge>
                        </div>
                        <div className="mt-2 text-xs text-muted-foreground">
                          Počet bodů: {areas[selectedAreaIndex].coordinates.length}
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="coordinates" className="space-y-4 pt-4">
                      <div className="w-full h-[200px] bg-muted rounded-md relative overflow-hidden">
                        <div
                          className="w-full h-full"
                          style={{
                            backgroundImage: `url(https://api.mapbox.com/styles/v1/mapbox/streets-v11/static/${areas[selectedAreaIndex].coordinates[0].lng},${areas[selectedAreaIndex].coordinates[0].lat},13,0/800x400?access_token=pk.eyJ1IjoiZGVtb3VzZXIiLCJhIjoiY2txOHkwdWUzMDkzcjJvcWk2ZzFzZ2h6ZSJ9.TxMGHqwYLO1QnfEUL8F7_A)`,
                            backgroundSize: 'cover',
                            backgroundPosition: 'center'
                          }}
                        >
                          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                            <Square className="h-8 w-8 text-blue-500" />
                          </div>
                        </div>
                      </div>

                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={() => {
                          // Přepneme na záložku s mapou
                          const tabsElement = document.querySelector('[role="tablist"]');
                          if (tabsElement) {
                            const viewTabButton = tabsElement.querySelector('[value="view"]') as HTMLButtonElement;
                            if (viewTabButton) {
                              viewTabButton.click();

                              // Vybereme oblast na mapě
                              setTimeout(() => {
                                window.dispatchEvent(new CustomEvent('selectAreaOnMap', {
                                  detail: { areaId: areas[selectedAreaIndex].id }
                                }));
                              }, 100);
                            }
                          }
                        }}
                      >
                        <Map className="mr-2 h-4 w-4" />
                        Zobrazit oblast na mapě
                      </Button>

                      <ScrollArea className="h-[200px] rounded-md border">
                        <div className="p-4 space-y-2">
                          {areas[selectedAreaIndex].coordinates.map((coord, coordIndex) => (
                            <div key={coordIndex} className="flex items-center space-x-2">
                              <div className="font-mono text-xs">
                                Bod {coordIndex + 1}:
                              </div>
                              <div className="flex-1 grid grid-cols-2 gap-2">
                                <Input
                                  value={coord.lat}
                                  onChange={(e) => {
                                    const newCoordinates = [...areas[selectedAreaIndex].coordinates];
                                    newCoordinates[coordIndex] = {
                                      ...newCoordinates[coordIndex],
                                      lat: parseFloat(e.target.value) || 0,
                                    };
                                    const updatedArea = {
                                      ...areas[selectedAreaIndex],
                                      coordinates: newCoordinates,
                                    };
                                    update(selectedAreaIndex, updatedArea);
                                  }}
                                  placeholder="Šířka"
                                  type="number"
                                  step="0.000001"
                                />
                                <Input
                                  value={coord.lng}
                                  onChange={(e) => {
                                    const newCoordinates = [...areas[selectedAreaIndex].coordinates];
                                    newCoordinates[coordIndex] = {
                                      ...newCoordinates[coordIndex],
                                      lng: parseFloat(e.target.value) || 0,
                                    };
                                    const updatedArea = {
                                      ...areas[selectedAreaIndex],
                                      coordinates: newCoordinates,
                                    };
                                    update(selectedAreaIndex, updatedArea);
                                  }}
                                  placeholder="Délka"
                                  type="number"
                                  step="0.000001"
                                />
                              </div>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => {
                                  if (areas[selectedAreaIndex].coordinates.length <= 3) {
                                    alert("Oblast musí mít alespoň 3 body");
                                    return;
                                  }
                                  const newCoordinates = [...areas[selectedAreaIndex].coordinates];
                                  newCoordinates.splice(coordIndex, 1);
                                  const updatedArea = {
                                    ...areas[selectedAreaIndex],
                                    coordinates: newCoordinates,
                                  };
                                  update(selectedAreaIndex, updatedArea);
                                }}
                              >
                                <Trash2 className="h-4 w-4 text-destructive" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>

                      <Button
                        variant="outline"
                        onClick={() => {
                          const lastCoord = areas[selectedAreaIndex].coordinates[areas[selectedAreaIndex].coordinates.length - 1];
                          const newCoordinates = [...areas[selectedAreaIndex].coordinates, {
                            lat: lastCoord.lat + 0.001,
                            lng: lastCoord.lng + 0.001,
                          }];
                          const updatedArea = {
                            ...areas[selectedAreaIndex],
                            coordinates: newCoordinates,
                          };
                          update(selectedAreaIndex, updatedArea);
                        }}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Přidat bod
                      </Button>
                    </TabsContent>

                    <TabsContent value="photos" className="pt-4">
                      <PhotoUploadSection
                        control={control}
                        entityIndex={selectedAreaIndex}
                        entityType="areas"
                      />
                    </TabsContent>
                  </Tabs>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="border-t pt-4">
          <div className="flex justify-between w-full">
            <div className="text-sm text-muted-foreground">
              Celkem oblastí: {areas.length}
            </div>
            {selectedAreaId && (
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedAreaId(null);
                  setActiveTab("list");
                }}
              >
                Zpět na seznam
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
