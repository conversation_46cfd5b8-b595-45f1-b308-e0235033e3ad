"use client";

import { useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { Control, useFieldArray } from "react-hook-form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Image,
  Upload,
  Trash2,
  Calendar,
  MapPin,
  FileText,
  Link as LinkIcon,
  Plus,
  X
} from "lucide-react";
import { PhoneNumbersModuleFormValues } from "./schemas";

interface PhotoUploadSectionProps {
  control: Control<PhoneNumbersModuleFormValues>;
  phoneIndex: number;
}

export function PhotoUploadSection({ control, phoneIndex }: PhotoUploadSectionProps) {
  const [activeTab, setActiveTab] = useState("upload");
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const { fields, append, remove } = useFieldArray({
    control,
    name: `phones.${phoneIndex}.photos`,
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // V reálné implementaci by zde bylo nahrání souboru na server/cloud storage
      // Pro demonstraci pouze vytvoříme lokální URL pro náhled
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    }
  };

  const handleAddPhoto = () => {
    // V reálné implementaci by zde bylo získání URL z cloud storage
    // Pro demonstraci použijeme lokální URL nebo placeholder
    append({
      id: uuidv4(),
      fileName: "photo.jpg",
      description: "",
      dateTaken: new Date().toISOString().split('T')[0],
      location: "",
      notes: "",
      downloadURL: previewUrl || "https://via.placeholder.com/300x200?text=Fotografie+telefonního+čísla",
    });

    // Reset formuláře
    setPreviewUrl(null);
    const fileInput = document.getElementById("photo-upload") as HTMLInputElement;
    if (fileInput) {
      fileInput.value = "";
    }
  };

  const FormItemRHF = ({ label, name, placeholder, type = "text", as = "input", rows = 3 }: any) => (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            {as === "input" ? (
              <Input
                {...field}
                placeholder={placeholder}
                type={type}
              />
            ) : (
              <Textarea
                {...field}
                placeholder={placeholder}
                rows={rows}
              />
            )}
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Image className="mr-2 h-5 w-5 text-primary" />
          Fotodokumentace
        </CardTitle>
        <CardDescription>
          Přidejte fotografie související s telefonním číslem
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload">Nahrát fotografii</TabsTrigger>
            <TabsTrigger value="gallery">Galerie ({fields.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-4">
            <div className="grid gap-4">
              <div className="space-y-2">
                <Label htmlFor="photo-upload">Vyberte fotografii</Label>
                <Input
                  id="photo-upload"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                />
              </div>

              {previewUrl && (
                <div className="space-y-2">
                  <Label>Náhled</Label>
                  <div className="border rounded-md overflow-hidden">
                    <img
                      src={previewUrl}
                      alt="Náhled"
                      className="w-full h-auto max-h-[200px] object-contain"
                    />
                  </div>
                </div>
              )}

              <div className="space-y-2">
                <FormItemRHF
                  label="Popis fotografie"
                  name={`temp-description`}
                  placeholder="Zadejte popis fotografie"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <FormItemRHF
                    label="Datum pořízení"
                    name={`temp-dateTaken`}
                    placeholder="RRRR-MM-DD"
                    type="date"
                  />
                </div>

                <div className="space-y-2">
                  <FormItemRHF
                    label="Místo pořízení"
                    name={`temp-location`}
                    placeholder="Zadejte místo pořízení"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <FormItemRHF
                  label="Poznámky"
                  name={`temp-notes`}
                  placeholder="Zadejte poznámky k fotografii"
                  as="textarea"
                />
              </div>

              <Button
                type="button"
                onClick={handleAddPhoto}
                disabled={!previewUrl}
                className="w-full"
              >
                <Plus className="mr-2 h-4 w-4" />
                Přidat fotografii
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="gallery" className="space-y-4">
            {fields.length === 0 ? (
              <div className="text-center py-8 border rounded-md bg-muted/20">
                <Image className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">Zatím nebyly přidány žádné fotografie</p>
                <Button
                  variant="outline"
                  className="mt-4"
                  onClick={() => setActiveTab("upload")}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Nahrát fotografii
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {fields.map((field, index) => (
                  <Card key={field.id} className="overflow-hidden">
                    <div className="relative h-[150px] bg-muted">
                      <img
                        src={field.downloadURL || "https://via.placeholder.com/300x200?text=Fotografie+telefonního+čísla"}
                        alt={field.description || "Fotografie"}
                        className="w-full h-full object-cover"
                      />
                      <Button
                        variant="destructive"
                        size="icon"
                        className="absolute top-2 right-2 h-8 w-8 rounded-full"
                        onClick={() => remove(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    <CardContent className="p-3 space-y-2">
                      <div className="space-y-1">
                        <FormItemRHF
                          label="Popis"
                          name={`phones.${phoneIndex}.photos.${index}.description`}
                          placeholder="Popis fotografie"
                        />
                      </div>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Calendar className="h-3 w-3 mr-1" />
                        <FormItemRHF
                          name={`phones.${phoneIndex}.photos.${index}.dateTaken`}
                          placeholder="Datum pořízení"
                          type="date"
                        />
                      </div>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <MapPin className="h-3 w-3 mr-1" />
                        <FormItemRHF
                          name={`phones.${phoneIndex}.photos.${index}.location`}
                          placeholder="Místo pořízení"
                        />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
