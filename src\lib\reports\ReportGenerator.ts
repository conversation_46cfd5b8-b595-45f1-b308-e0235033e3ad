import { ReportTemplate } from './templates/ReportTemplate';
import { HTMLGenerator } from './generators/HTMLGenerator';
import { WordGenerator } from './generators/WordGenerator';
import { PDFGenerator } from './generators/PDFGenerator';
import { ServerDataCollector } from './ServerDataCollector';

interface ReportSettings {
  title: string;
  subject: string;
  jid: string;
  documentNumber: string;
  location: string;
  date: string;
  department: string;
  purpose: string;
  selectedModules: string[];
  moduleOrder: string[];
  format: 'html' | 'word' | 'pdf';
}

interface GenerateReportParams {
  caseId: string;
  reportType: 'full' | 'partial';
  settings: ReportSettings;
}

export class ReportGenerator {
  private dataCollector: ServerDataCollector;
  private htmlGenerator: HTMLGenerator;
  private wordGenerator: WordGenerator;
  private pdfGenerator: PDFGenerator;

  constructor() {
    this.dataCollector = new ServerDataCollector();
    this.htmlGenerator = new HTMLGenerator();
    this.wordGenerator = new WordGenerator();
    this.pdfGenerator = new PDFGenerator();
  }

  async generateReport(params: GenerateReportParams): Promise<Buffer | string> {
    const { caseId, reportType, settings } = params;

    // Získání dat z databáze
    const caseData = await this.dataCollector.collectCaseData(caseId);

    // Určení modulů k zahrnutí
    const modulesToInclude = reportType === 'full'
      ? this.getModulesWithData(caseData)
      : settings.selectedModules;

    // Seřazení modulů podle požadovaného pořadí
    const orderedModules = reportType === 'full'
      ? this.getDefaultModuleOrder(modulesToInclude)
      : settings.moduleOrder;

    // Vytvoření reportu podle formátu
    switch (settings.format) {
      case 'html':
        return await this.htmlGenerator.generate({
          caseData,
          settings,
          modules: orderedModules
        });
      case 'word':
        return await this.wordGenerator.generate({
          caseData,
          settings,
          modules: orderedModules
        });
      case 'pdf':
        return await this.pdfGenerator.generate({
          caseData,
          settings,
          modules: orderedModules
        });
      default:
        throw new Error(`Nepodporovaný formát: ${settings.format}`);
    }
  }

  private getModulesWithData(caseData: any): string[] {
    const modulesWithData: string[] = [];

    // Kontrola jednotlivých modulů na přítomnost dat
    if (caseData.personalInfo && Object.keys(caseData.personalInfo).length > 0) {
      modulesWithData.push('personal-info');
    }
    if (caseData.company && Object.keys(caseData.company).length > 0) {
      modulesWithData.push('company');
    }
    if (caseData.realEstate && caseData.realEstate.length > 0) {
      modulesWithData.push('real-estate');
    }
    if (caseData.training && Object.keys(caseData.training).length > 0) {
      modulesWithData.push('training');
    }
    if (caseData.emailAnalysis && caseData.emailAnalysis.length > 0) {
      modulesWithData.push('email-analysis');
    }
    if (caseData.phoneNumbers && caseData.phoneNumbers.length > 0) {
      modulesWithData.push('phone-numbers');
    }
    if (caseData.ipAddresses && caseData.ipAddresses.length > 0) {
      modulesWithData.push('ip-addresses');
    }
    if (caseData.mapOverlays && Object.keys(caseData.mapOverlays).length > 0) {
      modulesWithData.push('map-overlays');
    }
    // Přidáme kontrolu pro Facebook data
    if (caseData.facebookData && caseData.facebookData.length > 0) {
      modulesWithData.push('facebook');
    }
    // Přidáme kontrolu pro Instagram data
    if (caseData.instagramData && caseData.instagramData.length > 0) {
      modulesWithData.push('instagram');
    }

    return modulesWithData;
  }

  private getDefaultModuleOrder(modules: string[]): string[] {
    // Výchozí pořadí modulů
    const defaultOrder = [
      'personal-info',
      'company',
      'real-estate',
      'training',
      'email-analysis',
      'phone-numbers',
      'ip-addresses',
      'map-overlays',
      'facebook',
      'instagram'
    ];

    return defaultOrder.filter(moduleId => modules.includes(moduleId));
  }
}
