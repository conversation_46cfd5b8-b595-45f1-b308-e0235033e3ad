import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { phone, countryCode } = await request.json();

    if (!phone) {
      return NextResponse.json(
        { error: 'Telefonní číslo je povinné' },
        { status: 400 }
      );
    }

    // NumVerify API konstanty
    const NUMVERIFY_API_KEY = '********************************';
    const NUMVERIFY_API_URL = 'http://apilayer.net/api/validate';
    
    // Sestavení URL pro API volání
    const apiUrl = `${NUMVERIFY_API_URL}?access_key=${NUMVERIFY_API_KEY}&number=${phone}&country_code=${countryCode}&format=1`;

    console.log('Calling NumVerify API:', apiUrl);

    // Volání NumVerify API
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    
    console.log('NumVerify API response:', data);

    return NextResponse.json(data);

  } catch (error) {
    console.error('Error calling NumVerify API:', error);
    
    return NextResponse.json(
      { 
        error: 'Chyba při validaci telefonního čísla',
        details: error instanceof Error ? error.message : 'Neznámá chyba'
      },
      { status: 500 }
    );
  }
} 