/**
 * Aktuální události - funkce pro získávání a zpracování zpráv
 */

/**
 * Načtení výchozích zpráv
 */
function loadDefaultNews() {
    console.log('Načtení výchozích zpráv');

    // Najít aktuální modul aktuálních událostí
    const newsModule = document.querySelector('.module[id^="module-aktualni-udalosti"]');
    if (!newsModule) {
        console.error('Modul aktuálních událostí nebyl nalezen');
        return;
    }

    // Zobrazení načítání
    const newsContainer = newsModule.querySelector('#news-results');
    if (newsContainer) {
        newsContainer.innerHTML = `
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Načítání zpráv...</span>
            </div>
        `;
    }

    // Získání zpráv z GDELT Project (veřejně dostupné API bez nutnosti registrace)
    fetchGdeltNews()
        .then(news => {
            displayNews(news);
        })
        .catch(error => {
            console.error('Chyba při načítání zpráv:', error);

            // Pokud se nepodaří načíst zprávy z GDELT, zkusíme alternativní zdroj
            fetchAlternativeNews()
                .then(news => {
                    displayNews(news);
                })
                .catch(alternativeError => {
                    console.error('Chyba při načítání alternativních zpráv:', alternativeError);

                    if (newsContainer) {
                        newsContainer.innerHTML = `
                            <div class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                <span>Nepodařilo se načíst zprávy. Zkuste to prosím znovu.</span>
                            </div>
                        `;
                    }
                });
        });
}

/**
 * Získání zpráv z GDELT Project
 * @returns {Promise<Array>} - Pole zpráv
 */
function fetchGdeltNews() {
    console.log('Získání zpráv z GDELT Project');

    // Vytvoření URL pro GDELT DOC 2.0 API
    // Dokumentace: https://blog.gdeltproject.org/gdelt-doc-2-0-api-debuts/
    const baseUrl = 'https://api.gdeltproject.org/api/v2/doc/doc';

    // Sestavení parametrů dotazu
    const params = new URLSearchParams();

    // Přidání klíčových slov (pokud existují)
    if (activeNewsFilters.keywords && activeNewsFilters.keywords.length > 0) {
        params.append('query', activeNewsFilters.keywords.join(' OR '));
    }

    // Přidání jazyků
    if (activeNewsFilters.languages && activeNewsFilters.languages.length > 0) {
        // GDELT používá kódy jazyků ve formátu "lang:cs"
        const langFilter = activeNewsFilters.languages.map(lang => `lang:${lang}`).join(' OR ');
        params.append('filter', langFilter);
    }

    // Přidání časového rozmezí
    if (activeNewsFilters.fromDate) {
        // GDELT používá formát YYYYMMDDHHMMSS
        const fromDate = activeNewsFilters.fromDate.replace(/-/g, '');
        params.append('startdatetime', `${fromDate}000000`);
    }

    if (activeNewsFilters.toDate) {
        // GDELT používá formát YYYYMMDDHHMMSS
        const toDate = activeNewsFilters.toDate.replace(/-/g, '');
        params.append('enddatetime', `${toDate}235959`);
    }

    // Přidání formátu výstupu (JSON)
    params.append('format', 'json');

    // Přidání počtu výsledků
    params.append('maxrecords', '100');

    // Přidání řazení (od nejnovějších)
    params.append('sort', 'DateDesc');

    // Sestavení kompletní URL
    const url = `${baseUrl}?${params.toString()}`;

    // Použijeme CORS proxy pro obejití CORS omezení
    const corsProxyUrl = `https://corsproxy.io/?${encodeURIComponent(url)}`;

    // Provedení požadavku
    return fetch(corsProxyUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error('Nepodařilo se získat data z GDELT API.');
            }
            return response.json();
        })
        .then(data => {
            // Zpracování odpovědi z GDELT API
            if (data && data.articles) {
                // Transformace dat do jednotného formátu
                return data.articles.map(article => ({
                    id: article.url || `gdelt-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                    title: article.title || 'Bez názvu',
                    description: article.seentext || '',
                    content: article.socialimage || '',
                    url: article.url || '',
                    imageUrl: article.socialimage || '',
                    source: article.domain || 'GDELT',
                    author: article.sourcecountry || '',
                    publishedAt: article.seendate || '',
                    category: article.sourcecountry || 'general',
                    language: article.language || 'en',
                    sentiment: calculateSentiment(article.tone || 0),
                    entities: extractEntities(article.persons || '', article.organizations || '', article.locations || '')
                }));
            } else {
                console.log('GDELT API nevrátila žádné články, zkusíme alternativní zdroj');
                return fetchAlternativeNews();
            }
        })
        .catch(error => {
            console.error('Chyba při získávání zpráv z GDELT API:', error);
            console.log('Zkusíme alternativní zdroj');
            return fetchAlternativeNews();
        });
}

/**
 * Generování simulovaných zpráv pro demonstrační účely
 * @returns {Array} - Pole simulovaných zpráv
 */
function generateSimulatedNews() {
    console.log('Generování simulovaných zpráv pro demonstraci');

    // Základní témata pro zprávy
    const topics = [
        {
            title: 'Vláda schválila nový zákon o kybernetické bezpečnosti',
            description: 'Vláda dnes schválila nový zákon o kybernetické bezpečnosti, který má posílit ochranu kritické infrastruktury před kybernetickými útoky. Zákon zavádí přísnější požadavky na zabezpečení informačních systémů a stanovuje vyšší pokuty za porušení bezpečnostních pravidel.',
            source: 'ČTK',
            language: 'cs',
            entities: {
                persons: ['Petr Fiala', 'Vít Rakušan'],
                organizations: ['Vláda ČR', 'NÚKIB'],
                locations: ['Praha', 'Česká republika']
            }
        },
        {
            title: 'Česká národní banka ponechala úrokové sazby beze změny',
            description: 'Bankovní rada České národní banky na svém dnešním zasedání ponechala úrokové sazby beze změny. Základní úroková sazba tak zůstává na úrovni 5,75 procenta. Podle guvernéra ČNB je současná měnová politika adekvátní vzhledem k aktuálnímu vývoji inflace.',
            source: 'Hospodářské noviny',
            language: 'cs',
            entities: {
                persons: ['Aleš Michl'],
                organizations: ['ČNB', 'Bankovní rada'],
                locations: ['Praha']
            }
        },
        {
            title: 'Policie zadržela podezřelého z kybernetického útoku na nemocnice',
            description: 'Policie České republiky zadržela muže podezřelého z kybernetického útoku na několik českých nemocnic. Podle policejního mluvčího se jedná o třicetiletého muže z Moravskoslezského kraje, který je obviněn z trestného činu neoprávněného přístupu k počítačovému systému.',
            source: 'iDNES.cz',
            language: 'cs',
            entities: {
                persons: [],
                organizations: ['Policie ČR', 'NCOZ'],
                locations: ['Moravskoslezský kraj', 'Česká republika']
            }
        },
        {
            title: 'European Commission proposes new regulations for AI',
            description: 'The European Commission has proposed new regulations for artificial intelligence that aim to ensure AI systems used in the EU are safe, transparent, and respect fundamental rights. The proposal includes strict rules for high-risk AI applications and bans certain AI practices.',
            source: 'Reuters',
            language: 'en',
            entities: {
                persons: ['Ursula von der Leyen', 'Margrethe Vestager'],
                organizations: ['European Commission', 'European Union'],
                locations: ['Brussels', 'Europe']
            }
        },
        {
            title: 'UK police arrest suspected leader of major cybercrime group',
            description: 'British police have arrested a man suspected of being a leader of a major cybercrime group responsible for numerous ransomware attacks against businesses and government institutions. The 28-year-old suspect was detained in London following an international operation.',
            source: 'BBC News',
            language: 'en',
            entities: {
                persons: [],
                organizations: ['National Crime Agency', 'Europol'],
                locations: ['London', 'United Kingdom']
            }
        },
        {
            title: 'US intelligence warns of increased cyber threats from China',
            description: 'A new report from US intelligence agencies warns of increased cyber espionage activities from China targeting critical infrastructure and technology companies. The report details sophisticated hacking campaigns aimed at stealing intellectual property and sensitive data.',
            source: 'The New York Times',
            language: 'en',
            entities: {
                persons: ['Jake Sullivan', 'Antony Blinken'],
                organizations: ['NSA', 'FBI', 'CISA'],
                locations: ['United States', 'China']
            }
        },
        {
            title: 'Major security flaw discovered in widely used software library',
            description: 'Security researchers have discovered a critical vulnerability in a widely used open-source software library that could affect thousands of applications worldwide. The flaw could allow attackers to execute arbitrary code remotely on affected systems.',
            source: 'The Guardian',
            language: 'en',
            entities: {
                persons: [],
                organizations: ['CERT', 'Open Source Initiative'],
                locations: ['Global']
            }
        },
        {
            title: 'Российские хакеры атаковали европейские энергетические компании',
            description: 'По данным европейских спецслужб, группа российских хакеров провела серию кибератак на энергетические компании в нескольких странах Европы. Атаки были направлены на получение доступа к критической инфраструктуре и сбор разведывательной информации.',
            source: 'РИА Новости',
            language: 'ru',
            entities: {
                persons: [],
                organizations: ['ФСБ', 'Европол'],
                locations: ['Россия', 'Европа']
            }
        },
        {
            title: 'Alemania refuerza su legislación contra ciberataques',
            description: 'El gobierno alemán ha aprobado una nueva ley para fortalecer la seguridad cibernética del país tras una serie de ataques a infraestructuras críticas. La legislación impone requisitos más estrictos para las empresas que operan en sectores estratégicos.',
            source: 'El País',
            language: 'es',
            entities: {
                persons: ['Olaf Scholz'],
                organizations: ['BSI', 'Gobierno Federal Alemán'],
                locations: ['Berlín', 'Alemania']
            }
        },
        {
            title: 'La France renforce sa coopération internationale en matière de cybersécurité',
            description: 'La France a signé plusieurs accords de coopération avec des pays européens pour renforcer la lutte contre les cybermenaces. Ces accords prévoient un partage d\'informations et des exercices conjoints pour améliorer la résilience face aux attaques informatiques.',
            source: 'Le Monde',
            language: 'fr',
            entities: {
                persons: ['Emmanuel Macron'],
                organizations: ['ANSSI', 'Ministère des Armées'],
                locations: ['Paris', 'France']
            }
        }
    ];

    // Generování 100 zpráv na základě témat
    const news = [];

    // Aktuální datum
    const now = new Date();

    // Generování zpráv
    for (let i = 0; i < 100; i++) {
        // Výběr náhodného tématu
        const topic = topics[Math.floor(Math.random() * topics.length)];

        // Náhodné datum v posledních 7 dnech
        const date = new Date(now);
        date.setDate(date.getDate() - Math.floor(Math.random() * 7));

        // Vytvoření zprávy
        const newsItem = {
            id: `simulated-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            title: topic.title,
            description: topic.description,
            content: topic.description,
            url: 'https://example.com/news/' + Math.random().toString(36).substr(2, 9),
            imageUrl: 'https://via.placeholder.com/300x200?text=' + encodeURIComponent(topic.title.substring(0, 20)),
            source: topic.source,
            author: '',
            publishedAt: date.toISOString(),
            category: 'general',
            language: topic.language,
            sentiment: ['positive', 'neutral', 'negative'][Math.floor(Math.random() * 3)],
            entities: topic.entities
        };

        news.push(newsItem);
    }

    // Filtrování podle klíčových slov
    let filteredNews = news;

    if (activeNewsFilters.keywords && activeNewsFilters.keywords.length > 0) {
        const keywords = activeNewsFilters.keywords.map(keyword => keyword.toLowerCase());

        filteredNews = news.filter(item => {
            const text = (item.title + ' ' + item.description).toLowerCase();
            return keywords.some(keyword => text.includes(keyword));
        });
    }

    // Filtrování podle jazyků
    if (activeNewsFilters.languages && activeNewsFilters.languages.length > 0) {
        filteredNews = filteredNews.filter(item =>
            activeNewsFilters.languages.includes(item.language)
        );
    }

    // Řazení podle data publikace (od nejnovějších)
    filteredNews.sort((a, b) => {
        const dateA = new Date(a.publishedAt);
        const dateB = new Date(b.publishedAt);
        return dateB - dateA;
    });

    return filteredNews;
}

/**
 * Získání zpráv z alternativního zdroje (RSS feedy českých médií)
 * @returns {Promise<Array>} - Pole zpráv
 */
function fetchAlternativeNews() {
    console.log('Získání zpráv z alternativního zdroje');

    // Seznam RSS feedů českých a zahraničních médií
    const rssFeeds = [
        // České zdroje
        { url: 'https://www.seznamzpravy.cz/rss/feed', source: 'Seznam Zprávy', language: 'cs' },
        { url: 'https://ct24.ceskatelevize.cz/rss/hlavni-zpravy', source: 'ČT24', language: 'cs' },
        { url: 'https://www.irozhlas.cz/rss/irozhlas', source: 'iROZHLAS', language: 'cs' },
        { url: 'https://www.novinky.cz/rss', source: 'Novinky.cz', language: 'cs' },
        { url: 'https://www.idnes.cz/zpravy/rss.xml', source: 'iDNES.cz', language: 'cs' },

        // Anglické zdroje
        { url: 'https://rss.nytimes.com/services/xml/rss/nyt/World.xml', source: 'The New York Times', language: 'en' },
        { url: 'https://feeds.bbci.co.uk/news/world/rss.xml', source: 'BBC News', language: 'en' },
        { url: 'https://www.theguardian.com/world/rss', source: 'The Guardian', language: 'en' },
        { url: 'https://feeds.skynews.com/feeds/rss/world.xml', source: 'Sky News', language: 'en' },

        // Německé zdroje
        { url: 'https://www.spiegel.de/international/index.rss', source: 'Der Spiegel', language: 'de' },

        // Francouzské zdroje
        { url: 'https://www.lemonde.fr/en/rss/une.xml', source: 'Le Monde', language: 'fr' },

        // Španělské zdroje
        { url: 'https://feeds.elpais.com/mrss-s/pages/ep/site/elpais.com/section/internacional/portada', source: 'El País', language: 'es' }
    ];

    // Výběr RSS feedů podle filtrů
    let selectedFeeds = rssFeeds;

    // Filtrování podle zdrojů
    if (activeNewsFilters.sources && activeNewsFilters.sources.length > 0) {
        selectedFeeds = rssFeeds.filter(feed => {
            const sourceName = feed.source.toLowerCase();
            return activeNewsFilters.sources.some(source => sourceName.includes(source.toLowerCase()));
        });
    }

    // Filtrování podle jazyků
    if (activeNewsFilters.languages && activeNewsFilters.languages.length > 0) {
        selectedFeeds = selectedFeeds.filter(feed =>
            activeNewsFilters.languages.includes(feed.language)
        );
    }

    // Pokud nejsou vybrány žádné feedy, použijeme všechny
    if (selectedFeeds.length === 0) {
        selectedFeeds = rssFeeds;
    }

    // Omezení na maximálně 5 feedů pro rychlejší načítání, ale zajistíme různé jazyky
    if (selectedFeeds.length > 5) {
        // Nejprve seřadíme feedy podle jazyků, abychom měli různé jazyky
        const feedsByLanguage = {};

        selectedFeeds.forEach(feed => {
            if (!feedsByLanguage[feed.language]) {
                feedsByLanguage[feed.language] = [];
            }
            feedsByLanguage[feed.language].push(feed);
        });

        // Vybereme z každého jazyka alespoň jeden feed
        const finalFeeds = [];

        for (const lang in feedsByLanguage) {
            if (finalFeeds.length < 5) {
                finalFeeds.push(feedsByLanguage[lang][0]);
            }
        }

        // Pokud máme méně než 5 feedů, doplníme dalšími
        if (finalFeeds.length < 5) {
            for (const lang in feedsByLanguage) {
                for (let i = 1; i < feedsByLanguage[lang].length && finalFeeds.length < 5; i++) {
                    finalFeeds.push(feedsByLanguage[lang][i]);
                }
            }
        }

        selectedFeeds = finalFeeds;
    }

    // Funkce pro parsování RSS feedu
    const parseRssFeed = (feed) => {
        // Použití alternativního CORS proxy
        const corsProxy = 'https://corsproxy.io/?';
        const url = `${corsProxy}${encodeURIComponent(feed.url)}`;

        return fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Nepodařilo se získat RSS feed z ${feed.source}`);
                }
                return response.text();
            })
            .then(text => {
                try {
                    // Parsování XML
                    const parser = new DOMParser();
                    const xml = parser.parseFromString(text, 'text/xml');

                    // Kontrola, zda je XML validní
                    if (xml.querySelector('parsererror')) {
                        throw new Error(`Neplatný XML formát z ${feed.source}`);
                    }

                    // Hledání položek (některé RSS feedy používají jiné tagy)
                    let items = xml.querySelectorAll('item');

                    // Pokud nejsou nalezeny položky, zkusíme alternativní tag 'entry'
                    if (items.length === 0) {
                        items = xml.querySelectorAll('entry');
                    }

                    // Transformace položek do jednotného formátu
                    return Array.from(items).map(item => {
                        // Různé RSS feedy mohou používat různé tagy
                        const title =
                            item.querySelector('title')?.textContent ||
                            'Bez názvu';

                        const description =
                            item.querySelector('description')?.textContent ||
                            item.querySelector('summary')?.textContent ||
                            item.querySelector('content')?.textContent ||
                            '';

                        const link =
                            item.querySelector('link')?.textContent ||
                            item.querySelector('link')?.getAttribute('href') ||
                            '';

                        const pubDate =
                            item.querySelector('pubDate')?.textContent ||
                            item.querySelector('published')?.textContent ||
                            item.querySelector('updated')?.textContent ||
                            '';

                        const guid =
                            item.querySelector('guid')?.textContent ||
                            item.querySelector('id')?.textContent ||
                            '';

                        // Extrakce obrázku z popisu (pokud existuje)
                        let imageUrl = '';
                        const imgMatch = description.match(/<img[^>]+src="([^">]+)"/);
                        if (imgMatch && imgMatch[1]) {
                            imageUrl = imgMatch[1];
                        }

                        // Pokud není nalezen obrázek v popisu, zkusíme najít v enclosure nebo media:content
                        if (!imageUrl) {
                            const enclosure = item.querySelector('enclosure[type^="image"]');
                            if (enclosure) {
                                imageUrl = enclosure.getAttribute('url');
                            }
                        }

                        if (!imageUrl) {
                            const mediaContent = item.querySelector('media\\:content[type^="image"], content[type^="image"]');
                            if (mediaContent) {
                                imageUrl = mediaContent.getAttribute('url');
                            }
                        }

                        return {
                            id: guid || link || `rss-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
                            title: title,
                            description: stripHtml(description),
                            content: description,
                            url: link,
                            imageUrl: imageUrl,
                            source: feed.source,
                            author: feed.source,
                            publishedAt: pubDate,
                            category: 'general',
                            language: feed.language || 'cs', // Použití jazyka z definice feedu
                            sentiment: 'neutral',
                            entities: extractEntitiesFromText(title + ' ' + stripHtml(description))
                        };
                    });
                } catch (error) {
                    console.error(`Chyba při parsování RSS feedu ${feed.source}:`, error);
                    return [];
                }
            })
            .catch(error => {
                console.error(`Chyba při zpracování RSS feedu ${feed.source}:`, error);
                return [];
            });
    };

    // Zpracování všech vybraných RSS feedů
    return Promise.all(selectedFeeds.map(parseRssFeed))
        .then(results => {
            // Sloučení výsledků ze všech feedů
            const allNews = results.flat();

            // Kontrola, zda byly získány nějaké zprávy
            if (allNews.length === 0) {
                console.log('Nebyly získány žádné zprávy z RSS feedů, použijeme simulovaná data');
                return generateSimulatedNews();
            }

            // Filtrování podle klíčových slov
            let filteredNews = allNews;

            if (activeNewsFilters.keywords && activeNewsFilters.keywords.length > 0) {
                const keywords = activeNewsFilters.keywords.map(keyword => keyword.toLowerCase());

                filteredNews = allNews.filter(news => {
                    const text = (news.title + ' ' + news.description).toLowerCase();
                    return keywords.some(keyword => text.includes(keyword));
                });
            }

            // Řazení podle data publikace (od nejnovějších)
            filteredNews.sort((a, b) => {
                const dateA = new Date(a.publishedAt);
                const dateB = new Date(b.publishedAt);
                return dateB - dateA;
            });

            // Omezení počtu výsledků
            return filteredNews.slice(0, 100);
        })
        .catch(error => {
            console.error('Chyba při získávání zpráv z RSS feedů:', error);
            return generateSimulatedNews();
        });
}

/**
 * Odstranění HTML tagů z textu
 * @param {string} html - HTML text
 * @returns {string} - Text bez HTML tagů
 */
function stripHtml(html) {
    const tmp = document.createElement('div');
    tmp.innerHTML = html;
    return tmp.textContent || tmp.innerText || '';
}

/**
 * Výpočet sentimentu zprávy
 * @param {number} tone - Hodnota tónu z GDELT API
 * @returns {string} - Sentiment (positive, neutral, negative)
 */
function calculateSentiment(tone) {
    if (tone > 2) {
        return 'positive';
    } else if (tone < -2) {
        return 'negative';
    } else {
        return 'neutral';
    }
}

/**
 * Extrakce entit z textu
 * @param {string} persons - Seznam osob
 * @param {string} organizations - Seznam organizací
 * @param {string} locations - Seznam lokací
 * @returns {Object} - Objekt s entitami
 */
function extractEntities(persons, organizations, locations) {
    return {
        persons: persons ? persons.split(';').map(p => p.trim()).filter(p => p) : [],
        organizations: organizations ? organizations.split(';').map(o => o.trim()).filter(o => o) : [],
        locations: locations ? locations.split(';').map(l => l.trim()).filter(l => l) : []
    };
}

/**
 * Extrakce entit z textu pomocí jednoduchých pravidel
 * @param {string} text - Text pro extrakci entit
 * @returns {Object} - Objekt s entitami
 */
function extractEntitiesFromText(text) {
    // Seznam známých českých politiků a organizací pro jednoduchou detekci
    const knownPersons = [
        'Petr Fiala', 'Andrej Babiš', 'Miloš Zeman', 'Petr Pavel', 'Markéta Pekarová Adamová',
        'Tomio Okamura', 'Ivan Bartoš', 'Vít Rakušan', 'Marian Jurečka', 'Zbyněk Stanjura',
        'Jana Černochová', 'Alena Schillerová', 'Karel Havlíček', 'Danuše Nerudová'
    ];

    const knownOrganizations = [
        'ODS', 'ANO', 'STAN', 'Piráti', 'SPD', 'KDU-ČSL', 'TOP 09', 'ČSSD', 'KSČM',
        'Vláda', 'Parlament', 'Senát', 'Poslanecká sněmovna', 'Ústavní soud', 'Nejvyšší soud',
        'Ministerstvo', 'ČNB', 'Evropská unie', 'NATO', 'OSN'
    ];

    // Detekce osob
    const persons = knownPersons.filter(person => text.includes(person));

    // Detekce organizací
    const organizations = knownOrganizations.filter(org => {
        // Kontrola celých slov (ne částí slov)
        const regex = new RegExp(`\\b${org}\\b`, 'i');
        return regex.test(text);
    });

    // Detekce lokací (jednoduchá pravidla pro česká města)
    const czechCities = [
        'Praha', 'Brno', 'Ostrava', 'Plzeň', 'Liberec', 'Olomouc', 'Ústí nad Labem',
        'Hradec Králové', 'České Budějovice', 'Pardubice', 'Zlín', 'Jihlava', 'Karlovy Vary'
    ];

    const locations = czechCities.filter(city => text.includes(city));

    return {
        persons,
        organizations,
        locations
    };
}
