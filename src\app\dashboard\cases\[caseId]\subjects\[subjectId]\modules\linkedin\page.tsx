"use client";

import { useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Subject } from "@/types"; 
import LinkedInModule from "@/components/modules/linkedin/LinkedInModule";
import { ModulePageSkeleton } from "@/components/modules/ModulePageSkeleton";
import { useToast } from "@/hooks/use-toast";
import { LinkedInModuleData } from "@/types/linkedin";

export default function LinkedInModulePage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [subject, setSubject] = useState<Subject | null>(null);
  // const [moduleData, setModuleData] = useState<LinkedInModuleData | null>(null); // Specific type

  const caseId = params.caseId as string;
  const subjectId = params.subjectId as string;
  const moduleId = "linkedin"; 

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch subject data
        const subjectDocRef = doc(db, "cases", caseId, "subjects", subjectId);
        const subjectSnap = await getDoc(subjectDocRef);
        
        if (!subjectSnap.exists()) {
          toast({
            title: "Subjekt nenalezen",
            description: "Požadovaný subjekt neexistuje nebo byl smazán.",
            variant: "destructive",
          });
          router.push(`/dashboard/cases/${caseId}`);
          return;
        }
        
        setSubject({ id: subjectSnap.id, ...subjectSnap.data() } as Subject);
        
        // Fetch module data (if it exists)
        // const moduleDocRef = doc(db, "cases", caseId, "subjects", subjectId, "moduleData", moduleId);
        // const moduleSnap = await getDoc(moduleDocRef);
        // if (moduleSnap.exists()) {
        //   setModuleData({ id: moduleSnap.id, ...moduleSnap.data() } as LinkedInModuleData);
        // } else {
        //   setModuleData(null); // Or initialize with default structure
        // }
      } catch (error: any) {
        toast({
          title: "Chyba při načítání dat",
          description: error.message,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };
    
    if (caseId && subjectId) {
      fetchData();
    }
  }, [caseId, subjectId, router, toast]);

  if (loading) {
    return <ModulePageSkeleton title="LinkedIn OSINT" />;
  }

  if (!subject) {
    return <div>Subjekt nenalezen.</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <LinkedInModule caseId={caseId} subject={subject} moduleId={moduleId} onClose={() => router.push(`/dashboard/cases/${caseId}/subjects/${subjectId}`)} />
    </div>
  );
} 