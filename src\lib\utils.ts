import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { format, parseISO } from "date-fns"
import { Timestamp } from "firebase/firestore"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Formátuje datum do čitelného formátu
 * @param dateString - ISO string data
 * @returns Formátovaný řetězec data
 */
export function formatDate(dateString: string): string {
  if (!dateString) return 'Neuvedeno';

  try {
    const date = parseISO(dateString);
    // Jednoduchý formát data bez závislosti na locale
    return date.toLocaleDateString('cs-CZ');
  } catch (error) {
    console.error('Error formatting date:', error);
    return dateString;
  }
}

/**
 * Formátuje timestamp (Firestore Timestamp nebo ISO string) do čitelného formátu
 * @param timestamp - Firestore Timestamp nebo ISO string
 * @returns Formátovaný řetězec data
 */
export function formatTimestamp(timestamp: Timestamp | string | Date | undefined | null): string {
  if (!timestamp) return 'Neuvedeno';

  try {
    let date: Date;

    if (timestamp instanceof Timestamp) {
      date = timestamp.toDate();
    } else if (timestamp instanceof Date) {
      date = timestamp;
    } else if (typeof timestamp === 'string') {
      date = new Date(timestamp);
    } else {
      return 'Neplatné datum';
    }

    // Formát data pro českou lokalizaci
    return date.toLocaleDateString('cs-CZ', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    });
  } catch (error) {
    console.error('Error formatting timestamp:', error);
    return String(timestamp);
  }
}
