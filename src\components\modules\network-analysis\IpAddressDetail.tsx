"use client";

import { useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { Control, useFieldArray, useFormContext } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  Globe,
  Activity,
  PlusCircle,
  X,
  Map,
  Shield,
  Server,
  Link,
  Laptop
} from "lucide-react";
import {
  NetworkAnalysisModuleFormValues,
  ipAddressTypes,
  ipAddressSources,
  ipAddressUsages,
  ipAddressVerificationStatuses,
  networkProtocols,
  networkConnectionTypes,
  portStatuses,
  malwareIndicatorTypes,
  malwareSeverityLevels
} from "./schemas";
import { DomainRecord, NetworkDevice } from "@/types";
import { PhotoUploadSection } from "./PhotoUploadSection";
import { IpAddressValidator } from "./IpAddressValidator";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";

interface IpAddressDetailProps {
  control: Control<NetworkAnalysisModuleFormValues>;
  ipIndex: number;
  domains: DomainRecord[];
  devices: NetworkDevice[];
}

interface FormItemRHFProps {
  label: string;
  name: string;
  control: Control<NetworkAnalysisModuleFormValues>;
  placeholder?: string;
  type?: string;
  disabled?: boolean;
  className?: string;
  as?: 'input' | 'textarea';
  rows?: number;
  smallLabel?: boolean;
}

export function IpAddressDetail({ control, ipIndex, domains, devices }: IpAddressDetailProps) {
  const [activeTab, setActiveTab] = useState("basic");
  const [validationResults, setValidationResults] = useState<any>(null);
  const { watch, setValue } = useFormContext();

  // Port scan results
  const {
    fields: portScanFields,
    append: appendPortScan,
    remove: removePortScan
  } = useFieldArray({
    control,
    name: `ipAddresses.${ipIndex}.portScanResults`,
  });

  // Network activities
  const {
    fields: activityFields,
    append: appendActivity,
    remove: removeActivity
  } = useFieldArray({
    control,
    name: `ipAddresses.${ipIndex}.activities`,
  });

  // Malware indicators
  const {
    fields: malwareFields,
    append: appendMalware,
    remove: removeMalware
  } = useFieldArray({
    control,
    name: `ipAddresses.${ipIndex}.malwareIndicators`,
  });

  // Related domains and devices
  const relatedDomains = watch(`ipAddresses.${ipIndex}.relatedDomains`) || [];
  const relatedDevices = watch(`ipAddresses.${ipIndex}.relatedDevices`) || [];

  const handleAddPortScan = () => {
    appendPortScan({
      id: uuidv4(),
      port: 80,
      protocol: "http",
      status: "open",
      scanDate: new Date().toISOString().split('T')[0],
    });
  };

  const handleAddActivity = () => {
    appendActivity({
      id: uuidv4(),
      date: new Date().toISOString().split('T')[0],
      activityType: "connection",
      description: "",
    });
  };

  const handleAddMalware = () => {
    appendMalware({
      id: uuidv4(),
      indicatorType: "ip",
      value: watch(`ipAddresses.${ipIndex}.ipAddress`) || "",
      detectionDate: new Date().toISOString().split('T')[0],
      severity: "medium",
    });
  };

  const handleDomainChange = (domainId: string, checked: boolean) => {
    const currentDomains = [...relatedDomains];

    if (checked) {
      if (!currentDomains.includes(domainId)) {
        setValue(`ipAddresses.${ipIndex}.relatedDomains`, [...currentDomains, domainId], { shouldDirty: true });
      }
    } else {
      setValue(
        `ipAddresses.${ipIndex}.relatedDomains`,
        currentDomains.filter(id => id !== domainId),
        { shouldDirty: true }
      );
    }
  };

  const handleDeviceChange = (deviceId: string, checked: boolean) => {
    const currentDevices = [...relatedDevices];

    if (checked) {
      if (!currentDevices.includes(deviceId)) {
        setValue(`ipAddresses.${ipIndex}.relatedDevices`, [...currentDevices, deviceId], { shouldDirty: true });
      }
    } else {
      setValue(
        `ipAddresses.${ipIndex}.relatedDevices`,
        currentDevices.filter(id => id !== deviceId),
        { shouldDirty: true }
      );
    }
  };

  const handleValidationComplete = (results: any) => {
    // Uložení výsledků validace pro pozdější zobrazení
    setValidationResults(results);

    // Aktualizace formuláře s výsledky validace
    setValue(`ipAddresses.${ipIndex}.verificationStatus`,
      results.isValid ? "verified" : "invalid",
      { shouldDirty: true }
    );
    setValue(`ipAddresses.${ipIndex}.verificationDate`, new Date().toISOString().split('T')[0], { shouldDirty: true });

    if (results.geoLocation) {
      setValue(`ipAddresses.${ipIndex}.geoLocation`, results.geoLocation, { shouldDirty: true });
    }

    if (results.usage) {
      setValue(`ipAddresses.${ipIndex}.usage`, results.usage, { shouldDirty: true });
    }

    if (results.hostname) {
      setValue(`ipAddresses.${ipIndex}.hostname`, results.hostname, { shouldDirty: true });
    }

    // Pokud je IP adresa veřejná, nastavíme typ připojení na přímé
    if (results.networkType === 'Veřejná') {
      setValue(`ipAddresses.${ipIndex}.connectionType`, "direct", { shouldDirty: true });
    }

    // Nastavíme typ IP adresy
    if (results.ipType) {
      setValue(`ipAddresses.${ipIndex}.ipType`, results.ipType, { shouldDirty: true });
    }
  };

  const FormItemRHF = ({ label, name, control, placeholder, type = "text", disabled = false, className, as = 'input', rows, smallLabel }: FormItemRHFProps) => (
    <FormField
      control={control}
      name={name as any}
      disabled={disabled}
      render={({ field }) => (
        <FormItem className={className}>
          <FormLabel className={smallLabel ? "text-xs" : "text-sm"}>{label}</FormLabel>
          <FormControl>
            {as === 'input' ? (
              <Input
                {...field}
                placeholder={placeholder}
                type={type}
              />
            ) : (
              <Textarea
                {...field}
                placeholder={placeholder}
                rows={rows || 3}
              />
            )}
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );

  return (
    <div className="border rounded-lg p-4">
      <div className="flex items-center mb-4">
        <Globe className="mr-2 h-5 w-5 text-primary" />
        <h3 className="text-lg font-semibold">Detail IP adresy</h3>
      </div>
      <div className="text-sm text-muted-foreground mb-4">
        Správa informací o IP adrese
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="basic">Základní informace</TabsTrigger>
          <TabsTrigger value="verification">Ověření</TabsTrigger>
          <TabsTrigger value="geolocation">Geolokace</TabsTrigger>
          <TabsTrigger value="ports">Porty</TabsTrigger>
          <TabsTrigger value="activities">Aktivity</TabsTrigger>
          <TabsTrigger value="malware">Malware</TabsTrigger>
          <TabsTrigger value="relations">Vztahy</TabsTrigger>
          <TabsTrigger value="photos">Fotodokumentace</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItemRHF
              control={control}
              name={`ipAddresses.${ipIndex}.ipAddress`}
              label="IP adresa"
              placeholder="Např. *********** nebo 2001:db8::"
            />

            <FormField
              control={control}
              name={`ipAddresses.${ipIndex}.ipType`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Typ IP adresy</FormLabel>
                  <Select
                    value={field.value || ""}
                    onValueChange={field.onChange}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Vyberte typ IP adresy" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {ipAddressTypes.map(type => (
                        <SelectItem key={type} value={type}>
                          {type === "ipv4" ? "IPv4" : "IPv6"}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={control}
              name={`ipAddresses.${ipIndex}.source`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Zdroj</FormLabel>
                  <Select
                    value={field.value || ""}
                    onValueChange={field.onChange}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Vyberte zdroj" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {ipAddressSources.map(source => (
                        <SelectItem key={source} value={source}>
                          {source === "osint" ? "OSINT" :
                           source === "investigation" ? "Vyšetřování" :
                           source === "public_data" ? "Veřejné zdroje" :
                           source === "social_media" ? "Sociální sítě" :
                           source === "direct_communication" ? "Přímá komunikace" :
                           source === "server_logs" ? "Serverové logy" :
                           source === "email_headers" ? "Hlavičky e-mailů" :
                           source === "network_scan" ? "Síťový sken" :
                           "Jiný zdroj"}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <FormItemRHF
              control={control}
              name={`ipAddresses.${ipIndex}.discoveryDate`}
              label="Datum objevení"
              type="date"
            />
          </div>

          {watch(`ipAddresses.${ipIndex}.source`) === "other" && (
            <FormItemRHF
              control={control}
              name={`ipAddresses.${ipIndex}.otherSourceDetail`}
              label="Upřesnění jiného zdroje"
              placeholder="Zadejte podrobnosti o zdroji"
            />
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={control}
              name={`ipAddresses.${ipIndex}.usage`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Využití</FormLabel>
                  <Select
                    value={field.value || ""}
                    onValueChange={field.onChange}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Vyberte využití" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {ipAddressUsages.map(usage => (
                        <SelectItem key={usage} value={usage}>
                          {usage === "residential" ? "Rezidenční" :
                           usage === "business" ? "Firemní" :
                           usage === "hosting" ? "Hosting" :
                           usage === "vpn" ? "VPN" :
                           usage === "proxy" ? "Proxy" :
                           usage === "tor_exit_node" ? "Tor Exit Node" :
                           usage === "mobile" ? "Mobilní" :
                           usage === "datacenter" ? "Datacenter" :
                           "Jiné využití"}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name={`ipAddresses.${ipIndex}.connectionType`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Typ připojení</FormLabel>
                  <Select
                    value={field.value || ""}
                    onValueChange={field.onChange}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Vyberte typ připojení" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {networkConnectionTypes.map(type => (
                        <SelectItem key={type} value={type}>
                          {type === "direct" ? "Přímé" :
                           type === "vpn" ? "VPN" :
                           type === "proxy" ? "Proxy" :
                           type === "tor" ? "Tor" :
                           "Jiný typ"}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
          </div>

          {watch(`ipAddresses.${ipIndex}.usage`) === "other" && (
            <FormItemRHF
              control={control}
              name={`ipAddresses.${ipIndex}.otherUsageDetail`}
              label="Upřesnění jiného využití"
              placeholder="Zadejte podrobnosti o využití"
            />
          )}

          {watch(`ipAddresses.${ipIndex}.connectionType`) === "other" && (
            <FormItemRHF
              control={control}
              name={`ipAddresses.${ipIndex}.otherConnectionTypeDetail`}
              label="Upřesnění jiného typu připojení"
              placeholder="Zadejte podrobnosti o typu připojení"
            />
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItemRHF
              control={control}
              name={`ipAddresses.${ipIndex}.hostname`}
              label="Hostname"
              placeholder="Např. server.example.com"
            />

            <FormItemRHF
              control={control}
              name={`ipAddresses.${ipIndex}.macAddress`}
              label="MAC adresa"
              placeholder="Např. 00:1A:2B:3C:4D:5E"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItemRHF
              control={control}
              name={`ipAddresses.${ipIndex}.owner`}
              label="Vlastník"
              placeholder="Jméno vlastníka IP adresy"
            />

            <FormItemRHF
              control={control}
              name={`ipAddresses.${ipIndex}.associatedOrganization`}
              label="Přidružená organizace"
              placeholder="Název organizace"
            />
          </div>

          <FormItemRHF
            control={control}
            name={`ipAddresses.${ipIndex}.notes`}
            label="Poznámky"
            placeholder="Zadejte poznámky k IP adrese"
            as="textarea"
            rows={4}
          />
        </TabsContent>

        <TabsContent value="verification" className="space-y-4">
          <IpAddressValidator
            initialIp={watch(`ipAddresses.${ipIndex}.ipAddress`) || ""}
            onValidationComplete={handleValidationComplete}
            savedResults={validationResults}
          />

          <div className="border rounded-lg p-4 space-y-4">
            <h3 className="text-lg font-semibold">Stav ověření</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={control}
                name={`ipAddresses.${ipIndex}.verificationStatus`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Stav ověření</FormLabel>
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Vyberte stav ověření" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {ipAddressVerificationStatuses.map(status => (
                          <SelectItem key={status} value={status}>
                            {status === "verified" ? "Ověřeno" :
                             status === "unverified" ? "Neověřeno" :
                             status === "invalid" ? "Neplatné" :
                             status === "suspicious" ? "Podezřelé" :
                             "Neznámý stav"}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />

              <FormItemRHF
                control={control}
                name={`ipAddresses.${ipIndex}.verificationDate`}
                label="Datum ověření"
                type="date"
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="geolocation" className="space-y-4">
          <div className="border rounded-lg p-4 space-y-4">
            <div className="flex items-center mb-4">
              <Map className="mr-2 h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Geolokace</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemRHF
                control={control}
                name={`ipAddresses.${ipIndex}.geoLocation.country`}
                label="Země"
                placeholder="Např. Česká republika"
              />

              <FormItemRHF
                control={control}
                name={`ipAddresses.${ipIndex}.geoLocation.region`}
                label="Region"
                placeholder="Např. Středočeský kraj"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemRHF
                control={control}
                name={`ipAddresses.${ipIndex}.geoLocation.city`}
                label="Město"
                placeholder="Např. Praha"
              />

              <FormItemRHF
                control={control}
                name={`ipAddresses.${ipIndex}.geoLocation.postalCode`}
                label="PSČ"
                placeholder="Např. 110 00"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemRHF
                control={control}
                name={`ipAddresses.${ipIndex}.geoLocation.latitude`}
                label="Zeměpisná šířka"
                placeholder="Např. 50.0755"
                type="number"
              />

              <FormItemRHF
                control={control}
                name={`ipAddresses.${ipIndex}.geoLocation.longitude`}
                label="Zeměpisná délka"
                placeholder="Např. 14.4378"
                type="number"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemRHF
                control={control}
                name={`ipAddresses.${ipIndex}.geoLocation.timezone`}
                label="Časové pásmo"
                placeholder="Např. Europe/Prague"
              />

              <FormItemRHF
                control={control}
                name={`ipAddresses.${ipIndex}.geoLocation.isp`}
                label="Poskytovatel internetových služeb (ISP)"
                placeholder="Např. O2 Czech Republic"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemRHF
                control={control}
                name={`ipAddresses.${ipIndex}.geoLocation.organization`}
                label="Organizace"
                placeholder="Např. O2 Czech Republic a.s."
              />

              <FormItemRHF
                control={control}
                name={`ipAddresses.${ipIndex}.geoLocation.asn`}
                label="ASN"
                placeholder="Např. AS5610"
              />
            </div>

            <FormItemRHF
              control={control}
              name={`ipAddresses.${ipIndex}.geoLocation.asnOrganization`}
              label="ASN organizace"
              placeholder="Např. O2 Czech Republic, a.s."
            />
          </div>
        </TabsContent>

        <TabsContent value="ports" className="space-y-4">
          <div className="border rounded-lg p-4 space-y-4">
            <div className="flex items-center mb-4">
              <Server className="mr-2 h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Porty</h3>
            </div>
            <div className="text-sm text-muted-foreground mb-4">
              Správa výsledků skenování portů
            </div>

            {portScanFields.length === 0 ? (
              <div className="text-center py-8 border rounded-md bg-muted/20">
                <Server className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">Zatím nebyly přidány žádné výsledky skenování portů</p>
              </div>
            ) : (
              portScanFields.map((field, index) => (
                <div key={field.id} className="border rounded-lg p-4 space-y-4 shadow-sm bg-card-foreground/5">
                  <div className="flex justify-between items-center mb-2">
                    <p className="font-semibold text-md">Port {watch(`ipAddresses.${ipIndex}.portScanResults.${index}.port`) || index + 1}</p>
                    <Button type="button" variant="ghost" size="icon" onClick={() => removePortScan(index)} className="text-destructive hover:bg-destructive/10">
                      <X className="h-5 w-5" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormItemRHF
                      control={control}
                      name={`ipAddresses.${ipIndex}.portScanResults.${index}.port`}
                      label="Číslo portu"
                      placeholder="Např. 80"
                      type="number"
                    />

                    <FormField
                      control={control}
                      name={`ipAddresses.${ipIndex}.portScanResults.${index}.protocol`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Protokol</FormLabel>
                          <Select
                            value={field.value || ""}
                            onValueChange={field.onChange}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Vyberte protokol" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {networkProtocols.map(protocol => (
                                <SelectItem key={protocol} value={protocol}>
                                  {protocol.toUpperCase()}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={control}
                      name={`ipAddresses.${ipIndex}.portScanResults.${index}.status`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Stav</FormLabel>
                          <Select
                            value={field.value || ""}
                            onValueChange={field.onChange}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Vyberte stav" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {portStatuses.map(status => (
                                <SelectItem key={status} value={status}>
                                  {status === "open" ? "Otevřený" :
                                   status === "closed" ? "Zavřený" :
                                   "Filtrovaný"}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF
                      control={control}
                      name={`ipAddresses.${ipIndex}.portScanResults.${index}.service`}
                      label="Služba"
                      placeholder="Např. HTTP"
                    />

                    <FormItemRHF
                      control={control}
                      name={`ipAddresses.${ipIndex}.portScanResults.${index}.version`}
                      label="Verze"
                      placeholder="Např. Apache 2.4.41"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF
                      control={control}
                      name={`ipAddresses.${ipIndex}.portScanResults.${index}.scanDate`}
                      label="Datum skenu"
                      type="date"
                    />

                    <FormItemRHF
                      control={control}
                      name={`ipAddresses.${ipIndex}.portScanResults.${index}.banner`}
                      label="Banner"
                      placeholder="Např. Apache/2.4.41 (Ubuntu)"
                    />
                  </div>

                  <FormItemRHF
                    control={control}
                    name={`ipAddresses.${ipIndex}.portScanResults.${index}.notes`}
                    label="Poznámky"
                    placeholder="Zadejte poznámky k portu"
                    as="textarea"
                  />
                </div>
              ))
            )}

            <Button
              type="button"
              variant="outline"
              onClick={handleAddPortScan}
              className="w-full"
            >
              <PlusCircle className="mr-2 h-4 w-4" />
              Přidat port
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="activities" className="space-y-4">
          <div className="border rounded-lg p-4 space-y-4">
            <div className="flex items-center mb-4">
              <Activity className="mr-2 h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Aktivity</h3>
            </div>
            <div className="text-sm text-muted-foreground mb-4">
              Správa síťových aktivit spojených s IP adresou
            </div>

            {activityFields.length === 0 ? (
              <div className="text-center py-8 border rounded-md bg-muted/20">
                <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">Zatím nebyly přidány žádné aktivity</p>
              </div>
            ) : (
              activityFields.map((field, index) => (
                <div key={field.id} className="border rounded-lg p-4 space-y-4 shadow-sm bg-card-foreground/5">
                  <div className="flex justify-between items-center mb-2">
                    <p className="font-semibold text-md">Aktivita {index + 1}</p>
                    <Button type="button" variant="ghost" size="icon" onClick={() => removeActivity(index)} className="text-destructive hover:bg-destructive/10">
                      <X className="h-5 w-5" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF
                      control={control}
                      name={`ipAddresses.${ipIndex}.activities.${index}.date`}
                      label="Datum"
                      type="date"
                    />

                    <FormItemRHF
                      control={control}
                      name={`ipAddresses.${ipIndex}.activities.${index}.activityType`}
                      label="Typ aktivity"
                      placeholder="Např. Přihlášení, Stažení, atd."
                    />
                  </div>

                  <FormItemRHF
                    control={control}
                    name={`ipAddresses.${ipIndex}.activities.${index}.description`}
                    label="Popis"
                    placeholder="Zadejte popis aktivity"
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF
                      control={control}
                      name={`ipAddresses.${ipIndex}.activities.${index}.sourceIp`}
                      label="Zdrojová IP"
                      placeholder="Např. ***********"
                    />

                    <FormItemRHF
                      control={control}
                      name={`ipAddresses.${ipIndex}.activities.${index}.destinationIp`}
                      label="Cílová IP"
                      placeholder="Např. *******"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={control}
                      name={`ipAddresses.${ipIndex}.activities.${index}.protocol`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Protokol</FormLabel>
                          <Select
                            value={field.value || ""}
                            onValueChange={field.onChange}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Vyberte protokol" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {networkProtocols.map(protocol => (
                                <SelectItem key={protocol} value={protocol}>
                                  {protocol.toUpperCase()}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />

                    <FormItemRHF
                      control={control}
                      name={`ipAddresses.${ipIndex}.activities.${index}.port`}
                      label="Port"
                      placeholder="Např. 80"
                      type="number"
                    />
                  </div>

                  <FormItemRHF
                    control={control}
                    name={`ipAddresses.${ipIndex}.activities.${index}.userAgent`}
                    label="User Agent"
                    placeholder="Např. Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36..."
                  />

                  <FormItemRHF
                    control={control}
                    name={`ipAddresses.${ipIndex}.activities.${index}.notes`}
                    label="Poznámky"
                    placeholder="Zadejte poznámky k aktivitě"
                    as="textarea"
                  />
                </div>
              ))
            )}

            <Button
              type="button"
              variant="outline"
              onClick={handleAddActivity}
              className="w-full"
            >
              <PlusCircle className="mr-2 h-4 w-4" />
              Přidat aktivitu
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="malware" className="space-y-4">
          <div className="border rounded-lg p-4 space-y-4">
            <div className="flex items-center mb-4">
              <Shield className="mr-2 h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Indikátory malwaru</h3>
            </div>
            <div className="text-sm text-muted-foreground mb-4">
              Správa indikátorů malwaru spojených s IP adresou
            </div>

            {malwareFields.length === 0 ? (
              <div className="text-center py-8 border rounded-md bg-muted/20">
                <Shield className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">Zatím nebyly přidány žádné indikátory malwaru</p>
              </div>
            ) : (
              malwareFields.map((field, index) => (
                <div key={field.id} className="border rounded-lg p-4 space-y-4 shadow-sm bg-card-foreground/5">
                  <div className="flex justify-between items-center mb-2">
                    <p className="font-semibold text-md">Indikátor {index + 1}</p>
                    <Button type="button" variant="ghost" size="icon" onClick={() => removeMalware(index)} className="text-destructive hover:bg-destructive/10">
                      <X className="h-5 w-5" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={control}
                      name={`ipAddresses.${ipIndex}.malwareIndicators.${index}.indicatorType`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Typ indikátoru</FormLabel>
                          <Select
                            value={field.value || ""}
                            onValueChange={field.onChange}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Vyberte typ indikátoru" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {malwareIndicatorTypes.map(type => (
                                <SelectItem key={type} value={type}>
                                  {type === "hash" ? "Hash" :
                                   type === "filename" ? "Název souboru" :
                                   type === "url" ? "URL" :
                                   type === "ip" ? "IP adresa" :
                                   type === "domain" ? "Doména" :
                                   "Jiný typ"}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />

                    <FormItemRHF
                      control={control}
                      name={`ipAddresses.${ipIndex}.malwareIndicators.${index}.value`}
                      label="Hodnota"
                      placeholder="Např. IP adresa, hash, URL, atd."
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF
                      control={control}
                      name={`ipAddresses.${ipIndex}.malwareIndicators.${index}.malwareFamily`}
                      label="Rodina malwaru"
                      placeholder="Např. Emotet, TrickBot, atd."
                    />

                    <FormItemRHF
                      control={control}
                      name={`ipAddresses.${ipIndex}.malwareIndicators.${index}.detectionDate`}
                      label="Datum detekce"
                      type="date"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF
                      control={control}
                      name={`ipAddresses.${ipIndex}.malwareIndicators.${index}.source`}
                      label="Zdroj"
                      placeholder="Např. VirusTotal, MISP, atd."
                    />

                    <FormField
                      control={control}
                      name={`ipAddresses.${ipIndex}.malwareIndicators.${index}.severity`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Závažnost</FormLabel>
                          <Select
                            value={field.value || ""}
                            onValueChange={field.onChange}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Vyberte závažnost" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {malwareSeverityLevels.map(level => (
                                <SelectItem key={level} value={level}>
                                  {level === "low" ? "Nízká" :
                                   level === "medium" ? "Střední" :
                                   level === "high" ? "Vysoká" :
                                   "Kritická"}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormItemRHF
                    control={control}
                    name={`ipAddresses.${ipIndex}.malwareIndicators.${index}.notes`}
                    label="Poznámky"
                    placeholder="Zadejte poznámky k indikátoru malwaru"
                    as="textarea"
                  />
                </div>
              ))
            )}

            <Button
              type="button"
              variant="outline"
              onClick={handleAddMalware}
              className="w-full"
            >
              <PlusCircle className="mr-2 h-4 w-4" />
              Přidat indikátor malwaru
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="relations" className="space-y-4">
          <div className="border rounded-lg p-4 space-y-4">
            <div className="flex items-center mb-4">
              <Link className="mr-2 h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Související domény</h3>
            </div>
            <div className="text-sm text-muted-foreground mb-4">
              Správa domén spojených s IP adresou
            </div>

            {domains.length === 0 ? (
              <div className="text-center py-8 border rounded-md bg-muted/20">
                <Server className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">Zatím nebyly přidány žádné domény</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {domains.map(domain => (
                  <div key={domain.id} className="flex items-center space-x-2 border p-3 rounded-md">
                    <Checkbox
                      id={`domain-${domain.id}`}
                      checked={relatedDomains.includes(domain.id)}
                      onCheckedChange={(checked) => handleDomainChange(domain.id, !!checked)}
                    />
                    <Label htmlFor={`domain-${domain.id}`} className="cursor-pointer flex-1">
                      {domain.domainName || "Neuvedeno"}
                    </Label>
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="border rounded-lg p-4 space-y-4">
            <div className="flex items-center mb-4">
              <Laptop className="mr-2 h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Související zařízení</h3>
            </div>
            <div className="text-sm text-muted-foreground mb-4">
              Správa zařízení spojených s IP adresou
            </div>

            {devices.length === 0 ? (
              <div className="text-center py-8 border rounded-md bg-muted/20">
                <Laptop className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">Zatím nebyly přidány žádná zařízení</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {devices.map(device => (
                  <div key={device.id} className="flex items-center space-x-2 border p-3 rounded-md">
                    <Checkbox
                      id={`device-${device.id}`}
                      checked={relatedDevices.includes(device.id)}
                      onCheckedChange={(checked) => handleDeviceChange(device.id, !!checked)}
                    />
                    <Label htmlFor={`device-${device.id}`} className="cursor-pointer flex-1">
                      {device.name || `${device.deviceType} (${device.ipAddress || "bez IP"})`}
                    </Label>
                  </div>
                ))}
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="photos">
          <PhotoUploadSection control={control} entityIndex={ipIndex} entityType="ipAddresses" />
        </TabsContent>
      </Tabs>
    </div>
  );
}
