"use client";

import { useState } from "react";
import { useFieldArray, Control } from "react-hook-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Globe, PlusCircle, Trash2, Flag, Building, Calendar } from "lucide-react";
import { FormItemRHF } from "./FormComponents";
import { ForeignBusinessInvolvement } from "./schemas";
import { cn } from "@/lib/utils";
import type { BusinessActivityFormValues } from "./BusinessActivityForm";

interface ForeignBusinessSectionProps {
  control: Control<BusinessActivityFormValues>;
}

export function ForeignBusinessSection({ control }: ForeignBusinessSectionProps) {
  const [expanded, setExpanded] = useState(false);

  const { fields, append, remove } = useFieldArray({
    control,
    name: "foreignInvolvements",
  });

  const handleAddForeignBusiness = () => {
    append({
      id: crypto.randomUUID(),
      name: "",
      country: "",
      description: "",
      isActive: true,
      isOffshore: false,
    });
  };

  return (
    <Card className={cn(expanded ? "" : "hover:border-primary/50 cursor-pointer transition-all")}>
      <CardHeader 
        className="flex flex-row items-center justify-between"
        onClick={() => setExpanded(!expanded)}
      >
        <CardTitle className="text-lg flex items-center">
          <Globe className="mr-2 h-5 w-5" />
          Zahraniční aktivity
        </CardTitle>
        <Badge variant={fields.length > 0 ? "default" : "outline"}>
          {fields.length} záznamů
        </Badge>
      </CardHeader>
      {expanded && (
        <CardContent className="space-y-6 pt-0">
          <FormItemRHF
            label="Shrnutí zahraničních aktivit"
            name="foreignBusinessSummary"
            control={control}
            as="textarea"
            rows={3}
            placeholder="Obecné shrnutí zahraničních aktivit subjektu..."
          />

          {fields.map((item, index) => (
            <Card key={item.id} className="p-4 shadow-sm bg-card-foreground/5 relative">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => remove(index)}
                className="absolute top-2 right-2 text-destructive hover:bg-destructive/10"
              >
                <Trash2 className="h-5 w-5" />
              </Button>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormItemRHF
                  label="Název společnosti/aktivity"
                  name={`foreignInvolvements.${index}.name`}
                  control={control}
                  placeholder="Název zahraniční společnosti"
                />
                <FormItemRHF
                  label="Země"
                  name={`foreignInvolvements.${index}.country`}
                  control={control}
                  placeholder="Země působení"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormItemRHF
                  label="Role subjektu"
                  name={`foreignInvolvements.${index}.role`}
                  control={control}
                  placeholder="Např. jednatel, společník, investor..."
                />
                <div className="flex flex-col space-y-2">
                  <div className="flex items-center space-x-4">
                    <FormItemRHF
                      label="Aktivní"
                      name={`foreignInvolvements.${index}.isActive`}
                      control={control}
                      as="switch"
                    />
                    <FormItemRHF
                      label="Offshore"
                      name={`foreignInvolvements.${index}.isOffshore`}
                      control={control}
                      as="switch"
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormItemRHF
                  label="Datum zahájení"
                  name={`foreignInvolvements.${index}.startDate`}
                  control={control}
                  type="date"
                />
                <FormItemRHF
                  label="Datum ukončení"
                  name={`foreignInvolvements.${index}.endDate`}
                  control={control}
                  type="date"
                />
              </div>

              <FormItemRHF
                label="Popis aktivit"
                name={`foreignInvolvements.${index}.description`}
                control={control}
                as="textarea"
                rows={3}
                placeholder="Detailní popis zahraničních aktivit..."
              />

              <FormItemRHF
                label="Poznámky"
                name={`foreignInvolvements.${index}.notes`}
                control={control}
                as="textarea"
                rows={2}
                placeholder="Další poznámky k zahraniční aktivitě..."
              />
            </Card>
          ))}

          <Button
            type="button"
            variant="outline"
            onClick={handleAddForeignBusiness}
            className="w-full"
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Přidat zahraniční aktivitu
          </Button>
        </CardContent>
      )}
    </Card>
  );
}
