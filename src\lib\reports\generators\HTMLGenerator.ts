import type { CaseData } from '../DataCollector';

interface GenerateParams {
  caseData: CaseData;
  settings: any;
  modules: string[];
}

interface ReportTemplate {
  title: string;
  sections: string[];
}

export class HTMLGenerator {
  private template: ReportTemplate;

  constructor() {
    this.template = { title: '', sections: [] };
  }

  async generate(params: GenerateParams): Promise<string> {
    const { caseData, settings, modules } = params;

    // Vytvoření HTML struktury podle nového návrhu
    const html = `
<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSINT Report - ${settings.subject || caseData.name}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Times New Roman', serif;
            font-size: 11pt;
            line-height: 1.4;
            color: #000000;
            background: #ffffff;
            margin: 0;
            padding: 20px;
        }

        .document-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
        }

        /* Zachovaná úvodní stránka */
        .header {
            border-bottom: 2px solid #333333;
            padding-bottom: 15px;
            margin-bottom: 30px;
        }

        .police-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
            font-weight: bold;
        }

        .police-left {
            font-size: 12pt;
        }

        .police-right {
            text-align: right;
        }

        .department-info {
            font-size: 10pt;
            margin-bottom: 15px;
            line-height: 1.3;
        }

        .document-info {
            display: flex;
            justify-content: space-between;
            font-size: 10pt;
        }

        .document-title {
            font-size: 18pt;
            font-weight: bold;
            text-align: center;
            margin: 40px 0;
            color: #000000;
        }

        .subject-name {
            font-size: 14pt;
            text-align: center;
            margin-bottom: 30px;
            font-weight: bold;
            color: #000000;
        }

        .protocol-section {
            margin: 30px 0;
        }

        .protocol-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #000000;
        }

        .protocol-text {
            text-align: justify;
            line-height: 1.5;
            color: #000000;
        }

        .warning-section {
            margin: 30px 0;
            padding: 15px;
            border: 1px solid #666666;
            background-color: #f5f5f5;
        }

        .warning-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #000000;
        }

        ul {
            margin: 10px 0;
            padding-left: 20px;
        }

        li {
            margin-bottom: 5px;
            color: #000000;
        }

        /* Čisté stránky */
        .page-break {
            margin-top: 50px;
            border-top: 2px solid #333333;
            padding-top: 30px;
        }

        .page-header {
            margin-bottom: 30px;
        }

        .page-number {
            font-weight: bold;
            margin-bottom: 5px;
            font-size: 11pt;
            color: #000000;
        }

        .page-divider {
            border-bottom: 1px solid #333333;
            margin-bottom: 20px;
        }

        /* Sekce */
        .section {
            margin: 30px 0;
        }

        .section-title {
            font-size: 13pt;
            font-weight: bold;
            color: #000000;
            margin-bottom: 20px;
            border-bottom: 1px solid #666666;
            padding-bottom: 3px;
        }

        /* Datové bloky - čisté, bez rámečků */
        .data-block {
            margin: 25px 0;
        }

        .data-title {
            font-size: 12pt;
            font-weight: bold;
            color: #000000;
            margin-bottom: 15px;
        }

        .data-grid {
            margin: 15px 0;
        }

        .data-row {
            display: table;
            width: 100%;
            margin-bottom: 8px;
        }

        .data-label {
            display: table-cell;
            width: 140px;
            font-size: 10pt;
            color: #666666;
            font-weight: normal;
            vertical-align: top;
            padding-right: 15px;
        }

        .data-value {
            display: table-cell;
            font-size: 11pt;
            color: #000000;
            font-weight: normal;
            line-height: 1.3;
        }

        .data-value.emphasized {
            font-weight: bold;
        }

        /* Seznamy */
        .data-list {
            margin: 15px 0;
        }

        .data-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .data-item:last-child {
            border-bottom: none;
        }

        .item-primary {
            font-size: 11pt;
            color: #000000;
            font-weight: bold;
            margin-bottom: 2px;
        }

        .item-secondary {
            font-size: 10pt;
            color: #666666;
            margin-bottom: 2px;
        }

        .item-status {
            font-size: 10pt;
            color: #000000;
            font-weight: bold;
        }

        /* Poznámky */
        .note-block {
            margin: 20px 0;
            padding: 15px 0;
            border-top: 1px solid #666666;
            border-bottom: 1px solid #666666;
        }

        .note-title {
            font-size: 11pt;
            font-weight: bold;
            color: #000000;
            margin-bottom: 8px;
        }

        .note-text {
            font-size: 11pt;
            color: #333333;
            line-height: 1.4;
        }

        /* Podpis */
        .signature-block {
            margin-top: 40px;
            text-align: right;
        }

        .signature-content {
            display: inline-block;
            text-align: left;
            border-top: 1px solid #333333;
            padding-top: 15px;
            min-width: 200px;
        }

        .signature-title {
            font-size: 10pt;
            color: #666666;
            margin-bottom: 25px;
        }

        .signature-name {
            font-size: 11pt;
            font-weight: bold;
            color: #000000;
        }

        /* Tisk optimalizace */
        @media print {
            body {
                margin: 0;
                padding: 15mm;
                background: white;
            }

            .document-container {
                max-width: none;
            }

            .page-break {
                page-break-before: always;
                margin-top: 0;
                border-top: none;
                padding-top: 0;
            }

            .section, .data-block {
                page-break-inside: avoid;
            }
        }

        /* Responzivita */
        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            .police-header {
                flex-direction: column;
                gap: 15px;
            }

            .document-info {
                flex-direction: column;
                gap: 10px;
            }

            .data-row {
                display: block;
            }

            .data-label {
                display: block;
                width: auto;
                padding-right: 0;
                margin-bottom: 5px;
            }

            .data-value {
                display: block;
            }
        }

        /* Kompatibilita pro starší moduly */
        .module {
            margin-bottom: 20px;
        }

        .module h3 {
            color: #000000;
            font-size: 12pt;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }

        .info-table td {
            padding: 5px 0;
            border-bottom: 1px solid #f0f0f0;
            vertical-align: top;
        }

        .info-table .label {
            font-weight: bold;
            width: 140px;
            color: #666666;
            font-size: 10pt;
            padding-right: 15px;
        }

        .info-table .value {
            color: #000000;
            font-size: 11pt;
        }

        .subsection {
            margin-top: 15px;
        }

        .subsection h4 {
            color: #000000;
            font-size: 11pt;
            margin-bottom: 8px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="document-container">
        <!-- Zachovaná úvodní stránka -->
        <div class="header">
            <div class="police-header">
                <div class="police-left">
                    POLICIE ČESKÉ REPUBLIKY<br>
                    Krajské ředitelství policie Pardubického kraje
                </div>
                <div class="police-right">
                    JID:
                </div>
            </div>
            <div class="department-info">
                ${settings.department.replace(/\n/g, '<br>')}
            </div>
            <div class="document-info">
                <div class="left-info">
                    <div>Č. j. ${settings.documentNumber || ''}</div>
                </div>
                <div class="right-info">
                    <div>${settings.location} ${settings.date}</div>
                    <div>Počet stran: ${this.calculatePages(modules)}</div>
                </div>
            </div>
        </div>

        <div class="document-title">
            ${settings.title}
        </div>

        <div class="subject-name">
            ${settings.subject || 'Jméno, příjmení, datum narození'}
        </div>

        <div class="protocol-section">
            <div class="protocol-title">Cíl protokolu:</div>
            <div class="protocol-text">
                ${settings.purpose}
            </div>
        </div>

        <div class="warning-section" style="margin-top: 60px;">
            <div class="warning-title">Důležité upozornění:</div>
            <div class="protocol-text">
                Tento OSINT průzkum využívá kombinovaný přístup zahrnující:
                <ul>
                    <li>Systematické vyhledávání v interních informačních systémech Policie ČR</li>
                    <li>Komplexní prověření veškerých dostupných zdrojů v otevřeném prostoru Internet</li>
                </ul>
                Průzkum klade důraz na získání maximálního množství relevantních informací z otevřených zdrojů při současném zachování operativní bezpečnosti a eliminaci možnosti odhalení zájmu o sledovanou osobu.
            </div>
        </div>

        <!-- Dynamicky generované moduly -->
        ${await this.generateModules(caseData, modules)}

        <!-- Podpisová doložka na konci dokumentu -->
        <div class="signature-block">
            <div class="signature-content">
                <div class="signature-title">Zpracoval:</div>
                <div class="signature-name">${this.extractInvestigatorName(settings.department)}</div>
            </div>
        </div>
    </div>
</body>
</html>`;

    return html;
  }

  private async generateModules(caseData: CaseData, modules: string[]): Promise<string> {
    let html = '';
    let pageNumber = 2; // Začínáme od 2. stránky

    // Základní údaje
    if (caseData.personalInfo) {
      html += `
      <div class="page-break">
          <div class="page-header">
              <div class="page-number">${pageNumber}. strana</div>
              <div class="page-divider"></div>
          </div>

          <section class="section">
              <h2 class="section-title">Základní údaje</h2>
              ${this.generatePersonalInfoHTML(caseData.personalInfo)}
              ${this.generateAddressesHTML(caseData.personalInfo?.addresses)}
          </section>
      </div>`;
      pageNumber++;
    }

    // Komunikační prostředky
    if (caseData.phoneNumbers || caseData.emailAnalysis) {
      html += `
      <div class="page-break">
          <div class="page-header">
              <div class="page-number">${pageNumber}. strana</div>
              <div class="page-divider"></div>
          </div>

          <section class="section">
              <h2 class="section-title">Komunikační prostředky</h2>
              ${caseData.phoneNumbers ? this.generatePhoneNumbersHTML(caseData.phoneNumbers) : ''}
              ${caseData.emailAnalysis ? this.generateEmailAnalysisHTML(caseData.emailAnalysis) : ''}
          </section>
      </div>`;
      pageNumber++;
    }

    // Další moduly
    for (const moduleId of modules) {
      if (!['personal-info', 'addresses', 'phone-numbers', 'emails', 'social-media'].includes(moduleId)) {
        html += `
        <div class="page-break">
            <div class="page-header">
                <div class="page-number">${pageNumber}. strana</div>
                <div class="page-divider"></div>
            </div>

            <section class="section">
                ${this.generateModuleContentHTML(moduleId, caseData)}
            </section>
        </div>`;
        pageNumber++;
      }
    }

    return html;
  }

  private generateModuleContentHTML(moduleId: string, caseData: CaseData): string {
    switch (moduleId) {
      case 'company':
        return `<h2 class="section-title">Informace o firmě</h2>${this.generateCompanyHTML(caseData.company)}`;
      case 'real-estate':
        return `<h2 class="section-title">Nemovitosti</h2>${this.generateRealEstateHTML(caseData.realEstate || [])}`;
      case 'training':
        return `<h2 class="section-title">Výcvik a bezpečnostní rizika</h2>${this.generateTrainingHTML(caseData.training)}`;
      case 'ip-addresses':
        return `<h2 class="section-title">IP adresy</h2>${this.generateIpAddressesHTML(caseData.ipAddresses || [])}`;
      case 'map-overlays':
        return `<h2 class="section-title">Mapové překryvy</h2>${this.generateMapOverlaysHTML(caseData.mapOverlays)}`;
      case 'facebook':
        return `<h2 class="section-title">Facebook profily</h2>${this.generateFacebookHTML(caseData.facebookData || [])}`;
      case 'instagram':
        return `<h2 class="section-title">Instagram profily</h2>${this.generateInstagramHTML(caseData.instagramData || [])}`;
      default:
        return `<h2 class="section-title">Neznámý modul</h2><div class="data-block"><p>Modul ${moduleId} není podporován.</p></div>`;
    }
  }

  private generatePersonalInfoHTML(data: any): string {
    if (!data) return '';

    return `
    <div class="data-block">
        <div class="data-title">Osobní údaje</div>
        <div class="data-grid">
            <div class="data-row">
                <div class="data-label">Jméno a příjmení:</div>
                <div class="data-value emphasized">${data.firstName || ''} ${data.lastName || ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Datum narození:</div>
                <div class="data-value">${data.birthDate ? new Date(data.birthDate).toLocaleDateString('cs-CZ') : ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Místo narození:</div>
                <div class="data-value">${data.birthPlace || ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Státní příslušnost:</div>
                <div class="data-value">${data.nationality || ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Číslo OP:</div>
                <div class="data-value">${data.idNumber || ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Stav ověření:</div>
                <div class="data-value emphasized">Ověřeno z úředních zdrojů</div>
            </div>
        </div>

        ${data.notes ? `
        <div class="note-block">
            <div class="note-title">Poznámky:</div>
            <div class="note-text">${data.notes}</div>
        </div>
        ` : ''}
    </div>`;
  }

  private generateCompanyHTML(data: any): string {
    if (!data) return '';

    return `
    <div class="data-block">
        <div class="data-title">Informace o firmě</div>
        <div class="data-grid">
            <div class="data-row">
                <div class="data-label">Název:</div>
                <div class="data-value emphasized">${data.name || ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">IČO:</div>
                <div class="data-value">${data.ico || ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">DIČ:</div>
                <div class="data-value">${data.dic || ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Právní forma:</div>
                <div class="data-value">${data.legalForm || ''}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Stav:</div>
                <div class="data-value emphasized">${data.status || 'Aktivní'}</div>
            </div>
        </div>
    </div>`;
  }

  private generateAddressesHTML(data: any[]): string {
    if (!data || data.length === 0) return '';

    return `
    <div class="data-block">
        <div class="data-title">Adresy</div>
        <div class="data-list">
            ${data.map((address, index) => `
            <div class="data-item">
                <div class="item-primary">${address.street || ''} ${address.number || ''}</div>
                <div class="item-secondary">${address.city || ''} ${address.zip || ''}</div>
                <div class="item-status">${address.type || 'Trvalá'}</div>
            </div>
            `).join('')}
        </div>
    </div>`;
  }

  private generatePhoneNumbersHTML(data: any[]): string {
    if (!data || data.length === 0) return '';

    return `
    <div class="data-block">
        <div class="data-title">Telefonní čísla</div>
        <div class="data-list">
            ${data.map((phone, index) => `
            <div class="data-item">
                <div class="item-primary">${phone.number || ''}</div>
                <div class="item-secondary">${phone.carrier || ''}</div>
                <div class="item-status">Ověřeno</div>
            </div>
            `).join('')}
        </div>
    </div>`;
  }

  private generateEmailAnalysisHTML(data: any[]): string {
    if (!data || data.length === 0) return '';

    return `
    <div class="data-block">
        <div class="data-title">Emailové adresy</div>
        <div class="data-list">
            ${data.map((email, index) => `
            <div class="data-item">
                <div class="item-primary">${email.address || ''}</div>
                <div class="item-secondary">${email.provider || ''}</div>
                <div class="item-status">Ověřeno</div>
            </div>
            `).join('')}
        </div>
    </div>`;
  }

  private generateRealEstateHTML(data: any[]): string {
    if (!data || data.length === 0) return '';

    return `
    <div class="data-block">
        <div class="data-title">Nemovitosti</div>
        <div class="data-list">
            ${data.map((property, index) => `
            <div class="data-item">
                <div class="item-primary">${property.address || ''}</div>
                <div class="item-secondary">${property.type || ''} - ${property.ownership || ''}</div>
                <div class="item-status">Ověřeno</div>
            </div>
            `).join('')}
        </div>
    </div>`;
  }

  private generateTrainingHTML(data: any): string {
    if (!data) return '';

    return `
    <div class="data-block">
        <div class="data-title">Výcvik a bezpečnostní rizika</div>
        ${data.policeTraining && data.policeTraining.length > 0 ? `
        <div class="data-list">
            ${data.policeTraining.map((training: any, index: number) => `
            <div class="data-item">
                <div class="item-primary">${training.rank || 'Nespecifikováno'}</div>
                <div class="item-secondary">${training.unit || ''}</div>
                <div class="item-status">Ověřeno</div>
            </div>
            `).join('')}
        </div>
        ` : `
        <div class="note-block">
            <div class="note-title">Poznámka:</div>
            <div class="note-text">Nebyly nalezeny žádné záznamy o výcviku.</div>
        </div>
        `}
    </div>`;
  }

  private generateIpAddressesHTML(data: any[]): string {
    if (!data || data.length === 0) return '';

    return `
    <div class="data-block">
        <div class="data-title">IP adresy</div>
        <div class="data-list">
            ${data.map((ip, index) => `
            <div class="data-item">
                <div class="item-primary">${ip.address || ''}</div>
                <div class="item-secondary">${ip.location || ''} - ${ip.provider || ''}</div>
                <div class="item-status">Ověřeno</div>
            </div>
            `).join('')}
        </div>
    </div>`;
  }

  private generateMapOverlaysHTML(data: any): string {
    if (!data) return '';

    return `
    <div class="data-block">
        <div class="data-title">Mapové překryvy</div>
        <div class="data-grid">
            <div class="data-row">
                <div class="data-label">Počet bodů:</div>
                <div class="data-value">${data.points ? data.points.length : 0}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Počet oblastí:</div>
                <div class="data-value">${data.areas ? data.areas.length : 0}</div>
            </div>
            <div class="data-row">
                <div class="data-label">Počet tras:</div>
                <div class="data-value">${data.routes ? data.routes.length : 0}</div>
            </div>
        </div>
    </div>`;
  }

  private generateFacebookHTML(data: any[]): string {
    if (!data || data.length === 0) return '';

    let html = '';

    data.forEach((profile, index) => {
      html += `
      <div class="data-block">
        <div class="data-title">Facebook profil: ${profile.profileName || profile.username || 'Neurčený název'}</div>

        <div class="data-grid">
          <div class="data-row">
            <div class="data-label">Profilové jméno:</div>
            <div class="data-value emphasized">${profile.profileName || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Uživatelské jméno:</div>
            <div class="data-value">${profile.username || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">URL profilu:</div>
            <div class="data-value">${profile.profileUrl || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Současné bydliště:</div>
            <div class="data-value">${profile.currentLocation || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Současné zaměstnání:</div>
            <div class="data-value">${profile.currentJob || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Vzdělání:</div>
            <div class="data-value">${profile.education || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Vztahový stav:</div>
            <div class="data-value">${profile.relationshipStatus || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Počet přátel:</div>
            <div class="data-value">${profile.friendsCount || 'Neznámý'}</div>
          </div>
        </div>

        ${profile.aboutMe ? `
        <div class="note-block">
          <div class="note-title">O osobě:</div>
          <div class="note-text">${profile.aboutMe}</div>
        </div>
        ` : ''}

        ${profile.contactInfo ? `
        <div class="note-block">
          <div class="note-title">Kontaktní údaje:</div>
          <div class="note-text">${profile.contactInfo}</div>
        </div>
        ` : ''}

        ${profile.investigationNotes ? `
        <div class="note-block">
          <div class="note-title">Poznámky k vyšetřování:</div>
          <div class="note-text">${profile.investigationNotes}</div>
        </div>
        ` : ''}

        ${this.generateFacebookPostsHTML(profile.posts)}
        ${this.generateFacebookPhotosHTML(profile.photos)}
      </div>
      `;
    });

    return html;
  }

  private generateFacebookPostsHTML(posts: any[]): string {
    if (!posts || posts.length === 0) return '';

    let html = `
    <div class="sub-block">
      <div class="sub-title">Příspěvky</div>
      <div class="data-list">
    `;

    posts.forEach((post, index) => {
      html += `
      <div class="data-item">
        <div class="item-primary">${post.date ? `Datum: ${post.date}` : 'Datum neznámé'}</div>
        <div class="item-text">${post.text || ''}</div>
        ${post.likesCount ? `<div class="item-secondary">Počet reakcí: ${post.likesCount}</div>` : ''}
        ${post.sharesCount ? `<div class="item-secondary">Počet sdílení: ${post.sharesCount}</div>` : ''}
        ${post.notes ? `<div class="item-secondary">Poznámky: ${post.notes}</div>` : ''}
      </div>
      `;
    });

    html += `
      </div>
    </div>
    `;

    return html;
  }

  private generateFacebookPhotosHTML(photos: any[]): string {
    if (!photos || photos.length === 0) return '';

    let html = `
    <div class="sub-block">
      <div class="sub-title">Fotografie</div>
      <div class="photo-gallery">
    `;

    photos.forEach((photo, index) => {
      html += `
      <div class="photo-item" style="text-align: center; margin-bottom: 20px;">
        ${photo.downloadURL ? `<img src="${photo.downloadURL}" alt="Fotografie ${index + 1}" style="max-width: 100%; max-height: 400px; margin: 0 auto; display: block;">` : ''}
        ${photo.description ? `<div class="photo-description" style="text-align: center; margin-top: 10px;">${photo.description}</div>` : ''}
        ${photo.dateTaken ? `<div class="photo-date" style="text-align: center; margin-top: 5px;">Datum pořízení: ${photo.dateTaken}</div>` : ''}
      </div>
      `;
    });

    html += `
      </div>
    </div>
    `;

    return html;
  }

  private generateInstagramHTML(data: any[]): string {
    if (!data || data.length === 0) return '';

    let html = '';

    data.forEach((profile, index) => {
      html += `
      <div class="data-block">
        <div class="data-title">Instagram profil: ${profile.displayName || profile.username || 'Neurčený název'}</div>

        <div class="data-grid">
          <div class="data-row">
            <div class="data-label">Jméno na profilu:</div>
            <div class="data-value emphasized">${profile.displayName || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Uživatelské jméno:</div>
            <div class="data-value">@${profile.username || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">URL profilu:</div>
            <div class="data-value">${profile.profileUrl || ''}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Počet sledujících:</div>
            <div class="data-value">${profile.followersCount || 'Neznámý'}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Sleduje účtů:</div>
            <div class="data-value">${profile.followingCount || 'Neznámý'}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Počet příspěvků:</div>
            <div class="data-value">${profile.postsCount || 'Neznámý'}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Ověřený účet:</div>
            <div class="data-value">${profile.isVerified ? 'Ano' : 'Ne'}</div>
          </div>
          <div class="data-row">
            <div class="data-label">Soukromý účet:</div>
            <div class="data-value">${profile.isPrivate ? 'Ano' : 'Ne'}</div>
          </div>
        </div>

        ${profile.bio ? `
        <div class="note-block">
          <div class="note-title">Bio:</div>
          <div class="note-text">${profile.bio}</div>
        </div>
        ` : ''}

        ${profile.website ? `
        <div class="note-block">
          <div class="note-title">Webová stránka:</div>
          <div class="note-text">${profile.website}</div>
        </div>
        ` : ''}

        ${profile.notes ? `
        <div class="note-block">
          <div class="note-title">Poznámky:</div>
          <div class="note-text">${profile.notes}</div>
        </div>
        ` : ''}

        ${this.generateInstagramPostsHTML(profile.posts)}
        ${this.generateInstagramReelsHTML(profile.reels)}
        ${this.generateInstagramStoriesHTML(profile.stories)}
        ${this.generateInstagramPhotosHTML(profile.photos)}
      </div>
      `;
    });

    return html;
  }

  private generateInstagramPostsHTML(posts: any[]): string {
    if (!posts || posts.length === 0) return '';

    let html = `
    <div class="sub-block">
      <div class="sub-title">Příspěvky</div>
      <div class="data-list">
    `;

    posts.forEach((post, index) => {
      html += `
      <div class="data-item">
        <div class="item-primary">${post.date ? `Datum: ${post.date}` : 'Datum neznámé'}</div>
        <div class="item-text">${post.caption || ''}</div>
        ${post.hashtags ? `<div class="item-secondary">Hashtagy: ${Array.isArray(post.hashtags) ? post.hashtags.join(', ') : post.hashtags}</div>` : ''}
        ${post.likesCount ? `<div class="item-secondary">Počet reakcí: ${post.likesCount}</div>` : ''}
        ${post.commentsCount ? `<div class="item-secondary">Počet komentářů: ${post.commentsCount}</div>` : ''}
        ${post.location ? `<div class="item-secondary">Lokace: ${post.location}</div>` : ''}
      </div>
      `;
    });

    html += `
      </div>
    </div>
    `;

    return html;
  }

  private generateInstagramReelsHTML(reels: any[]): string {
    if (!reels || reels.length === 0) return '';

    let html = `
    <div class="sub-block">
      <div class="sub-title">Reels</div>
      <div class="data-list">
    `;

    reels.forEach((reel, index) => {
      html += `
      <div class="data-item">
        <div class="item-primary">${reel.date ? `Datum: ${reel.date}` : 'Datum neznámé'}</div>
        <div class="item-text">${reel.caption || ''}</div>
        ${reel.hashtags ? `<div class="item-secondary">Hashtagy: ${Array.isArray(reel.hashtags) ? reel.hashtags.join(', ') : reel.hashtags}</div>` : ''}
        ${reel.likesCount ? `<div class="item-secondary">Počet reakcí: ${reel.likesCount}</div>` : ''}
        ${reel.commentsCount ? `<div class="item-secondary">Počet komentářů: ${reel.commentsCount}</div>` : ''}
        ${reel.duration ? `<div class="item-secondary">Délka: ${reel.duration} sekund</div>` : ''}
      </div>
      `;
    });

    html += `
      </div>
    </div>
    `;

    return html;
  }

  private generateInstagramStoriesHTML(stories: any[]): string {
    if (!stories || stories.length === 0) return '';

    let html = `
    <div class="sub-block">
      <div class="sub-title">Stories</div>
      <div class="data-list">
    `;

    stories.forEach((story, index) => {
      html += `
      <div class="data-item">
        <div class="item-primary">${story.date ? `Datum: ${story.date}` : 'Datum neznámé'}</div>
        <div class="item-text">${story.content || ''}</div>
        ${story.type ? `<div class="item-secondary">Typ: ${story.type}</div>` : ''}
        ${story.location ? `<div class="item-secondary">Lokace: ${story.location}</div>` : ''}
      </div>
      `;
    });

    html += `
      </div>
    </div>
    `;

    return html;
  }

  private generateInstagramPhotosHTML(photos: any[]): string {
    if (!photos || photos.length === 0) return '';

    let html = `
    <div class="sub-block">
      <div class="sub-title">Fotografie</div>
      <div class="photo-gallery">
    `;

    photos.forEach((photo, index) => {
      html += `
      <div class="photo-item" style="text-align: center; margin-bottom: 20px;">
        ${photo.downloadURL ? `<img src="${photo.downloadURL}" alt="Fotografie ${index + 1}" style="max-width: 100%; max-height: 400px; margin: 0 auto; display: block;">` : ''}
        ${photo.description ? `<div class="photo-description" style="text-align: center; margin-top: 10px;">${photo.description}</div>` : ''}
        ${photo.dateTaken ? `<div class="photo-date" style="text-align: center; margin-top: 5px;">Datum pořízení: ${photo.dateTaken}</div>` : ''}
      </div>
      `;
    });

    html += `
      </div>
    </div>
    `;

    return html;
  }

  private calculatePages(modules: string[]): number {
    return Math.max(modules.length + 1, 3); // Minimálně 3 stránky
  }

  private extractInvestigatorName(department: string): string {
    // Extrahuje jméno ze sekce department
    const lines = department.split('\n');
    const nameLine = lines.find(line => line.includes('Vyšetřovatel:') || line.includes('Zpracoval:'));
    if (nameLine) {
      return nameLine.split(':')[1]?.trim() || 'Jméno vyšetřovatele';
    }
    return 'Jméno vyšetřovatele';
  }
}
