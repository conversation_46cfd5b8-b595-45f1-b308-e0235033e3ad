"use client";

import { useEffect, useState } from "react";
import { useParams } from "next/navigation";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { EmailAnalysisModuleData, Subject } from "@/types";
import { EmailAnalysisModule } from "@/components/modules/email-analysis/EmailAnalysisModule";
import { ModulePageSkeleton } from "@/components/modules/ModulePageSkeleton";
import { useRouter } from "next/navigation";
import { useToast } from "@/hooks/use-toast";

export default function EmailAnalysisModulePage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [subject, setSubject] = useState<Subject | null>(null);
  const [moduleData, setModuleData] = useState<EmailAnalysisModuleData | null>(null);

  const caseId = params.caseId as string;
  const subjectId = params.subjectId as string;
  const moduleId = "email_analysis";

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Načtení subjektu
        const subjectDocRef = doc(db, "cases", caseId, "subjects", subjectId);
        const subjectSnap = await getDoc(subjectDocRef);
        
        if (!subjectSnap.exists()) {
          toast({
            title: "Subjekt nenalezen",
            description: "Požadovaný subjekt neexistuje nebo byl smazán.",
            variant: "destructive",
          });
          router.push(`/dashboard/cases/${caseId}`);
          return;
        }
        
        setSubject({ id: subjectSnap.id, ...subjectSnap.data() } as Subject);
        
        // Načtení dat modulu
        const moduleDocRef = doc(db, "cases", caseId, "subjects", subjectId, "moduleData", moduleId);
        const moduleSnap = await getDoc(moduleDocRef);
        
        if (moduleSnap.exists()) {
          setModuleData({ id: moduleSnap.id, ...moduleSnap.data() } as EmailAnalysisModuleData);
        } else {
          setModuleData(null);
        }
      } catch (error: any) {
        toast({
          title: "Chyba při načítání dat",
          description: error.message,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };
    
    if (caseId && subjectId) {
      fetchData();
    }
  }, [caseId, subjectId, router, toast]);

  const handleModuleSaved = (moduleId: string, wasNew: boolean) => {
    toast({
      title: "Data modulu uložena",
      description: "Informace o emailové analýze byly úspěšně uloženy.",
    });
    
    // Přesměrování zpět na detail případu
    router.push(`/dashboard/cases/${caseId}`);
  };

  if (loading) {
    return <ModulePageSkeleton title="Emailová analýza" />;
  }

  if (!subject) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold">Subjekt nenalezen</h1>
          <p className="mt-2">Požadovaný subjekt neexistuje nebo byl smazán.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <EmailAnalysisModule
        caseId={caseId}
        subject={subject}
        onModuleSaved={handleModuleSaved}
      />
    </div>
  );
}
