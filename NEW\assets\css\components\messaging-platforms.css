/* Styly pro modul monitoringu komunikačních platforem */

/* <PERSON><PERSON><PERSON>žky pro platformy */
.messaging-platforms-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 15px;
    overflow-x: auto;
    scrollbar-width: thin;
}

.messaging-tab {
    padding: 10px 15px;
    background: none;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-weight: 500;
    color: var(--dark-text);
    transition: all 0.2s ease;
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 8px;
}

.messaging-tab i {
    font-size: 16px;
}

.messaging-tab:hover {
    color: var(--primary-color);
    background-color: rgba(0, 0, 0, 0.03);
}

.messaging-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* Obsah platforem */
.messaging-content {
    display: none;
}

.messaging-content.active {
    display: block;
}

.platform-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.platform-header h4 {
    margin: 0;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.platform-actions {
    display: flex;
    gap: 8px;
}

/* Kontejner kanálů */
.channels-container {
    display: flex;
    gap: 20px;
    height: 500px;
}

.channels-list {
    width: 300px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow-y: auto;
    background-color: #f9f9f9;
    flex-shrink: 0;
}

.channel-detail {
    flex: 1;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow-y: auto;
    background-color: white;
    padding: 15px;
}

/* Položky kanálů */
.channel-item {
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.channel-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.channel-item.active {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-left: 3px solid var(--primary-color);
}

.channel-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.channel-name {
    font-weight: 500;
    color: var(--dark-text);
}

.channel-type {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.1);
    color: var(--dark-text);
}

.channel-info {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: var(--muted-text);
}

.channel-members {
    display: flex;
    align-items: center;
    gap: 4px;
}

.channel-last-update {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* Prázdné stavy */
.no-channels-message, .no-channel-selected {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px;
    text-align: center;
    color: var(--muted-text);
    height: 100%;
}

.no-channels-message i, .no-channel-selected i {
    font-size: 32px;
    margin-bottom: 15px;
    opacity: 0.5;
}

/* Detail kanálu */
.channel-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.channel-detail-title {
    font-size: 18px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.channel-detail-actions {
    display: flex;
    gap: 8px;
}

.channel-detail-info {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 20px;
}

.channel-detail-info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.channel-detail-info-label {
    font-size: 12px;
    color: var(--muted-text);
}

.channel-detail-info-value {
    font-weight: 500;
}

.channel-detail-tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 15px;
}

.channel-detail-tab {
    padding: 8px 15px;
    background: none;
    border: none;
    border-bottom: 2px solid transparent;
    cursor: pointer;
    font-weight: 500;
    color: var(--dark-text);
    transition: all 0.2s ease;
}

.channel-detail-tab:hover {
    color: var(--primary-color);
}

.channel-detail-tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

.channel-detail-content {
    display: none;
}

.channel-detail-content.active {
    display: block;
}

/* Zprávy */
.message-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.message-item {
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: #f9f9f9;
}

.message-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.message-sender {
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.message-sender-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    overflow: hidden;
}

.message-sender-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.message-time {
    font-size: 12px;
    color: var(--muted-text);
}

.message-content {
    margin-bottom: 8px;
    line-height: 1.5;
}

.message-attachments {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.message-attachment {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    width: 100px;
    height: 100px;
    position: relative;
}

.message-attachment img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.message-attachment-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.message-attachment:hover .message-attachment-overlay {
    opacity: 1;
}

.message-attachment-actions {
    display: flex;
    gap: 8px;
}

.message-attachment-action {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--dark-text);
    transition: all 0.2s ease;
}

.message-attachment-action:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Nastavení */
.settings-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.settings-section {
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    background-color: #f9f9f9;
}

.settings-section h5 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    color: var(--primary-color);
}

/* Fotogalerie */
.messaging-platforms-gallery-container {
    margin-top: 30px;
}

.messaging-platforms-gallery-container h5 {
    margin-bottom: 15px;
    font-size: 16px;
    color: var(--primary-color);
}

/* Responzivní design */
@media (max-width: 768px) {
    .channels-container {
        flex-direction: column;
        height: auto;
    }

    .channels-list {
        width: 100%;
        max-height: 300px;
    }
}

/* Dialog pro přidání kanálu/serveru/skupiny */
.add-channel-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1100;
    animation: fade-in 0.3s ease;
}

.add-channel-dialog-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.add-channel-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
}

.add-channel-dialog-header h3 {
    margin: 0;
    font-size: 20px;
    color: var(--primary-color);
}

.add-channel-dialog-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    font-size: 18px;
    padding: 5px;
}

.add-channel-dialog-body {
    padding: 20px;
}

.add-channel-dialog-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Dialog pro zobrazení zpráv uživatele */
.user-messages-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1100;
    animation: fade-in 0.3s ease;
}

.user-messages-dialog-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.user-messages-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
}

.user-messages-dialog-header h3 {
    margin: 0;
    font-size: 20px;
    color: var(--primary-color);
}

.user-messages-dialog-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    font-size: 18px;
    padding: 5px;
}

.user-messages-dialog-body {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e9ecef;
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    overflow: hidden;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.user-name {
    font-weight: 500;
    font-size: 18px;
}

.user-username {
    color: var(--muted-text);
    font-size: 14px;
}

.user-platform {
    font-size: 14px;
    color: var(--primary-color);
}

.user-messages-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.user-message-item {
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: #f9f9f9;
}

.user-message-time {
    font-size: 12px;
    color: var(--muted-text);
    margin-bottom: 5px;
}

.user-message-content {
    margin-bottom: 8px;
    line-height: 1.5;
}

.user-message-attachments {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.user-message-attachment {
    border: 1px solid var(--border-color);
    border-radius: 4px;
    overflow: hidden;
    width: 100px;
    height: 100px;
    position: relative;
}

.user-message-attachment img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-message-attachment.document {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 8px;
    width: auto;
    height: auto;
    background-color: #f0f0f0;
}

.user-messages-dialog-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
}

/* Dialog pro zobrazení média */
.media-viewer-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1100;
    animation: fade-in 0.3s ease;
}

.media-viewer-dialog-content {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
}

.media-viewer-dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
}

.media-viewer-dialog-header h3 {
    margin: 0;
    font-size: 20px;
    color: var(--primary-color);
}

.media-viewer-dialog-close {
    background: none;
    border: none;
    color: #6c757d;
    cursor: pointer;
    font-size: 18px;
    padding: 5px;
}

.media-viewer-dialog-body {
    padding: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f0f0f0;
}

.media-viewer-image {
    max-width: 100%;
    max-height: 60vh;
    object-fit: contain;
}

.media-viewer-dialog-footer {
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

@keyframes fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
