"use client";

import { z } from "zod";
import { YesNoUnknown } from "@/types";

// Photo Metadata Schema
export const photoMetadataSchema = z.object({
  id: z.string(),
  fileName: z.string().optional(),
  storagePath: z.string().optional(),
  downloadURL: z.string().optional(),
  description: z.string().optional(),
  dateTaken: z.string().optional(),
  sourceURL: z.string().optional(),
  location: z.string().optional(),
  notes: z.string().optional(),
});
export type PhotoMetadata = z.infer<typeof photoMetadataSchema>;

// Training Types
export const trainingTypes = [
  "professional",
  "military",
  "security",
  "weapons",
  "martial_arts",
  "driving",
  "aviation",
  "diving",
  "survival",
  "medical",
  "technical",
  "language",
  "police",
  "intelligence",
  "special_forces",
  "combat",
  "tactical",
  "explosives",
  "sniper",
  "reconnaissance",
  "other"
] as const;

// Training Status
export const trainingStatuses = [
  "completed",
  "ongoing",
  "planned",
  "abandoned",
  "failed"
] as const;

// Certificate Validity
export const certificateValidities = [
  "valid",
  "expired",
  "revoked",
  "unknown"
] as const;

// Training Sources
export const trainingSources = [
  "self_reported",
  "verified",
  "social_media",
  "witness",
  "intelligence",
  "public_records",
  "other"
] as const;

// Martial Arts Belt Colors
export const martialArtsBeltColors = [
  "white",
  "yellow",
  "orange",
  "green",
  "blue",
  "purple",
  "brown",
  "black",
  "red",
  "other"
] as const;

// Military Ranks
export const militaryRanks = [
  "private",
  "corporal",
  "sergeant",
  "lieutenant",
  "captain",
  "major",
  "colonel",
  "general",
  "other"
] as const;

// Police Ranks
export const policeRanks = [
  "rotny",
  "strazmistr",
  "nadstrazmistr",
  "podpraporcik",
  "praporcik",
  "nadpraporcik",
  "podporucik",
  "porucik",
  "nadporucik",
  "kapitan",
  "major",
  "podplukovnik",
  "plukovnik",
  "general",
  "other"
] as const;

// Danger Assessment
export const dangerAssessments = [
  "none",
  "low",
  "medium",
  "high",
  "extreme",
  "unknown"
] as const;

// Training Record Schema
export const trainingRecordSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Název školení je povinný"),
  trainingType: z.enum(trainingTypes),
  otherTrainingTypeDetail: z.string().optional(),
  institution: z.string().optional(),
  location: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  status: z.enum(trainingStatuses),
  description: z.string().optional(),
  skills: z.array(z.string()).optional().default([]),

  // Certifikace
  certificateObtained: z.boolean().default(false),
  certificateName: z.string().optional(),
  certificateId: z.string().optional(),
  certificateValidity: z.enum(certificateValidities).optional(),
  certificateValidUntil: z.string().optional(),

  // Informace o zdroji
  source: z.enum(trainingSources).optional(),
  otherSourceDetail: z.string().optional(),
  verificationStatus: z.enum(["yes", "no", "unknown"] as [YesNoUnknown, ...YesNoUnknown[]]).optional(),
  verificationNotes: z.string().optional(),

  // Rozšířené informace o bojových uměních
  martialArtStyle: z.string().optional(),
  beltColor: z.enum(martialArtsBeltColors).optional(),
  beltDegree: z.number().int().min(0).max(10).optional(),
  competitionAchievements: z.string().optional(),

  // Vojenské informace
  militaryBranch: z.string().optional(),
  militarySpecialization: z.string().optional(),
  militaryRank: z.enum(militaryRanks).optional(),
  militaryUnit: z.string().optional(),
  conscriptionService: z.boolean().optional().default(false),
  conscriptionYears: z.string().optional(),

  // Policejní informace
  policeDepartment: z.string().optional(),
  policeSpecialization: z.string().optional(),
  policeRank: z.enum(policeRanks).optional(),
  policeUnit: z.string().optional(),

  // Informace o zbraních
  weaponTypes: z.array(z.string()).optional().default([]),
  weaponLicenses: z.array(z.string()).optional().default([]),
  explosivesTraining: z.boolean().optional().default(false),

  // Hodnocení nebezpečnosti
  dangerAssessment: z.enum(dangerAssessments).optional(),
  dangerAssessmentReason: z.string().optional(),

  // Obecné poznámky a dokumentace
  notes: z.string().optional(),
  photos: z.array(photoMetadataSchema).optional().default([]),
  documentReferences: z.string().optional(),
}).superRefine((data, ctx) => {
  if (data.trainingType === 'other' && !data.otherTrainingTypeDetail?.trim()) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Upřesnění typu školení je povinné.", path: ['otherTrainingTypeDetail'] });
  }

  if (data.source === 'other' && !data.otherSourceDetail?.trim()) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Upřesnění zdroje je povinné.", path: ['otherSourceDetail'] });
  }

  if (data.startDate && data.endDate) {
    const start = new Date(data.startDate);
    const end = new Date(data.endDate);
    if (start > end) {
      ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Datum zahájení nemůže být později než datum ukončení.", path: ['startDate'] });
    }
  }

  if (data.certificateObtained) {
    if (!data.certificateName?.trim()) {
      ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Název certifikátu je povinný, pokud byl certifikát získán.", path: ['certificateName'] });
    }
  }

  // Validace pro bojová umění
  if (data.trainingType === 'martial_arts') {
    if (!data.martialArtStyle?.trim()) {
      ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Styl bojového umění je povinný pro tento typ výcviku.", path: ['martialArtStyle'] });
    }
  }

  // Validace pro vojenský výcvik
  if (data.trainingType === 'military' || data.trainingType === 'special_forces') {
    if (!data.militaryBranch?.trim()) {
      ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Složka ozbrojených sil je povinná pro tento typ výcviku.", path: ['militaryBranch'] });
    }
  }

  // Validace pro policejní výcvik
  if (data.trainingType === 'police') {
    if (!data.policeDepartment?.trim()) {
      ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Oddělení policie je povinné pro tento typ výcviku.", path: ['policeDepartment'] });
    }
  }

  // Validace pro výcvik se zbraněmi
  if (data.trainingType === 'weapons' && data.weaponTypes?.length === 0) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Uveďte alespoň jeden typ zbraně pro tento výcvik.", path: ['weaponTypes'] });
  }
});

export type TrainingRecord = z.infer<typeof trainingRecordSchema>;

// Training Module Schema
export const trainingModuleSchema = z.object({
  trainings: z.array(trainingRecordSchema).optional().default([]),
  generalNotes: z.string().optional(),
});

export type TrainingModuleFormValues = z.infer<typeof trainingModuleSchema>;
