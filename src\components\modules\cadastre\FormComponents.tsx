"use client";

import React from "react";
import { <PERSON>, Controller, FieldPath, FieldV<PERSON><PERSON>, use<PERSON><PERSON><PERSON>er, useFieldArray } from "react-hook-form";
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { PlusCircle, Trash2 } from "lucide-react";

interface FormItemRHFProps<T extends FieldValues> {
  label: string;
  name: FieldPath<T>;
  control: Control<T>;
  placeholder?: string;
  description?: string;
  type?: string;
  as?: "input" | "textarea" | "switch";
  rows?: number;
  className?: string;
  smallLabel?: boolean;
  disabled?: boolean;
}

// Jednoduchá verze FormItemRHF bez memoizace
export function FormItemRHF<T extends FieldValues>({
  label,
  name,
  control,
  placeholder,
  description,
  type = "text",
  as = "input",
  rows = 3,
  className,
  smallLabel = false,
  disabled = false,
}: FormItemRHFProps<T>) {
  // Vrátíme zpět k použití Controller komponenty
  if (as === "textarea") {
    return (
      <div className={cn("w-full", className)}>
        <FormLabel className={cn("font-semibold", smallLabel ? "text-xs" : "text-sm")}>{label}</FormLabel>
        <Controller
          control={control}
          name={name}
          disabled={disabled}
          render={({ field, fieldState: { error } }) => (
            <>
              <Textarea
                placeholder={placeholder}
                rows={rows}
                disabled={disabled}
                {...field}
                value={field.value || ""}
                className={cn(smallLabel ? "text-xs py-1" : "text-sm", error ? "border-destructive" : "")}
              />
              {description && <FormDescription>{description}</FormDescription>}
              {error && <FormMessage className="text-xs">{error.message}</FormMessage>}
            </>
          )}
        />
      </div>
    );
  } else if (as === "switch") {
    return (
      <div className={cn("w-full", className)}>
        <Controller
          control={control}
          name={name}
          disabled={disabled}
          render={({ field, fieldState: { error } }) => (
            <>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                  id={name}
                />
                <FormLabel htmlFor={name} className={cn("!mt-0 cursor-pointer", smallLabel ? "text-xs" : "text-sm")}>
                  {label}
                </FormLabel>
              </div>
              {description && <FormDescription>{description}</FormDescription>}
              {error && <FormMessage className="text-xs">{error.message}</FormMessage>}
            </>
          )}
        />
      </div>
    );
  } else {
    return (
      <div className={cn("w-full", className)}>
        <FormLabel className={cn("font-semibold", smallLabel ? "text-xs" : "text-sm")}>{label}</FormLabel>
        <Controller
          control={control}
          name={name}
          disabled={disabled}
          render={({ field, fieldState: { error } }) => (
            <>
              <Input
                type={type}
                placeholder={placeholder}
                disabled={disabled}
                {...field}
                value={field.value || ""}
                className={cn(smallLabel ? "text-xs h-8 py-1" : "text-sm h-10", error ? "border-destructive" : "")}
              />
              {description && <FormDescription>{description}</FormDescription>}
              {error && <FormMessage className="text-xs">{error.message}</FormMessage>}
            </>
          )}
        />
      </div>
    );
  }
}



interface SelectOption {
  value: string;
  label: string;
}

interface FormItemSelectRHFProps<T extends FieldValues> {
  label: string;
  name: FieldPath<T>;
  control: Control<T>;
  options: SelectOption[];
  placeholder?: string;
  description?: string;
  className?: string;
  smallLabel?: boolean;
  disabled?: boolean;
}

// Jednoduchá verze FormItemSelectRHF bez memoizace
export function FormItemSelectRHF<T extends FieldValues>({
  label,
  name,
  control,
  options,
  placeholder,
  description,
  className,
  smallLabel = false,
  disabled = false,
}: FormItemSelectRHFProps<T>) {
  // Vrátíme zpět k použití Controller komponenty
  return (
    <div className={cn("w-full", className)}>
      <FormLabel className={cn("font-semibold", smallLabel ? "text-xs" : "text-sm")}>{label}</FormLabel>
      <Controller
        control={control}
        name={name}
        disabled={disabled}
        render={({ field, fieldState: { error } }) => (
          <>
            <Select
              onValueChange={field.onChange}
              defaultValue={field.value}
              value={field.value}
              disabled={disabled}
            >
              <SelectTrigger className={cn(smallLabel ? "text-xs h-8 py-1" : "text-sm h-10", error ? "border-destructive" : "")}>
                <SelectValue placeholder={placeholder} />
              </SelectTrigger>
              <SelectContent>
                {options.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {description && <FormDescription>{description}</FormDescription>}
            {error && <FormMessage className="text-xs">{error.message}</FormMessage>}
          </>
        )}
      />
    </div>
  );
}

interface StringArrayFieldProps<T extends FieldValues> {
  label: string;
  name: FieldPath<T>;
  control: Control<T>;
  placeholder?: string;
  addButtonText?: string;
  itemLabel?: string;
}

// Jednoduchá verze StringArrayField bez memoizace
export function StringArrayField<T extends FieldValues>({
  label,
  name,
  control,
  placeholder = "Přidat položku...",
  addButtonText = "Přidat položku",
  itemLabel = "Položka",
}: StringArrayFieldProps<T>) {
  const { fields, append, remove } = useFieldArray({
    control,
    name,
  });

  return (
    <div className="space-y-2">
      <FormLabel className="font-semibold text-sm">{label}</FormLabel>
      <div className="space-y-2">
        {fields.map((field, index) => (
          <div key={field.id} className="flex items-center gap-2">
            <div className="flex-grow">
              <Controller
                control={control}
                name={`${name}.${index}` as any}
                render={({ field }) => (
                  <div className="flex items-center gap-2">
                    <Input
                      {...field}
                      placeholder={`${itemLabel} ${index + 1}`}
                      className="text-sm h-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => remove(index)}
                      className="text-destructive hover:bg-destructive/10 h-8 w-8"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              />
            </div>
          </div>
        ))}
      </div>
      <Button
        type="button"
        variant="outline"
        size="sm"
        onClick={() => append("")}
        className="mt-2"
      >
        <PlusCircle className="mr-2 h-4 w-4" />
        {addButtonText}
      </Button>
    </div>
  );
}
