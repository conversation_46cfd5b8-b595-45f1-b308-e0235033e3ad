'use server';
/**
 * @fileOverview A Genkit flow to search companies by name in ARES API.
 *
 * - searchCompaniesInAres - Searches companies by name in ARES.
 * - AresSearchInput - The input type for the searchCompaniesInAres function.
 * - AresSearchOutput - The return type for the searchCompaniesInAres function.
 */

import { ai } from '@/ai/genkit';
import { z } from 'genkit';

const AresSearchInputSchema = z.object({
  companyName: z.string().min(3, "Název společnosti musí mít alespoň 3 znaky.").describe('The name of the company to search for.'),
});
export type AresSearchInput = z.infer<typeof AresSearchInputSchema>;

const AresCompanyResultSchema = z.object({
  ico: z.string().describe('The IČO of the company.'),
  name: z.string().describe('The official name of the company.'),
  address: z.string().describe('The registered address of the company.'),
  legalForm: z.string().optional().describe('The legal form of the company (e.g., s.r.o., a.s.).'),
});

const AresSearchOutputSchema = z.object({
  companies: z.array(AresCompanyResultSchema).describe('List of companies matching the search criteria.'),
  totalCount: z.number().describe('Total number of companies found.'),
});
export type AresSearchOutput = z.infer<typeof AresSearchOutputSchema>;

// Helper function to process search results from ARES
function processAresSearchResults(data: any): AresSearchOutput {
  // Handle direct lookup result (single entity)
  if (data && data.ico && !data.ekonomickeSubjekty) {
    // Format address for direct lookup
    let formattedAddress = 'Adresa není k dispozici';
    if (data.sidlo) {
      if (data.sidlo.textovaAdresa) {
        formattedAddress = data.sidlo.textovaAdresa;
      } else {
        // Attempt to construct from parts if textovaAdresa is missing
        const { ulice, cisloDomovni, cisloOrientacni, nazevObce, psc, stat } = data.sidlo;
        let addressParts = [];
        if (ulice && (cisloDomovni || cisloOrientacni)) {
          addressParts.push(`${ulice} ${cisloDomovni || ''}${cisloOrientacni ? `/${cisloOrientacni}` : ''}`.trim());
        } else if (ulice) {
          addressParts.push(ulice);
        }
        if (nazevObce) addressParts.push(nazevObce);
        if (psc) addressParts.push(psc);
        if (stat) addressParts.push(stat);
        if (addressParts.length > 0) {
          formattedAddress = addressParts.join(', ');
        }
      }
    }

    // Create a single-item array with the direct lookup result
    return {
      companies: [{
        ico: data.ico || '',
        name: data.obchodniJmeno || '',
        legalForm: data.pravniForma?.nazev || '',
        address: formattedAddress,
      }],
      totalCount: 1,
    };
  }

  // Handle search results (multiple entities)
  if (!data || !data.ekonomickeSubjekty) {
    return { companies: [], totalCount: 0 };
  }

  const companies = data.ekonomickeSubjekty.map((company: any) => {
    // Format address
    let formattedAddress = 'Adresa není k dispozici';
    if (company.sidlo) {
      if (company.sidlo.textovaAdresa) {
        formattedAddress = company.sidlo.textovaAdresa;
      } else {
        // Attempt to construct from parts if textovaAdresa is missing
        const { ulice, cisloDomovni, cisloOrientacni, nazevObce, psc, stat } = company.sidlo;
        let addressParts = [];
        if (ulice && (cisloDomovni || cisloOrientacni)) {
          addressParts.push(`${ulice} ${cisloDomovni || ''}${cisloOrientacni ? `/${cisloOrientacni}` : ''}`.trim());
        } else if (ulice) {
          addressParts.push(ulice);
        }
        if (nazevObce) addressParts.push(nazevObce);
        if (psc) addressParts.push(psc);
        if (stat) addressParts.push(stat);
        if (addressParts.length > 0) {
          formattedAddress = addressParts.join(', ');
        }
      }
    }

    return {
      ico: company.ico || '',
      name: company.obchodniJmeno || '',
      legalForm: company.pravniForma?.nazev || undefined,
      address: formattedAddress,
    };
  });

  return {
    companies,
    totalCount: data.pocetCelkem || companies.length,
  };
}

export async function searchCompaniesInAres(input: AresSearchInput): Promise<AresSearchOutput> {
  return aresSearchFlow(input);
}

const aresSearchFlow = ai.defineFlow(
  {
    name: 'aresSearchFlow',
    inputSchema: AresSearchInputSchema,
    outputSchema: AresSearchOutputSchema,
  },
  async ({ companyName }: AresSearchInput) => {
    console.log('aresSearchFlow: Vyhledání firem podle názvu v ARES:', companyName);
    const encodedName = encodeURIComponent(companyName);

    // Determine if the search term is likely an IČO (8 digits) or a name
    const isIcoSearch = /^\d{8}$/.test(companyName);

    let url;
    if (isIcoSearch) {
      // If it's an IČO, use the direct lookup endpoint
      url = `https://ares.gov.cz/ekonomicke-subjekty-v-be/rest/ekonomicke-subjekty/${companyName}`;
    } else {
      // Otherwise search by name, including both legal entities and individuals
      url = `https://ares.gov.cz/ekonomicke-subjekty-v-be/rest/ekonomicke-subjekty/vyhledat?obchodniJmeno=${encodedName}&pocet=10&typ=vse`;
    }

    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        const errorBody = await response.text();
        console.error('ARES API error response:', errorBody);
        throw new Error(`Chyba při komunikaci s ARES API: ${response.status} ${response.statusText}. Detail: ${errorBody}`);
      }

      const data = await response.json();
      console.log('aresSearchFlow: Data z ARES API:', data);

      const processedData = processAresSearchResults(data);
      console.log('aresSearchFlow: Zpracovaná data:', processedData);
      return processedData;

    } catch (error: any) {
      console.error('aresSearchFlow: Selhání fetch operace nebo zpracování:', error);
      if (error instanceof Error) {
        throw error; // Re-throw known errors
      }
      throw new Error('Nepodařilo se navázat spojení s ARES API nebo zpracovat odpověď.');
    }
  }
);
