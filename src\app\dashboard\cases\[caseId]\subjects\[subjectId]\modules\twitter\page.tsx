"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Subject } from "@/types"; // Assuming a generic Subject type or a specific TwitterModuleData type
import TwitterModule from "@/components/modules/twitter/TwitterModule"; // Adjust path as needed
import { ModulePageSkeleton } from "@/components/modules/ModulePageSkeleton";
import { useToast } from "@/hooks/use-toast";

// Define a type for Twitter module data if you have one, otherwise use 'any' or a generic
// import { TwitterModuleData } from "@/types/twitter"; // Example

export default function TwitterModulePage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [subject, setSubject] = useState<Subject | null>(null);
  // const [moduleData, setModuleData] = useState<TwitterModuleData | null>(null); // Use specific type if available

  const caseId = params.caseId as string;
  const subjectId = params.subjectId as string;
  const moduleId = "twitter"; // Module ID for Twitter

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Fetch subject data
        const subjectDocRef = doc(db, "cases", caseId, "subjects", subjectId);
        const subjectSnap = await getDoc(subjectDocRef);
        
        if (!subjectSnap.exists()) {
          toast({
            title: "Subjekt nenalezen",
            description: "Požadovaný subjekt neexistuje nebo byl smazán.",
            variant: "destructive",
          });
          router.push(`/dashboard/cases/${caseId}`);
          return;
        }
        
        setSubject({ id: subjectSnap.id, ...subjectSnap.data() } as Subject);
        
        // Fetch module data (if it exists, for now it's a new module)
        // const moduleDocRef = doc(db, "cases", caseId, "subjects", subjectId, "moduleData", moduleId);
        // const moduleSnap = await getDoc(moduleDocRef);
        // if (moduleSnap.exists()) {
        //   setModuleData({ id: moduleSnap.id, ...moduleSnap.data() } as TwitterModuleData);
        // } else {
        //   setModuleData(null); // Or initialize with default structure
        // }
      } catch (error: any) {
        toast({
          title: "Chyba při načítání dat",
          description: error.message,
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };
    
    if (caseId && subjectId) {
      fetchData();
    }
  }, [caseId, subjectId, router, toast]);

  if (loading) {
    return <ModulePageSkeleton title="Twitter OSINT" />;
  }

  if (!subject) {
    // This case should be handled by the redirect in useEffect, but as a fallback:
    return <div>Subjekt nenalezen.</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <TwitterModule caseId={caseId} subject={subject} moduleId={moduleId} onClose={() => router.push(`/dashboard/cases/${caseId}/subjects/${subjectId}`)} />
    </div>
  );
} 