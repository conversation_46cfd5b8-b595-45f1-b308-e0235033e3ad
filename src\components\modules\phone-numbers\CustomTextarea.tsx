"use client";

import React, { useRef, useEffect } from 'react';
import { Textarea } from '@/components/ui/textarea';

interface CustomTextareaProps extends React.ComponentProps<'textarea'> {
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  placeholder?: string;
  rows?: number;
  className?: string;
}

export function CustomTextarea({
  value,
  onChange,
  placeholder,
  rows = 3,
  className,
  ...props
}: CustomTextareaProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Tato funkce zajistí, že se focus neztratí po zadání znaku
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e);
  };

  return (
    <Textarea
      ref={textareaRef}
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      rows={rows}
      className={className}
      {...props}
    />
  );
}
