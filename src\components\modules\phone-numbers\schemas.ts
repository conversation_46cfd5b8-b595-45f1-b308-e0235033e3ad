"use client";

import { z } from "zod";
import { 
  PhoneSource, 
  PhoneVerificationStatus, 
  PhoneType, 
  PhoneCarrier, 
  PhoneServiceRegistration 
} from "@/types";

// Photo Metadata Schema
export const photoMetadataSchema = z.object({
  id: z.string(),
  fileName: z.string().optional(),
  storagePath: z.string().optional(),
  downloadURL: z.string().optional(),
  description: z.string().optional(),
  dateTaken: z.string().optional(),
  sourceURL: z.string().optional(),
  location: z.string().optional(),
  notes: z.string().optional(),
});
export type PhotoMetadata = z.infer<typeof photoMetadataSchema>;

// Zdroje telefonních čísel
export const phoneSources: [PhoneSource, ...PhoneSource[]] = [
  "osint",
  "investigation",
  "public_data",
  "social_media",
  "direct_communication",
  "official_document",
  "other"
];

// Stavy ověření telefonních čísel
export const phoneVerificationStatuses: [PhoneVerificationStatus, ...PhoneVerificationStatus[]] = [
  "verified",
  "unverified",
  "invalid",
  "disconnected",
  "unknown"
];

// Typy telefonních čísel
export const phoneTypes: [PhoneType, ...PhoneType[]] = [
  "mobile",
  "landline",
  "business",
  "fax",
  "voip",
  "other"
];

// Operátoři
export const phoneCarriers: [PhoneCarrier, ...PhoneCarrier[]] = [
  "o2",
  "t-mobile",
  "vodafone",
  "other",
  "unknown"
];

// Registrované služby
export const phoneServiceRegistrations: [PhoneServiceRegistration, ...PhoneServiceRegistration[]] = [
  "whatsapp",
  "signal",
  "telegram",
  "viber",
  "facebook",
  "other"
];

// Schéma pro kontakt
export const phoneContactSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Jméno kontaktu je povinné"),
  phoneNumber: z.string().min(1, "Telefonní číslo je povinné"),
  relationship: z.string().optional(),
  notes: z.string().optional(),
});

// Schéma pro aktivitu
export const phoneActivitySchema = z.object({
  id: z.string(),
  date: z.string().min(1, "Datum je povinné"),
  activityType: z.string().min(1, "Typ aktivity je povinný"),
  description: z.string().min(1, "Popis je povinný"),
  location: z.string().optional(),
  duration: z.string().optional(),
  notes: z.string().optional(),
});

// Schéma pro výsledek validace
export const phoneValidationResultSchema = z.object({
  isValid: z.boolean(),
  internationalFormat: z.string().optional(),
  countryCode: z.string().optional(),
  countryName: z.string().optional(),
  carrier: z.string().optional(),
  phoneType: z.enum(phoneTypes).optional(),
  validationDate: z.string().optional(),
});

// Schéma pro záznam telefonního čísla
export const phoneRecordSchema = z.object({
  id: z.string(),
  phoneNumber: z.string().min(1, "Telefonní číslo je povinné"),
  source: z.enum(phoneSources),
  otherSourceDetail: z.string().optional(),
  discoveryDate: z.string().optional(),
  verificationStatus: z.enum(phoneVerificationStatuses),
  verificationDate: z.string().optional(),
  phoneType: z.enum(phoneTypes).optional(),
  carrier: z.enum(phoneCarriers).optional(),
  owner: z.string().optional(),
  associatedOrganization: z.string().optional(),
  registeredServices: z.array(z.enum(phoneServiceRegistrations)).optional().default([]),
  contacts: z.array(phoneContactSchema).optional().default([]),
  activities: z.array(phoneActivitySchema).optional().default([]),
  validationResult: phoneValidationResultSchema.optional(),
  notes: z.string().optional(),
  photos: z.array(photoMetadataSchema).optional().default([]),
}).superRefine((data, ctx) => {
  // Validace formátu telefonního čísla
  const phoneRegex = /^(\+\d{1,3}\s?)?(\d{3}\s?){2,4}\d{0,3}$/;
  if (!phoneRegex.test(data.phoneNumber)) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Neplatný formát telefonního čísla (např. +420 123 456 789)",
      path: ["phoneNumber"]
    });
  }

  // Pokud je zdroj "other", musí být vyplněno otherSourceDetail
  if (data.source === "other" && !data.otherSourceDetail?.trim()) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "Při výběru 'Jiný zdroj' je nutné uvést podrobnosti",
      path: ["otherSourceDetail"]
    });
  }
});

// Schéma pro celý modul telefonních čísel
export const phoneNumbersModuleSchema = z.object({
  phones: z.array(phoneRecordSchema).default([]),
  generalNotes: z.string().optional(),
});

export type PhoneNumbersModuleFormValues = z.infer<typeof phoneNumbersModuleSchema>;
