"use client";

import { useState } from "react";
import { useFieldArray, useFormContext } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { NetworkAnalysisModuleFormValues } from "./schemas";
import { PhotoUploadSection } from "./PhotoUploadSection";
import { DomainValidator } from "./DomainValidator";
import { Globe, Server, Plus, Trash2 } from "lucide-react";

interface DomainDetailFormProps {
  domainIndex: number;
}

export function DomainDetailForm({ domainIndex }: DomainDetailFormProps) {
  const { control, register, watch, setValue } = useFormContext<NetworkAnalysisModuleFormValues>();
  const [activeTab, setActiveTab] = useState("general");
  const [validationResults, setValidationResults] = useState<any>(null);

  const {
    fields: subdomainFields,
    append: appendSubdomain,
    remove: removeSubdomain
  } = useFieldArray({
    control,
    name: `domains.${domainIndex}.subdomains`
  });

  const domain = watch(`domains.${domainIndex}`);

  const handleAddSubdomain = () => {
    appendSubdomain({
      name: "",
      ipAddress: "",
      description: ""
    });
  };

  const handleDomainValidationComplete = (results: any) => {
    // Uložení výsledků validace pro pozdější zobrazení
    setValidationResults(results);

    if (results.isValid && results.exists) {
      // Aktualizace nameserverů
      if (results.nsRecords && results.nsRecords.length > 0) {
        setValue(`domains.${domainIndex}.nameservers`, results.nsRecords.join('\n'));
      }

      // Aktualizace MX záznamů
      if (results.mxRecords && results.mxRecords.length > 0) {
        setValue(`domains.${domainIndex}.mxRecords`, results.mxRecords.join('\n'));
      }

      // Přidání subdomén pro IP adresy
      if (results.ipAddresses && results.ipAddresses.length > 0) {
        // Přidáme pouze hlavní doménu jako subdoménu, pokud ještě není přidána
        const mainDomainName = results.domain;
        const existingSubdomains = watch(`domains.${domainIndex}.subdomains`) || [];

        if (!existingSubdomains.some(sd => sd.name === mainDomainName)) {
          appendSubdomain({
            name: mainDomainName,
            ipAddress: results.ipAddresses[0],
            description: "Hlavní doména"
          });
        }

        // Přidáme www subdoménu, pokud ještě není přidána
        const wwwName = `www.${mainDomainName}`;
        if (!existingSubdomains.some(sd => sd.name === wwwName)) {
          appendSubdomain({
            name: wwwName,
            ipAddress: results.ipAddresses[0],
            description: "WWW subdoména"
          });
        }
      }
    }
  };

  return (
    <div className="space-y-6">
      <Tabs defaultValue="general" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="general">Obecné</TabsTrigger>
          <TabsTrigger value="validator">Validace</TabsTrigger>
          <TabsTrigger value="subdomains">Subdomény</TabsTrigger>
          <TabsTrigger value="dns">DNS záznamy</TabsTrigger>
          <TabsTrigger value="photos">Fotodokumentace</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4 pt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor={`domains.${domainIndex}.name`}>Název domény</Label>
              <Input
                id={`domains.${domainIndex}.name`}
                placeholder="example.com"
                {...register(`domains.${domainIndex}.name`)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor={`domains.${domainIndex}.registrar`}>Registrátor</Label>
              <Input
                id={`domains.${domainIndex}.registrar`}
                placeholder="Např. GoDaddy, Namecheap, ..."
                {...register(`domains.${domainIndex}.registrar`)}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor={`domains.${domainIndex}.registrationDate`}>Datum registrace</Label>
              <Input
                id={`domains.${domainIndex}.registrationDate`}
                type="date"
                {...register(`domains.${domainIndex}.registrationDate`)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor={`domains.${domainIndex}.expirationDate`}>Datum expirace</Label>
              <Input
                id={`domains.${domainIndex}.expirationDate`}
                type="date"
                {...register(`domains.${domainIndex}.expirationDate`)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor={`domains.${domainIndex}.registrantName`}>Jméno registranta</Label>
            <Input
              id={`domains.${domainIndex}.registrantName`}
              placeholder="Jméno osoby nebo organizace"
              {...register(`domains.${domainIndex}.registrantName`)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor={`domains.${domainIndex}.registrantEmail`}>Email registranta</Label>
            <Input
              id={`domains.${domainIndex}.registrantEmail`}
              placeholder="<EMAIL>"
              type="email"
              {...register(`domains.${domainIndex}.registrantEmail`)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor={`domains.${domainIndex}.status`}>Status domény</Label>
            <Select
              defaultValue={domain.status || "active"}
              onValueChange={(value) => {
                // Toto je řešeno přes react-hook-form, ale musíme použít tento způsob pro Select
                const event = {
                  target: {
                    name: `domains.${domainIndex}.status`,
                    value
                  }
                };
                register(`domains.${domainIndex}.status`).onChange(event as any);
              }}
            >
              <SelectTrigger id={`domains.${domainIndex}.status`}>
                <SelectValue placeholder="Vyberte status domény" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Aktivní</SelectItem>
                <SelectItem value="inactive">Neaktivní</SelectItem>
                <SelectItem value="expired">Expirovaná</SelectItem>
                <SelectItem value="pending">Čekající</SelectItem>
                <SelectItem value="locked">Zamčená</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor={`domains.${domainIndex}.notes`}>Poznámky</Label>
            <Textarea
              id={`domains.${domainIndex}.notes`}
              placeholder="Další informace o doméně..."
              rows={4}
              {...register(`domains.${domainIndex}.notes`)}
            />
          </div>
        </TabsContent>

        <TabsContent value="validator" className="pt-4">
          <DomainValidator
            initialDomain={domain.name || ""}
            onValidationComplete={handleDomainValidationComplete}
            savedResults={validationResults}
          />
        </TabsContent>

        <TabsContent value="subdomains" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle>Subdomény</CardTitle>
                <Button type="button" variant="outline" size="sm" onClick={handleAddSubdomain}>
                  <Plus className="h-4 w-4 mr-2" />
                  Přidat subdoménu
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {subdomainFields.length === 0 ? (
                <div className="text-center py-4 text-muted-foreground">
                  Zatím nebyly přidány žádné subdomény
                </div>
              ) : (
                <div className="space-y-4">
                  {subdomainFields.map((field, index) => (
                    <div key={field.id} className="border rounded-md p-4 relative">
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute top-2 right-2"
                        onClick={() => removeSubdomain(index)}
                      >
                        <Trash2 className="h-4 w-4 text-muted-foreground" />
                      </Button>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div className="space-y-2">
                          <Label htmlFor={`domains.${domainIndex}.subdomains.${index}.name`}>
                            Název subdomény
                          </Label>
                          <Input
                            id={`domains.${domainIndex}.subdomains.${index}.name`}
                            placeholder="Např. blog, mail, www"
                            {...register(`domains.${domainIndex}.subdomains.${index}.name`)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`domains.${domainIndex}.subdomains.${index}.ipAddress`}>
                            IP adresa
                          </Label>
                          <Input
                            id={`domains.${domainIndex}.subdomains.${index}.ipAddress`}
                            placeholder="Např. ***********"
                            {...register(`domains.${domainIndex}.subdomains.${index}.ipAddress`)}
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`domains.${domainIndex}.subdomains.${index}.description`}>
                          Popis
                        </Label>
                        <Textarea
                          id={`domains.${domainIndex}.subdomains.${index}.description`}
                          placeholder="Popis subdomény..."
                          rows={2}
                          {...register(`domains.${domainIndex}.subdomains.${index}.description`)}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="dns" className="space-y-4 pt-4">
          <Card>
            <CardHeader>
              <CardTitle>DNS záznamy</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor={`domains.${domainIndex}.nameservers`}>Nameservery</Label>
                  <Textarea
                    id={`domains.${domainIndex}.nameservers`}
                    placeholder="Zadejte nameservery, jeden na řádek"
                    rows={3}
                    {...register(`domains.${domainIndex}.nameservers`)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor={`domains.${domainIndex}.mxRecords`}>MX záznamy</Label>
                  <Textarea
                    id={`domains.${domainIndex}.mxRecords`}
                    placeholder="Zadejte MX záznamy, jeden na řádek"
                    rows={3}
                    {...register(`domains.${domainIndex}.mxRecords`)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor={`domains.${domainIndex}.txtRecords`}>TXT záznamy</Label>
                  <Textarea
                    id={`domains.${domainIndex}.txtRecords`}
                    placeholder="Zadejte TXT záznamy, jeden na řádek"
                    rows={3}
                    {...register(`domains.${domainIndex}.txtRecords`)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="photos" className="pt-4">
          <PhotoUploadSection control={control} entityIndex={domainIndex} entityType="domains" />
        </TabsContent>
      </Tabs>
    </div>
  );
}
