"use client";

import { z } from "zod";
import { YesNoUnknown } from "@/types";

// Foreign Business Activities Schema
export const foreignBusinessInvolvementSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  country: z.string().optional(),
  description: z.string().optional(),
  role: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  isActive: z.boolean().optional(),
  isOffshore: z.boolean().optional(),
  notes: z.string().optional(),
});
export type ForeignBusinessInvolvement = z.infer<typeof foreignBusinessInvolvementSchema>;

// Insolvency and Execution Schema
export const insolvencyRecordSchema = z.object({
  id: z.string(),
  type: z.enum(["insolvency", "execution", "bankruptcy", "restructuring", "other"]),
  otherTypeDetail: z.string().optional(),
  fileNumber: z.string().optional(),
  court: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  status: z.enum(["active", "completed", "rejected", "suspended", "other"]).optional(),
  creditors: z.array(z.string()).optional(),
  amount: z.preprocess(
    (val) => (val === "" || val === null || val === undefined || Number.isNaN(Number(val))) ? null : Number(val),
    z.number().nullable().optional()
  ),
  currency: z.string().optional(),
  description: z.string().optional(),
  notes: z.string().optional(),
});
export type InsolvencyRecord = z.infer<typeof insolvencyRecordSchema>;

// Real Estate Schema
export const realEstateRecordSchema = z.object({
  id: z.string(),
  type: z.enum(["residential", "commercial", "industrial", "land", "agricultural", "other"]),
  otherTypeDetail: z.string().optional(),
  address: z.string().optional(),
  cadastralArea: z.string().optional(),
  parcelNumber: z.string().optional(),
  buildingNumber: z.string().optional(),
  ownership: z.string().optional(), // e.g., "full", "partial (50%)", etc.
  acquisitionDate: z.string().optional(),
  marketValue: z.preprocess(
    (val) => (val === "" || val === null || val === undefined || Number.isNaN(Number(val))) ? null : Number(val),
    z.number().nullable().optional()
  ),
  currency: z.string().optional().default("CZK"),
  mortgageInfo: z.string().optional(),
  usageDescription: z.string().optional(),
  notes: z.string().optional(),
});
export type RealEstateRecord = z.infer<typeof realEstateRecordSchema>;

// Personnel Connections Schema
export const personnelConnectionSchema = z.object({
  id: z.string(),
  personName: z.string().optional(),
  relationship: z.string().optional(),
  company: z.string().optional(),
  position: z.string().optional(),
  description: z.string().optional(),
  isSignificant: z.boolean().optional(),
  notes: z.string().optional(),
});
export type PersonnelConnection = z.infer<typeof personnelConnectionSchema>;

// Photo Documentation Schema
export const businessPhotoSchema = z.object({
  id: z.string(),
  fileName: z.string().optional(),
  storagePath: z.string().optional(),
  downloadURL: z.string().optional(),
  description: z.string().optional(),
  dateTaken: z.string().optional(),
  sourceURL: z.string().optional(),
  category: z.enum([
    "premises", 
    "property", 
    "documents", 
    "people", 
    "products", 
    "other"
  ]).optional(),
  location: z.string().optional(),
  notes: z.string().optional(),
});
export type BusinessPhoto = z.infer<typeof businessPhotoSchema>;

// Combined schemas for the business activity module
export const businessActivityExtendedSchema = z.object({
  // Foreign Business Activities
  foreignBusinessSummary: z.string().optional(),
  foreignInvolvements: z.array(foreignBusinessInvolvementSchema).optional().default([]),
  
  // Insolvency and Execution
  insolvencyStatus: z.enum(["yes", "no", "unknown"] as [YesNoUnknown, ...YesNoUnknown[]]).optional(),
  insolvencyDetails: z.string().optional(),
  insolvencyRecords: z.array(insolvencyRecordSchema).optional().default([]),
  
  // Real Estate
  realEstateRecords: z.array(realEstateRecordSchema).optional().default([]),
  
  // Personnel Connections
  personnelConnections: z.string().optional(),
  personnelConnectionRecords: z.array(personnelConnectionSchema).optional().default([]),
  
  // Photo Documentation
  businessPhotos: z.array(businessPhotoSchema).optional().default([]),
});

export type BusinessActivityExtended = z.infer<typeof businessActivityExtendedSchema>;
