"use client";

import { Control } from "react-hook-form";
import { NetworkAnalysisModuleFormValues } from "./schemas";
import { IpAddressRecord } from "@/types";
import { Card, CardContent } from "@/components/ui/card";
import { Laptop } from "lucide-react";

interface DeviceDetailProps {
  control: Control<NetworkAnalysisModuleFormValues>;
  deviceIndex: number;
  ipAddresses: IpAddressRecord[];
}

export function DeviceDetail({ control, deviceIndex, ipAddresses }: DeviceDetailProps) {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center mb-4">
          <Laptop className="mr-2 h-5 w-5 text-primary" />
          <h3 className="text-lg font-semibold">Detail zařízení (Stub)</h3>
        </div>
        <p>Tato komponenta je zatím ve vývoji.</p>
      </CardContent>
    </Card>
  );
}
