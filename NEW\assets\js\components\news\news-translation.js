/**
 * Aktu<PERSON><PERSON><PERSON> události - funkce pro překlad a vylepšení detailu zprávy
 */

/**
 * Přidání event listenerů pro tlačítka v detailu zprávy
 * @param {HTMLElement} dialog - Element dialogu
 * @param {string} newsId - ID zprávy
 */
function addNewsDetailEventListeners(dialog, newsId) {
    // Event listener pro tlačítko překladu
    const translateButton = dialog.querySelector('.btn-translate');
    if (translateButton) {
        translateButton.addEventListener('click', function() {
            translateNews(newsId);
        });
    }

    // Event listener pro přidání zprávy do fotogalerie
    const addPhotoButton = dialog.querySelector('.news-detail-add-photo');
    if (addPhotoButton) {
        addPhotoButton.addEventListener('click', function() {
            const newsId = this.getAttribute('data-news-id');
            if (newsId && newsCache[newsId]) {
                addNewsToPhotoGallery(newsCache[newsId]);

                // Zobrazení potvrzení
                showNotification('Zpráva byla přidána do fotogalerie', 'success');
            }
        });
    }
}

/**
 * Překlad zprávy
 * @param {string} newsId - ID zprávy
 */
function translateNews(newsId) {
    console.log('Překlad zprávy:', newsId);

    // Kontrola, zda zpráva existuje v cache
    if (!newsCache[newsId]) {
        console.error('Zpráva nebyla nalezena v cache:', newsId);
        showNotification('Zpráva nebyla nalezena v cache.', 'error');
        return;
    }

    // Získání zprávy z cache
    const news = newsCache[newsId];

    // Kontrola, zda zpráva má text k překladu
    if (!news.title && !news.description) {
        console.error('Zpráva nemá text k překladu');
        showNotification('Zpráva nemá text k překladu.', 'error');
        return;
    }

    // Kontrola, zda zpráva není v češtině
    if (news.language === 'cs') {
        showNotification('Zpráva je již v českém jazyce, překlad není potřeba.', 'info');
        return;
    }

    // Zobrazení kontejneru pro překlad
    const translationContainer = document.getElementById('news-translation-container');
    if (translationContainer) {
        translationContainer.style.display = 'block';

        // Zobrazení načítání
        translationContainer.innerHTML = `
            <h4>Překlad</h4>
            <div id="news-translation-content" class="news-detail-translation-content">
                <div class="loading-spinner">
                    <i class="fas fa-spinner fa-spin"></i>
                    <span>Překládám...</span>
                </div>
            </div>
        `;
    } else {
        console.error('Kontejner pro překlad nebyl nalezen');
        showNotification('Chyba při zobrazení překladu.', 'error');
        return;
    }

    // Zobrazení notifikace o zahájení překladu
    showNotification(`Překládám zprávu z jazyka ${news.language.toUpperCase()} do češtiny...`, 'info');

    // Simulace překladu (v reálné aplikaci by zde byl API požadavek na překladač)
    setTimeout(() => {
        // Překlad titulku a popisu
        const translatedTitle = simulateTranslation(news.title, news.language);
        const translatedDescription = simulateTranslation(news.description, news.language);

        // Aktualizace obsahu překladu
        const translationContent = document.getElementById('news-translation-content');
        if (translationContent) {
            translationContent.innerHTML = `
                <div class="news-translation-title">
                    <strong>Titulek:</strong> ${translatedTitle}
                </div>
                <div class="news-translation-description">
                    <strong>Popis:</strong> ${translatedDescription}
                </div>
            `;

            // Zobrazení notifikace o dokončení překladu
            showNotification('Překlad byl úspěšně dokončen.', 'success');
        }
    }, 1500); // Simulace zpoždění API požadavku
}

/**
 * Simulace překladu textu
 * @param {string} text - Text k překladu
 * @param {string} sourceLanguage - Zdrojový jazyk
 * @returns {string} - Přeložený text
 */
function simulateTranslation(text, sourceLanguage) {
    if (!text) return '';

    // V reálné aplikaci by zde byl API požadavek na překladač
    // Pro účely demonstrace pouze přidáme poznámku o překladu
    return `[Přeloženo z ${sourceLanguage.toUpperCase()}] ${text}`;
}

/**
 * Zobrazení notifikace
 * @param {string} message - Zpráva
 * @param {string} type - Typ notifikace (success, error, warning, info)
 */
function showNotification(message, type = 'info') {
    console.log('Zobrazení notifikace:', message, type);

    // Vytvoření notifikace
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    // Ikona podle typu
    let icon = 'fa-info-circle';
    if (type === 'success') icon = 'fa-check-circle';
    if (type === 'error') icon = 'fa-exclamation-circle';
    if (type === 'warning') icon = 'fa-exclamation-triangle';

    // Obsah notifikace
    notification.innerHTML = `
        <div class="notification-icon">
            <i class="fas ${icon}"></i>
        </div>
        <div class="notification-message">
            ${message}
        </div>
        <button type="button" class="notification-close">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Přidání notifikace do stránky
    document.body.appendChild(notification);

    // Event listener pro zavření notifikace
    notification.querySelector('.notification-close').addEventListener('click', function() {
        notification.remove();
    });

    // Automatické zavření po 3 sekundách
    setTimeout(() => {
        if (document.body.contains(notification)) {
            notification.classList.add('notification-hiding');

            // Odstranění notifikace po animaci
            setTimeout(() => {
                if (document.body.contains(notification)) {
                    notification.remove();
                }
            }, 300);
        }
    }, 3000);
}

/**
 * Přidání zprávy do fotogalerie
 * @param {Object} news - Zpráva
 */
function addNewsToPhotoGallery(news) {
    console.log('Přidání zprávy do fotogalerie:', news);

    // Najít aktuální modul aktuálních událostí
    const newsModule = document.querySelector('.module[id^="module-aktualni-udalosti"]');
    if (!newsModule) {
        console.error('Modul aktuálních událostí nebyl nalezen');
        return;
    }

    // Najít galerii v modulu
    const gallery = newsModule.querySelector('.news-photo-gallery');
    if (!gallery) {
        console.error('Fotogalerie nebyla nalezena v modulu aktuálních událostí');
        return;
    }

    // Kontrola, zda zpráva obsahuje obrázek
    if (news.imageUrl) {
        // Přidání fotografie do galerie
        addNewsPhotoToGallery(gallery, news.title, news.imageUrl);
    } else {
        // Pokud zpráva nemá obrázek, přidáme placeholder s textem zprávy
        const placeholderUrl = `https://via.placeholder.com/300x200/e9ecef/212529?text=${encodeURIComponent(news.title.substring(0, 20))}`;
        addNewsPhotoToGallery(gallery, news.title, placeholderUrl);
    }
}
