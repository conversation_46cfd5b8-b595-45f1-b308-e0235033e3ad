import { FacebookModuleData, FacebookFriend } from "@/types/facebook";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, ExternalLink, Lock, Unlock } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";

interface FacebookFriendsProps {
  data: FacebookModuleData | null;
}

export default function FacebookFriends({ data }: FacebookFriendsProps) {
  if (!data || !data.friends || data.friends.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="text-muted-foreground mb-2">Žádní přátelé k zobrazení</div>
        <p className="text-sm text-muted-foreground">
          Přidejte přátele v režimu úprav.
        </p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Přátelé ({data.friends.length})</h3>
        {data.friendsCount !== undefined && data.friendsCount > data.friends.length && (
          <Badge variant="outline">
            <Users className="h-3 w-3 mr-1" /> 
            Zobrazeno {data.friends.length} z {data.friendsCount}
          </Badge>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {data.friends.map((friend) => (
          <FriendCard key={friend.id} friend={friend} />
        ))}
      </div>
    </div>
  );
}

function FriendCard({ friend }: { friend: FacebookFriend }) {
  return (
    <Card>
      <CardContent className="p-4 flex items-center gap-3">
        {/* Friend Image */}
        <div className="relative w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
          {friend.imageUrl ? (
            <Image 
              src={friend.imageUrl} 
              alt={friend.name} 
              fill 
              className="object-cover"
              unoptimized
            />
          ) : (
            <div className="w-full h-full bg-blue-100 flex items-center justify-center">
              <span className="text-blue-600 font-bold">
                {friend.name.charAt(0)}
              </span>
            </div>
          )}
        </div>
        
        {/* Friend Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <h4 className="font-medium truncate">{friend.name}</h4>
            {friend.isPublic !== undefined && (
              <Badge variant="outline" className={friend.isPublic ? "bg-green-50" : "bg-amber-50"}>
                {friend.isPublic ? (
                  <Unlock className="h-3 w-3 text-green-600" />
                ) : (
                  <Lock className="h-3 w-3 text-amber-600" />
                )}
              </Badge>
            )}
          </div>
          
          {friend.mutualFriends !== undefined && (
            <p className="text-xs text-muted-foreground">
              <Users className="h-3 w-3 inline mr-1" /> 
              {friend.mutualFriends} společných přátel
            </p>
          )}
        </div>
        
        {/* External Link */}
        {friend.profileUrl && (
          <Button variant="ghost" size="icon" asChild className="flex-shrink-0">
            <a href={friend.profileUrl} target="_blank" rel="noopener noreferrer">
              <ExternalLink className="h-4 w-4" />
            </a>
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
