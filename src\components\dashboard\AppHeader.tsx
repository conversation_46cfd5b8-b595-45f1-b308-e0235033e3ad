
"use client";

import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { LogOut, ShieldCheck, UserCircle } from 'lucide-react';
import { useRouter } from "next/navigation";
import { useAuth } from '@/hooks/useAuth';
import { auth } from '@/lib/firebase';
import { signOut } from 'firebase/auth';

export function AppHeader() {
  const router = useRouter();
  const { user, userProfile, loading } = useAuth();

  const handleLogout = async () => {
    try {
      await signOut(auth);
      router.push("/login");
    } catch (error) {
      console.error("Logout failed:", error);
      // Optionally show an error message to the user
    }
  };

  const getDisplayName = () => {
    if (userProfile) {
      let nameParts = [];
      if (userProfile.title) nameParts.push(userProfile.title);
      if (userProfile.firstName) nameParts.push(userProfile.firstName);
      if (userProfile.lastName) nameParts.push(userProfile.lastName);
      if (userProfile.rank) nameParts.push(userProfile.rank);
      if (nameParts.length > 0) return nameParts.join(" ").trim().replace(/\s+/g, ' ');
      if (userProfile.email) return userProfile.email; // Fallback to email from profile
    }
    if (user) {
      if (user.displayName) return user.displayName; // Firebase Auth displayName
      if (user.email) return user.email; // Firebase Auth email
    }
    return "Uživatel";
  };

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-card shadow-sm">
      <div className="container mx-auto flex h-16 items-center justify-between px-4 md:px-6">
        <Link href="/dashboard/cases" className="flex items-center gap-2">
          <ShieldCheck className="h-8 w-8 text-primary" />
          <span className="text-xl font-bold text-primary">P&R Solutions OSINT</span>
        </Link>
        
        <div className="flex items-center gap-4">
          {loading ? (
            <span className="text-sm text-muted-foreground">Načítání...</span>
          ) : user ? (
            <>
              <span className="text-sm font-medium text-foreground flex items-center gap-2">
                <UserCircle className="h-5 w-5 text-muted-foreground" />
                {getDisplayName()}
              </span>
              <Button variant="outline" onClick={handleLogout}>
                <LogOut className="mr-2 h-4 w-4" />
                Odhlásit se
              </Button>
            </>
          ) : (
            <Button variant="outline" onClick={() => router.push("/login")}>
              Přihlásit se
            </Button>
          )}
        </div>
      </div>
    </header>
  );
}
