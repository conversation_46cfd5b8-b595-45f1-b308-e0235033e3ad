"use client";

import { useState, useCallback, useRef } from "react";
import { useFieldArray, Control, UseFormReturn } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Trash2, ImageUp, ChevronUp, ChevronDown } from "lucide-react";
import { LoadingSpinner } from "@/components/ui/loading";
import { useToast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

interface PhotoDocumentationSectionProps {
  form: UseFormReturn<any>;
  title?: string;
  description?: string;
  namePrefix?: string;
  photoHint?: string;
  caseId?: string;
  subjectId?: string;
  moduleId?: string;
}

export function PhotoDocumentationSection({ 
  form, 
  title = "Fotodokumentace",
  description = "Nahrajte a prohlédněte si fotografie. Fotografie se ukládají trvale do aplikace.",
  namePrefix = "photos",
  photoHint = "phone analysis screenshot call log",
  caseId = 'phone-analysis',
  subjectId = 'phone-analysis', 
  moduleId = 'phone-analysis'
}: PhotoDocumentationSectionProps) {
  const { toast } = useToast();
  const [uploadingPhotos, setUploadingPhotos] = useState<{[key: number]: boolean}>({});
  const [photoUrls, setPhotoUrls] = useState<{[key: number]: string}>({});
  const fileInputRefs = useRef<{[key: number]: HTMLInputElement | null}>({});

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: namePrefix
  });

  const watchedPhotos = form.watch(namePrefix) || [];

  // Funkce pro přidání nové fotografie
  const handleAddPhoto = useCallback(() => {
    append({
      id: crypto.randomUUID(),
      downloadURL: '',
      description: '',
      dateTaken: '',
      fileName: '',
      sourceURL: ''
    });
  }, [append]);

  // Funkce pro odstranění fotografie
  const handleRemovePhoto = useCallback((index: number) => {
    remove(index);
    // Vyčistíme také lokální stavy
    setUploadingPhotos(prev => {
      const newState = { ...prev };
      delete newState[index];
      return newState;
    });
    setPhotoUrls(prev => {
      const newState = { ...prev };
      delete newState[index];
      return newState;
    });
  }, [remove]);

  // Funkce pro přesunutí fotografie
  const movePhoto = useCallback((fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= fields.length) return;
    
    const photos = [...watchedPhotos];
    const [movedPhoto] = photos.splice(fromIndex, 1);
    photos.splice(toIndex, 0, movedPhoto);
    
    form.setValue(namePrefix as any, photos);
    
    // Aktualizujeme také photoUrls
    const newPhotoUrls = { ...photoUrls };
    const movedUrl = newPhotoUrls[fromIndex];
    delete newPhotoUrls[fromIndex];
    
    // Přesuneme URL
    Object.keys(newPhotoUrls).forEach(key => {
      const numKey = parseInt(key);
      if (numKey > fromIndex && numKey <= toIndex) {
        newPhotoUrls[numKey - 1] = newPhotoUrls[numKey];
        delete newPhotoUrls[numKey];
      } else if (numKey < fromIndex && numKey >= toIndex) {
        newPhotoUrls[numKey + 1] = newPhotoUrls[numKey];
        delete newPhotoUrls[numKey];
      }
    });
    
    newPhotoUrls[toIndex] = movedUrl;
    setPhotoUrls(newPhotoUrls);
  }, [fields.length, form, photoUrls, namePrefix, watchedPhotos]);

  // Funkce pro zpracování obrázku
  const processImageFile = useCallback(async (file: File, index: number) => {
    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: "Soubor je příliš velký",
        description: "Maximální velikost fotografie je 10MB.",
        variant: "destructive"
      });
      return;
    }

    if (!file.type.startsWith('image/')) {
      toast({
        title: "Neplatný formát souboru",
        description: "Podporované jsou pouze obrázky (JPEG, PNG, GIF, WEBP).",
        variant: "destructive"
      });
      return;
    }

    setUploadingPhotos(prev => ({ ...prev, [index]: true }));

    try {
      const base64 = await fileToBase64(file);

      const response = await fetch('/api/upload-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          base64Data: base64,
          caseId: caseId,
          subjectId: subjectId,
          moduleId: moduleId,
          fileName: file.name
        })
      });

      if (!response.ok) {
        throw new Error('Nepodařilo se uložit fotografii na server');
      }

      const { imagePath } = await response.json();

      setPhotoUrls(prev => ({
        ...prev,
        [index]: imagePath
      }));
      
      form.setValue(`${namePrefix}.${index}.downloadURL` as any, imagePath);
      form.setValue(`${namePrefix}.${index}.fileName` as any, file.name);
      form.setValue(`${namePrefix}.${index}.description` as any, `Nahraná fotografie: ${file.name}`);
      form.setValue(`${namePrefix}.${index}.id` as any, crypto.randomUUID());

      toast({
        title: "Fotografie uložena",
        description: `Fotografie byla úspěšně uložena do aplikace: ${imagePath}`,
      });
    } catch (error: any) {
      console.error('Chyba při ukládání fotografie:', error);
      toast({
        title: "Chyba ukládání",
        description: error.message || "Nepodařilo se uložit fotografii",
        variant: "destructive"
      });
    } finally {
      setUploadingPhotos(prev => ({ ...prev, [index]: false }));
    }
  }, [toast, form, namePrefix, caseId, subjectId, moduleId]);

  // Funkce pro konverzi souboru na base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const base64 = reader.result as string;
        resolve(base64.split(',')[1]); // Odstraníme data:image/... prefix
      };
      reader.onerror = error => reject(error);
    });
  };

  // Funkce pro nahrání fotografie
  const handlePhotoUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>, index: number) => {
    const file = event.target.files?.[0];
    if (file) {
      processImageFile(file, index);
    }
  }, [processImageFile]);

  // Funkce pro vložení z clipboardu
  const handlePaste = useCallback((event: React.ClipboardEvent, index: number) => {
    event.preventDefault();
    const items = event.clipboardData?.items;
    if (!items) return;

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.type.indexOf('image') !== -1) {
        const file = item.getAsFile();
        if (file) {
          processImageFile(file, index);
        }
        break;
      }
    }
  }, [processImageFile]);

  // Funkce pro získání placeholder obrázku
  const getPlaceholderImage = (index: number) => {
    return `data:image/svg+xml;base64,${btoa(`
      <svg width="450" height="450" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f3f4f6"/>
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" font-family="Arial, sans-serif" font-size="16" fill="#9ca3af">
          Fotografie ${index + 1}
        </text>
      </svg>
    `)}`;
  };

  return (
    <Card className="shadow-md">
      <CardHeader>
        <CardTitle className="text-xl flex items-center">
          <ImageUp className="mr-3 h-6 w-6 text-primary" />
          {title}
        </CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {fields.map((field, index) => (
          <Card key={field.id} className="p-4 space-y-4 shadow-sm bg-card-foreground/5">
            <div className="flex justify-between items-center mb-2">
              <p className="font-semibold text-md">Záznam o fotografii {index + 1}</p>
              <div className="flex items-center gap-1">
                <Button 
                  type="button" 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => movePhoto(index, index - 1)}
                  disabled={index === 0}
                  className="h-8 w-8"
                  title="Posunout nahoru"
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
                <Button 
                  type="button" 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => movePhoto(index, index + 1)}
                  disabled={index === fields.length - 1}
                  className="h-8 w-8"
                  title="Posunout dolů"
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
                <Button 
                  type="button" 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => handleRemovePhoto(index)} 
                  className="text-destructive hover:bg-destructive/10 h-8 w-8" 
                  title="Smazat fotografii"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="flex flex-col md:flex-row items-start gap-6">
              <div className="relative">
                <img
                  src={photoUrls[index] || getPlaceholderImage(index)}
                  alt={`Náhled fotografie ${index + 1}`}
                  data-ai-hint={photoHint}
                  className="w-[450px] h-[450px] object-cover rounded-lg border-2 border-muted shadow-md bg-background mx-auto"
                  onError={(e) => (e.currentTarget.src = getPlaceholderImage(index))}
                />
                {uploadingPhotos[index] && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg">
                    <LoadingSpinner size="md" className="text-white" />
                  </div>
                )}
              </div>
              
              <div className="flex-grow space-y-4 w-full">
                <div>
                  <Label className="font-semibold text-sm">Popis fotografie</Label>
                  <textarea
                    {...form.register(`${namePrefix}.${index}.description`)}
                    placeholder="Např. Screenshot volání, SMS historie, kontakty..."
                    rows={3}
                    className="w-full border rounded px-3 py-2 text-sm"
                  />
                </div>
                <div>
                  <Label className="font-semibold text-sm">Cesta k souboru (automaticky vyplněno)</Label>
                  <Input
                    {...form.register(`${namePrefix}.${index}.downloadURL`)}
                    disabled={true}
                    placeholder="Automaticky se vyplní po nahrání fotografie"
                    className="text-sm"
                  />
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <div>
                <Label className="font-semibold text-sm">Datum pořízení (cca)</Label>
                <Input
                  {...form.register(`${namePrefix}.${index}.dateTaken`)}
                  type="text"
                  placeholder="Např. Leden 2023, 15.03.2022"
                  className="text-sm"
                />
              </div>
              <div>
                <Label className="font-semibold text-sm">Název souboru (původní)</Label>
                <Input
                  {...form.register(`${namePrefix}.${index}.fileName`)}
                  placeholder="např. phone_screenshot.jpg, call_log.png"
                  className="text-sm"
                />
              </div>
            </div>

            <div>
              <Label className="font-semibold text-sm">URL zdroje (kde byla nalezena)</Label>
              <Input
                {...form.register(`${namePrefix}.${index}.sourceURL`)}
                placeholder="URL zdroje nebo aplikace..."
                className="text-sm"
              />
            </div>

            <div className="mt-4">
              <Label 
                htmlFor={`photo-upload-${index}`} 
                className={cn(
                  "flex items-center justify-center h-32 w-full border-2 border-dashed rounded-md transition-colors",
                  uploadingPhotos[index] 
                    ? "cursor-not-allowed border-muted bg-muted/30" 
                    : "cursor-pointer hover:border-primary/50 hover:bg-accent/30"
                )}
                onPaste={(e) => handlePaste(e, index)}
                tabIndex={0}
              >
                <div className="flex flex-col items-center text-muted-foreground">
                  {uploadingPhotos[index] ? (
                    <>
                      <LoadingSpinner size="md" className="text-primary" />
                      <p className="text-sm mt-1">Ukládání fotografie na server...</p>
                    </>
                  ) : (
                    <>
                      <ImageUp className="h-8 w-8"/>
                      <p className="text-sm mt-1">Klikněte pro nahrání nebo stiskněte Ctrl+V</p>
                      <p className="text-xs text-muted-foreground/70 mt-1">Max. 10MB, formáty: JPEG, PNG, GIF, WEBP</p>
                    </>
                  )}
                </div>
              </Label>
              <Input
                id={`photo-upload-${index}`}
                type="file"
                accept="image/*"
                className="sr-only"
                onChange={(e) => handlePhotoUpload(e, index)}
                disabled={uploadingPhotos[index]}
                ref={(el) => {
                  fileInputRefs.current[index] = el;
                }}
              />
            </div>
          </Card>
        ))}
        <Button 
          type="button" 
          variant="outline" 
          onClick={handleAddPhoto} 
          size="sm"
        >
          <ImageUp className="mr-2 h-4 w-4" />
          Přidat záznam o fotografii
        </Button>
      </CardContent>
    </Card>
  );
} 