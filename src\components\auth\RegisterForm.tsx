
"use client";

import Link from "next/link";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { UserPlus, Briefcase, Badge, Building, Mail, LockKeyhole } from "lucide-react"; // Added Mail, removed User
import { useRouter } from "next/navigation";
import { createUserWithEmailAndPassword, updateProfile } from "firebase/auth";
import { auth, db } from "@/lib/firebase";
import { useAuth } from "@/hooks/useAuth";
import { doc, setDoc, serverTimestamp } from "firebase/firestore"; // Removed collection, query, where, getDocs

export function RegisterForm() {
  const router = useRouter();
  const { setError: setAuthError } = useAuth();

  const [rank, setRank] = useState("");
  const [title, setTitle] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState(""); // Added email state
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [oec, setOec] = useState("");
  const [department, setDepartment] = useState("");
  
  const [error, setErrorState] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setLoading(true);
    setErrorState(null);
    setAuthError(null);

    if (!email.trim()) {
      setErrorState("E-mail je povinný.");
      setLoading(false);
      return;
    }
    if (password !== confirmPassword) {
      setErrorState("Hesla se neshodují.");
      setLoading(false);
      return;
    }
    if (password.length < 6) {
      setErrorState("Heslo musí mít alespoň 6 znaků.");
      setLoading(false);
      return;
    }

    const trimmedEmail = email.trim();

    try {
      // Firebase Auth user creation with actual email
      const userCredential = await createUserWithEmailAndPassword(auth, trimmedEmail, password);
      const user = userCredential.user;

      // Set displayName in Firebase Auth
      await updateProfile(user, {
        displayName: `${firstName} ${lastName}`.trim(),
      });
      
      // Store additional user info in Firestore
      const userDocRef = doc(db, "users", user.uid);
      await setDoc(userDocRef, {
        uid: user.uid,
        email: trimmedEmail, // Store the actual email
        firstName,
        lastName,
        rank,
        title,
        oec,
        department,
        createdAt: serverTimestamp(),
      });
      
      alert("Registrace proběhla úspěšně. Nyní se můžete přihlásit.");
      router.push("/login");
    } catch (e: any) {
      console.error("Registration failed:", e);
      let friendlyMessage = "Došlo k chybě při registraci.";
      if (e.code === 'auth/email-already-in-use') {
        friendlyMessage = "Tento e-mail je již zaregistrován. Zkuste jiný.";
      } else if (e.code === 'auth/weak-password') {
        friendlyMessage = "Heslo je příliš slabé. Použijte alespoň 6 znaků.";
      } else if (e.code === 'auth/invalid-email') {
        friendlyMessage = "Formát e-mailu není správný.";
      } else {
        friendlyMessage = e.message || friendlyMessage;
      }
      setErrorState(friendlyMessage);
      setAuthError(friendlyMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl shadow-2xl">
      <CardHeader className="text-center">
         <div className="mx-auto bg-primary text-primary-foreground rounded-full p-3 w-fit mb-4">
          <UserPlus size={32} />
        </div>
        <CardTitle className="text-3xl font-bold">Vytvořit účet</CardTitle>
        <CardDescription>Zaregistrujte se pro přístup k platformě P&R Solutions OSINT</CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1.5">
              <Label htmlFor="rank">Hodnost</Label>
              <Input id="rank" placeholder="např. ppor." value={rank} onChange={(e) => setRank(e.target.value)} />
            </div>
            <div className="space-y-1.5">
              <Label htmlFor="title">Titul</Label>
              <Input id="title" placeholder="např. Ing." value={title} onChange={(e) => setTitle(e.target.value)} />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1.5">
              <Label htmlFor="firstName">Jméno <span className="text-destructive">*</span></Label>
              <Input id="firstName" placeholder="Jan" required value={firstName} onChange={(e) => setFirstName(e.target.value)} />
            </div>
            <div className="space-y-1.5">
              <Label htmlFor="lastName">Příjmení <span className="text-destructive">*</span></Label>
              <Input id="lastName" placeholder="Novák" required value={lastName} onChange={(e) => setLastName(e.target.value)} />
            </div>
          </div>
          
          {/* Email input */}
          <div className="space-y-1.5">
            <Label htmlFor="email">E-mail <span className="text-destructive">*</span></Label>
             <div className="relative">
              <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
              <Input 
                id="email" 
                type="email"
                placeholder="<EMAIL>" 
                required 
                value={email} 
                onChange={(e) => setEmail(e.target.value)} 
                className="pl-10"
                autoComplete="email"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1.5">
              <Label htmlFor="password">Heslo <span className="text-destructive">*</span></Label>
              <div className="relative">
                <LockKeyhole className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <Input id="password" type="password" required placeholder="******** (min. 6 znaků)" value={password} onChange={(e) => setPassword(e.target.value)} className="pl-10" autoComplete="new-password"/>
              </div>
            </div>
            <div className="space-y-1.5">
              <Label htmlFor="confirmPassword">Potvrdit heslo <span className="text-destructive">*</span></Label>
              <div className="relative">
                <LockKeyhole className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <Input id="confirmPassword" type="password" required placeholder="********" value={confirmPassword} onChange={(e) => setConfirmPassword(e.target.value)} className="pl-10" autoComplete="new-password" />
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-1.5">
              <Label htmlFor="oec">OEČ (Osobní Evidenční Číslo)</Label>
               <div className="relative">
                <Badge className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <Input id="oec" placeholder="123456" value={oec} onChange={(e) => setOec(e.target.value)} className="pl-10" />
              </div>
            </div>
            <div className="space-y-1.5">
              <Label htmlFor="department">Oddělení</Label>
              <div className="relative">
                <Building className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground" />
                <Select onValueChange={setDepartment} value={department}>
                  <SelectTrigger id="department" className="pl-10">
                    <SelectValue placeholder="Vyberte oddělení" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="kyberkriminalita">Oddělení kyberkriminality</SelectItem>
                    <SelectItem value="analyza">Analytické oddělení</SelectItem>
                    <SelectItem value="obecna">Obecná kriminalita</SelectItem>
                    <SelectItem value="hospodarska">Hospodářská kriminalita</SelectItem>
                    <SelectItem value="ostatni">Ostatní</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          {error && <p className="text-sm text-destructive text-center md:col-span-2">{error}</p>}
          <Button type="submit" className="w-full text-lg py-6 mt-6" disabled={loading}>
            {loading ? "Registrace..." : "Zaregistrovat se"}
          </Button>
        </form>
      </CardContent>
      <CardFooter className="flex justify-center">
        <Link href="/login" className="text-sm text-primary hover:underline">
          Máte již účet? Přihlaste se
        </Link>
      </CardFooter>
    </Card>
  );
}
