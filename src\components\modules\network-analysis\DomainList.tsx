"use client";

import { Control, useFieldArray } from "react-hook-form";
import { NetworkAnalysisModuleFormValues } from "./schemas";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Server, MoreHorizontal, Plus, Trash2, Edit, Eye } from "lucide-react";
import { formatDate } from "@/lib/utils";

interface DomainListProps {
  control: Control<NetworkAnalysisModuleFormValues>;
  onSelectDomain: (index: number) => void;
  onAddDomain: () => void;
}

export function DomainList({ control, onSelectDomain, onAddDomain }: DomainListProps) {
  const { fields, remove } = useFieldArray({
    control,
    name: "domains",
  });

  const handleDeleteDomain = (index: number) => {
    if (confirm("Opravdu chcete smazat tuto doménu?")) {
      remove(index);
    }
  };

  if (fields.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Domény</CardTitle>
          <CardDescription>
            Zatím nebyly přidány žádné domény
          </CardDescription>
        </CardHeader>
        <CardContent className="flex flex-col items-center justify-center py-10">
          <Server className="h-16 w-16 text-muted-foreground mb-4" />
          <p className="text-muted-foreground mb-4 text-center">
            Přidejte domény spojené s tímto subjektem pro analýzu síťové aktivity
          </p>
          <Button onClick={onAddDomain}>
            <Plus className="mr-2 h-4 w-4" />
            Přidat doménu
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Domény</CardTitle>
        <CardDescription>
          Seznam domén spojených se subjektem
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Doména</TableHead>
              <TableHead>Registrátor</TableHead>
              <TableHead>Datum registrace</TableHead>
              <TableHead>Datum expirace</TableHead>
              <TableHead>Registrant</TableHead>
              <TableHead className="text-right">Akce</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {fields.map((domain, index) => (
              <TableRow key={domain.id} className="cursor-pointer hover:bg-muted/50" onClick={() => {
                setTimeout(() => {
                  onSelectDomain(index);
                }, 0);
              }}>
                <TableCell className="font-medium">{domain.domainName || "Neuvedeno"}</TableCell>
                <TableCell>{domain.registrar || "Neuvedeno"}</TableCell>
                <TableCell>{domain.registrationDate ? formatDate(domain.registrationDate) : "Neuvedeno"}</TableCell>
                <TableCell>{domain.expirationDate ? formatDate(domain.expirationDate) : "Neuvedeno"}</TableCell>
                <TableCell>{domain.registrantName || domain.registrantOrganization || "Neuvedeno"}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="icon" onClick={(e) => e.stopPropagation()}>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        setTimeout(() => {
                          onSelectDomain(index);
                        }, 0);
                      }}>
                        <Eye className="mr-2 h-4 w-4" />
                        Zobrazit detail
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={(e) => {
                        e.stopPropagation();
                        setTimeout(() => {
                          onSelectDomain(index);
                        }, 0);
                      }}>
                        <Edit className="mr-2 h-4 w-4" />
                        Upravit
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        className="text-destructive focus:text-destructive"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteDomain(index);
                        }}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Smazat
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
}
