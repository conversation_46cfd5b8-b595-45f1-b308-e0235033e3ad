"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { MapOverlaysForm } from "./MapOverlaysForm";
import { MapOverlaysModuleData, Subject } from "@/types";
import { saveMapOverlaysData } from "@/lib/firebase/firestore";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ArrowLeft, Save } from "lucide-react";
import { toast } from "sonner";

interface MapOverlaysModuleProps {
  caseId: string;
  subject: Subject;
  initialData?: MapOverlaysModuleData;
}

export function MapOverlaysModule({
  caseId,
  subject,
  initialData,
}: MapOverlaysModuleProps) {
  const router = useRouter();
  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async (data: any) => {
    console.log("MapOverlaysModule - handleSave called with data:", data);
    setIsSaving(true);
    try {
      // Zajistíme, že máme všechny potřebné vlastnosti
      const dataToSave = {
        ...data,
        subjectId: subject.id,
        points: data.points || [],
        areas: data.areas || [],
        routes: data.routes || [],
        externalLayers: data.externalLayers || [],
        analysisResults: data.analysisResults || [],
      };

      await saveMapOverlaysData(caseId, subject.id, dataToSave);
      toast.success("Data byla úspěšně uložena");

      // Pouze obnovíme stránku, ale nezavíráme modul
      router.refresh();

      // Vrátíme true, aby formulář věděl, že uložení proběhlo úspěšně
      return true;
    } catch (error) {
      console.error("Error saving map overlays data:", error);
      toast.error("Chyba při ukládání dat");
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="icon" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h2 className="text-2xl font-bold">Mapové overlapy</h2>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            onClick={() => document.getElementById("save-map-overlays-form")?.click()}
            disabled={isSaving}
          >
            {isSaving ? (
              <>Ukládání...</>
            ) : (
              <>
                <Save className="mr-2 h-4 w-4" />
                Uložit
              </>
            )}
          </Button>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>
            {subject.type === "physical"
              ? `${subject.firstName} ${subject.lastName}`
              : subject.name}
          </CardTitle>
          <CardDescription>
            Modul pro práci s mapovými vrstvami a jejich analýzu
          </CardDescription>
        </CardHeader>
        <CardContent>
          <MapOverlaysForm
            caseId={caseId}
            subject={subject}
            initialData={initialData}
            onSave={handleSave}
          />
        </CardContent>
      </Card>
    </div>
  );
}
