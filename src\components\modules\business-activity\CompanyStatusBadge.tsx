"use client";

import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface CompanyStatusBadgeProps {
  status: string;
  className?: string;
}

export function CompanyStatusBadge({ status, className }: CompanyStatusBadgeProps) {
  if (!status) return null;

  const lowerStatus = status.toLowerCase();
  
  // Determine variant based on status
  let variant: "default" | "secondary" | "destructive" | "outline" = "default";
  
  if (lowerStatus.includes("aktivní") || lowerStatus === "aktivní") {
    variant = "default"; // Green for active
  } else if (lowerStatus.includes("likvidac") || lowerStatus.includes("zánik") || lowerStatus.includes("zanik")) {
    variant = "destructive"; // Red for liquidation or terminated
  } else if (lowerStatus.includes("transformac") || lowerStatus.includes("změn") || lowerStatus.includes("zmen")) {
    variant = "secondary"; // Orange/yellow for transformation or change
  } else {
    variant = "outline"; // Default for unknown status
  }

  return (
    <Badge variant={variant} className={cn("text-xs font-normal", className)}>
      {status}
    </Badge>
  );
}
