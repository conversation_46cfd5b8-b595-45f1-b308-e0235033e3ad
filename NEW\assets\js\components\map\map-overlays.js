/**
 * Mapové overlapy - funkce pro práci s mapovými vrstvami a jejich analýzu
 */

// API klíče pro různé mapové služby
const MAP_API_KEYS = {
    mapbox: '', // Mapbox API - vyžaduje registraci
    google: '', // Google Maps API - vyžaduje registraci
    openstreetmap: '' // OpenStreetMap API - není potřeba pro základní použití
};

// Globální proměnné pro mapu
let map = null;
let mapLayers = {};
let activeBasemap = 'streets';
let markers = [];
let heatmapLayer = null;
let clusterLayer = null;
let drawingManager = null;
let currentAnalysis = null;

/**
 * Inicializace funkcí pro mapové overlapy
 */
function initMapOverlays() {
    console.log('Inicializace modulu mapových overlapů');
    
    // Najít aktuální modul mapových overlapů
    const mapModule = document.querySelector('.module[id^="module-mapove-overlapy"]');
    if (!mapModule) {
        console.error('Modul mapových overlapů nebyl nalezen');
        return;
    }
    
    console.log('Nalezen modul mapových overlapů:', mapModule.id);
    
    // Inicializace mapy
    initMap(mapModule);
    
    // Přidání event listenerů pro tlačítka v modulu mapových overlapů
    mapModule.addEventListener('click', function(event) {
        // Přepínání mapových podkladů
        if (event.target.closest('.basemap-selector')) {
            const basemapType = event.target.closest('.basemap-selector').getAttribute('data-basemap');
            if (basemapType) {
                changeBasemap(basemapType);
            }
        }
        
        // Přepínání vrstev
        if (event.target.closest('.layer-toggle')) {
            const layerName = event.target.closest('.layer-toggle').getAttribute('data-layer');
            const checkbox = event.target.closest('.layer-toggle').querySelector('input[type="checkbox"]');
            if (layerName && checkbox) {
                toggleLayer(layerName, checkbox.checked);
            }
        }
        
        // Přidání bodu
        if (event.target.closest('.add-point')) {
            enableDrawingMode('point');
        }
        
        // Přidání oblasti
        if (event.target.closest('.add-area')) {
            enableDrawingMode('polygon');
        }
        
        // Přidání trasy
        if (event.target.closest('.add-route')) {
            enableDrawingMode('polyline');
        }
        
        // Vyhledání adresy
        if (event.target.closest('.search-address')) {
            searchAddress();
        }
        
        // Vytvoření heat mapy
        if (event.target.closest('.create-heatmap')) {
            createHeatmap();
        }
        
        // Vytvoření clusterů
        if (event.target.closest('.create-clusters')) {
            createClusters();
        }
        
        // Analýza oblasti
        if (event.target.closest('.analyze-area')) {
            analyzeArea();
        }
        
        // Export mapy
        if (event.target.closest('.export-map')) {
            exportMap();
        }
        
        // Přidání dat z externího zdroje
        if (event.target.closest('.add-external-data')) {
            addExternalData();
        }
    });
    
    // Přidání event listenerů pro formuláře
    const addressSearchForm = mapModule.querySelector('#address-search-form');
    if (addressSearchForm) {
        addressSearchForm.addEventListener('submit', function(event) {
            event.preventDefault();
            searchAddress();
        });
    }
    
    // Inicializace fotogalerie
    initMapPhotoGallery(mapModule);
}

/**
 * Inicializace mapy
 * @param {HTMLElement} mapModule - Element modulu mapových overlapů
 */
function initMap(mapModule) {
    console.log('Inicializace mapy');
    
    // Najít kontejner pro mapu
    const mapContainer = mapModule.querySelector('#map-container');
    if (!mapContainer) {
        console.error('Kontejner pro mapu nebyl nalezen');
        return;
    }
    
    // Inicializace mapy pomocí Leaflet (open-source mapová knihovna)
    if (typeof L !== 'undefined') {
        // Leaflet je načten
        map = L.map(mapContainer.id).setView([50.0755, 14.4378], 13); // Praha jako výchozí pohled
        
        // Přidání základní vrstvy OpenStreetMap
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);
        
        // Přidání měřítka
        L.control.scale().addTo(map);
        
        // Inicializace vrstev
        initLayers();
        
        // Inicializace nástrojů pro kreslení
        initDrawingTools();
        
        console.log('Mapa byla úspěšně inicializována');
    } else {
        // Leaflet není načten, zobrazíme placeholder
        mapContainer.innerHTML = `
            <div class="map-placeholder">
                <i class="fas fa-map-marked-alt"></i>
                <p>Pro zobrazení mapy je potřeba načíst knihovnu Leaflet. Přidejte následující kód do hlavičky HTML:</p>
                <pre>
&lt;link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" /&gt;
&lt;script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"&gt;&lt;/script&gt;
                </pre>
            </div>
        `;
        console.error('Knihovna Leaflet není načtena');
    }
}

/**
 * Inicializace vrstev
 */
function initLayers() {
    console.log('Inicializace vrstev');
    
    // Definice dostupných vrstev
    mapLayers = {
        // Základní mapové podklady
        basemaps: {
            streets: L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }),
            satellite: L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
                attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community'
            }),
            terrain: L.tileLayer('https://stamen-tiles-{s}.a.ssl.fastly.net/terrain/{z}/{x}/{y}{r}.png', {
                attribution: 'Map tiles by <a href="http://stamen.com">Stamen Design</a>, <a href="http://creativecommons.org/licenses/by/3.0">CC BY 3.0</a> &mdash; Map data &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            }),
            dark: L.tileLayer('https://cartodb-basemaps-{s}.global.ssl.fastly.net/dark_all/{z}/{x}/{y}{r}.png', {
                attribution: '&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a> &copy; <a href="http://cartodb.com/attributions">CartoDB</a>'
            })
        },
        
        // Datové vrstvy
        dataLayers: {
            // Vrstva pro markery
            markers: L.layerGroup(),
            
            // Vrstva pro oblasti
            areas: L.layerGroup(),
            
            // Vrstva pro trasy
            routes: L.layerGroup()
        }
    };
    
    // Přidání základní vrstvy na mapu
    mapLayers.basemaps[activeBasemap].addTo(map);
    
    // Přidání datových vrstev na mapu
    Object.values(mapLayers.dataLayers).forEach(layer => {
        layer.addTo(map);
    });
}

/**
 * Inicializace nástrojů pro kreslení
 */
function initDrawingTools() {
    console.log('Inicializace nástrojů pro kreslení');
    
    // Kontrola, zda je načtena knihovna Leaflet.draw
    if (typeof L.Control.Draw !== 'undefined') {
        // Leaflet.draw je načten
        const drawOptions = {
            position: 'topright',
            draw: {
                polyline: {
                    shapeOptions: {
                        color: '#f357a1',
                        weight: 3
                    }
                },
                polygon: {
                    allowIntersection: false,
                    drawError: {
                        color: '#e1e100',
                        message: '<strong>Chyba:</strong> Polygon se nemůže protínat!'
                    },
                    shapeOptions: {
                        color: '#3388ff'
                    }
                },
                circle: false,
                rectangle: {
                    shapeOptions: {
                        color: '#3388ff'
                    }
                },
                marker: true
            },
            edit: {
                featureGroup: L.featureGroup([
                    mapLayers.dataLayers.markers,
                    mapLayers.dataLayers.areas,
                    mapLayers.dataLayers.routes
                ]),
                remove: true
            }
        };
        
        drawingManager = new L.Control.Draw(drawOptions);
        map.addControl(drawingManager);
        
        // Event listener pro vytvoření nového prvku
        map.on(L.Draw.Event.CREATED, function(event) {
            const layer = event.layer;
            const type = event.layerType;
            
            // Přidání prvku do příslušné vrstvy
            if (type === 'marker') {
                mapLayers.dataLayers.markers.addLayer(layer);
                markers.push(layer);
            } else if (type === 'polygon' || type === 'rectangle') {
                mapLayers.dataLayers.areas.addLayer(layer);
            } else if (type === 'polyline') {
                mapLayers.dataLayers.routes.addLayer(layer);
            }
            
            // Zobrazení popup s informacemi o prvku
            layer.bindPopup(createPopupContent(type));
            layer.on('click', function() {
                this.openPopup();
            });
        });
    } else {
        // Leaflet.draw není načten
        console.error('Knihovna Leaflet.draw není načtena');
    }
}

/**
 * Vytvoření obsahu popup okna pro prvek
 * @param {string} type - Typ prvku (marker, polygon, polyline)
 * @returns {string} - HTML obsah popup okna
 */
function createPopupContent(type) {
    let content = '';
    
    if (type === 'marker') {
        content = `
            <div class="popup-content">
                <h4>Bod zájmu</h4>
                <div class="popup-form">
                    <div class="form-group">
                        <label>Název:</label>
                        <input type="text" class="popup-input popup-name" placeholder="Zadejte název">
                    </div>
                    <div class="form-group">
                        <label>Popis:</label>
                        <textarea class="popup-input popup-description" placeholder="Zadejte popis"></textarea>
                    </div>
                    <div class="form-group">
                        <label>Kategorie:</label>
                        <select class="popup-input popup-category">
                            <option value="general">Obecné</option>
                            <option value="crime">Trestný čin</option>
                            <option value="person">Osoba</option>
                            <option value="vehicle">Vozidlo</option>
                            <option value="building">Budova</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn-inline save-popup-data">
                            <i class="fas fa-save"></i> Uložit
                        </button>
                    </div>
                </div>
            </div>
        `;
    } else if (type === 'polygon' || type === 'rectangle') {
        content = `
            <div class="popup-content">
                <h4>Oblast zájmu</h4>
                <div class="popup-form">
                    <div class="form-group">
                        <label>Název:</label>
                        <input type="text" class="popup-input popup-name" placeholder="Zadejte název">
                    </div>
                    <div class="form-group">
                        <label>Popis:</label>
                        <textarea class="popup-input popup-description" placeholder="Zadejte popis"></textarea>
                    </div>
                    <div class="form-group">
                        <label>Typ oblasti:</label>
                        <select class="popup-input popup-category">
                            <option value="general">Obecné</option>
                            <option value="crime-area">Oblast trestné činnosti</option>
                            <option value="surveillance">Sledovaná oblast</option>
                            <option value="restricted">Omezená oblast</option>
                            <option value="search">Oblast pátrání</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn-inline save-popup-data">
                            <i class="fas fa-save"></i> Uložit
                        </button>
                        <button type="button" class="btn-inline analyze-popup-area">
                            <i class="fas fa-chart-bar"></i> Analyzovat
                        </button>
                    </div>
                </div>
            </div>
        `;
    } else if (type === 'polyline') {
        content = `
            <div class="popup-content">
                <h4>Trasa</h4>
                <div class="popup-form">
                    <div class="form-group">
                        <label>Název:</label>
                        <input type="text" class="popup-input popup-name" placeholder="Zadejte název">
                    </div>
                    <div class="form-group">
                        <label>Popis:</label>
                        <textarea class="popup-input popup-description" placeholder="Zadejte popis"></textarea>
                    </div>
                    <div class="form-group">
                        <label>Typ trasy:</label>
                        <select class="popup-input popup-category">
                            <option value="general">Obecné</option>
                            <option value="vehicle-route">Trasa vozidla</option>
                            <option value="person-route">Trasa osoby</option>
                            <option value="escape-route">Úniková trasa</option>
                            <option value="patrol">Hlídková trasa</option>
                        </select>
                    </div>
                    <div class="form-actions">
                        <button type="button" class="btn-inline save-popup-data">
                            <i class="fas fa-save"></i> Uložit
                        </button>
                        <button type="button" class="btn-inline analyze-popup-route">
                            <i class="fas fa-chart-line"></i> Analyzovat
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    return content;
}
