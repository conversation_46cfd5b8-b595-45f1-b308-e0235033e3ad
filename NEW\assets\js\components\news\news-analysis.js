/**
 * Akt<PERSON><PERSON><PERSON><PERSON> ud<PERSON>los<PERSON> - <PERSON>ce pro zobrazení a analýzu zpráv
 */

/**
 * Zobrazení zpráv
 * @param {Array} news - Pole zpráv
 */
function displayNews(news) {
    console.log('Zobrazení zpráv:', news);
    
    // Najít aktuální modul aktuálních událostí
    const newsModule = document.querySelector('.module[id^="module-aktualni-udalosti"]');
    if (!newsModule) {
        console.error('Modul aktuálních událostí nebyl nalezen');
        return;
    }
    
    // Najít kontejner pro výsledky
    const newsContainer = newsModule.querySelector('#news-results');
    if (!newsContainer) {
        console.error('Kontejner pro výsledky nebyl nalezen');
        return;
    }
    
    // Kontrola, zda existují zprávy
    if (!news || news.length === 0) {
        newsContainer.innerHTML = `
            <div class="no-results">
                <i class="fas fa-search"></i>
                <p>Nebyly nalezeny žádné zprávy odpovídající zadaným kritériím.</p>
            </div>
        `;
        return;
    }
    
    // Uložení zpráv do cache
    newsCache = {};
    news.forEach(item => {
        newsCache[item.id] = item;
    });
    
    // Vytvoření HTML pro zprávy
    let newsHtml = `
        <div class="news-header">
            <h4>Nalezené zprávy (${news.length})</h4>
            <div class="news-actions">
                <button type="button" class="btn-inline export-news">
                    <i class="fas fa-file-export"></i> Exportovat
                </button>
                <button type="button" class="btn-inline setup-monitoring">
                    <i class="fas fa-eye"></i> Nastavit monitoring
                </button>
            </div>
        </div>
        <div class="news-list">
    `;
    
    // Přidání zpráv
    news.forEach(item => {
        // Formátování data publikace
        const publishedDate = item.publishedAt ? formatPublishedDate(item.publishedAt) : 'Neznámé datum';
        
        // Určení třídy pro sentiment
        let sentimentClass = 'sentiment-neutral';
        let sentimentIcon = 'fa-meh';
        
        if (item.sentiment === 'positive') {
            sentimentClass = 'sentiment-positive';
            sentimentIcon = 'fa-smile';
        } else if (item.sentiment === 'negative') {
            sentimentClass = 'sentiment-negative';
            sentimentIcon = 'fa-frown';
        }
        
        // Vytvoření HTML pro zprávu
        newsHtml += `
            <div class="news-item ${sentimentClass}" data-news-id="${item.id}">
                <div class="news-item-header">
                    <div class="news-item-source">${item.source}</div>
                    <div class="news-item-date">${publishedDate}</div>
                    <div class="news-item-sentiment">
                        <i class="far ${sentimentIcon}"></i>
                    </div>
                </div>
                <div class="news-item-content">
                    <div class="news-item-title">${item.title}</div>
                    <div class="news-item-description">${item.description}</div>
                </div>
                ${item.imageUrl ? `
                <div class="news-item-image">
                    <img src="${item.imageUrl}" alt="${item.title}" onerror="this.style.display='none'">
                </div>
                ` : ''}
                <div class="news-item-footer">
                    <div class="news-item-entities">
        `;
        
        // Přidání entit (pokud existují)
        if (item.entities) {
            // Přidání osob
            if (item.entities.persons && item.entities.persons.length > 0) {
                item.entities.persons.slice(0, 2).forEach(person => {
                    newsHtml += `<span class="entity entity-person">${person}</span>`;
                });
            }
            
            // Přidání organizací
            if (item.entities.organizations && item.entities.organizations.length > 0) {
                item.entities.organizations.slice(0, 2).forEach(org => {
                    newsHtml += `<span class="entity entity-organization">${org}</span>`;
                });
            }
            
            // Přidání lokací
            if (item.entities.locations && item.entities.locations.length > 0) {
                item.entities.locations.slice(0, 2).forEach(location => {
                    newsHtml += `<span class="entity entity-location">${location}</span>`;
                });
            }
        }
        
        newsHtml += `
                    </div>
                    <div class="news-item-actions">
                        <button type="button" class="btn-inline news-item-detail" data-news-id="${item.id}">
                            <i class="fas fa-info-circle"></i> Detail
                        </button>
                        <a href="${item.url}" target="_blank" class="btn-inline news-item-source-link">
                            <i class="fas fa-external-link-alt"></i> Zdroj
                        </a>
                    </div>
                </div>
            </div>
        `;
    });
    
    newsHtml += `</div>`;
    
    // Přidání analýzy zpráv
    newsHtml += `
        <div class="news-analysis">
            <div class="news-analysis-header">
                <h4>Analýza zpráv</h4>
            </div>
            <div class="news-analysis-content">
                <div class="news-analysis-section">
                    <h5>Nejčastější entity</h5>
                    <div class="entity-cloud" id="entity-cloud">
                        ${generateEntityCloud(news)}
                    </div>
                </div>
                <div class="news-analysis-section">
                    <h5>Sentiment zpráv</h5>
                    <div class="sentiment-chart" id="sentiment-chart">
                        ${generateSentimentChart(news)}
                    </div>
                </div>
                <div class="news-analysis-section">
                    <h5>Časová osa</h5>
                    <div class="timeline-chart" id="timeline-chart">
                        ${generateTimelineChart(news)}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Aktualizace kontejneru
    newsContainer.innerHTML = newsHtml;
    
    // Přidání event listenerů pro tlačítka detailu
    const detailButtons = newsContainer.querySelectorAll('.news-item-detail');
    detailButtons.forEach(button => {
        button.addEventListener('click', function() {
            const newsId = this.getAttribute('data-news-id');
            if (newsId) {
                showNewsDetail(newsId);
            }
        });
    });
}

/**
 * Formátování data publikace
 * @param {string} dateString - Datum publikace
 * @returns {string} - Formátované datum
 */
function formatPublishedDate(dateString) {
    try {
        const date = new Date(dateString);
        
        // Kontrola, zda je datum platné
        if (isNaN(date.getTime())) {
            return dateString;
        }
        
        // Formátování data
        return date.toLocaleDateString('cs-CZ', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        console.error('Chyba při formátování data:', error);
        return dateString;
    }
}

/**
 * Generování oblaku entit
 * @param {Array} news - Pole zpráv
 * @returns {string} - HTML pro oblak entit
 */
function generateEntityCloud(news) {
    // Extrakce všech entit ze zpráv
    const entities = {
        persons: [],
        organizations: [],
        locations: []
    };
    
    news.forEach(item => {
        if (item.entities) {
            // Přidání osob
            if (item.entities.persons && item.entities.persons.length > 0) {
                entities.persons = entities.persons.concat(item.entities.persons);
            }
            
            // Přidání organizací
            if (item.entities.organizations && item.entities.organizations.length > 0) {
                entities.organizations = entities.organizations.concat(item.entities.organizations);
            }
            
            // Přidání lokací
            if (item.entities.locations && item.entities.locations.length > 0) {
                entities.locations = entities.locations.concat(item.entities.locations);
            }
        }
    });
    
    // Počítání četnosti entit
    const entityCounts = {};
    
    // Počítání osob
    entities.persons.forEach(person => {
        entityCounts[person] = (entityCounts[person] || 0) + 1;
    });
    
    // Počítání organizací
    entities.organizations.forEach(org => {
        entityCounts[org] = (entityCounts[org] || 0) + 1;
    });
    
    // Počítání lokací
    entities.locations.forEach(location => {
        entityCounts[location] = (entityCounts[location] || 0) + 1;
    });
    
    // Seřazení entit podle četnosti
    const sortedEntities = Object.entries(entityCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 20); // Omezení na 20 nejčastějších entit
    
    // Pokud nejsou žádné entity, vrátíme informaci
    if (sortedEntities.length === 0) {
        return `<p>Nebyly nalezeny žádné entity.</p>`;
    }
    
    // Vytvoření HTML pro oblak entit
    let cloudHtml = '';
    
    sortedEntities.forEach(([entity, count]) => {
        // Určení velikosti fontu podle četnosti (min: 12px, max: 24px)
        const maxCount = sortedEntities[0][1];
        const fontSize = 12 + (count / maxCount) * 12;
        
        // Určení typu entity
        let entityClass = 'entity-other';
        
        if (entities.persons.includes(entity)) {
            entityClass = 'entity-person';
        } else if (entities.organizations.includes(entity)) {
            entityClass = 'entity-organization';
        } else if (entities.locations.includes(entity)) {
            entityClass = 'entity-location';
        }
        
        cloudHtml += `
            <span class="entity-cloud-item ${entityClass}" style="font-size: ${fontSize}px;">
                ${entity} <span class="entity-count">(${count})</span>
            </span>
        `;
    });
    
    return cloudHtml;
}

/**
 * Generování grafu sentimentu
 * @param {Array} news - Pole zpráv
 * @returns {string} - HTML pro graf sentimentu
 */
function generateSentimentChart(news) {
    // Počítání sentimentu
    const sentimentCounts = {
        positive: 0,
        neutral: 0,
        negative: 0
    };
    
    news.forEach(item => {
        if (item.sentiment) {
            sentimentCounts[item.sentiment]++;
        } else {
            sentimentCounts.neutral++;
        }
    });
    
    // Výpočet procentuálního zastoupení
    const total = news.length;
    const positivePercent = Math.round((sentimentCounts.positive / total) * 100) || 0;
    const neutralPercent = Math.round((sentimentCounts.neutral / total) * 100) || 0;
    const negativePercent = Math.round((sentimentCounts.negative / total) * 100) || 0;
    
    // Vytvoření HTML pro graf sentimentu
    return `
        <div class="sentiment-bars">
            <div class="sentiment-bar">
                <div class="sentiment-bar-label">Pozitivní</div>
                <div class="sentiment-bar-value" style="width: ${positivePercent}%;">
                    <span>${positivePercent}%</span>
                </div>
            </div>
            <div class="sentiment-bar">
                <div class="sentiment-bar-label">Neutrální</div>
                <div class="sentiment-bar-value" style="width: ${neutralPercent}%;">
                    <span>${neutralPercent}%</span>
                </div>
            </div>
            <div class="sentiment-bar">
                <div class="sentiment-bar-label">Negativní</div>
                <div class="sentiment-bar-value" style="width: ${negativePercent}%;">
                    <span>${negativePercent}%</span>
                </div>
            </div>
        </div>
    `;
}

/**
 * Generování časové osy
 * @param {Array} news - Pole zpráv
 * @returns {string} - HTML pro časovou osu
 */
function generateTimelineChart(news) {
    // Vytvoření mapy pro počítání zpráv podle data
    const dateMap = new Map();
    
    // Zpracování dat publikace
    news.forEach(item => {
        if (item.publishedAt) {
            try {
                const date = new Date(item.publishedAt);
                
                // Kontrola, zda je datum platné
                if (!isNaN(date.getTime())) {
                    // Formátování data (pouze datum bez času)
                    const dateString = date.toISOString().split('T')[0];
                    
                    // Přidání do mapy
                    dateMap.set(dateString, (dateMap.get(dateString) || 0) + 1);
                }
            } catch (error) {
                console.error('Chyba při zpracování data:', error);
            }
        }
    });
    
    // Seřazení dat
    const sortedDates = Array.from(dateMap.entries()).sort((a, b) => a[0].localeCompare(b[0]));
    
    // Pokud nejsou žádná data, vrátíme informaci
    if (sortedDates.length === 0) {
        return `<p>Nebyly nalezeny žádné zprávy s platným datem publikace.</p>`;
    }
    
    // Vytvoření HTML pro časovou osu
    let timelineHtml = `<div class="timeline-bars">`;
    
    sortedDates.forEach(([date, count]) => {
        // Formátování data
        const formattedDate = new Date(date).toLocaleDateString('cs-CZ', {
            day: '2-digit',
            month: '2-digit'
        });
        
        // Výpočet výšky sloupce (max: 100px)
        const maxCount = Math.max(...Array.from(dateMap.values()));
        const height = Math.max(20, (count / maxCount) * 100);
        
        timelineHtml += `
            <div class="timeline-bar" title="${formattedDate}: ${count} zpráv">
                <div class="timeline-bar-value" style="height: ${height}px;">
                    <span>${count}</span>
                </div>
                <div class="timeline-bar-label">${formattedDate}</div>
            </div>
        `;
    });
    
    timelineHtml += `</div>`;
    
    return timelineHtml;
}
