/* Styly pro modul komplex<PERSON><PERSON><PERSON> v<PERSON>hledávání */

/* <PERSON><PERSON> */
.alert {
    padding: 10px 15px;
    margin-bottom: 15px;
    border-radius: 4px;
    font-size: 14px;
}

.alert-info {
    background-color: #e3f2fd;
    color: #0d47a1;
    border-left: 4px solid #1976d2;
}

.alert-warning {
    background-color: #fff3e0;
    color: #e65100;
    border-left: 4px solid #ff9800;
}

.alert i {
    margin-right: 5px;
}

/* <PERSON><PERSON><PERSON><PERSON> k<PERSON> */
.complex-search-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    background-color: #f9f9f9;
    border-radius: 5px;
    overflow: hidden;
    font-family: 'Segoe UI', Arial, sans-serif;
}

/* Hlavička modulu */
.complex-search-header {
    padding: 15px;
    background-color: #fff;
    border-bottom: 1px solid #ddd;
}

.complex-search-header h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    color: #333;
}

.complex-search-header p {
    margin: 0;
    font-size: 14px;
    color: #666;
}

/* <PERSON><PERSON><PERSON><PERSON> vyhledávání */
.complex-search-form {
    padding: 15px;
    background-color: #f0f0f0;
    border-bottom: 1px solid #ddd;
}

.search-type-selector {
    margin-bottom: 15px;
}

.search-type-selector label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: bold;
    color: #333;
}

.search-query-container {
    margin-bottom: 15px;
}

.search-query-container label {
    display: block;
    margin-bottom: 5px;
    font-size: 14px;
    font-weight: bold;
    color: #333;
}

.search-input-group {
    display: flex;
    gap: 10px;
}

.search-input-group input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
}

.search-input-group button {
    padding: 8px 15px;
    background-color: #1a3c89;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.search-input-group button:hover {
    background-color: #15306d;
}

.search-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.search-option {
    display: flex;
    align-items: center;
}

.search-option label {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333;
    cursor: pointer;
}

.search-option input[type="checkbox"] {
    margin-right: 5px;
}

/* Výsledky vyhledávání */
.complex-search-results {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    border-radius: 0 0 5px 5px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
}

.results-header h4 {
    margin: 0;
    font-size: 16px;
    color: #333;
}

.results-actions {
    display: flex;
    gap: 10px;
}

.results-actions button {
    padding: 6px 12px;
    background-color: #f8f9fa;
    color: #333;
    border: 1px solid #ccc;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.results-actions button:hover:not(:disabled) {
    background-color: #e2e6ea;
}

.results-actions button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.results-container {
    flex: 1;
    position: relative;
    overflow-y: auto;
    padding: 15px;
}

.results-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #1a3c89;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.results-loading p {
    margin: 0;
    font-size: 14px;
    color: #666;
}

.results-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    text-align: center;
}

.results-empty i {
    font-size: 48px;
    color: #ccc;
    margin-bottom: 15px;
}

.results-empty p {
    margin: 0 0 10px 0;
    font-size: 14px;
    color: #666;
    max-width: 500px;
}

.search-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
    justify-content: center;
}

.btn-search {
    padding: 10px 15px;
    background-color: #1a3c89;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.2s;
}

.btn-search:hover {
    background-color: #15306d;
}

.results-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Styl výsledku vyhledávání */
.search-result {
    display: flex;
    background-color: #f9f9f9;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.result-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    background-color: #1a3c89;
    color: white;
    font-size: 20px;
    position: relative;
}

/* Označení reálných dat */
.real-data-badge {
    position: absolute;
    top: 5px;
    right: 5px;
    color: #4caf50;
    font-size: 12px;
    background-color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.result-content {
    flex: 1;
    padding: 10px;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 5px;
}

.result-title {
    margin: 0;
    font-size: 16px;
    color: #1a3c89;
}

.result-source {
    font-size: 12px;
    color: #666;
    background-color: #f0f0f0;
    padding: 2px 6px;
    border-radius: 10px;
}

.result-url {
    margin-bottom: 5px;
}

.result-url a {
    font-size: 12px;
    color: #1a73e8;
    text-decoration: none;
    word-break: break-all;
}

.result-url a:hover {
    text-decoration: underline;
}

.result-description {
    margin-bottom: 5px;
    font-size: 14px;
    color: #333;
}

.result-details {
    margin-bottom: 10px;
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 4px;
    border-left: 3px solid #1a3c89;
}

.result-details h6 {
    margin: 0 0 5px 0;
    font-size: 14px;
    color: #1a3c89;
}

.result-details ul {
    margin: 0;
    padding-left: 20px;
    list-style-type: none;
}

.result-details li {
    margin-bottom: 3px;
    font-size: 13px;
    color: #333;
}

.result-details strong {
    color: #555;
}

.result-meta {
    display: flex;
    justify-content: flex-start;
    font-size: 12px;
    color: #666;
    flex-wrap: wrap;
    gap: 10px;
}

.real-data-indicator {
    background-color: #e8f5e9;
    color: #2e7d32;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
}

.direct-search-indicator {
    background-color: #e3f2fd;
    color: #0d47a1;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 500;
}

.direct-search-link {
    display: flex;
    align-items: center;
    gap: 5px;
}

.direct-search-link i {
    color: #1a73e8;
    font-size: 12px;
}

.result-actions {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 10px;
    background-color: #f0f0f0;
}

.add-to-entity, .open-search {
    white-space: nowrap;
    padding: 6px 10px;
    background-color: #f8f9fa;
    color: #333;
    border: 1px solid #ccc;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    display: flex;
    align-items: center;
    gap: 5px;
    text-decoration: none;
    margin-bottom: 5px;
}

.add-to-entity:hover, .open-search:hover {
    background-color: #e2e6ea;
}

.open-search {
    background-color: #e3f2fd;
    color: #0d47a1;
    border-color: #90caf9;
}

.open-search:hover {
    background-color: #bbdefb;
}

/* Responsivní úpravy */
@media (max-width: 768px) {
    .search-input-group {
        flex-direction: column;
    }

    .search-options {
        flex-direction: column;
        gap: 10px;
    }

    .search-result {
        flex-direction: column;
    }

    .result-icon {
        width: 100%;
        height: 40px;
    }

    .result-actions {
        padding: 10px;
    }

    .add-to-entity {
        width: 100%;
        justify-content: center;
    }
}
