"use client";

import type { SubmitHand<PERSON> } from 'react-hook-form';
import { useForm, useFieldA<PERSON>y, Controller, UseFormReturn } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import type {
  GunLicenseModuleData, RegisteredWeapon, PhysicalPersonSubject, PhotoMetadata,
  GunLicenseOwnershipStatus, GunLicenseGroup, HealthFitnessStatus, SecurityClearanceStatus, 
  WeaponCategory, WeaponLegalStatus, WeaponCondition, WeaponAcquisitionMethod,
  WeaponUsageContext, WeaponStorageMethod, InformationSource, InformationReliability, ThreatLevel
} from '@/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { PlusCircle, Trash2, ImageUp, Target, ShieldQuestion, FileText, Users, Ban, HelpCircle, Loader2, Upload, Copy, AlertTriangle, Activity, Shield, Search, FileImage } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { db } from '@/lib/firebase';
import { doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { cn } from '@/lib/utils';
import { useState, useCallback, useMemo, useEffect } from "react";
import {
  Form,
  FormField,
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { PhotoDocumentationSection } from './PhotoDocumentationSection';

const photoMetadataSchema = z.object({
  id: z.string().optional(),
  fileName: z.string().optional().default(''),
  downloadURL: z.string().optional().default(''),
  description: z.string().optional().default(''),
  dateTaken: z.string().optional().default(''),
  sourceURL: z.string().optional().default(''),
  storagePath: z.string().optional().default(''),
  location: z.string().optional().default(''),
  photographerInfo: z.string().optional().default(''),
  tags: z.string().optional().default(''),
  verified: z.boolean().optional().default(false),
});

const weaponAccessorySchema = z.object({
  id: z.string(),
  type: z.string(),
  brand: z.string().optional(),
  model: z.string().optional(),
  description: z.string().optional(),
  isLegal: z.boolean().optional(),
  photos: z.array(photoMetadataSchema).optional().default([]),
});

const weaponIncidentSchema = z.object({
  id: z.string(),
  date: z.string().optional(),
  incidentType: z.enum(['misuse', 'accident', 'threat', 'display', 'theft', 'other']),
  description: z.string().optional(),
  location: z.string().optional(),
  witnesses: z.array(z.string()).optional().default([]),
  policeInvolved: z.boolean().optional(),
  caseNumber: z.string().optional(),
  outcome: z.string().optional(),
  photos: z.array(photoMetadataSchema).optional().default([]),
  notes: z.string().optional(),
});

const registeredWeaponSchema = z.object({
  id: z.string(),
  weaponType: z.string().optional(),
  brand: z.string().optional(),
  model: z.string().optional(),
  serialNumber: z.string().optional(),
  category: z.enum(['A', 'A-I', 'B', 'C', 'C-I', 'D', 'other']).optional(),
  caliber: z.string().optional(),
  yearManufactured: z.number().optional(),
  countryOfOrigin: z.string().optional(),
  
  // Právní stav
  legalStatus: z.enum(['legal_registered', 'legal_unregistered', 'illegal_unregistered', 'stolen', 'converted', 'deactivated', 'unknown']).optional(),
  registrationNumber: z.string().optional(),
  registrationDate: z.string().optional(),
  registrationAuthority: z.string().optional(),
  permitNumber: z.string().optional(),
  permitValidUntil: z.string().optional(),
  
  // Fyzický stav
  condition: z.enum(['excellent', 'good', 'fair', 'poor', 'damaged', 'non_functional', 'unknown']).optional(),
  conditionDescription: z.string().optional(),
  modifications: z.string().optional(),
  distinguishingMarks: z.string().optional(),
  
  // Nabývání a vlastnictví
  acquisitionMethod: z.enum(['purchase_legal', 'purchase_illegal', 'inheritance', 'gift', 'theft', 'found', 'manufactured', 'converted', 'other', 'unknown']).optional(),
  acquisitionDate: z.string().optional(),
  acquisitionSource: z.string().optional(),
  purchasePrice: z.number().optional(),
  currentLocation: z.string().optional(),
  
  // Použití a uložení
  usageContext: z.enum(['sport', 'hunting', 'collection', 'self_defense', 'professional', 'criminal', 'threatening', 'display', 'unknown']).optional(),
  usageFrequency: z.string().optional(),
  lastUsed: z.string().optional(),
  storageMethod: z.enum(['gun_safe', 'locked_cabinet', 'trigger_lock', 'ammunition_separate', 'carried', 'hidden', 'unsecured', 'unknown']).optional(),
  storageLocation: z.string().optional(),
  
  // Munice a příslušenství
  ammunitionType: z.string().optional(),
  ammunitionQuantity: z.number().optional(),
  accessories: z.array(weaponAccessorySchema).optional().default([]),
  
  // Incidenty
  incidents: z.array(weaponIncidentSchema).optional().default([]),
  securityConcerns: z.string().optional(),
  misusageReports: z.string().optional(),
  
  // OSINT
  informationSource: z.enum(['social_media', 'surveillance', 'witness', 'documents', 'phone_analysis', 'investigation', 'open_source', 'other']).optional(),
  sourceDetail: z.string().optional(),
  informationReliability: z.enum(['verified', 'probable', 'possible', 'unconfirmed', 'unknown']).optional(),
  verificationDate: z.string().optional(),
  lastVerified: z.string().optional(),
  
  photos: z.array(photoMetadataSchema).optional().default([]),
  notes: z.string().optional(),
});

const gunLicenseModuleSchema = z.object({
  // Základní informace o ZP
  ownershipStatus: z.enum(['yes', 'no', 'expired', 'in-process', 'unknown']).optional(),
  licenseNumber: z.string().optional(),
  issueDate: z.string().optional(),
  validUntil: z.string().optional(),
  issuedBy: z.string().optional(),
  placeOfIssue: z.string().optional(),
  groups: z.array(z.enum(['A', 'B', 'C', 'D', 'E', 'F'])).optional().default([]),

  // Zdravotní a bezpečnostní způsobilost
  healthFitness: z.enum(['yes', 'limited', 'no', 'unknown']).optional(),
  healthRestrictions: z.string().optional(),
  medicalCertificateDate: z.string().optional(),
  medicalCertificateValidUntil: z.string().optional(),
  medicalExaminer: z.string().optional(),
  
  securityClearance: z.enum(['yes', 'no', 'unknown']).optional(),
  securityClearanceDate: z.string().optional(),
  securityClearanceAuthority: z.string().optional(),
  securityClearanceRestrictions: z.string().optional(),
  
  // Historie a právní status
  history: z.string().optional(),
  previousLicenses: z.string().optional(),
  licenseViolations: z.string().optional(),
  disciplinaryActions: z.string().optional(),
  courtProceedings: z.string().optional(),
  
  // Zbrojní licence (pro firmy)
  hasGunLicensePermit: z.boolean().optional().default(false),
  gunLicensePermitNumber: z.string().optional(),
  gunLicensePermitGroups: z.string().optional(),
  gunLicensePermitValidUntil: z.string().optional(),
  businessLicenseInfo: z.string().optional(),

  // Registrované zbraně
  registeredWeapons: z.array(registeredWeaponSchema).optional().default([]),
  
  // Střelecká činnost a výcvik
  shootingClubMembership: z.string().optional(),
  shootingCompetitions: z.string().optional(),
  weaponTraining: z.string().optional(),
  instructorCertifications: z.string().optional(),
  rangeActivity: z.string().optional(),
  
  // Sociální a osobní kontext
  notesOnSurroundingPersons: z.string().optional(),
  familyWeaponAccess: z.string().optional(),
  weaponHandlingInfo: z.string().optional(),
  weaponAccessNotes: z.string().optional(),
  weaponStorageInfo: z.string().optional(),
  securityMeasures: z.string().optional(),
  
  // Absence průkazu nebo problémy
  reasonForNoLicense: z.string().optional(),
  illegalPossessionInfo: z.string().optional(),
  interestInWeapons: z.string().optional(),
  weaponRelatedCriminalActivity: z.string().optional(),
  weaponThreats: z.string().optional(),
  
  // OSINT a bezpečnostní hodnocení
  informationSource: z.enum(['social_media', 'surveillance', 'witness', 'documents', 'phone_analysis', 'investigation', 'open_source', 'other']).optional(),
  sourceDetail: z.string().optional(),
  informationReliability: z.enum(['verified', 'probable', 'possible', 'unconfirmed', 'unknown']).optional(),
  verificationDate: z.string().optional(),
  lastVerified: z.string().optional(),
  
  weaponRiskAssessment: z.enum(['none', 'low', 'medium', 'high', 'critical', 'unknown']).optional(),
  riskAssessmentNotes: z.string().optional(),
  surveillanceNotes: z.string().optional(),
  investigationPriorities: z.string().optional(),
  
  // Poznámky a dokumentace
  investigationNotes: z.string().optional(),
  generalNotes: z.string().optional(),
  photos: z.array(photoMetadataSchema).optional().default([]),
});

type GunLicenseFormValues = z.infer<typeof gunLicenseModuleSchema>;

export type { GunLicenseFormValues };

interface GunLicenseFormProps {
  caseId: string;
  subject: PhysicalPersonSubject;
  existingData: GunLicenseModuleData | null;
  onSave: (moduleId: string, wasNew: boolean) => void;
}

// Možnosti pro select komponenty
const ownershipStatusOptions = [
  { value: "yes", label: "Ano, vlastní platný ZP" },
  { value: "no", label: "Ne, nevlastní ZP" },
  { value: "expired", label: "Neplatný / Odňatý ZP" },
  { value: "in-process", label: "V řízení o vydání ZP" },
  { value: "unknown", label: "Nezjištěno" },
];

const healthFitnessOptions = [
  { value: "yes", label: "Zdravotně způsobilý/á" },
  { value: "limited", label: "Způsobilý/á s omezením" },
  { value: "no", label: "Zdravotně nezpůsobilý/á" },
  { value: "unknown", label: "Nezjištěno" },
];

const securityClearanceOptions = [
  { value: "yes", label: "Splňuje bezpečnostní podmínky" },
  { value: "no", label: "Nesplňuje bezpečnostní podmínky" },
  { value: "unknown", label: "Nezjištěno" },
];

const weaponCategoryOptions = [
  { value: 'A', label: 'A - Zakázané zbraně' },
  { value: 'A-I', label: 'A-I - Zakázané (na výjimku)' },
  { value: 'B', label: 'B - Podléhající povolení' },
  { value: 'C', label: 'C - Podléhající ohlášení' },
  { value: 'C-I', label: 'C-I - Ohlášené (nově)' },
  { value: 'D', label: 'D - Ostatní (volně prodejné od 18 let)' },
  { value: 'other', label: 'Jiná / Nezjištěno' }
];

const weaponLegalStatusOptions = [
  { value: 'legal_registered', label: 'Legální registrovaná' },
  { value: 'legal_unregistered', label: 'Legální neregistrovaná' },
  { value: 'illegal_unregistered', label: 'Nelegální neregistrovaná' },
  { value: 'stolen', label: 'Kradená' },
  { value: 'converted', label: 'Přestavěná' },
  { value: 'deactivated', label: 'Deaktivovaná' },
  { value: 'unknown', label: 'Nezjištěno' }
];

const weaponConditionOptions = [
  { value: 'excellent', label: 'Výborný stav' },
  { value: 'good', label: 'Dobrý stav' },
  { value: 'fair', label: 'Průměrný stav' },
  { value: 'poor', label: 'Špatný stav' },
  { value: 'damaged', label: 'Poškozená' },
  { value: 'non_functional', label: 'Nefunkční' },
  { value: 'unknown', label: 'Nezjištěno' }
];

const weaponAcquisitionOptions = [
  { value: 'purchase_legal', label: 'Legální nákup' },
  { value: 'purchase_illegal', label: 'Nelegální nákup' },
  { value: 'inheritance', label: 'Dědictví' },
  { value: 'gift', label: 'Dar' },
  { value: 'theft', label: 'Krádež' },
  { value: 'found', label: 'Nalezená' },
  { value: 'manufactured', label: 'Vyrobená' },
  { value: 'converted', label: 'Přestavěná' },
  { value: 'other', label: 'Jiné' },
  { value: 'unknown', label: 'Nezjištěno' }
];

const weaponUsageOptions = [
  { value: 'sport', label: 'Sportovní účely' },
  { value: 'hunting', label: 'Lovecké účely' },
  { value: 'collection', label: 'Sběratelské účely' },
  { value: 'self_defense', label: 'Obrana' },
  { value: 'professional', label: 'Profesní účely' },
  { value: 'criminal', label: 'Trestná činnost' },
  { value: 'threatening', label: 'Vyhrožování' },
  { value: 'display', label: 'Prezentace' },
  { value: 'unknown', label: 'Nezjištěno' }
];

const weaponStorageOptions = [
  { value: 'gun_safe', label: 'Zbraňový trezor' },
  { value: 'locked_cabinet', label: 'Uzamčená skříň' },
  { value: 'trigger_lock', label: 'Spoušťový zámek' },
  { value: 'ammunition_separate', label: 'Munice odděleně' },
  { value: 'carried', label: 'Nosí při sobě' },
  { value: 'hidden', label: 'Skrytá' },
  { value: 'unsecured', label: 'Nezabezpečená' },
  { value: 'unknown', label: 'Nezjištěno' }
];

const informationSourceOptions = [
  { value: 'social_media', label: 'Sociální média' },
  { value: 'surveillance', label: 'Sledování' },
  { value: 'witness', label: 'Svědek' },
  { value: 'documents', label: 'Dokumenty' },
  { value: 'phone_analysis', label: 'Analýza telefonu' },
  { value: 'investigation', label: 'Vyšetřování' },
  { value: 'open_source', label: 'Otevřené zdroje' },
  { value: 'other', label: 'Jiný' }
];

const informationReliabilityOptions = [
  { value: 'verified', label: 'Ověřeno' },
  { value: 'probable', label: 'Pravděpodobné' },
  { value: 'possible', label: 'Možné' },
  { value: 'unconfirmed', label: 'Nepotvrzeno' },
  { value: 'unknown', label: 'Neznámo' }
];

const threatLevelOptions = [
  { value: 'none', label: 'Žádné riziko' },
  { value: 'low', label: 'Nízké riziko' },
  { value: 'medium', label: 'Střední riziko' },
  { value: 'high', label: 'Vysoké riziko' },
  { value: 'critical', label: 'Kritické riziko' },
  { value: 'unknown', label: 'Neznámé' }
];

const gunLicenseGroups = ['A', 'B', 'C', 'D', 'E', 'F'] as const;

export function GunLicenseForm({ caseId, subject, existingData, onSave }: GunLicenseFormProps) {
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const moduleId = "gun_license";

  // Stabilní defaultní hodnoty s memoizací
  const defaultValues = useMemo(() => {
    // Funkce pro bezpečné generování ID
    const generateId = () => Date.now().toString(36) + Math.random().toString(36).substr(2);
    
    return {
      ownershipStatus: existingData?.ownershipStatus || undefined,
      licenseNumber: existingData?.licenseNumber || "",
      issueDate: existingData?.issueDate || "",
      validUntil: existingData?.validUntil || "",
      issuedBy: existingData?.issuedBy || "",
      placeOfIssue: existingData?.placeOfIssue || "",
      groups: existingData?.groups || [],
      
      healthFitness: existingData?.healthFitness || undefined,
      healthRestrictions: existingData?.healthRestrictions || "",
      medicalCertificateDate: existingData?.medicalCertificateDate || "",
      medicalCertificateValidUntil: existingData?.medicalCertificateValidUntil || "",
      medicalExaminer: existingData?.medicalExaminer || "",
      
      securityClearance: existingData?.securityClearance || undefined,
      securityClearanceDate: existingData?.securityClearanceDate || "",
      securityClearanceAuthority: existingData?.securityClearanceAuthority || "",
      securityClearanceRestrictions: existingData?.securityClearanceRestrictions || "",
      
      history: existingData?.history || "",
      previousLicenses: existingData?.previousLicenses || "",
      licenseViolations: existingData?.licenseViolations || "",
      disciplinaryActions: existingData?.disciplinaryActions || "",
      courtProceedings: existingData?.courtProceedings || "",
      
      hasGunLicensePermit: existingData?.hasGunLicensePermit || false,
      gunLicensePermitNumber: existingData?.gunLicensePermitNumber || "",
      gunLicensePermitGroups: existingData?.gunLicensePermitGroups || "",
      gunLicensePermitValidUntil: existingData?.gunLicensePermitValidUntil || "",
      businessLicenseInfo: existingData?.businessLicenseInfo || "",
      
      registeredWeapons: existingData?.registeredWeapons?.map(w => ({
        ...w,
        id: w.id || generateId(),
        photos: w.photos?.map(p => ({...p, id: p.id || generateId()})) || [],
        accessories: w.accessories?.map(a => ({...a, id: a.id || generateId(), photos: a.photos?.map(p => ({...p, id: p.id || generateId()})) || []})) || [],
        incidents: w.incidents?.map(i => ({...i, id: i.id || generateId(), photos: i.photos?.map(p => ({...p, id: p.id || generateId()})) || [], witnesses: i.witnesses || []})) || [],
      })) || [],
      
      shootingClubMembership: existingData?.shootingClubMembership || "",
      shootingCompetitions: existingData?.shootingCompetitions || "",
      weaponTraining: existingData?.weaponTraining || "",
      instructorCertifications: existingData?.instructorCertifications || "",
      rangeActivity: existingData?.rangeActivity || "",
      
      notesOnSurroundingPersons: existingData?.notesOnSurroundingPersons || "",
      familyWeaponAccess: existingData?.familyWeaponAccess || "",
      weaponHandlingInfo: existingData?.weaponHandlingInfo || "",
      weaponAccessNotes: existingData?.weaponAccessNotes || "",
      weaponStorageInfo: existingData?.weaponStorageInfo || "",
      securityMeasures: existingData?.securityMeasures || "",
      
      reasonForNoLicense: existingData?.reasonForNoLicense || "",
      illegalPossessionInfo: existingData?.illegalPossessionInfo || "",
      interestInWeapons: existingData?.interestInWeapons || "",
      weaponRelatedCriminalActivity: existingData?.weaponRelatedCriminalActivity || "",
      weaponThreats: existingData?.weaponThreats || "",
      
      informationSource: existingData?.informationSource || undefined,
      sourceDetail: existingData?.sourceDetail || "",
      informationReliability: existingData?.informationReliability || undefined,
      verificationDate: existingData?.verificationDate || "",
      lastVerified: existingData?.lastVerified || "",
      
      weaponRiskAssessment: existingData?.weaponRiskAssessment || undefined,
      riskAssessmentNotes: existingData?.riskAssessmentNotes || "",
      surveillanceNotes: existingData?.surveillanceNotes || "",
      investigationPriorities: existingData?.investigationPriorities || "",
      
      investigationNotes: existingData?.investigationNotes || "",
      generalNotes: existingData?.generalNotes || "",
      photos: existingData?.photos?.map(p => ({...p, id: p.id || generateId()})) || [],
    };
  }, [existingData]);

  const form = useForm<GunLicenseFormValues>({
    resolver: zodResolver(gunLicenseModuleSchema),
    defaultValues,
  });

  // Debug logs pro formulář
  useEffect(() => {
    const subscription = form.watch((value, { name, type }) => {
      console.log('🔫 Form watch:', { name, type, value });
      console.log('🔫 Form errors:', form.formState.errors);
      console.log('🔫 Form isValid:', form.formState.isValid);
    });
    return () => subscription.unsubscribe();
  }, [form]);

  const { fields: weaponFields, append: appendWeapon, remove: removeWeapon } = useFieldArray({
    control: form.control,
    name: "registeredWeapons",
  });

  const { fields: modulePhotoFields, append: appendModulePhoto, remove: removeModulePhoto } = useFieldArray({
    control: form.control,
    name: "photos",
  });

  const watchedOwnershipStatus = form.watch("ownershipStatus");
  const watchedHasGunLicensePermit = form.watch("hasGunLicensePermit");

  // Funkce pro generování stabilního ID
  const generateStableId = useCallback(() => {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }, []);

  // Funkce pro odstranění undefined hodnot z objektu (rekurzivně)
  const removeUndefinedValues = (obj: any): any => {
    if (obj === null || obj === undefined) {
      return null;
    }
    
    if (Array.isArray(obj)) {
      return obj.map(removeUndefinedValues).filter(item => item !== undefined);
    }
    
    if (typeof obj === 'object') {
      const cleaned: any = {};
      for (const [key, value] of Object.entries(obj)) {
        if (value !== undefined) {
          const cleanedValue = removeUndefinedValues(value);
          if (cleanedValue !== undefined) {
            cleaned[key] = cleanedValue;
          }
        }
      }
      return cleaned;
    }
    
    return obj;
  };

  const onSubmitHandler: SubmitHandler<GunLicenseFormValues> = async (data) => {
    console.log('🔫 Gun License Form Submit (raw):', data);
    setIsSaving(true);
    try {
      const moduleDocRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);
      
      // Odstranit undefined hodnoty před uložením
      const cleanedData = removeUndefinedValues(data);
      console.log('🔫 Cleaned data (no undefined):', cleanedData);
      
      const dataToSave: any = {
        ...cleanedData,
        subjectId: subject.id,
        lastUpdatedAt: serverTimestamp(),
        createdAt: existingData?.createdAt || serverTimestamp(),
      };

      console.log('🔫 Saving to Firebase:', dataToSave);
      await setDoc(moduleDocRef, dataToSave, { merge: true });
      toast({ title: "Data modulu Zbrojní průkaz úspěšně uložena" });
      const wasNew = !existingData || !existingData.createdAt;
      onSave(moduleId, wasNew);
      console.log('🔫 Successfully saved, wasNew:', wasNew);
    } catch (error: any) {
      console.error('🔫 Error saving:', error);
      toast({ title: "Chyba při ukládání dat ZP", description: error.message, variant: "destructive" });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmitHandler)} className="space-y-8">
        <Card className="shadow-lg border border-primary/30">
          <CardHeader>
            <CardTitle className="text-xl flex items-center">
              <Target className="mr-3 h-6 w-6 text-primary"/>
              Zbrojní Průkaz: {subject.firstName} {subject.lastName}
            </CardTitle>
            <CardDescription>
              Komplexní OSINT analýza zbrojních průkazů, licencí a registrovaných zbraní subjektu.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6 pt-4">
            
            {/* Základní informace o ZP */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <FileText className="mr-2 h-5 w-5 text-primary/80"/>
                  Stav Zbrojního Průkazu
                </CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
                <FormItemSelectRHF 
                  label="Vlastní zbrojní průkaz?" 
                  name="ownershipStatus" 
                  control={form.control} 
                  options={ownershipStatusOptions} 
                  placeholder="-- Vyberte stav --"
                />
                {watchedOwnershipStatus === 'yes' && (
                  <FormItemRHF 
                    label="Číslo zbrojního průkazu" 
                    name="licenseNumber" 
                    control={form.control} 
                    placeholder="Zadejte číslo ZP" 
                  />
                )}
              </CardContent>
            </Card>

            {/* Detaily ZP (pokud vlastní) */}
            {watchedOwnershipStatus === 'yes' && (
              <Card className="bg-muted/20">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <ShieldQuestion className="mr-2 h-5 w-5 text-primary/80"/>
                    Detaily Zbrojního Průkazu
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF label="Datum vydání" name="issueDate" control={form.control} type="date" />
                    <FormItemRHF label="Platnost do" name="validUntil" control={form.control} type="date" />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF label="Vydal (útvar policie)" name="issuedBy" control={form.control} placeholder="Příslušný útvar policie" />
                    <FormItemRHF label="Místo vydání" name="placeOfIssue" control={form.control} placeholder="Místo vydání ZP" />
                  </div>
                  
                  <FormField
                    control={form.control}
                    name="groups"
                    render={() => (
                      <FormItem>
                        <FormLabel className="text-sm font-semibold">Skupiny ZP</FormLabel>
                        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3 p-3 border rounded-md bg-background">
                          {gunLicenseGroups.map((group) => (
                            <FormField
                              key={group}
                              control={form.control}
                              name="groups"
                              render={({ field }) => {
                                return (
                                  <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                                    <FormControl>
                                      <Checkbox
                                        checked={field.value?.includes(group)}
                                        onCheckedChange={(checked) => {
                                          return checked
                                            ? field.onChange([...(field.value || []), group])
                                            : field.onChange(
                                                (field.value || []).filter(
                                                  (value) => value !== group
                                                )
                                              )
                                        }}
                                      />
                                    </FormControl>
                                    <FormLabel className="font-normal text-xs sm:text-sm">
                                      {group} - {
                                        {A: 'Sběratelské', B: 'Sportovní', C: 'Lovecké', D: 'Výkon zaměstnání', E: 'Ochrana', F: 'Pyrotechnika'}[group]
                                      }
                                    </FormLabel>
                                  </FormItem>
                                )
                              }}
                            />
                          ))}
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemSelectRHF 
                      label="Zdravotní způsobilost" 
                      name="healthFitness" 
                      control={form.control} 
                      options={healthFitnessOptions} 
                      placeholder="-- Vyberte --"
                    />
                    <FormItemSelectRHF 
                      label="Bezpečnostní způsobilost" 
                      name="securityClearance" 
                      control={form.control} 
                      options={securityClearanceOptions} 
                      placeholder="-- Vyberte --"
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF label="Datum lékařského vyšetření" name="medicalCertificateDate" control={form.control} type="date" />
                    <FormItemRHF label="Lékařské platné do" name="medicalCertificateValidUntil" control={form.control} type="date" />
                  </div>
                  
                  <FormItemRHF label="Vyšetřující lékař" name="medicalExaminer" control={form.control} placeholder="Jméno a pracoviště lékaře" />
                  <FormItemRHF label="Zdravotní omezení" name="healthRestrictions" control={form.control} placeholder="Popis zdravotních omezení..." as="textarea" rows={2}/>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF label="Datum bezp. prověrky" name="securityClearanceDate" control={form.control} type="date" />
                    <FormItemRHF label="Autorität bezp. prověrky" name="securityClearanceAuthority" control={form.control} placeholder="Kdo provedl prověrku" />
                  </div>
                  
                  <FormItemRHF label="Bezpečnostní omezení" name="securityClearanceRestrictions" control={form.control} placeholder="Omezení ze strany bezpečnostní prověrky..." as="textarea" rows={2}/>
                  <FormItemRHF label="Historie ZP" name="history" control={form.control} placeholder="Předchozí ZP, odnětí, zamítnutí..." as="textarea" rows={3}/>
                </CardContent>
              </Card>
            )}

            {/* Pokud ZP nevlastní nebo je neplatný */}
            {(watchedOwnershipStatus === 'no' || watchedOwnershipStatus === 'expired') && (
              <Card className="bg-destructive/10 border-destructive/30">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <Ban className="mr-2 h-5 w-5 text-destructive"/>
                    Informace o absenci/neplatnosti ZP
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 pt-4">
                  <FormItemRHF label="Důvod absence/neplatnosti ZP" name="reasonForNoLicense" control={form.control} placeholder="Důvody zamítnutí, odnětí, propadnutí..." as="textarea" rows={3}/>
                  <FormItemRHF label="Indicie o nelegálním držení zbraní" name="illegalPossessionInfo" control={form.control} placeholder="Typy zbraní, okolnosti..." as="textarea" rows={3}/>
                  <FormItemRHF label="Obecný zájem o zbraněmi" name="interestInWeapons" control={form.control} placeholder="Návštěvy střelnic, militárie, sociální sítě..." as="textarea" rows={3}/>
                  <FormItemRHF label="Trestná činnost se zbraněmi" name="weaponRelatedCriminalActivity" control={form.control} placeholder="Zaznamenané případy..." as="textarea" rows={3}/>
                  <FormItemRHF label="Vyhrožování zbraněmi" name="weaponThreats" control={form.control} placeholder="Případy vyhrožování..." as="textarea" rows={3}/>
                </CardContent>
              </Card>
            )}
            
            {/* Zbrojní licence */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Activity className="mr-2 h-5 w-5 text-primary/80"/>
                  Zbrojní Licence (pro firmy/spec. účely)
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 pt-4">
                <FormField
                  control={form.control}
                  name="hasGunLicensePermit"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center space-x-3 space-y-0 rounded-md border p-3 shadow-sm bg-card-foreground/5">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel className="font-normal">Vlastní zbrojní licenci (nikoli průkaz)</FormLabel>
                    </FormItem>
                  )}
                />
                {watchedHasGunLicensePermit && (
                  <div className="space-y-4 mt-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormItemRHF label="Číslo zbrojní licence" name="gunLicensePermitNumber" control={form.control} placeholder="Číslo ZL"/>
                      <FormItemRHF label="Skupiny zbrojní licence" name="gunLicensePermitGroups" control={form.control} placeholder="Např. A, G, J..."/>
                    </div>
                    <FormItemRHF label="Platnost licence do" name="gunLicensePermitValidUntil" control={form.control} type="date"/>
                    <FormItemRHF label="Informace o firemní licenci" name="businessLicenseInfo" control={form.control} placeholder="Účel licence, rozsah oprávnění..." as="textarea" rows={3}/>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Registrované zbraně */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Target className="mr-2 h-5 w-5 text-primary/80"/>
                  Registrované Zbraně
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 pt-4">
                {weaponFields.map((weapon, index) => (
                  <WeaponCard
                    key={weapon.id}
                    form={form}
                    weaponIndex={index}
                    onRemove={() => removeWeapon(index)}
                    generateStableId={generateStableId}
                    caseId={caseId}
                    subjectId={subject.id}
                    moduleId={moduleId}
                  />
                ))}
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => appendWeapon({ 
                    id: generateStableId(), 
                    photos: [], 
                    accessories: [], 
                    incidents: [] 
                  })} 
                  className="mt-2"
                >
                  <PlusCircle className="mr-2 h-4 w-4"/>
                  Přidat registrovanou zbraň
                </Button>
              </CardContent>
            </Card>

            {/* Střelecká činnost a výcvik */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Activity className="mr-2 h-5 w-5 text-primary/80"/>
                  Střelecká činnost a výcvik
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 pt-4">
                <FormItemRHF label="Členství ve střeleckých klubech" name="shootingClubMembership" control={form.control} placeholder="Názvy klubů, členské čísla..." as="textarea" rows={2}/>
                <FormItemRHF label="Střelecké soutěže" name="shootingCompetitions" control={form.control} placeholder="Účast na soutěžích, výsledky..." as="textarea" rows={2}/>
                <FormItemRHF label="Výcvik se zbraněmi" name="weaponTraining" control={form.control} placeholder="Absolvované kurzy, certifikace..." as="textarea" rows={2}/>
                <FormItemRHF label="Instruktorské certifikace" name="instructorCertifications" control={form.control} placeholder="Oprávnění k výuce..." as="textarea" rows={2}/>
                <FormItemRHF label="Činnost na střelnicích" name="rangeActivity" control={form.control} placeholder="Pravidelnost, oblíbené střelnice..." as="textarea" rows={2}/>
              </CardContent>
            </Card>

            {/* Sociální kontext a bezpečnost */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Users className="mr-2 h-5 w-5 text-primary/80"/>
                  Sociální kontext a bezpečnost
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 pt-4">
                <FormItemRHF label="Osoby se ZP v blízkém okolí" name="notesOnSurroundingPersons" control={form.control} placeholder="Rodinní příslušníci, osoby ve společné domácnosti..." as="textarea" rows={3}/>
                <FormItemRHF label="Přístup rodiny ke zbraním" name="familyWeaponAccess" control={form.control} placeholder="Kdo má přístup, znalost kombinací..." as="textarea" rows={2}/>
                <FormItemRHF label="Informace o nakládání se zbraněmi" name="weaponHandlingInfo" control={form.control} placeholder="Způsob uložení, pravidelnost používání, střelnice..." as="textarea" rows={3}/>
                <FormItemRHF label="Poznámky k přístupu ke zbraním" name="weaponAccessNotes" control={form.control} placeholder="Členství v klubech, soutěže, zájem o militárie..." as="textarea" rows={3}/>
                <FormItemRHF label="Způsob uložení zbraní" name="weaponStorageInfo" control={form.control} placeholder="Detaily o uložení a zabezpečení..." as="textarea" rows={2}/>
                <FormItemRHF label="Bezpečnostní opatření" name="securityMeasures" control={form.control} placeholder="Dodatečná zabezpečení, alarm..." as="textarea" rows={2}/>
              </CardContent>
            </Card>

            {/* OSINT a bezpečnostní hodnocení */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <Search className="mr-2 h-5 w-5 text-primary/80"/>
                  OSINT a bezpečnostní hodnocení
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 pt-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItemSelectRHF 
                    label="Zdroj informací" 
                    name="informationSource" 
                    control={form.control} 
                    options={informationSourceOptions} 
                    placeholder="-- Vyberte zdroj --"
                  />
                  <FormItemSelectRHF 
                    label="Spolehlivost informací" 
                    name="informationReliability" 
                    control={form.control} 
                    options={informationReliabilityOptions} 
                    placeholder="-- Vyberte spolehlivost --"
                  />
                </div>
                
                <FormItemRHF label="Detail zdroje" name="sourceDetail" control={form.control} placeholder="Konkrétní zdroj, link, kontakt..." />
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItemRHF label="Datum ověření" name="verificationDate" control={form.control} type="date" />
                  <FormItemRHF label="Naposledy ověřeno" name="lastVerified" control={form.control} type="date" />
                </div>
                
                <FormItemSelectRHF 
                  label="Úroveň rizika se zbraněmi" 
                  name="weaponRiskAssessment" 
                  control={form.control} 
                  options={threatLevelOptions} 
                  placeholder="-- Vyberte úroveň rizika --"
                />
                
                <FormItemRHF label="Poznámky k hodnocení rizika" name="riskAssessmentNotes" control={form.control} placeholder="Zdůvodnění hodnocení rizika..." as="textarea" rows={3}/>
                <FormItemRHF label="Poznámky ze sledování" name="surveillanceNotes" control={form.control} placeholder="Pozorování, chování..." as="textarea" rows={3}/>
                <FormItemRHF label="Priority vyšetřování" name="investigationPriorities" control={form.control} placeholder="Co je třeba prověřit přednostně..." as="textarea" rows={2}/>
              </CardContent>
            </Card>

            {/* Fotodokumentace modulu */}
            <PhotoDocumentationSection 
              form={form} 
              title="Fotodokumentace modulu"
              description="Obecné fotografie týkající se zbrojních průkazů (dokumenty, místa, osoby)"
              namePrefix="photos" 
              photoHint="gun license document"
              caseId={caseId}
              subjectId={subject.id}
              moduleId={moduleId}
            />

            {/* Poznámky */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <FileText className="mr-2 h-5 w-5 text-primary/80"/>
                  Poznámky a dodatečné informace
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 pt-4">
                <FormItemRHF label="Poznámky vyšetřovatele" name="investigationNotes" control={form.control} placeholder="Interní poznámky pro vyšetřování..." as="textarea" rows={4}/>
                <FormItemRHF label="Obecné poznámky" name="generalNotes" control={form.control} placeholder="Ostatní důležité informace..." as="textarea" rows={4}/>
              </CardContent>
            </Card>

          </CardContent>
        </Card>

        <div className="flex justify-end pt-8 mt-8 border-t border-border">
          <Button 
            type="submit" 
            disabled={isSaving} 
            className="w-full md:w-auto text-lg py-3 px-6 shadow-md"
            onClick={() => {
              console.log('🔫 Submit button clicked');
              console.log('🔫 Form state:', form.formState);
              console.log('🔫 Form values:', form.getValues());
            }}
          >
            {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Uložit data modulu Zbrojní průkaz
          </Button>
        </div>
      </form>
    </Form>
  );
}

// Komponenta pro jednotlivou zbraň
interface WeaponCardProps {
  form: UseFormReturn<GunLicenseFormValues>;
  weaponIndex: number;
  onRemove: () => void;
  generateStableId: () => string;
  caseId: string;
  subjectId: string;
  moduleId: string;
}

function WeaponCard({ form, weaponIndex, onRemove, generateStableId, caseId, subjectId, moduleId }: WeaponCardProps) {
  const { fields: photoFields, append: appendPhoto, remove: removePhoto } = useFieldArray({
    control: form.control,
    name: `registeredWeapons.${weaponIndex}.photos`,
  });

  const baseName = `registeredWeapons.${weaponIndex}`;

  return (
    <Card className="p-4 shadow-sm bg-card-foreground/5 relative">
      <Button 
        type="button" 
        variant="ghost" 
        size="icon" 
        onClick={onRemove} 
        className="absolute top-2 right-2 text-destructive hover:bg-destructive/10"
      >
        <Trash2 className="h-5 w-5"/>
      </Button>
      
      <CardHeader className="px-0 pt-0 pb-3">
        <CardTitle className="text-md">Zbraň {weaponIndex + 1}</CardTitle>
      </CardHeader>
      
      <CardContent className="px-0 pb-0 space-y-4">
        {/* Základní údaje */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <FormItemRHF label="Druh zbraně" name={`${baseName}.weaponType`} control={form.control} placeholder="Např. pistole samonabíjecí"/>
          <FormItemRHF label="Značka" name={`${baseName}.brand`} control={form.control} placeholder="Např. Glock"/>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <FormItemRHF label="Model" name={`${baseName}.model`} control={form.control} placeholder="Např. 19 Gen5"/>
          <FormItemRHF label="Výrobní číslo" name={`${baseName}.serialNumber`} control={form.control} placeholder="Sériové číslo"/>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <FormItemSelectRHF 
            label="Kategorie zbraně" 
            name={`${baseName}.category`} 
            control={form.control} 
            options={weaponCategoryOptions} 
            placeholder="-- Vyberte --"
          />
          <FormItemRHF label="Ráže" name={`${baseName}.caliber`} control={form.control} placeholder="Např. 9mm Luger, .223 Rem"/>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <FormItemRHF label="Rok výroby" name={`${baseName}.yearManufactured`} control={form.control} type="number" placeholder="Např. 2020"/>
          <FormItemRHF label="Země původu" name={`${baseName}.countryOfOrigin`} control={form.control} placeholder="Např. Rakousko"/>
        </div>

        <Separator />

        {/* Právní stav */}
        <h5 className="font-semibold text-sm">Právní stav</h5>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <FormItemSelectRHF 
            label="Právní stav zbraně" 
            name={`${baseName}.legalStatus`} 
            control={form.control} 
            options={weaponLegalStatusOptions} 
            placeholder="-- Vyberte --"
          />
          <FormItemSelectRHF 
            label="Stav zbraně" 
            name={`${baseName}.condition`} 
            control={form.control} 
            options={weaponConditionOptions} 
            placeholder="-- Vyberte --"
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <FormItemRHF label="Registrační číslo" name={`${baseName}.registrationNumber`} control={form.control} placeholder="Č. registrace"/>
          <FormItemRHF label="Datum registrace" name={`${baseName}.registrationDate`} control={form.control} type="date"/>
        </div>

        <FormItemRHF label="Registrační úřad" name={`${baseName}.registrationAuthority`} control={form.control} placeholder="Kdo zbraň registroval"/>
        <FormItemRHF label="Modifikace a úpravy" name={`${baseName}.modifications`} control={form.control} placeholder="Provedené úpravy, přestavby..." as="textarea" rows={2}/>
        <FormItemRHF label="Charakteristické znaky" name={`${baseName}.distinguishingMarks`} control={form.control} placeholder="Rysky, poškození, gravírování..." as="textarea" rows={2}/>

        <Separator />

        {/* Nabývání */}
        <h5 className="font-semibold text-sm">Nabývání a vlastnictví</h5>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <FormItemSelectRHF 
            label="Způsob nabytí" 
            name={`${baseName}.acquisitionMethod`} 
            control={form.control} 
            options={weaponAcquisitionOptions} 
            placeholder="-- Vyberte --"
          />
          <FormItemRHF label="Datum nabytí" name={`${baseName}.acquisitionDate`} control={form.control} type="date"/>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <FormItemRHF label="Zdroj nabytí" name={`${baseName}.acquisitionSource`} control={form.control} placeholder="Od koho/kde získána"/>
          <FormItemRHF label="Pořizovací cena (Kč)" name={`${baseName}.purchasePrice`} control={form.control} type="number" placeholder="0"/>
        </div>
        
        <FormItemRHF label="Současné umístění" name={`${baseName}.currentLocation`} control={form.control} placeholder="Kde se zbraň aktuálně nachází"/>

        <Separator />

        {/* Použití a uložení */}
        <h5 className="font-semibold text-sm">Použití a uložení</h5>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <FormItemSelectRHF 
            label="Kontext použití" 
            name={`${baseName}.usageContext`} 
            control={form.control} 
            options={weaponUsageOptions} 
            placeholder="-- Vyberte --"
          />
          <FormItemSelectRHF 
            label="Způsob uložení" 
            name={`${baseName}.storageMethod`} 
            control={form.control} 
            options={weaponStorageOptions} 
            placeholder="-- Vyberte --"
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <FormItemRHF label="Frekvence používání" name={`${baseName}.usageFrequency`} control={form.control} placeholder="Jak často používaná"/>
          <FormItemRHF label="Naposledy použita" name={`${baseName}.lastUsed`} control={form.control} type="date"/>
        </div>
        
        <FormItemRHF label="Místo uložení" name={`${baseName}.storageLocation`} control={form.control} placeholder="Přesné místo uložení"/>

        <Separator />

        {/* Munice */}
        <h5 className="font-semibold text-sm">Munice</h5>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <FormItemRHF label="Typ munice" name={`${baseName}.ammunitionType`} control={form.control} placeholder="Typ a označení munice"/>
          <FormItemRHF label="Množství munice (ks)" name={`${baseName}.ammunitionQuantity`} control={form.control} type="number" placeholder="0"/>
        </div>

        <Separator />

        {/* Bezpečnost */}
        <h5 className="font-semibold text-sm">Bezpečnostní informace</h5>
        <FormItemRHF label="Bezpečnostní obavy" name={`${baseName}.securityConcerns`} control={form.control} placeholder="Rizika spojená s touto zbraní..." as="textarea" rows={2}/>
        <FormItemRHF label="Zprávy o zneužití" name={`${baseName}.misusageReports`} control={form.control} placeholder="Případy nesprávného použití..." as="textarea" rows={2}/>

        <Separator />

        {/* OSINT informace */}
        <h5 className="font-semibold text-sm">OSINT informace</h5>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <FormItemSelectRHF 
            label="Zdroj informací" 
            name={`${baseName}.informationSource`} 
            control={form.control} 
            options={informationSourceOptions} 
            placeholder="-- Vyberte --"
          />
          <FormItemSelectRHF 
            label="Spolehlivost" 
            name={`${baseName}.informationReliability`} 
            control={form.control} 
            options={informationReliabilityOptions} 
            placeholder="-- Vyberte --"
          />
        </div>
        
        <FormItemRHF label="Detail zdroje" name={`${baseName}.sourceDetail`} control={form.control} placeholder="Konkrétní zdroj informace"/>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <FormItemRHF label="Datum ověření" name={`${baseName}.verificationDate`} control={form.control} type="date"/>
          <FormItemRHF label="Naposledy ověřeno" name={`${baseName}.lastVerified`} control={form.control} type="date"/>
        </div>

        <Separator />

        {/* Fotografie zbraně */}
        <PhotoDocumentationSection 
          form={form} 
          title="Fotografie zbraně"
          description="Fotografie této konkrétní zbraně"
          namePrefix={`${baseName}.photos`} 
          photoHint="weapon firearm gun"
          caseId={caseId}
          subjectId={subjectId}
          moduleId={moduleId}
        />
        
        <FormItemRHF label="Poznámky ke zbrani" name={`${baseName}.notes`} control={form.control} as="textarea" rows={3} placeholder="Doplňující informace ke zbrani"/>
      </CardContent>
    </Card>
  );
}

// Pomocné komponenty pro formulář
interface FormItemRHFProps {
  label: string; 
  name: string; 
  control: any; 
  placeholder?: string;
  type?: string; 
  disabled?: boolean; 
  className?: string;
  as?: 'input' | 'textarea'; 
  rows?: number; 
  smallLabel?: boolean;
}

const FormItemRHF = ({ 
  label, 
  name, 
  control, 
  placeholder, 
  type = "text", 
  disabled = false, 
  className, 
  as = 'input', 
  rows, 
  smallLabel 
}: FormItemRHFProps) => (
  <FormField
    control={control} 
    name={name as any} 
    disabled={disabled}
    render={({ field, fieldState: { error } }) => (
      <FormItem className={cn("w-full", className)}>
        <FormLabel className={cn("font-semibold", smallLabel ? "text-xs" : "text-sm")}>
          {label}
        </FormLabel>
        <FormControl>
          {as === 'input' ? (
            <Input 
              {...field} 
              value={field.value || ''} 
              placeholder={placeholder} 
              type={type} 
              className={cn(
                smallLabel ? "text-xs h-8 py-1" : "text-sm h-10", 
                error ? "border-destructive" : ""
              )} 
            />
          ) : (
            <Textarea 
              {...field} 
              value={field.value || ''} 
              placeholder={placeholder} 
              rows={rows} 
              className={cn(
                smallLabel ? "text-xs py-1" : "text-sm", 
                error ? "border-destructive" : ""
              )} 
            />
          )}
        </FormControl>
        {error && <FormMessage className="text-xs" />}
      </FormItem>
    )}
  />
);

interface FormItemSelectRHFProps {
  label: string; 
  name: string; 
  control: any; 
  placeholder?: string;
  options: {value: string; label: string}[];
  disabled?: boolean; 
  className?: string; 
  smallLabel?: boolean;
}

const FormItemSelectRHF = ({ 
  label, 
  name, 
  control, 
  placeholder, 
  options, 
  disabled = false, 
  className, 
  smallLabel 
}: FormItemSelectRHFProps) => (
  <FormField
    control={control} 
    name={name as any} 
    disabled={disabled}
    render={({ field, fieldState: { error } }) => (
      <FormItem className={cn("w-full", className)}>
        <FormLabel className={cn("font-semibold", smallLabel ? "text-xs" : "text-sm")}>
          {label}
        </FormLabel>
        <Select onValueChange={field.onChange} value={field.value || undefined}>
          <FormControl>
            <SelectTrigger className={cn(
              smallLabel ? "text-xs h-8 py-1" : "text-sm h-10", 
              error ? "border-destructive" : ""
            )}>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
          </FormControl>
          <SelectContent>
            {options.map(opt => (
              <SelectItem key={opt.value} value={opt.value}>
                {opt.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {error && <FormMessage className="text-xs" />}
      </FormItem>
    )}
  />
);
