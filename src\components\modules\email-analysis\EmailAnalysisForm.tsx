"use client";

import { useState } from "react";
import { use<PERSON><PERSON>, SubmitHandler } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { v4 as uuidv4 } from "uuid";
import { doc, setDoc, serverTimestamp } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { EmailAnalysisModuleFormValues, emailAnalysisModuleSchema } from "./schemas";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, EmailAnalysisModuleData, Subject } from "@/types";
import { EmailsList } from "./EmailsList";
import { EmailDetail } from "./EmailDetail";
import { EmailConnectionsGraph } from "./EmailConnectionsGraph";
import { Mail, Save, Plus, ArrowLeft, Network } from "lucide-react";

interface EmailAnalysisFormProps {
  caseId: string;
  subject: Subject;
  existingData: EmailAnalysisModuleData | null;
  onSave: (moduleId: string, wasNew: boolean) => void;
}

export function EmailAnalysisForm({ caseId, subject, existingData, onSave }: EmailAnalysisFormProps) {
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [selectedEmailId, setSelectedEmailId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>("list");

  const moduleId = "email_analysis";

  const form = useForm<EmailAnalysisModuleFormValues>({
    resolver: zodResolver(emailAnalysisModuleSchema),
    defaultValues: {
      emails: existingData?.emails || [],
      generalNotes: existingData?.generalNotes || "",
    },
  });

  const { control, handleSubmit, watch, setValue, getValues, formState } = form;
  const { isDirty } = formState;

  const emails = watch("emails");

  const handleAddEmail = () => {
    const newEmail: EmailRecord = {
      id: uuidv4(),
      emailAddress: "",
      source: "osint",
      verificationStatus: "unknown",
      dataBreaches: [],
      connectedProfiles: [],
      headersAnalysis: {
        emailRoute: [],
      },
      photos: [],
    };

    setValue("emails", [...emails, newEmail], { shouldDirty: true });
    setSelectedEmailId(newEmail.id);
    setActiveTab("detail");
  };

  const handleEditEmail = (id: string) => {
    setSelectedEmailId(id);
    setActiveTab("detail");
  };

  const handleDeleteEmail = (id: string) => {
    setValue(
      "emails",
      emails.filter((email) => email.id !== id),
      { shouldDirty: true }
    );

    if (selectedEmailId === id) {
      setSelectedEmailId(null);
      setActiveTab("list");
    }
  };

  const handleBackToList = () => {
    setSelectedEmailId(null);
    setActiveTab("list");
  };

  const onSubmitHandler: SubmitHandler<EmailAnalysisModuleFormValues> = async (data) => {
    setIsSaving(true);
    try {
      const moduleDocRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);
      const dataToSave: EmailAnalysisModuleData = {
        ...data,
        subjectId: subject.id,
        lastUpdatedAt: serverTimestamp(),
        createdAt: existingData?.createdAt || serverTimestamp(),
      };
      await setDoc(moduleDocRef, dataToSave, { merge: true });
      toast({ title: "Data modulu Emailová analýza uložena." });
      const wasNew = !existingData || !existingData.createdAt;
      onSave(moduleId, wasNew);
    } catch (error: any) {
      toast({
        title: "Chyba ukládání dat modulu",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const selectedEmail = emails.find((email) => email.id === selectedEmailId);

  return (
    <Form {...form}>
      <form onSubmit={handleSubmit(onSubmitHandler)}>
        <div className="space-y-4 pb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Mail className="h-6 w-6 text-primary" />
              <h2 className="text-2xl font-bold">Emailová analýza</h2>
            </div>
            <Button type="submit" disabled={!isDirty || isSaving}>
              {isSaving ? (
                <>Ukládám...</>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Uložit
                </>
              )}
            </Button>
          </div>

          <Separator />

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="mb-4">
              <TabsTrigger value="list">Seznam emailů</TabsTrigger>
              <TabsTrigger value="detail" disabled={!selectedEmailId}>
                Detail emailu
              </TabsTrigger>
              <TabsTrigger value="connections">
                <Network className="h-4 w-4 mr-2" />
                Vizualizace propojení
              </TabsTrigger>
              <TabsTrigger value="notes">Poznámky</TabsTrigger>
            </TabsList>

            <TabsContent value="list" className="space-y-4">
              <div className="flex justify-end">
                <Button type="button" onClick={handleAddEmail}>
                  <Plus className="mr-2 h-4 w-4" />
                  Přidat email
                </Button>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Seznam emailových adres</CardTitle>
                  <CardDescription>
                    Přehled všech emailových adres subjektu
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <EmailsList
                    emails={emails}
                    onEdit={handleEditEmail}
                    onDelete={handleDeleteEmail}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="detail" className="space-y-4">
              {selectedEmail && (
                <>
                  <div className="flex items-center space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleBackToList}
                    >
                      <ArrowLeft className="mr-2 h-4 w-4" />
                      Zpět na seznam
                    </Button>
                  </div>

                  <EmailDetail
                    control={control}
                    emailIndex={emails.findIndex(
                      (e) => e.id === selectedEmailId
                    )}
                  />
                </>
              )}
            </TabsContent>

            <TabsContent value="connections">
              <EmailConnectionsGraph
                emails={emails}
                subjectName={subject.name || "Subjekt"}
                subjectId={subject.id}
                caseId={caseId}
              />
            </TabsContent>

            <TabsContent value="notes">
              <Card>
                <CardHeader>
                  <CardTitle>Obecné poznámky</CardTitle>
                  <CardDescription>
                    Další informace o emailových adresách subjektu
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Textarea
                    {...form.register("generalNotes")}
                    placeholder="Zadejte obecné poznámky k modulu..."
                    className="min-h-[200px]"
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </form>
    </Form>
  );
}
