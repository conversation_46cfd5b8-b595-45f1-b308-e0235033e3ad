/* Styly pro galerii fotografií s popisky */

/* Kontejner pro galerii fotografií */
.photo-gallery-container {
    width: 100%;
    margin-bottom: 15px;
}

/* Galerie fotografií */
.photo-gallery {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 10px;
    border: 1px dashed #ccc;
    border-radius: 5px;
    background-color: #f9f9f9;
    min-height: 100px;
}

/* Stav p<PERSON>i p<PERSON>án<PERSON> souboru */
.photo-gallery.dragover {
    background-color: #e9f7fe;
    border-color: #3498db;
}

/* Položka v galerii */
.photo-gallery-item {
    position: relative;
    width: 150px;
    border-radius: 5px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    background-color: #fff;
    display: flex;
    flex-direction: column;
}

/* Obrázek v položce galerie */
.photo-gallery-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

/* Kontejner pro popisek přímo pod fotografií */
.photo-caption-container {
    padding: 5px;
    background-color: #f9f9f9;
    border-top: 1px solid #eee;
    width: 100%;
}

/* Textové pole pro popisek */
.photo-caption {
    width: 100%;
    min-height: 40px;
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 3px;
    resize: vertical;
    font-size: 12px;
    font-family: inherit;
}

/* Zaměřené textové pole pro popisek */
.photo-caption:focus {
    border-color: #3498db;
    outline: none;
    box-shadow: 0 0 3px rgba(52, 152, 219, 0.5);
}

/* Ovládací prvky položky galerie */
.photo-gallery-controls {
    position: absolute;
    top: 5px;
    right: 5px;
    display: flex;
    gap: 5px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

/* Zobrazení ovládacích prvků při najetí myší */
.photo-gallery-item:hover .photo-gallery-controls {
    opacity: 1;
}

/* Tlačítka v ovládacích prvcích */
.photo-gallery-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.8);
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    transition: background-color 0.2s ease;
}

/* Tlačítko pro smazání fotografie */
.photo-gallery-btn.remove {
    background-color: rgba(231, 76, 60, 0.8);
    color: white;
}

/* Hover efekt pro tlačítka */
.photo-gallery-btn:hover {
    background-color: rgba(255, 255, 255, 1);
}

.photo-gallery-btn.remove:hover {
    background-color: rgba(231, 76, 60, 1);
}

/* Tlačítko pro přidání fotografie */
.photo-gallery-add {
    width: 150px;
    height: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 2px dashed #ccc;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: #fff;
}

.photo-gallery-add:hover {
    border-color: #3498db;
    background-color: #f0f8ff;
}

.photo-gallery-add i {
    font-size: 24px;
    color: #3498db;
    margin-bottom: 10px;
}

.photo-gallery-add p {
    margin: 0;
    font-size: 14px;
    color: #666;
    text-align: center;
}

/* Kontejner pro popisky fotografií */
.photo-captions-container {
    margin-top: 10px;
}

/* Položka popisku */
.photo-caption-item {
    display: flex;
    margin-bottom: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px;
    background-color: #f9f9f9;
}

/* Miniatura fotografie v popisku */
.photo-caption-thumbnail {
    width: 60px;
    height: 60px;
    margin-right: 10px;
    flex-shrink: 0;
}

.photo-caption-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

/* Textové pole pro popisek */
.photo-caption-text {
    flex-grow: 1;
    resize: vertical;
    min-height: 60px;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Responzivní úpravy */
@media (max-width: 768px) {
    .photo-gallery-item {
        width: 120px;
    }

    .photo-gallery-item img {
        height: 120px;
    }

    .photo-gallery-add {
        width: 120px;
        height: 120px;
    }

    .photo-caption {
        min-height: 30px;
        font-size: 11px;
    }

    .photo-gallery-btn {
        width: 25px;
        height: 25px;
    }
}
