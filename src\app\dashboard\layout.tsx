
"use client"; // Required for hooks like use<PERSON><PERSON>, useRouter, useEffect

import { useEffect } from 'react';
import { AppHeader } from "@/components/dashboard/AppHeader";
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import type { Metadata } from 'next'; // Metadata can't be exported from client component

// Cannot export metadata from client component
// export const metadata: Metadata = {
//   title: 'Dashboard | P&R Solutions OSINT',
//   description: 'Hlavní panel analytické platformy P&R Solutions OSINT.',
// };

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    // Set document title dynamically since metadata can't be exported
    if (typeof window !== "undefined") {
      document.title = 'Dashboard | P&R Solutions OSINT';
    }

    if (!loading && !user) {
      router.replace('/login'); // Use replace to prevent going back to dashboard via browser back button
    }
  }, [user, loading, router]);

  if (loading) {
    // You can render a loading spinner or a blank page while auth state is being determined
    return (
      <div className="flex min-h-screen flex-col items-center justify-center">
        <p>Načítání...</p>
      </div>
    );
  }

  if (!user) {
    // This case should ideally be handled by the redirect,
    // but as a fallback, you might render null or a message.
    // Or, if redirect happens fast enough, this might not even be visible.
    return null; 
  }

  return (
    <div className="flex min-h-screen flex-col">
      <AppHeader />
      <main className="flex-1 bg-background">{children}</main> {/* Ensure background color consistency */}
    </div>
  );
}
