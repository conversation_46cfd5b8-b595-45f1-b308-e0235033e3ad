"use client";

import { useState, useEffect, useRef } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardT<PERSON>le, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { 
  Mail, Network, User, Building, MapPin, Phone, 
  Link, Filter, ZoomIn, ZoomOut, RefreshCw, 
  Download, Share2, Info, Search, AlertCircle
} from "lucide-react";
import { EmailRecord, ConnectedProfile } from "@/types";

interface EmailConnectionsGraphProps {
  emails: EmailRecord[];
  subjectName?: string;
  subjectId: string;
  caseId: string;
}

interface GraphNode {
  id: string;
  label: string;
  type: "email" | "profile" | "subject" | "organization" | "location" | "phone";
  data?: any;
}

interface GraphLink {
  source: string;
  target: string;
  label?: string;
  strength?: number;
}

interface GraphData {
  nodes: GraphNode[];
  links: GraphLink[];
}

export function EmailConnectionsGraph({ emails, subjectName = "Subjekt", subjectId, caseId }: EmailConnectionsGraphProps) {
  const [graphData, setGraphData] = useState<GraphData>({ nodes: [], links: [] });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedNode, setSelectedNode] = useState<GraphNode | null>(null);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [filterOptions, setFilterOptions] = useState({
    showEmails: true,
    showProfiles: true,
    showOrganizations: true,
    showLocations: false,
    showPhones: false,
    minConnectionStrength: "weak" // weak, medium, strong
  });
  
  const canvasRef = useRef<HTMLDivElement>(null);
  
  // Simulace načítání dat pro graf
  useEffect(() => {
    const loadGraphData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // Simulace zpoždění načítání dat
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Vytvoření uzlů a spojení pro graf
        const nodes: GraphNode[] = [];
        const links: GraphLink[] = [];
        
        // Přidání uzlu pro hlavní subjekt
        nodes.push({
          id: subjectId,
          label: subjectName,
          type: "subject"
        });
        
        // Přidání uzlů pro emaily
        emails.forEach(email => {
          nodes.push({
            id: email.id,
            label: email.emailAddress,
            type: "email",
            data: email
          });
          
          // Propojení emailu se subjektem
          links.push({
            source: subjectId,
            target: email.id,
            label: "vlastní",
            strength: 1
          });
          
          // Přidání uzlů pro organizace spojené s emailem
          if (email.associatedOrganization) {
            const orgId = `org_${email.associatedOrganization.replace(/\s+/g, '_')}`;
            
            // Kontrola, zda organizace již existuje v grafu
            if (!nodes.some(n => n.id === orgId)) {
              nodes.push({
                id: orgId,
                label: email.associatedOrganization,
                type: "organization"
              });
            }
            
            // Propojení emailu s organizací
            links.push({
              source: email.id,
              target: orgId,
              label: "patří",
              strength: 0.7
            });
          }
          
          // Přidání uzlů pro propojené profily
          if (email.connectedProfiles && email.connectedProfiles.length > 0) {
            email.connectedProfiles.forEach(profile => {
              const profileId = `profile_${profile.id}`;
              
              nodes.push({
                id: profileId,
                label: `${profile.username} (${profile.platformName})`,
                type: "profile",
                data: profile
              });
              
              // Propojení emailu s profilem
              links.push({
                source: email.id,
                target: profileId,
                label: "propojeno",
                strength: profile.verificationStatus === "yes" ? 0.9 : 0.5
              });
            });
          }
        });
        
        // Simulace dalších propojení mezi emaily (pro demonstraci)
        if (emails.length >= 2) {
          for (let i = 0; i < emails.length - 1; i++) {
            links.push({
              source: emails[i].id,
              target: emails[i + 1].id,
              label: "souvisí",
              strength: 0.3
            });
          }
        }
        
        setGraphData({ nodes, links });
      } catch (error: any) {
        console.error("Error loading graph data:", error);
        setError(error.message || "Chyba při načítání dat grafu");
      } finally {
        setIsLoading(false);
      }
    };
    
    loadGraphData();
  }, [emails, subjectId, subjectName]);
  
  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.2, 2));
  };
  
  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 0.2, 0.5));
  };
  
  const handleResetZoom = () => {
    setZoomLevel(1);
  };
  
  const handleNodeClick = (node: GraphNode) => {
    setSelectedNode(node);
  };
  
  const handleFilterChange = (key: keyof typeof filterOptions, value: any) => {
    setFilterOptions(prev => ({ ...prev, [key]: value }));
  };
  
  const getFilteredGraphData = (): GraphData => {
    const { showEmails, showProfiles, showOrganizations, showLocations, showPhones, minConnectionStrength } = filterOptions;
    
    // Filtrování uzlů podle typu
    const filteredNodes = graphData.nodes.filter(node => {
      if (node.type === "email" && !showEmails) return false;
      if (node.type === "profile" && !showProfiles) return false;
      if (node.type === "organization" && !showOrganizations) return false;
      if (node.type === "location" && !showLocations) return false;
      if (node.type === "phone" && !showPhones) return false;
      return true;
    });
    
    // Získání ID filtrovaných uzlů
    const filteredNodeIds = new Set(filteredNodes.map(node => node.id));
    
    // Filtrování spojení podle síly a existence uzlů
    const strengthThreshold = minConnectionStrength === "strong" ? 0.7 : minConnectionStrength === "medium" ? 0.4 : 0;
    const filteredLinks = graphData.links.filter(link => {
      if (!filteredNodeIds.has(link.source) || !filteredNodeIds.has(link.target)) return false;
      if ((link.strength || 0) < strengthThreshold) return false;
      return true;
    });
    
    return { nodes: filteredNodes, links: filteredLinks };
  };
  
  const getNodeColor = (type: string): string => {
    switch (type) {
      case "email":
        return "#3b82f6"; // blue-500
      case "profile":
        return "#10b981"; // emerald-500
      case "subject":
        return "#f97316"; // orange-500
      case "organization":
        return "#8b5cf6"; // violet-500
      case "location":
        return "#ef4444"; // red-500
      case "phone":
        return "#f59e0b"; // amber-500
      default:
        return "#6b7280"; // gray-500
    }
  };
  
  const getNodeTypeLabel = (type: string): string => {
    switch (type) {
      case "email":
        return "Email";
      case "profile":
        return "Profil";
      case "subject":
        return "Subjekt";
      case "organization":
        return "Organizace";
      case "location":
        return "Lokace";
      case "phone":
        return "Telefon";
      default:
        return type;
    }
  };
  
  // Simulace vykreslení grafu - v reálné implementaci by zde byl skutečný graf
  // s využitím knihovny jako D3.js, Vis.js nebo React Flow
  const renderGraph = () => {
    const filteredData = getFilteredGraphData();
    
    return (
      <div 
        ref={canvasRef} 
        className="w-full h-[400px] bg-slate-50 rounded-md border p-4 flex items-center justify-center"
        style={{ transform: `scale(${zoomLevel})` }}
      >
        {isLoading ? (
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin text-primary mx-auto mb-2" />
            <p>Načítání grafu propojení...</p>
          </div>
        ) : filteredData.nodes.length === 0 ? (
          <div className="text-center">
            <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p>Žádná data k zobrazení s aktuálními filtry</p>
          </div>
        ) : (
          <div className="text-center">
            <Network className="h-16 w-16 text-primary mx-auto mb-4" />
            <p className="text-lg font-medium">Graf propojení</p>
            <p className="text-sm text-muted-foreground mt-1">
              {filteredData.nodes.length} uzlů, {filteredData.links.length} propojení
            </p>
            <p className="text-xs text-muted-foreground mt-4">
              Toto je simulace grafu. V reálné implementaci by zde byl interaktivní graf.
            </p>
          </div>
        )}
      </div>
    );
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Network className="mr-2 h-5 w-5 text-primary" />
          Vizualizace propojení
        </CardTitle>
        <CardDescription>
          Vizualizace propojení mezi emailovými adresami a dalšími entitami
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="w-full md:w-3/4">
            <div className="flex justify-between items-center mb-2">
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="icon" onClick={handleZoomIn}>
                  <ZoomIn className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="icon" onClick={handleZoomOut}>
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="icon" onClick={handleResetZoom}>
                  <RefreshCw className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-1" />
                  Export
                </Button>
                <Button variant="outline" size="sm">
                  <Share2 className="h-4 w-4 mr-1" />
                  Sdílet
                </Button>
              </div>
            </div>
            
            {renderGraph()}
            
            <div className="flex flex-wrap gap-2 mt-2">
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                <div className="w-2 h-2 rounded-full bg-blue-500 mr-1"></div> Email
              </Badge>
              <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200">
                <div className="w-2 h-2 rounded-full bg-emerald-500 mr-1"></div> Profil
              </Badge>
              <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                <div className="w-2 h-2 rounded-full bg-orange-500 mr-1"></div> Subjekt
              </Badge>
              <Badge variant="outline" className="bg-violet-50 text-violet-700 border-violet-200">
                <div className="w-2 h-2 rounded-full bg-violet-500 mr-1"></div> Organizace
              </Badge>
              <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                <div className="w-2 h-2 rounded-full bg-red-500 mr-1"></div> Lokace
              </Badge>
              <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                <div className="w-2 h-2 rounded-full bg-amber-500 mr-1"></div> Telefon
              </Badge>
            </div>
          </div>
          
          <div className="w-full md:w-1/4 space-y-4">
            <div className="space-y-2">
              <h3 className="text-sm font-medium flex items-center">
                <Filter className="h-4 w-4 mr-1" />
                Filtry
              </h3>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-emails" className="text-xs">Emaily</Label>
                  <Switch
                    id="show-emails"
                    checked={filterOptions.showEmails}
                    onCheckedChange={(checked) => handleFilterChange("showEmails", checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-profiles" className="text-xs">Profily</Label>
                  <Switch
                    id="show-profiles"
                    checked={filterOptions.showProfiles}
                    onCheckedChange={(checked) => handleFilterChange("showProfiles", checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-organizations" className="text-xs">Organizace</Label>
                  <Switch
                    id="show-organizations"
                    checked={filterOptions.showOrganizations}
                    onCheckedChange={(checked) => handleFilterChange("showOrganizations", checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-locations" className="text-xs">Lokace</Label>
                  <Switch
                    id="show-locations"
                    checked={filterOptions.showLocations}
                    onCheckedChange={(checked) => handleFilterChange("showLocations", checked)}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-phones" className="text-xs">Telefony</Label>
                  <Switch
                    id="show-phones"
                    checked={filterOptions.showPhones}
                    onCheckedChange={(checked) => handleFilterChange("showPhones", checked)}
                  />
                </div>
              </div>
              
              <div className="pt-2">
                <Label htmlFor="connection-strength" className="text-xs">Síla propojení</Label>
                <Select
                  value={filterOptions.minConnectionStrength}
                  onValueChange={(value) => handleFilterChange("minConnectionStrength", value)}
                >
                  <SelectTrigger id="connection-strength" className="mt-1">
                    <SelectValue placeholder="Vyberte sílu propojení" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="weak">Všechna propojení</SelectItem>
                    <SelectItem value="medium">Střední a silná</SelectItem>
                    <SelectItem value="strong">Pouze silná</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <h3 className="text-sm font-medium flex items-center">
                <Info className="h-4 w-4 mr-1" />
                Detail uzlu
              </h3>
              
              {selectedNode ? (
                <div className="space-y-2">
                  <div className="flex items-center">
                    <div 
                      className="w-3 h-3 rounded-full mr-2" 
                      style={{ backgroundColor: getNodeColor(selectedNode.type) }}
                    ></div>
                    <Badge variant="outline">
                      {getNodeTypeLabel(selectedNode.type)}
                    </Badge>
                  </div>
                  
                  <p className="text-sm font-medium">{selectedNode.label}</p>
                  
                  {selectedNode.type === "email" && selectedNode.data && (
                    <div className="text-xs space-y-1">
                      <p>Zdroj: {selectedNode.data.source}</p>
                      {selectedNode.data.associatedOrganization && (
                        <p>Organizace: {selectedNode.data.associatedOrganization}</p>
                      )}
                      <p>Propojené profily: {selectedNode.data.connectedProfiles?.length || 0}</p>
                    </div>
                  )}
                  
                  {selectedNode.type === "profile" && selectedNode.data && (
                    <div className="text-xs space-y-1">
                      <p>Platforma: {selectedNode.data.platformName}</p>
                      <p>Uživatelské jméno: {selectedNode.data.username}</p>
                      {selectedNode.data.profileUrl && (
                        <p className="truncate">URL: {selectedNode.data.profileUrl}</p>
                      )}
                    </div>
                  )}
                  
                  <Button variant="outline" size="sm" className="w-full mt-2">
                    <Search className="h-3 w-3 mr-1" />
                    Zobrazit detail
                  </Button>
                </div>
              ) : (
                <div className="text-center py-4">
                  <p className="text-sm text-muted-foreground">
                    Klikněte na uzel v grafu pro zobrazení detailu
                  </p>
                </div>
              )}
            </div>
            
            <Separator />
            
            <div className="space-y-2">
              <h3 className="text-sm font-medium">Statistika</h3>
              <div className="grid grid-cols-2 gap-2">
                <div className="bg-slate-50 p-2 rounded-md">
                  <p className="text-xs text-muted-foreground">Emaily</p>
                  <p className="text-lg font-bold">{graphData.nodes.filter(n => n.type === "email").length}</p>
                </div>
                <div className="bg-slate-50 p-2 rounded-md">
                  <p className="text-xs text-muted-foreground">Profily</p>
                  <p className="text-lg font-bold">{graphData.nodes.filter(n => n.type === "profile").length}</p>
                </div>
                <div className="bg-slate-50 p-2 rounded-md">
                  <p className="text-xs text-muted-foreground">Organizace</p>
                  <p className="text-lg font-bold">{graphData.nodes.filter(n => n.type === "organization").length}</p>
                </div>
                <div className="bg-slate-50 p-2 rounded-md">
                  <p className="text-xs text-muted-foreground">Propojení</p>
                  <p className="text-lg font-bold">{graphData.links.length}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <p className="text-xs text-muted-foreground">
          Data aktualizována: {new Date().toLocaleString('cs-CZ')}
        </p>
        <Button variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-1" />
          Aktualizovat
        </Button>
      </CardFooter>
    </Card>
  );
}
