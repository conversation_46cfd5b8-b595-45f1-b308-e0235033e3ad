"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { NetworkAnalysisModuleData, Subject } from "@/types";
import { NetworkAnalysisForm } from "./NetworkAnalysisForm";
import { saveNetworkAnalysisData } from "@/lib/firebase/firestore";
import { Loader2, ArrowLeft } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";

interface NetworkAnalysisModuleProps {
  caseId: string;
  subject: Subject;
  initialData?: NetworkAnalysisModuleData | null;
}

export function NetworkAnalysisModule({
  caseId,
  subject,
  initialData,
}: NetworkAnalysisModuleProps) {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleBack = () => {
    router.push(`/cases/${caseId}/subjects/${subject.id}`);
  };

  const handleSave = async (data: any) => {
    setIsLoading(true);
    try {
      await saveNetworkAnalysisData(caseId, subject.id, data);
      toast({
        title: "Úspěch",
        description: "Data byla úspěšně uložena",
        variant: "default",
      });
      router.refresh();
    } catch (error) {
      console.error("Error saving network analysis data:", error);
      toast({
        title: "Chyba",
        description: "Chyba při ukládání dat",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[calc(100vh-200px)]">
        <div className="flex flex-col items-center gap-2">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-muted-foreground">Načítání...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center">
        <Button
          type="button"
          variant="ghost"
          size="icon"
          onClick={handleBack}
          className="mr-2"
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <div>
          <h2 className="text-2xl font-bold">IP adresy a síťová analýza</h2>
          <p className="text-muted-foreground">
            {subject.type === "physical"
              ? `${subject.firstName} ${subject.lastName}`
              : subject.name}
          </p>
        </div>
      </div>

      <NetworkAnalysisForm
        caseId={caseId}
        subject={subject}
        initialData={initialData}
        onSave={handleSave}
      />
    </div>
  );
}
