/**
 * <PERSON><PERSON>r<PERSON><PERSON><PERSON> modulů pro OSINT systém
 * Tento soubor obsahuje funkce pro správu modulů v OSINT systému.
 */

// Pole dostupných modulů
const availableModules = [
    {
        id: 'email-analysis',
        name: '<PERSON>l<PERSON>za e-mailů',
        description: 'Modul pro analýzu e-mailových adres, včetně ověření existence, vyhledávání v únicích dat a vizualizace.',
        icon: 'fa-envelope',
        category: 'analysis',
        jsFile: 'email-analysis.js',
        cssFile: 'email-analysis-styles.css',
        enabled: true
    },
    {
        id: 'phone-analysis',
        name: 'Analýza telefonních čísel',
        description: 'Modul pro analýzu telefonních čísel, včetně ověření platnosti, geolokace a vyhledávání v databázích.',
        icon: 'fa-phone',
        category: 'analysis',
        jsFile: 'phone-analysis.js',
        cssFile: 'phone-analysis-styles.css',
        enabled: true
    },
    {
        id: 'ip-analysis',
        name: 'Analýza IP/sítí',
        description: 'Modul pro analýzu IP adres a sítí, včetně geolokace, whois informací a skenování portů.',
        icon: 'fa-network-wired',
        category: 'analysis',
        jsFile: 'ip-analysis.js',
        cssFile: 'ip-analysis-styles.css',
        enabled: true
    },
    {
        id: 'darkweb-monitoring',
        name: 'Monitoring darkwebu',
        description: 'Modul pro monitoring darkwebu, včetně vyhledávání na darkwebových fórech a tržištích.',
        icon: 'fa-user-secret',
        category: 'monitoring',
        jsFile: 'darkweb-monitoring.js',
        cssFile: 'darkweb-monitoring-styles.css',
        enabled: true
    },
    {
        id: 'photo-analysis',
        name: 'Analýza fotografií',
        description: 'Modul pro analýzu fotografií, včetně extrakce metadat, rozpoznávání obličejů a vyhledávání podobných obrázků.',
        icon: 'fa-image',
        category: 'analysis',
        jsFile: 'photo-analysis.js',
        cssFile: 'photo-analysis-styles.css',
        enabled: true
    },
    {
        id: 'financial-monitoring',
        name: 'Finanční monitoring',
        description: 'Modul pro monitoring finančních transakcí, včetně sledování bankovních účtů a kryptoměnových adres.',
        icon: 'fa-money-bill-wave',
        category: 'monitoring',
        jsFile: 'financial-monitoring.js',
        cssFile: 'financial-monitoring-styles.css',
        enabled: true
    },
    {
        id: 'map-overlays',
        name: 'Mapové překryvy',
        description: 'Modul pro zobrazení dat na mapě, včetně geolokace osob, událostí a objektů.',
        icon: 'fa-map-marked-alt',
        category: 'visualization',
        jsFile: 'map-overlays.js',
        cssFile: 'map-overlays-styles.css',
        enabled: true
    },
    {
        id: 'automated-crawler',
        name: 'Automatizovaný crawler',
        description: 'Modul pro automatizované procházení webových stránek a extrakci dat.',
        icon: 'fa-spider',
        category: 'collection',
        jsFile: 'automated-crawler.js',
        cssFile: 'automated-crawler-styles.css',
        enabled: true
    },
    {
        id: 'news-monitoring',
        name: 'Monitoring zpráv',
        description: 'Modul pro monitoring zpravodajských webů a sociálních médií.',
        icon: 'fa-newspaper',
        category: 'monitoring',
        jsFile: 'news-monitoring.js',
        cssFile: 'news-monitoring-styles.css',
        enabled: true
    },
    {
        id: 'api-connectors',
        name: 'API konektory',
        description: 'Modul pro připojení k externím API a získávání dat.',
        icon: 'fa-plug',
        category: 'collection',
        jsFile: 'api-connectors.js',
        cssFile: 'api-connectors-styles.css',
        enabled: true
    },
    {
        id: 'messaging-platforms',
        name: 'Monitoring komunikačních platforem',
        description: 'Modul pro monitoring komunikačních platforem jako Telegram, Discord a WhatsApp.',
        icon: 'fa-comments',
        category: 'monitoring',
        jsFile: 'messaging-platforms.js',
        cssFile: 'messaging-platforms-styles.css',
        enabled: true
    },
    {
        id: 'correlation-tool',
        name: 'Korelační nástroj',
        description: 'Modul pro korelaci dat z různých zdrojů a vizualizaci vztahů mezi entitami.',
        icon: 'fa-project-diagram',
        category: 'analysis',
        jsFile: 'correlation-tool.js',
        cssFile: 'correlation-tool-styles.css',
        enabled: true
    },
    {
        id: 'timeline',
        name: 'Časová osa',
        description: 'Modul pro vizualizaci událostí na časové ose.',
        icon: 'fa-clock',
        category: 'visualization',
        jsFile: 'timeline.js',
        cssFile: 'timeline-styles.css',
        enabled: true
    },
    {
        id: 'complex-search',
        name: 'Komplexní vyhledávání',
        description: 'Modul pro komplexní vyhledávání entit na internetu pomocí Google dorků a dalších nástrojů.',
        icon: 'fa-search',
        category: 'collection',
        jsFile: 'complex-search.js',
        cssFile: 'complex-search-styles.css',
        enabled: true
    }
];

// Kategorie modulů
const moduleCategories = [
    {
        id: 'analysis',
        name: 'Analýza',
        icon: 'fa-microscope'
    },
    {
        id: 'collection',
        name: 'Sběr dat',
        icon: 'fa-database'
    },
    {
        id: 'monitoring',
        name: 'Monitoring',
        icon: 'fa-eye'
    },
    {
        id: 'visualization',
        name: 'Vizualizace',
        icon: 'fa-chart-bar'
    }
];

// Aktivní moduly
let activeModules = [];

/**
 * Inicializace správce modulů
 */
function initModuleManager() {
    console.log('Inicializace správce modulů');
    
    // Načtení aktivních modulů z lokálního úložiště
    loadActiveModules();
    
    // Inicializace UI pro přidání modulu
    initAddModuleUI();
    
    // Inicializace event listenerů
    initEventListeners();
}

/**
 * Načtení aktivních modulů z lokálního úložiště
 */
function loadActiveModules() {
    try {
        const savedModules = localStorage.getItem('active-modules');
        if (savedModules) {
            activeModules = JSON.parse(savedModules);
            console.log(`Načteno ${activeModules.length} aktivních modulů z lokálního úložiště`);
        } else {
            console.log('Žádné aktivní moduly v lokálním úložišti');
            activeModules = [];
        }
    } catch (error) {
        console.error('Chyba při načítání aktivních modulů:', error);
        activeModules = [];
    }
}

/**
 * Uložení aktivních modulů do lokálního úložiště
 */
function saveActiveModules() {
    try {
        localStorage.setItem('active-modules', JSON.stringify(activeModules));
        console.log(`Uloženo ${activeModules.length} aktivních modulů do lokálního úložiště`);
    } catch (error) {
        console.error('Chyba při ukládání aktivních modulů:', error);
        showNotification('Nepodařilo se uložit aktivní moduly.', 'error');
    }
}

/**
 * Inicializace UI pro přidání modulu
 */
function initAddModuleUI() {
    console.log('Inicializace UI pro přidání modulu');
    
    // Přidání tlačítka pro přidání modulu
    const addModuleBtn = document.getElementById('add-module-btn');
    if (addModuleBtn) {
        addModuleBtn.addEventListener('click', showAddModuleDialog);
    }
}

/**
 * Zobrazení dialogu pro přidání modulu
 */
function showAddModuleDialog() {
    console.log('Zobrazení dialogu pro přidání modulu');
    
    // Vytvoření dialogu
    const dialog = document.createElement('div');
    dialog.className = 'module-dialog';
    
    // HTML obsahu dialogu
    dialog.innerHTML = `
        <div class="module-dialog-content">
            <div class="module-dialog-header">
                <h4>Přidat modul</h4>
                <button type="button" class="module-dialog-close">&times;</button>
            </div>
            <div class="module-dialog-body">
                <div class="module-categories">
                    <h5>Kategorie modulů</h5>
                    <div class="category-list">
                        ${moduleCategories.map(category => `
                            <div class="category-item" data-category="${category.id}">
                                <i class="fas ${category.icon}"></i>
                                <span>${category.name}</span>
                            </div>
                        `).join('')}
                    </div>
                </div>
                <div class="module-list">
                    <h5>Dostupné moduly</h5>
                    <div class="module-items">
                        ${availableModules.map(module => `
                            <div class="module-item" data-module-id="${module.id}" data-category="${module.category}">
                                <div class="module-item-icon">
                                    <i class="fas ${module.icon}"></i>
                                </div>
                                <div class="module-item-content">
                                    <h6>${module.name}</h6>
                                    <p>${module.description}</p>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
            <div class="module-dialog-footer">
                <button type="button" class="btn btn-secondary" id="cancel-add-module">Zrušit</button>
                <button type="button" class="btn btn-primary" id="confirm-add-module" disabled>Přidat modul</button>
            </div>
        </div>
    `;
    
    // Přidání dialogu do dokumentu
    document.body.appendChild(dialog);
    
    // Přidání event listenerů
    const closeButton = dialog.querySelector('.module-dialog-close');
    const cancelButton = dialog.querySelector('#cancel-add-module');
    const confirmButton = dialog.querySelector('#confirm-add-module');
    const moduleItems = dialog.querySelectorAll('.module-item');
    const categoryItems = dialog.querySelectorAll('.category-item');
    
    // Zavření dialogu
    function closeDialog() {
        dialog.remove();
    }
    
    // Event listener pro zavření dialogu
    if (closeButton) closeButton.addEventListener('click', closeDialog);
    if (cancelButton) cancelButton.addEventListener('click', closeDialog);
    
    // Event listener pro výběr kategorie
    categoryItems.forEach(categoryItem => {
        categoryItem.addEventListener('click', function() {
            // Odstranění aktivní třídy ze všech kategorií
            categoryItems.forEach(item => item.classList.remove('active'));
            
            // Přidání aktivní třídy na vybranou kategorii
            this.classList.add('active');
            
            // Filtrování modulů podle kategorie
            const selectedCategory = this.getAttribute('data-category');
            moduleItems.forEach(moduleItem => {
                if (selectedCategory === 'all' || moduleItem.getAttribute('data-category') === selectedCategory) {
                    moduleItem.style.display = 'flex';
                } else {
                    moduleItem.style.display = 'none';
                }
            });
        });
    });
    
    // Event listener pro výběr modulu
    let selectedModuleId = null;
    moduleItems.forEach(moduleItem => {
        moduleItem.addEventListener('click', function() {
            // Odstranění aktivní třídy ze všech modulů
            moduleItems.forEach(item => item.classList.remove('active'));
            
            // Přidání aktivní třídy na vybraný modul
            this.classList.add('active');
            
            // Uložení ID vybraného modulu
            selectedModuleId = this.getAttribute('data-module-id');
            
            // Povolení tlačítka pro přidání modulu
            if (confirmButton) confirmButton.disabled = false;
        });
    });
    
    // Event listener pro přidání modulu
    if (confirmButton) {
        confirmButton.addEventListener('click', function() {
            if (selectedModuleId) {
                addModule(selectedModuleId);
                closeDialog();
            }
        });
    }
}

/**
 * Přidání modulu
 * @param {string} moduleId - ID modulu
 */
function addModule(moduleId) {
    console.log(`Přidání modulu: ${moduleId}`);
    
    // Získání modulu podle ID
    const moduleInfo = availableModules.find(module => module.id === moduleId);
    if (!moduleInfo) {
        showNotification(`Modul s ID ${moduleId} nebyl nalezen.`, 'error');
        return;
    }
    
    // Vytvoření ID instance modulu
    const instanceId = `module-${moduleId}-${Date.now()}`;
    
    // Přidání modulu do seznamu aktivních modulů
    activeModules.push({
        id: instanceId,
        moduleId: moduleId,
        name: moduleInfo.name,
        icon: moduleInfo.icon,
        position: {
            x: 0,
            y: 0
        },
        size: {
            width: 400,
            height: 300
        },
        created: new Date().toISOString()
    });
    
    // Uložení aktivních modulů
    saveActiveModules();
    
    // Vytvoření HTML modulu
    createModuleHTML(instanceId, moduleInfo);
    
    // Načtení skriptů a stylů modulu
    loadModuleResources(moduleId)
        .then(() => {
            // Inicializace modulu
            initModule(instanceId, moduleId);
            showNotification(`Modul ${moduleInfo.name} byl úspěšně přidán.`, 'success');
        })
        .catch(error => {
            console.error(`Chyba při načítání zdrojů modulu ${moduleId}:`, error);
            showNotification(`Nepodařilo se načíst zdroje modulu ${moduleInfo.name}.`, 'error');
        });
}

/**
 * Vytvoření HTML modulu
 * @param {string} instanceId - ID instance modulu
 * @param {Object} moduleInfo - Informace o modulu
 */
function createModuleHTML(instanceId, moduleInfo) {
    console.log(`Vytvoření HTML modulu: ${instanceId}`);
    
    // Vytvoření elementu modulu
    const moduleElement = document.createElement('div');
    moduleElement.className = 'module';
    moduleElement.id = instanceId;
    
    // HTML obsahu modulu
    moduleElement.innerHTML = `
        <div class="module-header">
            <h3 class="module-title">
                <i class="fas ${moduleInfo.icon}"></i> ${moduleInfo.name}
            </h3>
            <div class="module-actions">
                <button type="button" class="btn btn-secondary btn-sm module-expand">
                    <i class="fas fa-expand"></i>
                </button>
                <button type="button" class="btn btn-secondary btn-sm module-settings">
                    <i class="fas fa-cog"></i>
                </button>
                <button type="button" class="btn btn-secondary btn-sm module-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        <div class="module-content"></div>
    `;
    
    // Přidání modulu do kontejneru
    const moduleContainer = document.querySelector('.module-container');
    if (moduleContainer) {
        moduleContainer.appendChild(moduleElement);
    } else {
        console.error('Kontejner modulů nebyl nalezen.');
    }
    
    // Přidání event listenerů
    const closeButton = moduleElement.querySelector('.module-close');
    if (closeButton) {
        closeButton.addEventListener('click', function() {
            removeModule(instanceId);
        });
    }
}

/**
 * Inicializace modulu
 * @param {string} instanceId - ID instance modulu
 * @param {string} moduleId - ID modulu
 */
function initModule(instanceId, moduleId) {
    console.log(`Inicializace modulu: ${instanceId} (${moduleId})`);
    
    // Inicializace podle typu modulu
    switch (moduleId) {
        case 'complex-search':
            if (typeof initComplexSearch === 'function') {
                initComplexSearch();
            } else {
                console.error('Funkce initComplexSearch není k dispozici.');
            }
            break;
        case 'correlation-tool':
            if (typeof initCorrelationTool === 'function') {
                initCorrelationTool();
            } else {
                console.error('Funkce initCorrelationTool není k dispozici.');
            }
            break;
        // Další moduly...
        default:
            console.log(`Pro modul ${moduleId} není definována inicializační funkce.`);
    }
}

/**
 * Odstranění modulu
 * @param {string} instanceId - ID instance modulu
 */
function removeModule(instanceId) {
    console.log(`Odstranění modulu: ${instanceId}`);
    
    // Odstranění modulu ze seznamu aktivních modulů
    const moduleIndex = activeModules.findIndex(module => module.id === instanceId);
    if (moduleIndex !== -1) {
        activeModules.splice(moduleIndex, 1);
        
        // Uložení aktivních modulů
        saveActiveModules();
    }
    
    // Odstranění HTML modulu
    const moduleElement = document.getElementById(instanceId);
    if (moduleElement) {
        moduleElement.remove();
        showNotification('Modul byl úspěšně odstraněn.', 'success');
    }
}

/**
 * Inicializace event listenerů
 */
function initEventListeners() {
    console.log('Inicializace event listenerů');
    
    // Event listener pro tlačítko nastavení
    const settingsBtn = document.getElementById('settings-btn');
    if (settingsBtn) {
        settingsBtn.addEventListener('click', showSettingsDialog);
    }
}

/**
 * Zobrazení dialogu nastavení
 */
function showSettingsDialog() {
    console.log('Zobrazení dialogu nastavení');
    
    // Implementace dialogu nastavení...
    showNotification('Nastavení není implementováno.', 'info');
}

/**
 * Načtení skriptů a stylů modulu
 * @param {string} moduleId - ID modulu
 * @returns {Promise} - Promise, který se vyřeší po načtení všech souborů
 */
function loadModuleResources(moduleId) {
    const module = availableModules.find(module => module.id === moduleId);
    if (!module) {
        return Promise.reject(new Error(`Modul s ID ${moduleId} nebyl nalezen.`));
    }
    
    return Promise.all([
        loadScript(module.jsFile),
        loadStylesheet(module.cssFile)
    ]);
}

/**
 * Načtení JavaScript souboru
 * @param {string} src - Cesta k souboru
 * @returns {Promise} - Promise, který se vyřeší po načtení souboru
 */
function loadScript(src) {
    return new Promise((resolve, reject) => {
        // Kontrola, zda skript již není načten
        const existingScript = document.querySelector(`script[src="${src}"]`);
        if (existingScript) {
            resolve(existingScript);
            return;
        }
        
        const script = document.createElement('script');
        script.src = src;
        script.onload = () => resolve(script);
        script.onerror = () => reject(new Error(`Nepodařilo se načíst skript: ${src}`));
        document.head.appendChild(script);
    });
}

/**
 * Načtení CSS souboru
 * @param {string} href - Cesta k souboru
 * @returns {Promise} - Promise, který se vyřeší po načtení souboru
 */
function loadStylesheet(href) {
    return new Promise((resolve, reject) => {
        // Kontrola, zda stylesheet již není načten
        const existingStylesheet = document.querySelector(`link[href="${href}"]`);
        if (existingStylesheet) {
            resolve(existingStylesheet);
            return;
        }
        
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        link.onload = () => resolve(link);
        link.onerror = () => reject(new Error(`Nepodařilo se načíst stylesheet: ${href}`));
        document.head.appendChild(link);
    });
}

/**
 * Zobrazení notifikace
 * @param {string} message - Zpráva notifikace
 * @param {string} type - Typ notifikace (success, error, warning, info)
 */
function showNotification(message, type = 'info') {
    console.log(`Zobrazení notifikace: ${message} (${type})`);
    
    // Kontrola, zda existuje funkce pro zobrazení notifikace
    if (typeof window.showToast === 'function') {
        window.showToast(message, type);
        return;
    }
    
    // Záložní řešení - použití alert
    alert(`${type.toUpperCase()}: ${message}`);
}

// Inicializace správce modulů po načtení stránky
document.addEventListener('DOMContentLoaded', initModuleManager);

// Exportovat funkce pro použití v jiných modulech
window.addModule = addModule;
window.removeModule = removeModule;
window.showAddModuleDialog = showAddModuleDialog;
