/**
 * <PERSON><PERSON><PERSON> osa - funkce pro práci s událostmi
 */

/**
 * Generování unikátního ID pro událost
 * @returns {string} - Unikátní ID
 */
function generateEventId() {
    return 'event-' + Date.now() + '-' + Math.floor(Math.random() * 1000);
}

/**
 * Přidání události do časové osy
 * @param {Object} event - Událost k přidání
 */
function addEvent(event) {
    console.log('Přidání události do časové osy:', event.title);

    // Přidání události do pole událostí
    timelineEvents.push(event);

    // Uložení událostí
    saveTimelineData();

    // Překreslení časové osy
    renderTimelineEvents();

    // Skrytí prázdného stavu
    hideEmptyState();

    // Zobrazení notifikace
    showNotification(`Událost "${event.title}" byla ú<PERSON> p<PERSON>.`, 'success');
}

/**
 * Zobrazení dialogu pro přidání události
 * @param {string} [prefilledDatetime] - Předvyplněné datum a čas
 */
function showAddEventDialog(prefilledDatetime) {
    console.log('Zobrazení dialogu pro přidání události');

    // Vytvoření dialogu
    const dialog = document.createElement('div');
    dialog.className = 'add-event-dialog';

    // Formátování předvyplněného data pro input
    let formattedDatetime = '';
    if (prefilledDatetime) {
        const datetime = new Date(prefilledDatetime);
        if (!isNaN(datetime)) {
            formattedDatetime = formatDatetimeForInput(datetime);
        }
    }

    // Vytvoření obsahu dialogu
    dialog.innerHTML = `
        <div class="add-event-dialog-content">
            <div class="add-event-dialog-header">
                <h3>Přidat událost</h3>
                <button type="button" class="add-event-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="add-event-dialog-body">
                <div class="form-group">
                    <label>Název události *</label>
                    <input type="text" class="form-control" id="event-title" placeholder="Zadejte název události">
                </div>
                <div class="form-group">
                    <label>Popis události</label>
                    <textarea class="form-control" id="event-description" rows="3" placeholder="Zadejte popis události"></textarea>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Datum a čas *</label>
                        <input type="datetime-local" class="form-control" id="event-datetime" value="${formattedDatetime}">
                    </div>
                    <div class="form-group">
                        <label>Typ události</label>
                        <select class="form-control" id="event-type">
                            <option value="communication">Komunikace</option>
                            <option value="movement">Pohyb</option>
                            <option value="financial">Finanční transakce</option>
                            <option value="social">Sociální aktivita</option>
                            <option value="legal">Právní událost</option>
                            <option value="other">Ostatní</option>
                        </select>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Zdroj události</label>
                        <select class="form-control" id="event-source">
                            <option value="manual">Manuálně přidáno</option>
                            <option value="social">Sociální sítě</option>
                            <option value="financial">Finanční monitoring</option>
                            <option value="communication">Komunikační platformy</option>
                            <option value="location">Lokační data</option>
                            <option value="other">Ostatní</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Důležitost</label>
                        <select class="form-control" id="event-importance">
                            <option value="low">Nízká</option>
                            <option value="medium" selected>Střední</option>
                            <option value="high">Vysoká</option>
                            <option value="critical">Kritická</option>
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label>Lokace</label>
                    <input type="text" class="form-control" id="event-location" placeholder="Zadejte lokaci události">
                </div>
                <div class="form-group">
                    <label>Související osoby</label>
                    <input type="text" class="form-control" id="event-related-persons" placeholder="Zadejte související osoby oddělené čárkou">
                </div>
                <div class="form-group">
                    <label>Tagy</label>
                    <input type="text" class="form-control" id="event-tags" placeholder="Zadejte tagy oddělené čárkou">
                </div>
                <div class="form-group">
                    <label>Poznámky</label>
                    <textarea class="form-control" id="event-notes" rows="2" placeholder="Zadejte poznámky k události"></textarea>
                </div>
            </div>
            <div class="add-event-dialog-footer">
                <button type="button" class="btn-secondary add-event-dialog-cancel">Zrušit</button>
                <button type="button" class="btn-primary add-event-dialog-add">Přidat</button>
            </div>
        </div>
    `;

    // Přidání dialogu do stránky
    document.body.appendChild(dialog);

    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.add-event-dialog-close').addEventListener('click', () => dialog.remove());
    dialog.querySelector('.add-event-dialog-cancel').addEventListener('click', () => dialog.remove());

    dialog.querySelector('.add-event-dialog-add').addEventListener('click', function() {
        // Získání hodnot z formuláře
        const title = dialog.querySelector('#event-title').value.trim();
        const description = dialog.querySelector('#event-description').value.trim();
        const datetime = dialog.querySelector('#event-datetime').value;
        const type = dialog.querySelector('#event-type').value;
        const source = dialog.querySelector('#event-source').value;
        const importance = dialog.querySelector('#event-importance').value;
        const location = dialog.querySelector('#event-location').value.trim();
        const relatedPersons = dialog.querySelector('#event-related-persons').value.trim();
        const tags = dialog.querySelector('#event-tags').value.trim();
        const notes = dialog.querySelector('#event-notes').value.trim();

        // Validace povinných polí
        if (!title) {
            alert('Zadejte název události.');
            return;
        }

        if (!datetime) {
            alert('Zadejte datum a čas události.');
            return;
        }

        // Vytvoření nové události
        const newEvent = {
            id: generateEventId(),
            title: title,
            description: description,
            datetime: datetime,
            type: type,
            source: source,
            importance: importance,
            location: location,
            relatedPersons: relatedPersons ? relatedPersons.split(',').map(p => p.trim()) : [],
            tags: tags ? tags.split(',').map(t => t.trim()) : [],
            notes: notes,
            createdAt: new Date().toISOString(),
            media: []
        };

        // Přidání události do pole událostí
        addEvent(newEvent);

        // Zavření dialogu
        dialog.remove();
    });

    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}

/**
 * Zobrazení dialogu pro import událostí
 */
function showImportEventsDialog() {
    console.log('Zobrazení dialogu pro import událostí');

    // Vytvoření dialogu
    const dialog = document.createElement('div');
    dialog.className = 'import-events-dialog';

    // Vytvoření obsahu dialogu
    dialog.innerHTML = `
        <div class="import-events-dialog-content">
            <div class="import-events-dialog-header">
                <h3>Importovat události</h3>
                <button type="button" class="import-events-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="import-events-dialog-body">
                <div class="import-instructions">
                    <p>Vyberte zdroj, ze kterého chcete importovat události:</p>
                    <div class="import-note">
                        <i class="fas fa-info-circle"></i>
                        <span>Importují se pouze reálná data z ostatních modulů OSINT systému. Pokud v daném modulu nejsou žádná data, nebude možné nic importovat.</span>
                    </div>
                </div>
                <div class="import-options">
                    <div class="import-option" data-source="social">
                        <i class="fas fa-share-alt"></i>
                        <span>Sociální sítě</span>
                    </div>
                    <div class="import-option" data-source="financial">
                        <i class="fas fa-money-bill-wave"></i>
                        <span>Finanční monitoring</span>
                    </div>
                    <div class="import-option" data-source="communication">
                        <i class="fas fa-comments"></i>
                        <span>Komunikační platformy</span>
                    </div>
                    <div class="import-option" data-source="location">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>Lokační data</span>
                    </div>
                    <div class="import-option" data-source="file">
                        <i class="fas fa-file-import"></i>
                        <span>Ze souboru</span>
                    </div>
                </div>
                <div class="import-details" style="display: none;">
                    <!-- Zde bude zobrazen obsah podle vybraného zdroje -->
                </div>
            </div>
            <div class="import-events-dialog-footer">
                <button type="button" class="btn-secondary import-events-dialog-cancel">Zrušit</button>
                <button type="button" class="btn-primary import-events-dialog-import" disabled>Importovat</button>
            </div>
        </div>
    `;

    // Přidání dialogu do stránky
    document.body.appendChild(dialog);

    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.import-events-dialog-close').addEventListener('click', () => dialog.remove());
    dialog.querySelector('.import-events-dialog-cancel').addEventListener('click', () => dialog.remove());

    // Přidání event listenerů pro možnosti importu
    const importOptions = dialog.querySelectorAll('.import-option');
    importOptions.forEach(option => {
        option.addEventListener('click', function() {
            // Odstranění aktivní třídy ze všech možností
            importOptions.forEach(opt => opt.classList.remove('active'));

            // Přidání aktivní třídy na kliknutou možnost
            this.classList.add('active');

            // Zobrazení detailů importu
            const source = this.getAttribute('data-source');
            showImportDetails(dialog, source);

            // Povolení tlačítka pro import
            dialog.querySelector('.import-events-dialog-import').disabled = false;
        });
    });

    // Přidání event listeneru pro tlačítko importu
    dialog.querySelector('.import-events-dialog-import').addEventListener('click', function() {
        const activeOption = dialog.querySelector('.import-option.active');
        if (activeOption) {
            const source = activeOption.getAttribute('data-source');
            importEvents(dialog, source);
        }
    });

    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}

/**
 * Zobrazení detailů importu podle vybraného zdroje
 * @param {HTMLElement} dialog - Element dialogu
 * @param {string} source - Zdroj importu
 */
function showImportDetails(dialog, source) {
    const importDetails = dialog.querySelector('.import-details');
    if (!importDetails) return;

    // Zobrazení detailů
    importDetails.style.display = 'block';

    // Obsah podle zdroje
    let detailsContent = '';

    // Kontrola dostupnosti dat v modulech
    let availableData = false;
    let moduleCount = 0;

    switch (source) {
        case 'social':
            moduleCount = document.querySelectorAll('.module[id^="module-socialni"]').length;
            availableData = moduleCount > 0;

            detailsContent = `
                <h4>Import ze sociálních sítí</h4>
                ${availableData ? `
                <p>Nalezeno ${moduleCount} modulů sociálních sítí.</p>
                <p>Budou importovány všechny příspěvky, komentáře a další aktivity ze sociálních sítí.</p>
                <div class="import-note">
                    <i class="fas fa-info-circle"></i>
                    <span>Importují se pouze reálná data z modulů sociálních sítí. Žádná simulovaná data nebudou vytvořena.</span>
                </div>
                ` : `
                <div class="import-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Nebyly nalezeny žádné moduly sociálních sítí. Přidejte nejprve moduly sociálních sítí a naplňte je daty.</span>
                </div>
                `}
            `;
            break;
        case 'financial':
            moduleCount = document.querySelectorAll('.module[id^="module-financni"]').length;
            availableData = moduleCount > 0;

            detailsContent = `
                <h4>Import z finančního monitoringu</h4>
                ${availableData ? `
                <p>Nalezeno ${moduleCount} modulů finančního monitoringu.</p>
                <p>Budou importovány všechny finanční transakce, výběry a platby.</p>
                <div class="import-note">
                    <i class="fas fa-info-circle"></i>
                    <span>Importují se pouze reálná data z modulů finančního monitoringu. Žádná simulovaná data nebudou vytvořena.</span>
                </div>
                ` : `
                <div class="import-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Nebyly nalezeny žádné moduly finančního monitoringu. Přidejte nejprve moduly finančního monitoringu a naplňte je daty.</span>
                </div>
                `}
            `;
            break;
        case 'communication':
            moduleCount = document.querySelectorAll('.module[id^="module-komunikace"], .module[id^="module-messaging"]').length;
            availableData = moduleCount > 0;

            detailsContent = `
                <h4>Import z komunikačních platforem</h4>
                ${availableData ? `
                <p>Nalezeno ${moduleCount} modulů komunikačních platforem.</p>
                <p>Budou importovány všechny zprávy, hovory a další komunikace.</p>
                <div class="import-note">
                    <i class="fas fa-info-circle"></i>
                    <span>Importují se pouze reálná data z modulů komunikačních platforem. Žádná simulovaná data nebudou vytvořena.</span>
                </div>
                ` : `
                <div class="import-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Nebyly nalezeny žádné moduly komunikačních platforem. Přidejte nejprve moduly komunikačních platforem a naplňte je daty.</span>
                </div>
                `}
            `;
            break;
        case 'location':
            moduleCount = document.querySelectorAll('.module[id^="module-mapa"], .module[id^="module-lokace"]').length;
            availableData = moduleCount > 0;

            detailsContent = `
                <h4>Import z lokačních dat</h4>
                ${availableData ? `
                <p>Nalezeno ${moduleCount} modulů map a lokací.</p>
                <p>Budou importovány všechny lokace, pohyby a geografické údaje.</p>
                <div class="import-note">
                    <i class="fas fa-info-circle"></i>
                    <span>Importují se pouze reálná data z modulů map a lokací. Žádná simulovaná data nebudou vytvořena.</span>
                </div>
                ` : `
                <div class="import-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Nebyly nalezeny žádné moduly map a lokací. Přidejte nejprve moduly map a lokací a naplňte je daty.</span>
                </div>
                `}
            `;
            break;
        case 'file':
            detailsContent = `
                <h4>Import ze souboru</h4>
                <p>Vyberte soubor s událostmi (CSV, JSON):</p>
                <div class="import-file">
                    <input type="file" class="import-file-input" accept=".csv, .json">
                </div>
                <div class="import-note">
                    <i class="fas fa-info-circle"></i>
                    <span>Import ze souboru umožňuje načíst události z externích zdrojů. Podporovány jsou formáty CSV a JSON.</span>
                </div>
            `;
            break;
    }

    importDetails.innerHTML = detailsContent;
}

/**
 * Import událostí podle vybraného zdroje
 * @param {HTMLElement} dialog - Element dialogu
 * @param {string} source - Zdroj importu
 */
function importEvents(dialog, source) {
    console.log('Import událostí ze zdroje:', source);

    // Zobrazení notifikace o zahájení importu
    showNotification(`Zahajuji import událostí ze zdroje: ${source}`, 'info');

    // Získání reálných dat z vybraného modulu
    let importedEvents = [];

    switch (source) {
        case 'social':
            importedEvents = getEventsFromSocialModule();
            break;
        case 'financial':
            importedEvents = getEventsFromFinancialModule();
            break;
        case 'communication':
            importedEvents = getEventsFromCommunicationModule();
            break;
        case 'location':
            importedEvents = getEventsFromLocationModule();
            break;
        case 'file':
            // Pro import ze souboru bychom museli zpracovat nahraný soubor
            showNotification('Import ze souboru není v této verzi podporován.', 'warning');
            dialog.remove();
            return;
    }

    // Přidání importovaných událostí
    if (importedEvents.length > 0) {
        importedEvents.forEach(event => {
            addEvent(event);
        });

        // Zobrazení notifikace o dokončení importu
        showNotification(`Úspěšně importováno ${importedEvents.length} událostí.`, 'success');
    } else {
        showNotification(`V modulu ${getSourceName(source).toLowerCase()} nejsou k dispozici žádná data k importu.`, 'warning');
    }

    // Zavření dialogu
    dialog.remove();
}

/**
 * Získání událostí ze sociálních modulů
 * @returns {Array} - Pole událostí ze sociálních sítí
 */
function getEventsFromSocialModule() {
    console.log('Získávání událostí ze sociálních modulů');

    // Hledání modulů sociálních sítí
    const socialModules = document.querySelectorAll('.module[id^="module-socialni"]');
    if (!socialModules || socialModules.length === 0) {
        console.log('Nebyly nalezeny žádné moduly sociálních sítí');
        return [];
    }

    const events = [];

    // Procházení všech modulů sociálních sítí
    socialModules.forEach(module => {
        console.log('Zpracování modulu:', module.id);

        // Hledání příspěvků v modulu
        const posts = module.querySelectorAll('.social-post');
        if (!posts || posts.length === 0) {
            console.log('V modulu nebyly nalezeny žádné příspěvky');
            return;
        }

        // Procházení všech příspěvků
        posts.forEach(post => {
            // Získání dat z příspěvku
            const title = post.querySelector('.post-title')?.textContent || 'Příspěvek na sociální síti';
            const content = post.querySelector('.post-content')?.textContent || '';
            const dateStr = post.querySelector('.post-date')?.textContent || '';
            const author = post.querySelector('.post-author')?.textContent || '';
            const platform = post.querySelector('.post-platform')?.textContent || '';

            // Parsování data
            let datetime;
            try {
                // Pokus o parsování data z textu
                const dateMatch = dateStr.match(/(\d{1,2})\.(\d{1,2})\.(\d{4})\s+(\d{1,2}):(\d{2})/);
                if (dateMatch) {
                    const [_, day, month, year, hours, minutes] = dateMatch;
                    datetime = new Date(year, month - 1, day, hours, minutes).toISOString();
                } else {
                    datetime = new Date().toISOString();
                }
            } catch (error) {
                console.error('Chyba při parsování data:', error);
                datetime = new Date().toISOString();
            }

            // Vytvoření události
            const event = {
                id: generateEventId(),
                title: title,
                description: content,
                datetime: datetime,
                type: 'social',
                source: 'social',
                importance: 'medium',
                location: post.querySelector('.post-location')?.textContent || '',
                relatedPersons: author ? [author] : [],
                tags: [platform, 'sociální síť'],
                notes: `Zdroj: ${platform}`,
                createdAt: new Date().toISOString(),
                media: []
            };

            // Přidání události do pole
            events.push(event);
        });
    });

    console.log(`Nalezeno ${events.length} událostí ze sociálních modulů`);
    return events;
}

/**
 * Získání událostí z finančních modulů
 * @returns {Array} - Pole událostí z finančního monitoringu
 */
function getEventsFromFinancialModule() {
    console.log('Získávání událostí z finančních modulů');

    // Hledání modulů finančního monitoringu
    const financialModules = document.querySelectorAll('.module[id^="module-financni"]');
    if (!financialModules || financialModules.length === 0) {
        console.log('Nebyly nalezeny žádné moduly finančního monitoringu');
        return [];
    }

    const events = [];

    // Procházení všech modulů finančního monitoringu
    financialModules.forEach(module => {
        console.log('Zpracování modulu:', module.id);

        // Hledání transakcí v modulu
        const transactions = module.querySelectorAll('.financial-transaction');
        if (!transactions || transactions.length === 0) {
            console.log('V modulu nebyly nalezeny žádné transakce');
            return;
        }

        // Procházení všech transakcí
        transactions.forEach(transaction => {
            // Získání dat z transakce
            const title = transaction.querySelector('.transaction-title')?.textContent || 'Finanční transakce';
            const amount = transaction.querySelector('.transaction-amount')?.textContent || '';
            const dateStr = transaction.querySelector('.transaction-date')?.textContent || '';
            const type = transaction.querySelector('.transaction-type')?.textContent || '';

            // Parsování data
            let datetime;
            try {
                // Pokus o parsování data z textu
                const dateMatch = dateStr.match(/(\d{1,2})\.(\d{1,2})\.(\d{4})\s+(\d{1,2}):(\d{2})/);
                if (dateMatch) {
                    const [_, day, month, year, hours, minutes] = dateMatch;
                    datetime = new Date(year, month - 1, day, hours, minutes).toISOString();
                } else {
                    datetime = new Date().toISOString();
                }
            } catch (error) {
                console.error('Chyba při parsování data:', error);
                datetime = new Date().toISOString();
            }

            // Vytvoření události
            const event = {
                id: generateEventId(),
                title: title,
                description: `${type}: ${amount}`,
                datetime: datetime,
                type: 'financial',
                source: 'financial',
                importance: 'medium',
                location: transaction.querySelector('.transaction-location')?.textContent || '',
                relatedPersons: [],
                tags: ['finance', type],
                notes: transaction.querySelector('.transaction-notes')?.textContent || '',
                createdAt: new Date().toISOString(),
                media: []
            };

            // Přidání události do pole
            events.push(event);
        });
    });

    console.log(`Nalezeno ${events.length} událostí z finančních modulů`);
    return events;
}

/**
 * Získání událostí z komunikačních modulů
 * @returns {Array} - Pole událostí z komunikačních platforem
 */
function getEventsFromCommunicationModule() {
    console.log('Získávání událostí z komunikačních modulů');

    // Hledání modulů komunikačních platforem
    const communicationModules = document.querySelectorAll('.module[id^="module-komunikace"], .module[id^="module-messaging"]');
    if (!communicationModules || communicationModules.length === 0) {
        console.log('Nebyly nalezeny žádné moduly komunikačních platforem');
        return [];
    }

    const events = [];

    // Procházení všech modulů komunikačních platforem
    communicationModules.forEach(module => {
        console.log('Zpracování modulu:', module.id);

        // Hledání zpráv v modulu
        const messages = module.querySelectorAll('.message-item');
        if (!messages || messages.length === 0) {
            console.log('V modulu nebyly nalezeny žádné zprávy');
            return;
        }

        // Procházení všech zpráv
        messages.forEach(message => {
            // Získání dat ze zprávy
            const platform = message.querySelector('.message-platform')?.textContent || 'Komunikační platforma';
            const content = message.querySelector('.message-content')?.textContent || '';
            const dateStr = message.querySelector('.message-date')?.textContent || '';
            const sender = message.querySelector('.message-sender')?.textContent || '';
            const receiver = message.querySelector('.message-receiver')?.textContent || '';

            // Parsování data
            let datetime;
            try {
                // Pokus o parsování data z textu
                const dateMatch = dateStr.match(/(\d{1,2})\.(\d{1,2})\.(\d{4})\s+(\d{1,2}):(\d{2})/);
                if (dateMatch) {
                    const [_, day, month, year, hours, minutes] = dateMatch;
                    datetime = new Date(year, month - 1, day, hours, minutes).toISOString();
                } else {
                    datetime = new Date().toISOString();
                }
            } catch (error) {
                console.error('Chyba při parsování data:', error);
                datetime = new Date().toISOString();
            }

            // Vytvoření události
            const event = {
                id: generateEventId(),
                title: `${platform} - komunikace`,
                description: content,
                datetime: datetime,
                type: 'communication',
                source: 'communication',
                importance: 'medium',
                location: '',
                relatedPersons: [sender, receiver].filter(Boolean),
                tags: ['komunikace', platform],
                notes: `Od: ${sender}, Pro: ${receiver}`,
                createdAt: new Date().toISOString(),
                media: []
            };

            // Přidání události do pole
            events.push(event);
        });
    });

    console.log(`Nalezeno ${events.length} událostí z komunikačních modulů`);
    return events;
}

/**
 * Získání událostí z lokačních modulů
 * @returns {Array} - Pole událostí z lokačních dat
 */
function getEventsFromLocationModule() {
    console.log('Získávání událostí z lokačních modulů');

    // Hledání modulů map a lokací
    const locationModules = document.querySelectorAll('.module[id^="module-mapa"], .module[id^="module-lokace"]');
    if (!locationModules || locationModules.length === 0) {
        console.log('Nebyly nalezeny žádné moduly map a lokací');
        return [];
    }

    const events = [];

    // Procházení všech modulů map a lokací
    locationModules.forEach(module => {
        console.log('Zpracování modulu:', module.id);

        // Hledání lokací v modulu
        const locations = module.querySelectorAll('.location-marker, .map-marker');
        if (!locations || locations.length === 0) {
            console.log('V modulu nebyly nalezeny žádné lokace');
            return;
        }

        // Procházení všech lokací
        locations.forEach(location => {
            // Získání dat z lokace
            const title = location.querySelector('.marker-title')?.textContent || 'Lokace';
            const address = location.querySelector('.marker-address')?.textContent || '';
            const dateStr = location.querySelector('.marker-date')?.textContent || '';
            const description = location.querySelector('.marker-description')?.textContent || '';

            // Parsování data
            let datetime;
            try {
                // Pokus o parsování data z textu
                const dateMatch = dateStr.match(/(\d{1,2})\.(\d{1,2})\.(\d{4})\s+(\d{1,2}):(\d{2})/);
                if (dateMatch) {
                    const [_, day, month, year, hours, minutes] = dateMatch;
                    datetime = new Date(year, month - 1, day, hours, minutes).toISOString();
                } else {
                    datetime = new Date().toISOString();
                }
            } catch (error) {
                console.error('Chyba při parsování data:', error);
                datetime = new Date().toISOString();
            }

            // Vytvoření události
            const event = {
                id: generateEventId(),
                title: title,
                description: description,
                datetime: datetime,
                type: 'movement',
                source: 'location',
                importance: 'medium',
                location: address,
                relatedPersons: [],
                tags: ['lokace', 'pohyb'],
                notes: '',
                createdAt: new Date().toISOString(),
                media: []
            };

            // Přidání události do pole
            events.push(event);
        });
    });

    console.log(`Nalezeno ${events.length} událostí z lokačních modulů`);
    return events;
}

/**
 * Filtrování událostí podle aktuálních filtrů
 * @returns {Array} - Pole filtrovaných událostí
 */
function filterEvents() {
    console.log('Filtrování událostí podle aktuálních filtrů');

    // Pokud nejsou žádné události, vrátíme prázdné pole
    if (!timelineEvents || timelineEvents.length === 0) {
        return [];
    }

    // Filtrování podle typu
    let filteredEvents = timelineEvents;

    if (timelineSettings.filters.type !== 'all') {
        filteredEvents = filteredEvents.filter(event => event.type === timelineSettings.filters.type);
    }

    // Filtrování podle zdroje
    if (timelineSettings.filters.source !== 'all') {
        filteredEvents = filteredEvents.filter(event => event.source === timelineSettings.filters.source);
    }

    // Filtrování podle data od
    if (timelineSettings.filters.dateFrom) {
        const dateFrom = new Date(timelineSettings.filters.dateFrom);
        filteredEvents = filteredEvents.filter(event => new Date(event.datetime) >= dateFrom);
    }

    // Filtrování podle data do
    if (timelineSettings.filters.dateTo) {
        const dateTo = new Date(timelineSettings.filters.dateTo);
        dateTo.setHours(23, 59, 59, 999); // Nastavení na konec dne
        filteredEvents = filteredEvents.filter(event => new Date(event.datetime) <= dateTo);
    }

    // Filtrování podle klíčových slov
    if (timelineSettings.filters.keywords) {
        const keywords = timelineSettings.filters.keywords.toLowerCase().split(',').map(k => k.trim());

        filteredEvents = filteredEvents.filter(event => {
            // Kontrola názvu
            if (event.title && event.title.toLowerCase().includes(timelineSettings.filters.keywords.toLowerCase())) {
                return true;
            }

            // Kontrola popisu
            if (event.description && event.description.toLowerCase().includes(timelineSettings.filters.keywords.toLowerCase())) {
                return true;
            }

            // Kontrola poznámek
            if (event.notes && event.notes.toLowerCase().includes(timelineSettings.filters.keywords.toLowerCase())) {
                return true;
            }

            // Kontrola lokace
            if (event.location && event.location.toLowerCase().includes(timelineSettings.filters.keywords.toLowerCase())) {
                return true;
            }

            // Kontrola tagů
            if (event.tags && event.tags.some(tag => keywords.some(keyword => tag.toLowerCase().includes(keyword)))) {
                return true;
            }

            // Kontrola souvisejících osob
            if (event.relatedPersons && event.relatedPersons.some(person => keywords.some(keyword => person.toLowerCase().includes(keyword)))) {
                return true;
            }

            return false;
        });
    }

    return filteredEvents;
}

/**
 * Aplikace filtrů na časovou osu
 */
function applyTimelineFilters() {
    console.log('Aplikace filtrů na časovou osu');

    // Uložení nastavení
    saveTimelineData();

    // Překreslení událostí
    renderTimelineEvents();

    // Zobrazení notifikace
    showNotification('Filtry byly úspěšně aplikovány.', 'success');
}

/**
 * Reset filtrů časové osy
 */
function resetTimelineFilters() {
    console.log('Reset filtrů časové osy');

    // Reset filtrů
    timelineSettings.filters = {
        type: 'all',
        source: 'all',
        dateFrom: '',
        dateTo: '',
        keywords: ''
    };

    // Aktualizace UI
    updateTimelineUI();

    // Překreslení událostí
    renderTimelineEvents();

    // Zobrazení notifikace
    showNotification('Filtry byly resetovány.', 'success');
}

/**
 * Formátování data pro název souboru
 * @param {Date} date - Datum k formátování
 * @returns {string} - Formátovaný řetězec pro název souboru
 */
function formatDateForFilename(date) {
    if (!date || !(date instanceof Date) || isNaN(date)) {
        return '';
    }

    // Formát: YYYYMMDD_HHMMSS
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}${month}${day}_${hours}${minutes}${seconds}`;
}

/**
 * Export časové osy
 */
function exportTimeline() {
    console.log('Export časové osy');

    // Kontrola, zda existují události k exportu
    if (timelineEvents.length === 0) {
        showNotification('Časová osa neobsahuje žádné události k exportu.', 'warning');
        return;
    }

    // Příprava dat pro export
    const exportData = {
        events: timelineEvents,
        settings: timelineSettings,
        exportDate: new Date().toISOString(),
        version: '1.0'
    };

    // Konverze dat do JSON
    const jsonData = JSON.stringify(exportData, null, 2);

    // Vytvoření Blob a odkazu pro stažení
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');

    // Nastavení atributů odkazu
    link.setAttribute('href', url);
    link.setAttribute('download', `timeline_export_${formatDateForFilename(new Date())}.json`);
    link.style.visibility = 'hidden';

    // Přidání odkazu do stránky a kliknutí na něj
    document.body.appendChild(link);
    link.click();

    // Odstranění odkazu
    document.body.removeChild(link);

    // Zobrazení notifikace
    showNotification('Časová osa byla úspěšně exportována.', 'success');
}
