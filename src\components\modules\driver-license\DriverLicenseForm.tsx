"use client";

import type { <PERSON>mit<PERSON><PERSON><PERSON> } from 'react-hook-form';
import type { ChangeEvent } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import type { 
  DriverLicenseModuleData, 
  TrafficOffense, 
  PhysicalPersonSubject, 
  PhotoMetadata,
  InformationSource,
  InformationReliability,
  ThreatLevel
} from '@/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  Car, FileText, AlertTriangle, ImageUp, UserPlus, CalendarDays, Shield, 
  MapPin, Scale, Heart, History, StickyNote, Eye, 
  ChevronUp, ChevronDown, Building, Trash2, PlusCircle
} from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/loading';
import { useToast } from '@/hooks/use-toast';
import { db } from '@/lib/firebase';
import { doc, setDoc, Timestamp } from 'firebase/firestore';
import { useState, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils';
import {
  Form,
  FormField,
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

// Schemas for validation
const photoMetadataSchema = z.object({
  id: z.string().optional(),
  fileName: z.string().optional().default(''),
  downloadURL: z.string().optional().default(''),
  description: z.string().optional().default(''),
  dateTaken: z.string().optional().default(''),
  sourceURL: z.string().optional().default(''),
  storagePath: z.string().optional().default(''),
  location: z.string().optional().default(''),
  photographerInfo: z.string().optional().default(''),
  tags: z.string().optional().default(''),
  verified: z.boolean().optional().default(false),
});

const trafficOffenseSchema = z.object({
  id: z.string(),
  date: z.string().optional().default(''),
  description: z.string().optional().default(''),
  location: z.string().optional().default(''),
  exactAddress: z.string().optional().default(''),
  gpsCoordinates: z.string().optional().default(''),
  penaltyAmount: z.string().optional().default(''),
  pointsDeducted: z.number().nullable().optional(),
  offenseType: z.string().optional().default(''),
  officerName: z.string().optional().default(''),
  officerBadgeNumber: z.string().optional().default(''),
  caseNumber: z.string().optional().default(''),
  vehicleUsed: z.string().optional().default(''),
  legalStatus: z.enum(["pending", "resolved", "appealed", "dismissed"]).optional().default("pending"),
  courtDate: z.string().optional().default(''),
  appealStatus: z.string().optional().default(''),
  source: z.enum(["social_media", "surveillance", "witness", "documents", "phone_analysis", "investigation", "open_source", "other"]).optional().default("investigation"),
  sourceDetail: z.string().optional().default(''),
  verified: z.boolean().optional().default(false),
  verificationDate: z.string().optional().default(''),
  photos: z.array(photoMetadataSchema).optional().default([]),
  notes: z.string().optional().default(''),
});

const driverLicenseModuleSchema = z.object({
  // Základní údaje o řidičském průkazu
  licenseNumber: z.string().optional().default(''),
  validity: z.string().optional().default(''),
  validFrom: z.string().optional().default(''),
  validTo: z.string().optional().default(''),
  issuedBy: z.string().optional().default(''),
  placeOfIssue: z.string().optional().default(''),
  categories: z.string().optional().default(''),
  restrictions: z.string().optional().default(''),
  
  // Zdravotní způsobilost
  medicalFitness: z.enum(["yes", "no", "limited", "unknown"]).optional().default("unknown"),
  medicalRestrictions: z.string().optional().default(''),
  medicalCertificateDate: z.string().optional().default(''),
  medicalCertificateValidUntil: z.string().optional().default(''),
  
  // Historie řidičských průkazů
  previousLicenses: z.string().optional().default(''),
  licenseHistory: z.string().optional().default(''),
  
  // Evidenční karta řidiče
  pointsBalance: z.number().nullable().optional(),
  currentBanStatus: z.enum(["yes", "no"]).optional().default("no"),
  currentBanPeriod: z.string().optional().default(''),
  currentBanReason: z.string().optional().default(''),
  banHistory: z.string().optional().default(''),
  
  // Přestupky v dopravě
  offenses: z.array(trafficOffenseSchema).optional().default([]),
  
  // OSINT informace
  informationSource: z.enum(["social_media", "surveillance", "witness", "documents", "phone_analysis", "investigation", "open_source", "other"]).optional().default("investigation"),
  sourceDetail: z.string().optional().default(''),
  informationReliability: z.enum(["verified", "probable", "possible", "unconfirmed", "unknown"]).optional().default("unknown"),
  verificationDate: z.string().optional().default(''),
  lastVerified: z.string().optional().default(''),
  
  // Bezpečnostní hodnocení
  drivingRisk: z.enum(["none", "low", "medium", "high", "critical", "unknown"]).optional().default("unknown"),
  riskAssessment: z.string().optional().default(''),
  drivingBehaviorNotes: z.string().optional().default(''),
  
  // Poznámky a dokumenty
  investigationNotes: z.string().optional().default(''),
  notes: z.string().optional().default(''),
  photos: z.array(photoMetadataSchema).optional().default([]),
});

type DriverLicenseFormValues = z.infer<typeof driverLicenseModuleSchema>;

interface DriverLicenseFormProps {
  caseId: string;
  subject: PhysicalPersonSubject;
  existingData: DriverLicenseModuleData | null;
  onSave: (moduleId: string, wasNew: boolean) => void;
}

// Options for select fields
const medicalFitnessOptions = [
  { value: "yes", label: "Způsobilý/á" },
  { value: "no", label: "Nezpůsobilý/á" },
  { value: "limited", label: "Způsobilý/á s omezením" },
  { value: "unknown", label: "Neznámo" },
];

const informationSourceOptions = [
  { value: "social_media", label: "Sociální média" },
  { value: "surveillance", label: "Sledování" },
  { value: "witness", label: "Svědek" },
  { value: "documents", label: "Dokumenty" },
  { value: "phone_analysis", label: "Analýza telefonu" },
  { value: "investigation", label: "Vyšetřování" },
  { value: "open_source", label: "Otevřené zdroje" },
  { value: "other", label: "Jiný" },
];

const informationReliabilityOptions = [
  { value: "verified", label: "Ověřeno" },
  { value: "probable", label: "Pravděpodobné" },
  { value: "possible", label: "Možné" },
  { value: "unconfirmed", label: "Nepotvrzeno" },
  { value: "unknown", label: "Neznámo" },
];

const threatLevelOptions = [
  { value: "none", label: "Žádné" },
  { value: "low", label: "Nízké" },
  { value: "medium", label: "Střední" },
  { value: "high", label: "Vysoké" },
  { value: "critical", label: "Kritické" },
  { value: "unknown", label: "Neznámé" },
];

const legalStatusOptions = [
  { value: "pending", label: "Probíhající" },
  { value: "resolved", label: "Vyřešeno" },
  { value: "appealed", label: "Odvoláno" },
  { value: "dismissed", label: "Zamítnuto" },
];

// Helper to generate initial form values
const getInitialFormValues = (subject: PhysicalPersonSubject, existingData: DriverLicenseModuleData | null): DriverLicenseFormValues => {
  return {
    licenseNumber: existingData?.licenseNumber || '',
    validity: existingData?.validity || '',
    validFrom: existingData?.validFrom || '',
    validTo: existingData?.validTo || '',
    issuedBy: existingData?.issuedBy || '',
    placeOfIssue: existingData?.placeOfIssue || '',
    categories: existingData?.categories || '',
    restrictions: existingData?.restrictions || '',
    medicalFitness: existingData?.medicalFitness || "unknown",
    medicalRestrictions: existingData?.medicalRestrictions || '',
    medicalCertificateDate: existingData?.medicalCertificateDate || '',
    medicalCertificateValidUntil: existingData?.medicalCertificateValidUntil || '',
    previousLicenses: existingData?.previousLicenses || '',
    licenseHistory: existingData?.licenseHistory || '',
    pointsBalance: existingData?.pointsBalance || null,
    currentBanStatus: existingData?.currentBanStatus || "no",
    currentBanPeriod: existingData?.currentBanPeriod || '',
    currentBanReason: existingData?.currentBanReason || '',
    banHistory: existingData?.banHistory || '',
    offenses: existingData?.offenses?.map(o => ({
      ...o,
      id: o.id || crypto.randomUUID(),
      date: o.date || '',
      description: o.description || '',
      location: o.location || '',
      exactAddress: o.exactAddress || '',
      gpsCoordinates: o.gpsCoordinates || '',
      penaltyAmount: o.penaltyAmount || '',
      pointsDeducted: o.pointsDeducted || null,
      offenseType: o.offenseType || '',
      officerName: o.officerName || '',
      officerBadgeNumber: o.officerBadgeNumber || '',
      caseNumber: o.caseNumber || '',
      vehicleUsed: o.vehicleUsed || '',
      legalStatus: o.legalStatus || "pending",
      courtDate: o.courtDate || '',
      appealStatus: o.appealStatus || '',
      source: o.source || "investigation",
      sourceDetail: o.sourceDetail || '',
      verified: o.verified || false,
      verificationDate: o.verificationDate || '',
      photos: o.photos?.map(p => ({
        ...p,
        id: p.id || crypto.randomUUID(),
        fileName: p.fileName || '',
        downloadURL: p.downloadURL || '',
        description: p.description || '',
        dateTaken: p.dateTaken || '',
        sourceURL: p.sourceURL || '',
        storagePath: p.storagePath || '',
        location: p.location || '',
        photographerInfo: p.photographerInfo || '',
        tags: p.tags || '',
        verified: p.verified || false,
      })) || [],
      notes: o.notes || '',
    })) || [],
    informationSource: existingData?.informationSource || "investigation",
    sourceDetail: existingData?.sourceDetail || '',
    informationReliability: existingData?.informationReliability || "unknown",
    verificationDate: existingData?.verificationDate || '',
    lastVerified: existingData?.lastVerified || '',
    drivingRisk: existingData?.drivingRisk || "unknown",
    riskAssessment: existingData?.riskAssessment || '',
    drivingBehaviorNotes: existingData?.drivingBehaviorNotes || '',
    investigationNotes: existingData?.investigationNotes || '',
    notes: existingData?.notes || '',
    photos: existingData?.photos?.map(p => ({
      ...p,
      id: p.id || crypto.randomUUID(),
      fileName: p.fileName || '',
      downloadURL: p.downloadURL || '',
      description: p.description || '',
      dateTaken: p.dateTaken || '',
      sourceURL: p.sourceURL || '',
      storagePath: p.storagePath || '',
      location: p.location || '',
      photographerInfo: p.photographerInfo || '',
      tags: p.tags || '',
      verified: p.verified || false,
    })) || [],
  };
};

export function DriverLicenseForm({ caseId, subject, existingData, onSave }: DriverLicenseFormProps) {
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [uploadingPhotos, setUploadingPhotos] = useState<Record<string, boolean>>({});
  const [photoUrls, setPhotoUrls] = useState<Record<string, string>>({});
  const moduleId = "registr_ridicske_prukazy";

  const form = useForm<DriverLicenseFormValues>({
    resolver: zodResolver(driverLicenseModuleSchema),
    defaultValues: getInitialFormValues(subject, existingData),
  });

  // Field arrays
  const { fields: offenseFields, append: appendOffense, remove: removeOffense } = useFieldArray({ 
    control: form.control, 
    name: "offenses" 
  });

  const { fields: photoFields, append: appendPhoto, remove: removePhoto } = useFieldArray({ 
    control: form.control, 
    name: "photos" 
  });

  // Efekt pro synchronizaci photoUrls s form data
  useEffect(() => {
    const currentPhotos = form.getValues('photos') || [];
    const newPhotoUrls: Record<string, string> = {};
    
    currentPhotos.forEach((photo, photoIndex) => {
      if (photo.downloadURL) {
        const key = `main-${photoIndex}`;
        newPhotoUrls[key] = photo.downloadURL;
      }
    });

    // Také pro fotografie v přestupcích
    const currentOffenses = form.getValues('offenses') || [];
    currentOffenses.forEach((offense, offenseIndex) => {
      offense.photos?.forEach((photo, photoIndex) => {
        if (photo.downloadURL) {
          const key = `offense-${offenseIndex}-${photoIndex}`;
          newPhotoUrls[key] = photo.downloadURL;
        }
      });
    });
    
    setPhotoUrls(newPhotoUrls);
  }, [form, existingData]);

  const addNewOffense = () => {
    const newOffense: TrafficOffense = {
      id: crypto.randomUUID(),
      date: '',
      description: '',
      location: '',
      exactAddress: '',
      gpsCoordinates: '',
      penaltyAmount: '',
      pointsDeducted: null,
      offenseType: '',
      officerName: '',
      officerBadgeNumber: '',
      caseNumber: '',
      vehicleUsed: '',
      legalStatus: "pending",
      courtDate: '',
      appealStatus: '',
      source: "investigation",
      sourceDetail: '',
      verified: false,
      verificationDate: '',
      photos: [],
      notes: '',
    };
    appendOffense(newOffense as any);
  };

  const addNewPhoto = () => {
    appendPhoto({
      id: crypto.randomUUID(),
      fileName: '',
      downloadURL: '',
      description: '',
      dateTaken: '',
      sourceURL: '',
      storagePath: '',
      location: '',
      photographerInfo: '',
      tags: '',
      verified: false,
    });
  };

  const onSubmitHandler: SubmitHandler<DriverLicenseFormValues> = async (data) => {
    setIsSaving(true);
    try {
      const moduleDocRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);
      
      // Zpracování fotografií - odfiltrování prázdných fotografií
      const processedPhotos = data.photos?.filter(p => p.downloadURL && p.downloadURL.trim()).map(photo => ({
        id: photo.id || crypto.randomUUID(),
        downloadURL: photo.downloadURL,
        description: photo.description || '',
        dateTaken: photo.dateTaken || '',
        fileName: photo.fileName || '',
        sourceURL: photo.sourceURL || '',
        storagePath: photo.downloadURL,
        location: photo.location || '',
        photographerInfo: photo.photographerInfo || '',
        tags: photo.tags || '',
        verified: photo.verified || false,
      })) || [];

      // Zpracování přestupků s fotografiemi
      const processedOffenses = data.offenses?.map(offense => ({
        ...offense,
        photos: offense.photos?.filter(p => p.downloadURL && p.downloadURL.trim()).map(photo => ({
          id: photo.id || crypto.randomUUID(),
          downloadURL: photo.downloadURL,
          description: photo.description || '',
          dateTaken: photo.dateTaken || '',
          fileName: photo.fileName || '',
          sourceURL: photo.sourceURL || '',
          storagePath: photo.downloadURL,
          location: photo.location || '',
          photographerInfo: photo.photographerInfo || '',
          tags: photo.tags || '',
          verified: photo.verified || false,
        })) || []
      })) || [];
      
      const dataToSave: DriverLicenseModuleData = {
        ...data,
        photos: processedPhotos,
        offenses: processedOffenses,
        subjectId: subject.id,
        lastUpdatedAt: Timestamp.now(),
        createdAt: existingData?.createdAt || Timestamp.now(),
      };

      await setDoc(moduleDocRef, dataToSave);
      toast({ title: "Data modulu uložena", description: "Informace z modulu Registr řidičských průkazů byly úspěšně uloženy." });
      
      const wasNew = !existingData || !existingData.createdAt;
      onSave(moduleId, wasNew);

    } catch (error: any) {
      console.error("Error saving Driver License data:", error);
      toast({ title: "Chyba ukládání", description: error.message, variant: "destructive" });
    } finally {
      setIsSaving(false);
    }
  };

  // Funkce pro konverzi File na Base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  };

  // Funkce pro zpracování obrázku
  const processImageFile = useCallback(async (file: File, photoType: 'main' | 'offense', offenseIndex?: number, photoIndex?: number) => {
    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: "Soubor je příliš velký",
        description: "Maximální velikost fotografie je 10MB.",
        variant: "destructive"
      });
      return;
    }

    if (!file.type.startsWith('image/')) {
      toast({
        title: "Neplatný formát souboru",
        description: "Podporované jsou pouze obrázky (JPEG, PNG, GIF, WEBP).",
        variant: "destructive"
      });
      return;
    }

    const uploadKey = photoType === 'main' 
      ? `main-${photoIndex}` 
      : `offense-${offenseIndex}-${photoIndex}`;
    
    setUploadingPhotos(prev => ({ ...prev, [uploadKey]: true }));

    try {
      const base64 = await fileToBase64(file);

      const response = await fetch('/api/upload-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          base64Data: base64,
          caseId: caseId,
          subjectId: subject.id,
          moduleId: moduleId,
          fileName: file.name
        })
      });

      if (!response.ok) {
        throw new Error('Nepodařilo se uložit fotografii na server');
      }

      const { imagePath } = await response.json();

      setPhotoUrls(prev => ({
        ...prev,
        [uploadKey]: imagePath
      }));
      
      if (photoType === 'main') {
        form.setValue(`photos.${photoIndex}.downloadURL`, imagePath);
        form.setValue(`photos.${photoIndex}.fileName`, file.name);
        form.setValue(`photos.${photoIndex}.description`, `Nahraná fotografie: ${file.name}`);
        form.setValue(`photos.${photoIndex}.id`, crypto.randomUUID());
      } else {
        form.setValue(`offenses.${offenseIndex}.photos.${photoIndex}.downloadURL`, imagePath);
        form.setValue(`offenses.${offenseIndex}.photos.${photoIndex}.fileName`, file.name);
        form.setValue(`offenses.${offenseIndex}.photos.${photoIndex}.description`, `Nahraná fotografie: ${file.name}`);
        form.setValue(`offenses.${offenseIndex}.photos.${photoIndex}.id`, crypto.randomUUID());
      }

      toast({
        title: "Fotografie uložena",
        description: `Fotografie byla úspěšně uložena do aplikace: ${imagePath}`,
      });
    } catch (error) {
      console.error('Chyba při načítání fotografie:', error);
      toast({
        title: "Chyba při načítání fotografie",
        description: "Nepodařilo se načíst fotografii. Zkuste to prosím znovu.",
        variant: "destructive"
      });
    } finally {
      setUploadingPhotos(prev => ({ ...prev, [uploadKey]: false }));
    }
  }, [caseId, subject.id, moduleId, form, toast]);

  // Funkce pro paste obrázku ze schránky
  const handlePaste = useCallback(async (event: React.ClipboardEvent, photoType: 'main' | 'offense', offenseIndex?: number, photoIndex?: number) => {
    event.preventDefault();
    const items = event.clipboardData?.items;
    
    if (!items) return;
    
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.type.startsWith('image/')) {
        const file = item.getAsFile();
        if (file) {
          await processImageFile(file, photoType, offenseIndex, photoIndex);
        }
        break;
      }
    }
  }, [processImageFile]);

  // Funkce pro zpracování nahraného obrázku
  const handlePhotoUpload = useCallback(async (event: ChangeEvent<HTMLInputElement>, photoType: 'main' | 'offense', offenseIndex?: number, photoIndex?: number) => {
    const file = event.target.files?.[0];
    if (file) {
      await processImageFile(file, photoType, offenseIndex, photoIndex);
    }
  }, [processImageFile]);

  const getPlaceholderImage = () => `https://placehold.co/150x150.png`;

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmitHandler)} className="space-y-8">
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-xl flex items-center">
              <Car className="mr-3 h-6 w-6 text-primary" />
              Registr řidičských průkazů subjektu: {subject.firstName} {subject.lastName}
            </CardTitle>
            <CardDescription>
              Komplexní OSINT analýza řidičského oprávnění, přestupků a dopravního chování vyšetřovaného subjektu.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Základní údaje o řidičském průkazu */}
            <Card className="p-4 shadow-sm">
              <CardHeader className="px-0 pt-0 pb-4">
                <CardTitle className="text-md flex items-center">
                  <FileText className="mr-2 h-4 w-4 text-primary/80" />
                  Základní údaje o řidičském průkazu
                </CardTitle>
              </CardHeader>
              <CardContent className="px-0 pb-0 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItemRHF label="Číslo řidičského průkazu" name="licenseNumber" control={form.control} placeholder="Zadejte číslo ŘP" />
                  <FormItemRHF label="Platnost (text)" name="validity" control={form.control} placeholder="např. 01.01.2020 - 01.01.2030" />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItemRHF label="Platnost od" name="validFrom" control={form.control} type="date" />
                  <FormItemRHF label="Platnost do" name="validTo" control={form.control} type="date" />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItemRHF label="Vydal (úřad)" name="issuedBy" control={form.control} placeholder="Magistrát města..." />
                  <FormItemRHF label="Místo vydání" name="placeOfIssue" control={form.control} placeholder="Praha, Brno..." />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItemRHF label="Skupiny oprávnění" name="categories" control={form.control} placeholder="např. B, B1, AM" />
                  <FormItemRHF label="Omezení (harm. kódy)" name="restrictions" control={form.control} placeholder="např. 01.01, 78" />
                </div>
              </CardContent>
            </Card>

            {/* Zdravotní způsobilost */}
            <Card className="p-4 shadow-sm">
              <CardHeader className="px-0 pt-0 pb-4">
                <CardTitle className="text-md flex items-center">
                  <Heart className="mr-2 h-4 w-4 text-primary/80" />
                  Zdravotní způsobilost
                </CardTitle>
              </CardHeader>
              <CardContent className="px-0 pb-0 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItemSelectRHF label="Zdravotní způsobilost" name="medicalFitness" control={form.control} options={medicalFitnessOptions} placeholder="-- Vyberte --" />
                  <FormItemRHF label="Datum lékařského vyšetření" name="medicalCertificateDate" control={form.control} type="date" />
                </div>
                <FormItemRHF label="Platnost lékařského potvrzení do" name="medicalCertificateValidUntil" control={form.control} type="date" />
                <FormItemRHF label="Zdravotní omezení" name="medicalRestrictions" control={form.control} placeholder="Popis zdravotních omezení nebo požadavků..." as="textarea" rows={3} />
              </CardContent>
            </Card>

            {/* Historie řidičských průkazů */}
            <Card className="p-4 shadow-sm">
              <CardHeader className="px-0 pt-0 pb-4">
                <CardTitle className="text-md flex items-center">
                  <History className="mr-2 h-4 w-4 text-primary/80" />
                  Historie řidičských průkazů
                </CardTitle>
              </CardHeader>
              <CardContent className="px-0 pb-0 space-y-4">
                <FormItemRHF label="Předchozí řidičské průkazy" name="previousLicenses" control={form.control} placeholder="Informace o dříve vydaných ŘP..." as="textarea" rows={3} />
                <FormItemRHF label="Detailní historie ŘP" name="licenseHistory" control={form.control} placeholder="Výměny, duplikáty, ztráty, odnětí..." as="textarea" rows={3} />
              </CardContent>
            </Card>

            {/* Evidenční karta řidiče */}
            <Card className="p-4 shadow-sm">
              <CardHeader className="px-0 pt-0 pb-4">
                <CardTitle className="text-md flex items-center">
                  <Scale className="mr-2 h-4 w-4 text-primary/80" />
                  Evidenční karta řidiče
                </CardTitle>
              </CardHeader>
              <CardContent className="px-0 pb-0 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItemRHF label="Stav bodového konta" name="pointsBalance" control={form.control} type="number" placeholder="Počet bodů (0-12)" />
                  <FormItemSelectRHF label="Aktuální zákaz řízení" name="currentBanStatus" control={form.control} options={[{value: "no", label: "Nemá zákaz řízení"}, {value: "yes", label: "Má zákaz řízení"}]} placeholder="-- Vyberte --" />
                </div>
                
                {form.watch("currentBanStatus") === "yes" && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF label="Platnost zákazu od - do" name="currentBanPeriod" control={form.control} placeholder="např. 01.01.2023 - 01.01.2024" />
                    <FormItemRHF label="Důvod zákazu" name="currentBanReason" control={form.control} placeholder="Důvod zákazu řízení" />
                  </div>
                )}
                
                <FormItemRHF label="Historie zákazů řízení" name="banHistory" control={form.control} placeholder="Předchozí zákazy řízení a jejich důvody..." as="textarea" rows={3} />
              </CardContent>
            </Card>

            {/* Přestupky v dopravě */}
            <Card className="p-4 shadow-sm">
              <CardHeader className="px-0 pt-0 pb-4">
                <CardTitle className="text-md flex items-center">
                  <AlertTriangle className="mr-2 h-4 w-4 text-primary/80" />
                  Přestupky v dopravě
                </CardTitle>
              </CardHeader>
              <CardContent className="px-0 pb-0 space-y-4">
                {offenseFields.map((field, offenseIndex) => (
                  <Card key={field.id} className="p-4 shadow-inner bg-card-foreground/5 relative">
                    <Button 
                      type="button" 
                      variant="ghost" 
                      size="icon" 
                      onClick={() => removeOffense(offenseIndex)}
                      className="absolute top-4 right-4 text-destructive hover:bg-destructive/10"
                      aria-label="Odebrat přestupek"
                    >
                      <Trash2 className="h-5 w-5" />
                    </Button>
                    
                    <CardHeader className="px-0 pt-0 pb-4">
                      <CardTitle className="text-sm">Přestupek {offenseIndex + 1}</CardTitle>
                    </CardHeader>
                    
                    <CardContent className="px-0 pb-0 space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItemRHF label="Datum přestupku" name={`offenses.${offenseIndex}.date`} control={form.control} type="date" />
                        <FormItemRHF label="Typ přestupku" name={`offenses.${offenseIndex}.offenseType`} control={form.control} placeholder="Překročení rychlosti, alkohol..." />
                      </div>
                      
                      <FormItemRHF label="Popis přestupku" name={`offenses.${offenseIndex}.description`} control={form.control} placeholder="Detailní popis přestupku..." as="textarea" rows={2} />
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItemRHF label="Místo přestupku" name={`offenses.${offenseIndex}.location`} control={form.control} placeholder="Obecné místo" />
                        <FormItemRHF label="Přesná adresa" name={`offenses.${offenseIndex}.exactAddress`} control={form.control} placeholder="Konkrétní adresa" />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItemRHF label="GPS souřadnice" name={`offenses.${offenseIndex}.gpsCoordinates`} control={form.control} placeholder="50.0872, 14.4212" />
                        <FormItemRHF label="Použité vozidlo (SPZ)" name={`offenses.${offenseIndex}.vehicleUsed`} control={form.control} placeholder="Registrační značka" />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItemRHF label="Výše pokuty" name={`offenses.${offenseIndex}.penaltyAmount`} control={form.control} placeholder="Např. 2000 Kč" />
                        <FormItemRHF label="Odebrané body" name={`offenses.${offenseIndex}.pointsDeducted`} control={form.control} type="number" placeholder="Počet bodů" />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItemRHF label="Jméno policisty" name={`offenses.${offenseIndex}.officerName`} control={form.control} placeholder="Jméno a příjmení" />
                        <FormItemRHF label="Číslo odznaku" name={`offenses.${offenseIndex}.officerBadgeNumber`} control={form.control} placeholder="Služební číslo" />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItemRHF label="Číslo případu/protokolu" name={`offenses.${offenseIndex}.caseNumber`} control={form.control} placeholder="Číslo spisu" />
                        <FormItemSelectRHF label="Stav řízení" name={`offenses.${offenseIndex}.legalStatus`} control={form.control} options={legalStatusOptions} placeholder="-- Vyberte --" />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItemRHF label="Datum soudu" name={`offenses.${offenseIndex}.courtDate`} control={form.control} type="date" />
                        <FormItemRHF label="Stav odvolání" name={`offenses.${offenseIndex}.appealStatus`} control={form.control} placeholder="Informace o odvolání" />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormItemSelectRHF label="Zdroj informace" name={`offenses.${offenseIndex}.source`} control={form.control} options={informationSourceOptions} placeholder="-- Vyberte --" />
                        <FormItemRHF label="Detail zdroje" name={`offenses.${offenseIndex}.sourceDetail`} control={form.control} placeholder="Konkrétní zdroj" />
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name={`offenses.${offenseIndex}.verified`}
                          render={({ field: checkboxField }) => (
                            <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                              <FormControl>
                                <Checkbox
                                  checked={checkboxField.value}
                                  onCheckedChange={checkboxField.onChange}
                                  id={`verified-${offenseIndex}`}
                                />
                              </FormControl>
                              <Label htmlFor={`verified-${offenseIndex}`} className="text-sm font-normal cursor-pointer">
                                Ověřeno
                              </Label>
                            </FormItem>
                          )}
                        />
                        <FormItemRHF label="Datum ověření" name={`offenses.${offenseIndex}.verificationDate`} control={form.control} type="date" />
                      </div>
                      
                      <FormItemRHF label="Poznámky k přestupku" name={`offenses.${offenseIndex}.notes`} control={form.control} placeholder="Další informace..." as="textarea" rows={2} />
                    </CardContent>
                  </Card>
                ))}

                <Button type="button" variant="outline" onClick={addNewOffense} className="w-full md:w-auto">
                  <UserPlus className="mr-2 h-4 w-4" /> Přidat přestupek
                </Button>
              </CardContent>
            </Card>

            {/* OSINT informace */}
            <Card className="p-4 shadow-sm">
              <CardHeader className="px-0 pt-0 pb-4">
                <CardTitle className="text-md flex items-center">
                  <Eye className="mr-2 h-4 w-4 text-primary/80" />
                  OSINT informace
                </CardTitle>
              </CardHeader>
              <CardContent className="px-0 pb-0 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItemSelectRHF label="Zdroj informací" name="informationSource" control={form.control} options={informationSourceOptions} placeholder="-- Vyberte --" />
                  <FormItemRHF label="Detail zdroje" name="sourceDetail" control={form.control} placeholder="Konkrétní zdroj informace" />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormItemSelectRHF label="Spolehlivost informací" name="informationReliability" control={form.control} options={informationReliabilityOptions} placeholder="-- Vyberte --" />
                  <FormItemRHF label="Datum ověření" name="verificationDate" control={form.control} type="date" />
                </div>
                <FormItemRHF label="Naposledy ověřeno" name="lastVerified" control={form.control} type="date" />
              </CardContent>
            </Card>

            {/* Bezpečnostní hodnocení */}
            <Card className="p-4 shadow-sm">
              <CardHeader className="px-0 pt-0 pb-4">
                <CardTitle className="text-md flex items-center">
                  <Shield className="mr-2 h-4 w-4 text-primary/80" />
                  Bezpečnostní hodnocení
                </CardTitle>
              </CardHeader>
              <CardContent className="px-0 pb-0 space-y-4">
                <FormItemSelectRHF label="Riziko v dopravě" name="drivingRisk" control={form.control} options={threatLevelOptions} placeholder="-- Vyberte --" />
                <FormItemRHF label="Hodnocení rizika" name="riskAssessment" control={form.control} placeholder="Analýza rizik souvisejících s řízením..." as="textarea" rows={3} />
                <FormItemRHF label="Poznámky k chování při řízení" name="drivingBehaviorNotes" control={form.control} placeholder="Pozorování stylu řízení, agresivity, atd..." as="textarea" rows={3} />
              </CardContent>
            </Card>

            {/* Fotodokumentace */}
            <Card className="p-4 shadow-sm">
              <CardHeader className="px-0 pt-0 pb-4">
                <CardTitle className="text-md flex items-center">
                  <ImageUp className="mr-2 h-4 w-4 text-primary/80" />
                  Fotodokumentace (ŘP, EKR, dokumenty)
                </CardTitle>
                <CardDescription>
                  Nahrajte a spravujte fotografie řidičského průkazu a souvisejících dokumentů.
                </CardDescription>
              </CardHeader>
              <CardContent className="px-0 pb-0">
                <PhotoArrayField 
                  control={form.control} 
                  photoFields={photoFields}
                  removePhoto={removePhoto}
                  uploadingPhotos={uploadingPhotos}
                  photoUrls={photoUrls}
                  handlePhotoUpload={handlePhotoUpload}
                  handlePaste={handlePaste}
                  getPlaceholderImage={getPlaceholderImage}
                  photoType="main"
                />
                
                <Button type="button" variant="outline" size="sm" onClick={addNewPhoto} className="mt-4">
                  <PlusCircle className="mr-2 h-4 w-4" /> Přidat záznam o fotografii
                </Button>
              </CardContent>
            </Card>

            {/* Poznámky */}
            <Card className="p-4 shadow-sm">
              <CardHeader className="px-0 pt-0 pb-4">
                <CardTitle className="text-md flex items-center">
                  <StickyNote className="mr-2 h-4 w-4 text-primary/80" />
                  Poznámky
                </CardTitle>
              </CardHeader>
              <CardContent className="px-0 pb-0 space-y-4">
                <FormItemRHF label="Poznámky vyšetřovatele" name="investigationNotes" control={form.control} placeholder="Poznámky a postřehy vyšetřovatele..." as="textarea" rows={3} />
                <FormItemRHF label="Obecné poznámky" name="notes" control={form.control} placeholder="Ostatní důležité informace..." as="textarea" rows={3} />
              </CardContent>
            </Card>
          </CardContent>
        </Card>

        <div className="flex justify-end pt-8 mt-8 border-t border-border">
          <Button type="submit" disabled={isSaving} className="w-full md:w-auto text-lg py-3 px-6 shadow-md hover:shadow-lg transition-shadow">
            {isSaving ? (
              <div className="flex items-center">
                <LoadingSpinner size="sm" className="mr-2 text-white" />
                Ukládání dat...
              </div>
            ) : (
              "Uložit data modulu"
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}

// Komponenta pro pole fotografií
interface PhotoArrayFieldProps {
  control: any;
  photoFields: any[];
  removePhoto: (index: number) => void;
  uploadingPhotos: Record<string, boolean>;
  photoUrls: Record<string, string>;
  handlePhotoUpload: (event: ChangeEvent<HTMLInputElement>, photoType: 'main' | 'offense', offenseIndex?: number, photoIndex?: number) => void;
  handlePaste: (event: React.ClipboardEvent, photoType: 'main' | 'offense', offenseIndex?: number, photoIndex?: number) => void;
  getPlaceholderImage: () => string;
  photoType: 'main' | 'offense';
  offenseIndex?: number;
}

const PhotoArrayField: React.FC<PhotoArrayFieldProps> = ({ 
  control, 
  photoFields,
  removePhoto,
  uploadingPhotos, 
  photoUrls, 
  handlePhotoUpload, 
  handlePaste, 
  getPlaceholderImage,
  photoType,
  offenseIndex
}) => {
  const movePhoto = useCallback((fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= photoFields.length) return;
    // Implementace přesunutí fotografií - pro jednoduchost zatím vynecháno
  }, [photoFields.length]);

  return (
    <div className="space-y-4">
      {photoFields.map((photoField, photoIndex) => {
        const uploadKey = photoType === 'main' 
          ? `main-${photoIndex}` 
          : `offense-${offenseIndex}-${photoIndex}`;
        
        return (
          <Card key={photoField.id} className="p-4 space-y-4 shadow-sm bg-card-foreground/5">
            <div className="flex justify-between items-center mb-2">
              <p className="font-semibold text-md">Fotografie {photoIndex + 1}</p>
              <div className="flex items-center gap-1">
                <Button 
                  type="button" 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => movePhoto(photoIndex, photoIndex - 1)}
                  disabled={photoIndex === 0}
                  className="h-8 w-8"
                  title="Posunout nahoru"
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
                <Button 
                  type="button" 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => movePhoto(photoIndex, photoIndex + 1)}
                  disabled={photoIndex === photoFields.length - 1}
                  className="h-8 w-8"
                  title="Posunout dolů"
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
                <Button 
                  type="button" 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => removePhoto(photoIndex)} 
                  className="text-destructive hover:bg-destructive/10 h-8 w-8" 
                  title="Smazat fotografii"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="flex flex-col md:flex-row items-start gap-6">
              <div className="relative">
                <img
                  src={photoUrls[uploadKey] || getPlaceholderImage()}
                  alt={`Náhled fotografie ${photoIndex + 1}`}
                  className="w-[250px] h-[250px] object-cover rounded-lg border-2 border-muted shadow-md bg-background mx-auto"
                  onError={(e) => (e.currentTarget.src = getPlaceholderImage())}
                />
                {uploadingPhotos[uploadKey] && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg">
                    <LoadingSpinner size="md" className="text-white" />
                  </div>
                )}
              </div>
              
              <div className="flex-grow space-y-4 w-full">
                <FormItemRHF 
                  label="Popis fotografie" 
                  name={photoType === 'main' ? `photos.${photoIndex}.description` : `offenses.${offenseIndex}.photos.${photoIndex}.description`}
                  control={control} 
                  placeholder="Popis fotografie..." 
                  as="textarea" 
                  rows={3}
                />
                <FormItemRHF 
                  label="Cesta k souboru (automaticky vyplněno)" 
                  name={photoType === 'main' ? `photos.${photoIndex}.downloadURL` : `offenses.${offenseIndex}.photos.${photoIndex}.downloadURL`}
                  control={control} 
                  disabled={true} 
                  placeholder="Automaticky se vyplní po nahrání fotografie"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <FormItemRHF 
                label="Datum pořízení" 
                name={photoType === 'main' ? `photos.${photoIndex}.dateTaken` : `offenses.${offenseIndex}.photos.${photoIndex}.dateTaken`}
                control={control} 
                type="text" 
                placeholder="Např. Leden 2023" 
              />
              <FormItemRHF 
                label="Název souboru" 
                name={photoType === 'main' ? `photos.${photoIndex}.fileName` : `offenses.${offenseIndex}.photos.${photoIndex}.fileName`}
                control={control} 
                placeholder="foto.jpg" 
              />
            </div>
            
            <FormItemRHF 
              label="URL zdroje (kde byla nalezena)" 
              name={photoType === 'main' ? `photos.${photoIndex}.sourceURL` : `offenses.${offenseIndex}.photos.${photoIndex}.sourceURL`}
              control={control} 
              placeholder="https://socialmedia.com/..." 
              type="text"
            />

            <div className="mt-4">
              <Label 
                htmlFor={`photo-upload-${photoType}-${offenseIndex || 0}-${photoIndex}`} 
                className={cn(
                  "flex items-center justify-center h-32 w-full border-2 border-dashed rounded-md transition-colors",
                  uploadingPhotos[uploadKey] 
                    ? "cursor-not-allowed border-muted bg-muted/30" 
                    : "cursor-pointer hover:border-primary/50 hover:bg-accent/30"
                )}
                onPaste={(e) => handlePaste(e, photoType, offenseIndex, photoIndex)}
                tabIndex={0}
              >
                <div className="flex flex-col items-center text-muted-foreground">
                  {uploadingPhotos[uploadKey] ? (
                    <>
                      <LoadingSpinner size="md" className="text-primary" />
                      <p className="text-sm mt-1">Ukládání fotografie na server...</p>
                    </>
                  ) : (
                    <>
                      <ImageUp className="h-8 w-8"/>
                      <p className="text-sm mt-1">Klikněte pro nahrání nebo stiskněte Ctrl+V</p>
                      <p className="text-xs text-muted-foreground/70 mt-1">Max. 10MB, formáty: JPEG, PNG, GIF, WEBP</p>
                    </>
                  )}
                </div>
              </Label>
              <Input
                id={`photo-upload-${photoType}-${offenseIndex || 0}-${photoIndex}`}
                type="file"
                accept="image/*"
                className="sr-only"
                onChange={(e) => handlePhotoUpload(e, photoType, offenseIndex, photoIndex)}
                disabled={uploadingPhotos[uploadKey]}
              />
            </div>
          </Card>
        );
      })}
    </div>
  );
};

// Helper komponenty pro react-hook-form s ShadCN
interface FormItemRHFProps {
  label: string;
  name: string;
  control: any;
  placeholder?: string;
  type?: string;
  disabled?: boolean;
  className?: string;
  as?: 'input' | 'textarea';
  rows?: number;
  smallLabel?: boolean;
}

const FormItemRHF = ({ label, name, control, placeholder, type = "text", disabled = false, className, as = 'input', rows, smallLabel = false }: FormItemRHFProps) => (
  <FormField
    control={control}
    name={name as any}
    disabled={disabled}
    render={({ field, fieldState: { error } }) => (
      <FormItem className={cn("w-full", className)}>
        <FormLabel className={cn(smallLabel ? "text-xs font-medium" : "font-semibold text-sm")}>{label}</FormLabel>
        <FormControl>
          {as === 'input' ? (
            <Input 
              name={field.name}
              ref={field.ref}
              value={field.value || ''} 
              onChange={field.onChange}
              onBlur={field.onBlur}
              placeholder={placeholder} 
              type={type} 
              className={cn("text-base md:text-sm", error ? "border-destructive focus-visible:ring-destructive" : "")} 
            />
          ) : (
            <Textarea 
              name={field.name}
              ref={field.ref}
              value={field.value || ''} 
              onChange={field.onChange}
              onBlur={field.onBlur}
              placeholder={placeholder} 
              rows={rows} 
              className={cn("text-base md:text-sm", error ? "border-destructive focus-visible:ring-destructive" : "")} 
            />
          )}
        </FormControl>
        {error && <FormMessage className={cn("mt-1", smallLabel ? "text-xs" : "text-xs")} />}
      </FormItem>
    )}
  />
);

interface FormItemSelectRHFProps {
  label: string;
  name: string;
  control: any;
  placeholder?: string;
  options: {value: string; label: string}[];
  disabled?: boolean;
  className?: string;
  smallLabel?: boolean;
}

const FormItemSelectRHF = ({ label, name, control, placeholder, options, disabled = false, className, smallLabel = false }: FormItemSelectRHFProps) => (
  <FormField
    control={control}
    name={name as any}
    disabled={disabled}
    render={({ field, fieldState: { error } }) => (
      <FormItem className={cn("w-full", className)}>
        <FormLabel className={cn(smallLabel ? "text-xs font-medium" : "font-semibold text-sm")}>{label}</FormLabel>
        <Select onValueChange={field.onChange} value={field.value || ''}>
          <FormControl>
            <SelectTrigger className={cn("w-full text-base md:text-sm", error ? "border-destructive focus:ring-destructive" : "")}>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
          </FormControl>
          <SelectContent>
            {options.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
          </SelectContent>
        </Select>
        {error && <FormMessage className={cn("mt-1", smallLabel ? "text-xs" : "text-xs")} />}
      </FormItem>
    )}
  />
);
