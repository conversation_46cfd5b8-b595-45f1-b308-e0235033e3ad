<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSINT Analýza</title>
    <link rel="stylesheet" href="assets/css/osint-styles.css">
    <link rel="stylesheet" href="assets/css/components/photo-gallery.css">
    <link rel="stylesheet" href="assets/css/components/messaging-platforms.css">
    <link rel="stylesheet" href="assets/css/components/timeline.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.css" />
    <link rel="stylesheet" href="https://unpkg.com/leaflet.markercluster@1.5.3/dist/leaflet.markercluster.css" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script src="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.js"></script>
    <script src="https://unpkg.com/leaflet.heat@0.2.0/dist/leaflet-heat.js"></script>
    <script src="https://unpkg.com/leaflet.markercluster@1.5.3/dist/leaflet.markercluster.js"></script>
    <script src="https://unpkg.com/exif-js"></script>
</head>
<body>
    <!-- Horní navigační lišta -->
    <div class="pdf-toolbar">
        <h1><i class="fas fa-file-alt"></i> OSINT Analytická platforma</h1>
        <div class="pdf-toolbar-actions">
            <button class="pdf-button" id="export-pdf-btn">
                <i class="fas fa-file-pdf"></i> Exportovat do PDF
            </button>
        </div>
    </div>

    <div class="document-container">
        <!-- Postranní panel s obsahem -->
        <div class="sidebar">
            <div class="sidebar-title">Obsah dokumentu</div>
            <ul class="toc-list" id="dynamic-toc">
                <!-- Obsah bude generován dynamicky -->
                <li>
                    <div class="toc-section expanded">
                        <i class="fas fa-chevron-right"></i> Úvodní strana
                    </div>
                </li>
                <li>
                    <div class="toc-section">
                        <i class="fas fa-chevron-right"></i> 1. Základní údaje
                    </div>
                    <div class="toc-modules">
                        <!-- Moduly budou generovány dynamicky -->
                    </div>
                </li>
                <li>
                    <div class="toc-section">
                        <i class="fas fa-chevron-right"></i> 2. Sociální sítě
                    </div>
                    <div class="toc-modules">
                        <!-- Moduly budou generovány dynamicky -->
                    </div>
                </li>
                <li>
                    <div class="toc-section">
                        <i class="fas fa-chevron-right"></i> 3. Vozidla a majetek
                    </div>
                    <div class="toc-modules">
                        <!-- Moduly budou generovány dynamicky -->
                    </div>
                </li>
                <li>
                    <div class="toc-section">
                        <i class="fas fa-chevron-right"></i> 4. Lokace
                    </div>
                    <div class="toc-modules">
                        <!-- Moduly budou generovány dynamicky -->
                    </div>
                </li>
                <li>
                    <div class="toc-section">
                        <i class="fas fa-chevron-right"></i> 5. Dokumenty a důkazy
                    </div>
                    <div class="toc-modules">
                        <!-- Moduly budou generovány dynamicky -->
                    </div>
                </li>
                <li>
                    <div class="toc-section">
                        <i class="fas fa-chevron-right"></i> 6. Závěr a doporučení
                    </div>
                    <div class="toc-modules">
                        <!-- Moduly budou generovány dynamicky -->
                    </div>
                </li>
            </ul>
        </div>

        <!-- Hlavní obsah dokumentu -->
        <div class="content-wrapper">
            <!-- První stránka -->
            <div class="page">
                <div class="header">
                    <div class="police-header">
                        <div class="police-left" contenteditable="true">POLICIE ČESKÉ REPUBLIKY<br>
                        Krajské ředitelství policie Pardubického kraje</div>
                        <div class="police-right">JID: <span id="document-jid" contenteditable="true" class="placeholder" data-placeholder="Vyplňte JID"></span></div>
                    </div>
                    <div class="department-info" contenteditable="true">
                        Odbor analytiky a kyber. kriminality<br>
                        Oddělení kybernetické kriminality<br>
                        Na Spravedlnosti 2516, 530 48 Pardubice
                    </div>
                    <div class="document-info">
                        <div class="left-info">
                            <div>Č. j. <span id="document-number" contenteditable="true" class="placeholder" data-placeholder="Doplňte č.j."></span></div>
                        </div>
                        <div class="right-info">
                            <div><span contenteditable="true">Pardubice</span> <span id="document-date" contenteditable="true">17. března 2025</span></div>
                            <div>Počet stran: <span id="page-count">13</span></div>
                        </div>
                    </div>
                </div>

                <div class="title-section">
                    <div class="document-title">
                        OSINT (Open Source <span class="intel-word">intelligence</span>)
                    </div>
                    <div class="subject-name" contenteditable="true" class="placeholder" data-placeholder="Jméno, příjmení, datum narození">
                        Jméno, příjmení, datum narození
                    </div>
                </div>

                <div class="protocol-section">
                    <div class="protocol-title">Cíl protokolu:</div>
                    <div class="protocol-text" contenteditable="true" class="placeholder" data-placeholder="Zadejte cíl protokolu...">
                        Provést komplexní OSINT (Open Source Intelligence) analýzu zájmové osoby <span id="protocol-jmeno">...</span>, narozen dne <span id="protocol-datum">...</span>, s trvalým pobytem v lokalitě <span id="protocol-bydliste">...</span>. Účelem je shromáždit a analyzovat veškeré dostupné informace z veřejně přístupných zdrojů k vytvoření uceleného profilu sledované osoby, který bude sloužit jako podklad pro další služební úkony.
                    </div>
                </div>

                <div class="warning-section">
                    <div class="warning-title">Důležité upozornění:</div>
                    <div class="protocol-text">
                        Tento OSINT průzkum využívá kombinovaný přístup zahrnující:
                        <ul>
                            <li>Systematické vyhledávání v interních informačních systémech Policie ČR</li>
                            <li>Komplexní prověření veškerých dostupných zdrojů v otevřeném prostoru Internet</li>
                        </ul>
                        Průzkum klade důraz na získání maximálního množství relevantních informací z otevřených zdrojů při současném zachování operativní bezpečnosti a eliminaci možnosti odhalení zájmu o sledovanou osobu.
                    </div>
                </div>
            </div>

            <!-- Sekce Základní údaje -->
            <div class="continuous-page">
                <div class="section-title">
                    <div class="section-number">1</div>
                    <h2>Základní údaje</h2>
                </div>

                <div class="section-content" id="section-basic-info">
                    <!-- Zde budou přidávány moduly -->

                    <!-- Kontejner pro přidání modulu -->
                    <div class="add-module-container">
                        <button class="add-module-btn" type="button">
                            <i class="fas fa-plus-circle"></i> Přidat modul základních údajů
                        </button>
                        <div class="module-picker">
                            <!-- Sem se dynamicky přidají možnosti modulů -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sekce Sociální sítě -->
            <div class="continuous-page">
                <div class="section-title">
                    <div class="section-number">2</div>
                    <h2>Sociální sítě</h2>
                </div>

                <div class="section-content" id="section-social-networks">
                    <!-- Zde budou přidávány moduly sociálních sítí -->

                    <!-- Kontejner pro přidání modulu -->
                    <div class="add-module-container">
                        <button class="add-module-btn" type="button">
                            <i class="fas fa-plus-circle"></i> Přidat sociální síť
                        </button>
                        <div class="module-picker">
                            <!-- Sem se dynamicky přidají možnosti modulů -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sekce Vozidla a majetek -->
            <div class="continuous-page">
                <div class="section-title">
                    <div class="section-number">3</div>
                    <h2>Vozidla a majetek</h2>
                </div>

                <div class="section-content" id="section-vehicles-property">
                    <!-- Zde budou přidávány moduly vozidel a majetku -->

                    <!-- Kontejner pro přidání modulu -->
                    <div class="add-module-container">
                        <button class="add-module-btn" type="button">
                            <i class="fas fa-plus-circle"></i> Přidat modul vozidel a majetku
                        </button>
                        <div class="module-picker">
                            <!-- Sem se dynamicky přidají možnosti modulů -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sekce Lokace -->
            <div class="continuous-page">
                <div class="section-title">
                    <div class="section-number">4</div>
                    <h2>Lokace</h2>
                </div>

                <div class="section-content" id="section-locations">
                    <!-- Zde budou přidávány moduly lokací -->

                    <!-- Kontejner pro přidání modulu -->
                    <div class="add-module-container">
                        <button class="add-module-btn" type="button">
                            <i class="fas fa-plus-circle"></i> Přidat lokaci
                        </button>
                        <div class="module-picker">
                            <!-- Sem se dynamicky přidají možnosti modulů -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sekce Dokumenty -->
            <div class="continuous-page">
                <div class="section-title">
                    <div class="section-number">5</div>
                    <h2>Dokumenty a důkazy</h2>
                </div>

                <div class="section-content" id="section-documents">
                    <!-- Zde budou přidávány moduly dokumentů -->

                    <!-- Kontejner pro přidání modulu -->
                    <div class="add-module-container">
                        <button class="add-module-btn" type="button">
                            <i class="fas fa-plus-circle"></i> Přidat dokument nebo důkaz
                        </button>
                        <div class="module-picker">
                            <!-- Sem se dynamicky přidají možnosti modulů -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sekce Závěr a doporučení -->
            <div class="continuous-page">
                <div class="section-title">
                    <div class="section-number">6</div>
                    <h2>Závěr a doporučení</h2>
                </div>

                <div class="section-content" id="section-conclusion">
                    <div class="module">
                        <div class="module-header">
                            <div class="module-icon"><i class="fas fa-file-alt"></i></div>
                            <h3>Závěrečné shrnutí</h3>
                        </div>
                        <div class="module-content">
                            <div class="form-group">
                                <label>Shrnutí zjištěných informací</label>
                                <textarea class="form-control" rows="6" placeholder="Shrňte hlavní zjištění a výsledky OSINT analýzy..."></textarea>
                            </div>

                            <div class="form-group">
                                <label>Riziková zjištění</label>
                                <textarea class="form-control" rows="6" placeholder="Uveďte zjištění, která představují potenciální rizika..."></textarea>
                            </div>

                            <div class="form-group">
                                <label>Doporučení dalšího postupu</label>
                                <textarea class="form-control" rows="6" placeholder="Doporučte další kroky, které by měly být podniknuty..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Core scripts -->
    <script src="assets/js/core/osint-modules.js"></script>

    <!-- Component scripts -->
    <script src="assets/js/components/photo-gallery/photo-gallery-functions.js"></script>
    <script src="assets/js/components/photo-gallery/photo-analysis.js"></script>
    <script src="assets/js/components/photo-gallery/photo-analysis-functions.js"></script>
    <script src="assets/js/components/photo-gallery/photo-analysis-advanced.js"></script>

    <script src="assets/js/components/map/map-overlays.js"></script>
    <script src="assets/js/components/map/map-analysis.js"></script>
    <script src="assets/js/components/map/map-export.js"></script>
    <script src="assets/js/components/map/map-photo-gallery.js"></script>

    <script src="assets/js/components/news/news-monitoring.js"></script>
    <script src="assets/js/components/news/news-fetching.js"></script>
    <script src="assets/js/components/news/news-analysis.js"></script>
    <script src="assets/js/components/news/news-detail.js"></script>
    <script src="assets/js/components/news/news-gallery.js"></script>
    <script src="assets/js/components/news/news-translation.js"></script>

    <script src="assets/js/components/messaging/messaging-platforms.js"></script>
    <script src="assets/js/components/messaging/messaging-platforms-api.js"></script>
    <script src="assets/js/components/messaging/messaging-platforms-ui.js"></script>
    <script src="assets/js/components/messaging/messaging-platforms-utils.js"></script>
    <script src="assets/js/components/messaging/messaging-platforms-actions.js"></script>
    <script src="assets/js/components/messaging/messaging-platforms-media.js"></script>

    <script src="assets/js/components/timeline/timeline.js"></script>
    <script src="assets/js/components/timeline/timeline-events.js"></script>
    <script src="assets/js/components/timeline/timeline-utils.js"></script>
    <script src="assets/js/components/timeline/timeline-analysis.js"></script>
    <script src="assets/js/components/timeline/timeline-helpers.js"></script>

    <script src="assets/js/components/correlation/correlation-tool.js"></script>
    <script src="assets/js/components/search/complex-search.js"></script>

    <!-- Analysis scripts -->
    <script src="assets/js/analysis/email-analysis.js"></script>
    <script src="assets/js/analysis/phone-analysis.js"></script>
    <script src="assets/js/analysis/ip-analysis.js"></script>
    <script src="assets/js/analysis/financial-monitoring.js"></script>
    <script src="assets/js/analysis/financial-analysis.js"></script>
    <script src="assets/js/analysis/property-search.js"></script>

    <!-- Utility scripts -->
    <script src="assets/js/utils/fix-phone-module-final.js"></script>

    <!-- Additional styles -->
    <link rel="stylesheet" href="assets/css/components/correlation-tool.css">
    <link rel="stylesheet" href="assets/css/components/complex-search.css">
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Inicializace interaktivních prvků obsahu
            initTocInteractions();

            // Inicializace exportu do PDF
            initPdfExport();
        });

        // Inicializace interakcí obsahu
        function initTocInteractions() {
            // Přidání event listenerů na sekce v obsahu
            document.querySelectorAll('.toc-section').forEach(section => {
                section.addEventListener('click', function() {
                    const modules = this.nextElementSibling;
                    if (modules && modules.classList.contains('toc-modules')) {
                        modules.classList.toggle('expanded');
                        this.classList.toggle('expanded');
                    }

                    // Scroll k sekci
                    const sectionNumber = this.textContent.trim().split('.')[0];
                    const targetSection = document.querySelector(`.section-number:contains('${sectionNumber}')`);
                    if (targetSection) {
                        targetSection.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            });

            // Funkce pro aktualizaci obsahu při přidání nového modulu
            window.updateTableOfContents = function() {
                // Projít všechny sekce a moduly
                document.querySelectorAll('.section-content').forEach((section, index) => {
                    const sectionNumber = index + 1;
                    const modules = section.querySelectorAll('.module');
                    const tocModules = document.querySelectorAll('.toc-list li')[index].querySelector('.toc-modules');

                    // Vyčistit předchozí obsah
                    if (tocModules) {
                        tocModules.innerHTML = '';

                        // Přidat moduly do obsahu
                        modules.forEach(module => {
                            const moduleTitle = module.querySelector('.module-header h3').textContent;
                            const moduleIcon = module.querySelector('.module-icon i').className;

                            const moduleItem = document.createElement('div');
                            moduleItem.className = 'toc-module';
                            moduleItem.innerHTML = `<i class="${moduleIcon}"></i> ${moduleTitle}`;

                            moduleItem.addEventListener('click', function() {
                                module.scrollIntoView({ behavior: 'smooth' });
                            });

                            tocModules.appendChild(moduleItem);
                        });
                    }
                });
            };
        }

        // Inicializace exportu do PDF
        function initPdfExport() {
            document.getElementById('export-pdf-btn').addEventListener('click', function() {
                // Připravit data pro export
                prepareDataForPdfExport();

                // Upravit styly pro tisk
                const originalStyles = document.body.style.cssText;
                document.body.style.cssText = 'background: white !important; margin: 0 !important;';

                // Skrýt navigaci a další nepotřebné prvky
                const toolbar = document.querySelector('.pdf-toolbar');
                const sidebar = document.querySelector('.sidebar');
                const originalToolbarDisplay = toolbar.style.display;
                const originalSidebarDisplay = sidebar.style.display;
                toolbar.style.display = 'none';
                sidebar.style.display = 'none';

                // Nastavit wrapper na plnou šířku
                const wrapper = document.querySelector('.content-wrapper');
                const originalWrapperMargin = wrapper.style.marginLeft;
                wrapper.style.marginLeft = '0';
                wrapper.style.maxWidth = 'none';

                // Přidat třídu pro tisk
                document.body.classList.add('print-mode');

                // Vytvořit PDF
                window.print();

                // Obnovit původní styly
                document.body.style.cssText = originalStyles;
                toolbar.style.display = originalToolbarDisplay;
                sidebar.style.display = originalSidebarDisplay;
                wrapper.style.marginLeft = originalWrapperMargin;
                wrapper.style.maxWidth = '210mm';
                document.body.classList.remove('print-mode');
            });
        }

        // Připraví data pro export do PDF
        function prepareDataForPdfExport() {
            // Projít všechny formulářové prvky a nastavit data atributy pro tisk
            document.querySelectorAll('input, select, textarea').forEach(input => {
                const formGroup = input.closest('.form-group');
                if (formGroup) {
                    const label = formGroup.querySelector('label');
                    if (label) {
                        formGroup.setAttribute('data-label', label.textContent);
                    }

                    // Nastavit hodnotu dle typu vstupu
                    if (input.tagName === 'SELECT') {
                        const selectedOption = input.options[input.selectedIndex];
                        formGroup.setAttribute('data-value', selectedOption ? selectedOption.textContent : '');
                    } else if (input.tagName === 'TEXTAREA') {
                        formGroup.setAttribute('data-value', input.value.replace(/\n/g, '<br>'));
                    } else {
                        formGroup.setAttribute('data-value', input.value);
                    }
                }
            });

            // Speciální zpracování pro contenteditable prvky
            document.querySelectorAll('[contenteditable]').forEach(el => {
                el.setAttribute('data-print-content', el.innerHTML);
            });

            // Převést dynamické prvky na statický text
            formatDynamicElements();
        }

        // Formátování dynamických prvků pro tisk
        function formatDynamicElements() {
            // Převést dynamicky přidané bydliště, telefony a doklady do textové podoby
            formatAddedAddresses();
            formatAddedPhones();
            formatAddedDocuments();
        }

        // Formátování přidaných adres
        function formatAddedAddresses() {
            const container = document.getElementById('prechodna-bydliste');
            if (container && container.children.length > 0) {
                const addressesDiv = document.createElement('div');
                addressesDiv.className = 'print-addresses';
                addressesDiv.innerHTML = '<h4>Přechodná bydliště</h4>';

                const addressesList = document.createElement('ul');
                Array.from(container.children).forEach(addressItem => {
                    const input = addressItem.querySelector('input');
                    if (input && input.value.trim()) {
                        const li = document.createElement('li');
                        li.textContent = input.value;
                        addressesList.appendChild(li);
                    }
                });

                if (addressesList.children.length > 0) {
                    addressesDiv.appendChild(addressesList);
                    container.parentNode.insertBefore(addressesDiv, container.nextSibling);
                    addressesDiv.setAttribute('data-print-only', 'true');
                }
            }
        }

        // Formátování přidaných telefonů
        function formatAddedPhones() {
            const container = document.getElementById('telefonni-cisla');
            if (container && container.children.length > 0) {
                const phonesDiv = document.createElement('div');
                phonesDiv.className = 'print-phones';
                phonesDiv.innerHTML = '<h4>Telefonní čísla</h4>';

                const phonesList = document.createElement('ul');
                Array.from(container.children).forEach(phoneItem => {
                    const phoneInput = phoneItem.querySelector('input[placeholder="Zadejte telefonní číslo"]');
                    const typeSelect = phoneItem.querySelector('select');

                    if (phoneInput && phoneInput.value.trim() && typeSelect) {
                        const li = document.createElement('li');
                        const selectedType = typeSelect.options[typeSelect.selectedIndex].textContent;
                        li.textContent = `${phoneInput.value} (${selectedType})`;
                        phonesList.appendChild(li);
                    }
                });

                if (phonesList.children.length > 0) {
                    phonesDiv.appendChild(phonesList);
                    container.parentNode.insertBefore(phonesDiv, container.nextSibling);
                    phonesDiv.setAttribute('data-print-only', 'true');
                }
            }
        }

        // Formátování přidaných dokladů
        function formatAddedDocuments() {
            const container = document.getElementById('dalsi-doklady');
            if (container && container.children.length > 0) {
                const documentsDiv = document.createElement('div');
                documentsDiv.className = 'print-documents';
                documentsDiv.innerHTML = '<h4>Další doklady</h4>';

                const documentsList = document.createElement('ul');
                Array.from(container.children).forEach(docItem => {
                    const typeSelect = docItem.querySelector('select');
                    const numberInput = docItem.querySelector('input[placeholder="Zadejte číslo dokladu"]');
                    const issuerInput = docItem.querySelector('input[placeholder="Kdo doklad vydal"]');
                    const validityInput = docItem.querySelector('input[placeholder*="Platnost"]');

                    if (typeSelect && numberInput && numberInput.value.trim()) {
                        const li = document.createElement('li');
                        const selectedType = typeSelect.options[typeSelect.selectedIndex].textContent;
                        let docText = `${selectedType}: ${numberInput.value}`;

                        if (issuerInput && issuerInput.value.trim()) {
                            docText += `, vydal: ${issuerInput.value}`;
                        }

                        if (validityInput && validityInput.value.trim()) {
                            docText += `, platnost: ${validityInput.value}`;
                        }

                        li.textContent = docText;
                        documentsList.appendChild(li);
                    }
                });

                if (documentsList.children.length > 0) {
                    documentsDiv.appendChild(documentsList);
                    container.parentNode.insertBefore(documentsDiv, container.nextSibling);
                    documentsDiv.setAttribute('data-print-only', 'true');
                }
            }
        }
    </script>
</body>
</html>