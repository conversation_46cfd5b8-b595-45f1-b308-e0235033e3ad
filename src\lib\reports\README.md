# OSINT Report Generation System

Tento systém umožňuje generování komplexních OSINT reportů v různých formátech (HTML, Word, PDF) na základě dat shromážděných v modulech aplikace.

## Struktura

### Hlavní komponenty

1. **ReportGenerator** (`/components/reports/ReportGenerator.tsx`)
   - React komponenta pro uživatelské rozhraní
   - Umožňuje výběr modulů a jejich pořadí
   - Konfigurace základních informací reportu

2. **ReportGenerator** (`/lib/reports/ReportGenerator.ts`)
   - Hlavní třída pro orchestraci generování reportů
   - Koordinuje sběr dat a generování výstupu

3. **DataCollector** (`/lib/reports/DataCollector.ts`)
   - Sběr dat z Firebase databáze
   - Transformace dat do jednotného formátu
   - Kombinování dat ze všech subjektů případu

4. **Generátory**
   - **HTMLGenerator** - Generuje interaktivní HTML reporty
   - **WordGenerator** - Generuje Word dokumenty (zatím mock)
   - **PDFGenerator** - Generuje PDF dokumenty (zatím mock)

5. **ReportTemplate** (`/lib/reports/templates/ReportTemplate.ts`)
   - Šablony pro jednotlivé moduly
   - HTML struktura pro každý typ dat

## Použití

### Základní použití

```tsx
import ReportGenerator from '@/components/reports/ReportGenerator';

<ReportGenerator 
  caseId={caseId} 
  availableModules={[
    { 
      id: 'personal-info', 
      name: 'Osobní údaje', 
      category: 'Základní údaje', 
      icon: 'fas fa-user', 
      hasData: true 
    },
    // další moduly...
  ]}
/>
```

### API Endpoint

```typescript
POST /api/reports/generate
{
  "caseId": "string",
  "reportType": "full" | "partial",
  "settings": {
    "title": "OSINT (Open Source Intelligence)",
    "subject": "Jméno, příjmení, datum narození",
    "jid": "JID číslo",
    "documentNumber": "Číslo jednací",
    "location": "Pardubice",
    "date": "datum",
    "department": "Odbor...",
    "purpose": "Cíl protokolu",
    "selectedModules": ["module1", "module2"],
    "moduleOrder": ["module1", "module2"],
    "format": "html" | "word" | "pdf"
  }
}
```

## Mapování modulů

Systém mapuje moduly z Firebase na reportové moduly:

- `evidence_obyvatel` → `personal-info`
- `business_activity` → `company`
- `cadastre` → `real-estate`
- `training` → `training`
- `email_analysis` → `email-analysis`
- `phone_numbers` → `phone-numbers`
- `network_analysis` → `ip-addresses`
- `map_overlays` → `map-overlays`

## Struktura první stránky

Reporty zachovávají strukturu z NEW složky:

1. **Hlavička Policie ČR**
   - Logo a název
   - JID číslo
   - Odbor a kontaktní údaje

2. **Základní informace**
   - Číslo jednací
   - Místo a datum
   - Počet stran

3. **Název dokumentu**
   - "OSINT (Open Source Intelligence)"
   - Předmět analýzy

4. **Cíl protokolu**
   - Účel provedení analýzy

5. **Důležité upozornění**
   - Metodika a bezpečnostní aspekty

## Rozšíření

### Přidání nového modulu

1. Přidejte modul do `availableModules` v komponentě
2. Vytvořte extract metodu v `DataCollector`
3. Přidejte template metodu v `ReportTemplate`
4. Aktualizujte mapování v generátorech

### Přidání nového formátu

1. Vytvořte nový generátor v `/generators/`
2. Implementujte `generate` metodu
3. Přidejte do `ReportGenerator.ts`
4. Aktualizujte API endpoint

## Bezpečnost

- Všechna data jsou validována před zpracováním
- Přístup pouze pro autorizované uživatele
- Logování všech generovaných reportů
- Ochrana citlivých informací

## Budoucí vylepšení

1. **Skutečné Word/PDF generování**
   - Implementace pomocí docx/puppeteer
   - Zachování formátování

2. **Pokročilé šablony**
   - Více stylů reportů
   - Customizace vzhledu

3. **Export dat**
   - JSON/XML export
   - API pro externí systémy

4. **Analytické funkce**
   - Grafy a vizualizace
   - Statistiky případů
