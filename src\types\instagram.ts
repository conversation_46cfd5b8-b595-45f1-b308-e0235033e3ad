import { Timestamp } from "firebase/firestore";

export interface InstagramPost {
  id: string;
  imageUrl: string;
  imageFile?: File;
  imageBase64?: string; // Pro uklá<PERSON>án<PERSON> nahran<PERSON>ch obrázků
  caption?: string;
  date: string | Timestamp;
  likes: number;
  comments: number;
  location?: string;
  locationUrl?: string;
  hashtags?: string[];
  taggedUsers?: string[];
  isVideo?: boolean;
  videoUrl?: string;
  videoFile?: File;
  videoBase64?: string;
  isSaved?: boolean;
  isSponsored?: boolean;
  isReels?: boolean;
  isIGTV?: boolean;
  engagement?: number; // <PERSON><PERSON><PERSON> zap<PERSON> (engagement rate)
  commenters?: string[]; // Seznam komentujících
  topComments?: {
    username: string;
    text: string;
    likes: number;
    date: string | Timestamp;
  }[];
  filters?: string; // Použité filtry
  musicTrack?: {
    title: string;
    artist: string;
    url?: string;
  };
}

export interface InstagramStory {
  id: string;
  imageUrl: string;
  imageFile?: File;
  imageBase64?: string;
  date: string | Timestamp;
  expiresAt: string | Timestamp;
  location?: string;
  locationUrl?: string;
  hashtags?: string[];
  taggedUsers?: string[];
  isVideo?: boolean;
  videoUrl?: string;
  videoFile?: File;
  videoBase64?: string;
  viewers?: number;
  viewersList?: string[];
  stickers?: string[];
  polls?: {
    question: string;
    options: string[];
    results?: number[];
  }[];
  questions?: {
    question: string;
    answers?: string[];
  }[];
  links?: string[];
  musicTrack?: {
    title: string;
    artist: string;
    url?: string;
  };
}

export interface InstagramFollower {
  id: string;
  username: string;
  profileUrl: string;
  imageUrl?: string;
  imageFile?: File;
  imageBase64?: string;
  isVerified?: boolean;
  isPrivate?: boolean;
  fullName?: string;
  bio?: string;
  followingSince?: string | Timestamp;
  mutualFollowers?: number;
  mutualFollowersList?: string[];
  postsCount?: number;
  followersCount?: number;
  followingCount?: number;
  isBusinessAccount?: boolean;
  category?: string;
  contactInfo?: {
    email?: string;
    phone?: string;
    website?: string;
  };
  notes?: string;
}

export interface InstagramHighlight {
  id: string;
  title: string;
  coverUrl: string;
  coverFile?: File;
  coverBase64?: string;
  stories: InstagramStory[];
  date?: string | Timestamp;
  description?: string;
}

export interface InstagramReel {
  id: string;
  videoUrl: string;
  videoFile?: File;
  videoBase64?: string;
  thumbnailUrl?: string;
  thumbnailFile?: File;
  thumbnailBase64?: string;
  caption?: string;
  date: string | Timestamp;
  views: number;
  likes: number;
  comments: number;
  shares?: number;
  hashtags?: string[];
  taggedUsers?: string[];
  musicTrack?: {
    title: string;
    artist: string;
    url?: string;
  };
  duration?: number; // v sekundách
}

export interface InstagramSavedCollection {
  id: string;
  name: string;
  coverUrl?: string;
  coverFile?: File;
  coverBase64?: string;
  postsCount: number;
  isPrivate: boolean;
  posts?: InstagramPost[];
}

export interface InstagramModuleData {
  subjectId: string;
  profileUrl?: string;
  username?: string;
  displayName?: string;
  profileImageUrl?: string;
  profileImageFile?: File;
  profileImageBase64?: string;
  isVerified?: boolean;
  isPrivate?: boolean;
  isBusinessAccount?: boolean;
  isProfessionalAccount?: boolean;
  postsCount?: number;
  followersCount?: number;
  followingCount?: number;
  bio?: string;
  website?: string;
  websiteUrl?: string;
  category?: string;
  pronouns?: string;
  fullName?: string;
  joinDate?: string | Timestamp;
  lastActivity?: string | Timestamp;
  contactInfo?: {
    email?: string;
    phone?: string;
    address?: string;
    businessAddress?: string;
    businessPhone?: string;
    businessEmail?: string;
  };
  linkedAccounts?: {
    facebook?: string;
    twitter?: string;
    youtube?: string;
    tiktok?: string;
    other?: string[];
  };
  location?: string;
  recentLocations?: string[];
  frequentLocations?: {
    location: string;
    frequency: number;
  }[];
  posts?: InstagramPost[];
  stories?: InstagramStory[];
  highlights?: InstagramHighlight[];
  reels?: InstagramReel[];
  savedCollections?: InstagramSavedCollection[];
  followers?: InstagramFollower[];
  following?: InstagramFollower[];
  closeFriends?: InstagramFollower[];
  blockedAccounts?: InstagramFollower[];
  restrictedAccounts?: InstagramFollower[];
  taggedPosts?: InstagramPost[];
  mentionedIn?: InstagramPost[];
  activityPatterns?: {
    mostActiveDay?: string;
    mostActiveTime?: string;
    postFrequency?: string;
    commentFrequency?: string;
  };
  engagementRate?: number;
  topHashtags?: {
    hashtag: string;
    count: number;
  }[];
  topLocations?: {
    location: string;
    count: number;
  }[];
  topInteractions?: {
    username: string;
    count: number;
  }[];
  notes?: string;
  securityRisks?: string[];
  osintNotes?: string;
  lastUpdatedAt: Timestamp | null;
  createdAt: Timestamp | null;
}
