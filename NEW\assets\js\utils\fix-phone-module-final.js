/**
 * Finální oprava modulu telefonní analýzy
 * Tento skript nahradí hlavičku modulu telefonní analýzy přesně stejn<PERSON> hlav<PERSON>, jakou ma<PERSON><PERSON> o<PERSON> moduly
 */

document.addEventListener('DOMContentLoaded', function() {
    // Počkat na načtení všech ostatních skriptů
    setTimeout(fixPhoneModuleHeaders, 1000);
});

/**
 * Oprava hlaviček modulů telefonní analýzy
 */
function fixPhoneModuleHeaders() {
    console.log('Spouštím finální opravu hlaviček modulů telefonní analýzy');
    
    // Najít všechny moduly telefonní analýzy
    const phoneModules = Array.from(document.querySelectorAll('.module')).filter(module => {
        const header = module.querySelector('.module-header');
        if (!header) return false;
        
        const moduleIcon = header.querySelector('.module-icon i');
        const moduleTitle = header.querySelector('h3');
        
        return moduleIcon && moduleTitle && 
               moduleIcon.className.includes('fa-phone') && 
               moduleTitle.textContent.includes('Telefonní čísla');
    });
    
    console.log(`Nalezeno ${phoneModules.length} modulů telefonní analýzy`);
    
    // Opravit každý modul
    phoneModules.forEach((module, index) => {
        console.log(`Opravuji modul telefonní analýzy #${index + 1}`);
        
        // Odstranit starou hlavičku
        const oldHeader = module.querySelector('.module-header');
        if (!oldHeader) return;
        
        // Vytvořit novou hlavičku přesně podle vzoru z osint-modules.js
        const newHeader = document.createElement('div');
        newHeader.className = 'module-header';
        newHeader.innerHTML = `
            <div class="module-icon"><i class="fas fa-phone"></i></div>
            <h3>Telefonní čísla</h3>
            <div class="module-actions">
                <button type="button" class="module-action-btn move-up" title="Posunout nahoru">
                    <i class="fas fa-arrow-up"></i>
                </button>
                <button type="button" class="module-action-btn move-down" title="Posunout dolů">
                    <i class="fas fa-arrow-down"></i>
                </button>
                <button type="button" class="module-action-btn delete" title="Odstranit modul">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        
        // Nahradit starou hlavičku novou
        oldHeader.parentNode.replaceChild(newHeader, oldHeader);
        
        // Přidat event listenery pro tlačítka
        initModuleControlsForPhone(module);
    });
}

/**
 * Inicializace ovládacích prvků modulu telefonní analýzy
 * Přesná kopie funkce initModuleControls z osint-modules.js
 */
function initModuleControlsForPhone(moduleElement) {
    // Tlačítko pro smazání modulu
    moduleElement.querySelector('.delete').addEventListener('click', function() {
        if (confirm('Opravdu chcete odstranit tento modul?')) {
            moduleElement.remove();
            if (typeof updateTableOfContents === 'function') {
                updateTableOfContents();
            }
        }
    });

    // Tlačítko pro posun nahoru
    moduleElement.querySelector('.move-up').addEventListener('click', function() {
        const prevModule = moduleElement.previousElementSibling;
        if (prevModule && !prevModule.classList.contains('add-module-container')) {
            moduleElement.parentNode.insertBefore(moduleElement, prevModule);
            if (typeof updateTableOfContents === 'function') {
                updateTableOfContents();
            }
        }
    });

    // Tlačítko pro posun dolů
    moduleElement.querySelector('.move-down').addEventListener('click', function() {
        const nextModule = moduleElement.nextElementSibling;
        if (nextModule) {
            moduleElement.parentNode.insertBefore(nextModule, moduleElement);
            if (typeof updateTableOfContents === 'function') {
                updateTableOfContents();
            }
        }
    });
}
