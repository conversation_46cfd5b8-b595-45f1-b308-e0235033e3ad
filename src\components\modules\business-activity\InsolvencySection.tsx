"use client";

import { useState } from "react";
import { useFieldA<PERSON>y, Control } from "react-hook-form";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, PlusCircle, Trash2, FileText, Building, Calendar } from "lucide-react";
import { FormItemRHF, FormItemSelectRHF } from "./FormComponents";
import { InsolvencyRecord } from "./schemas";
import { cn } from "@/lib/utils";
import type { BusinessActivityFormValues } from "./BusinessActivityForm";

interface InsolvencySectionProps {
  control: Control<BusinessActivityFormValues>;
}

const insolvencyStatusOptions = [
  { value: "yes", label: "Ano" },
  { value: "no", label: "Ne" },
  { value: "unknown", label: "Nezjišt<PERSON><PERSON>" },
];

const insolvencyTypeOptions = [
  { value: "insolvency", label: "Insolvence" },
  { value: "execution", label: "Exekuce" },
  { value: "bankruptcy", label: "Konkurz" },
  { value: "restructuring", label: "Restrukturalizace" },
  { value: "other", label: "Jiné" },
];

const insolvencyStatusOptions2 = [
  { value: "active", label: "Aktivní" },
  { value: "completed", label: "Ukončeno" },
  { value: "rejected", label: "Zamítnuto" },
  { value: "suspended", label: "Pozastaveno" },
  { value: "other", label: "Jiný stav" },
];

export function InsolvencySection({ control }: InsolvencySectionProps) {
  const [expanded, setExpanded] = useState(false);

  const { fields, append, remove } = useFieldArray({
    control,
    name: "insolvencyRecords",
  });

  const handleAddInsolvency = () => {
    append({
      id: crypto.randomUUID(),
      type: "insolvency",
      status: "active",
      creditors: [],
    });
  };

  return (
    <Card className={cn(expanded ? "" : "hover:border-primary/50 cursor-pointer transition-all")}>
      <CardHeader 
        className="flex flex-row items-center justify-between"
        onClick={() => setExpanded(!expanded)}
      >
        <CardTitle className="text-lg flex items-center">
          <AlertTriangle className="mr-2 h-5 w-5" />
          Insolvence a exekuce
        </CardTitle>
        <Badge variant={fields.length > 0 ? "default" : "outline"}>
          {fields.length} záznamů
        </Badge>
      </CardHeader>
      {expanded && (
        <CardContent className="space-y-6 pt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItemSelectRHF
              label="Evidována insolvence/exekuce"
              name="insolvencyStatus"
              control={control}
              options={insolvencyStatusOptions}
              placeholder="-- Vyberte --"
            />
          </div>

          <FormItemRHF
            label="Obecné informace o insolvenci/exekuci"
            name="insolvencyDetails"
            control={control}
            as="textarea"
            rows={3}
            placeholder="Obecné informace o insolvenci nebo exekuci subjektu..."
          />

          {fields.map((item, index) => (
            <Card key={item.id} className="p-4 shadow-sm bg-card-foreground/5 relative">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => remove(index)}
                className="absolute top-2 right-2 text-destructive hover:bg-destructive/10"
              >
                <Trash2 className="h-5 w-5" />
              </Button>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormItemSelectRHF
                  label="Typ řízení"
                  name={`insolvencyRecords.${index}.type`}
                  control={control}
                  options={insolvencyTypeOptions}
                  placeholder="-- Vyberte typ --"
                />
                <FormItemRHF
                  label="Spisová značka"
                  name={`insolvencyRecords.${index}.fileNumber`}
                  control={control}
                  placeholder="Např. MSPH 94 INS 12345/2023"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormItemRHF
                  label="Soud"
                  name={`insolvencyRecords.${index}.court`}
                  control={control}
                  placeholder="Příslušný soud"
                />
                <FormItemSelectRHF
                  label="Stav řízení"
                  name={`insolvencyRecords.${index}.status`}
                  control={control}
                  options={insolvencyStatusOptions2}
                  placeholder="-- Vyberte stav --"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <FormItemRHF
                  label="Datum zahájení"
                  name={`insolvencyRecords.${index}.startDate`}
                  control={control}
                  type="date"
                />
                <FormItemRHF
                  label="Datum ukončení"
                  name={`insolvencyRecords.${index}.endDate`}
                  control={control}
                  type="date"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <FormItemRHF
                  label="Částka"
                  name={`insolvencyRecords.${index}.amount`}
                  control={control}
                  type="number"
                  placeholder="Vymáhaná částka"
                  className="md:col-span-2"
                />
                <FormItemRHF
                  label="Měna"
                  name={`insolvencyRecords.${index}.currency`}
                  control={control}
                  placeholder="CZK"
                />
              </div>

              <FormItemRHF
                label="Věřitelé"
                name={`insolvencyRecords.${index}.creditors`}
                control={control}
                as="textarea"
                rows={2}
                placeholder="Seznam věřitelů (jeden na řádek)"
              />

              <FormItemRHF
                label="Popis"
                name={`insolvencyRecords.${index}.description`}
                control={control}
                as="textarea"
                rows={3}
                placeholder="Detailní popis insolvence/exekuce..."
              />

              <FormItemRHF
                label="Poznámky"
                name={`insolvencyRecords.${index}.notes`}
                control={control}
                as="textarea"
                rows={2}
                placeholder="Další poznámky k insolvenci/exekuci..."
              />
            </Card>
          ))}

          <Button
            type="button"
            variant="outline"
            onClick={handleAddInsolvency}
            className="w-full"
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Přidat insolvenci/exekuci
          </Button>
        </CardContent>
      )}
    </Card>
  );
}
