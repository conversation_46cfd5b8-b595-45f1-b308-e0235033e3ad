"use client";

import { useState, useMemo } from "react";
import { useForm, useFieldArray, Control, FieldValues, FieldPath } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { db } from "@/lib/firebase";
import { doc, setDoc, Timestamp } from "firebase/firestore";
import { LoadingSpinner } from "@/components/ui/loading";
import { 
  Facebook, 
  Users, 
  MessageSquare, 
  Calendar, 
  Star,
  StarOff,
  PlusCircle,
  Trash2,
  Copy,
  Check,
  Expand,
  Minimize2,
  ImageIcon
} from "lucide-react";
import { FacebookModuleData, FacebookFriend, FacebookPost, FacebookEvent } from "@/types/facebook";
import { PhotoDocumentationSection } from "./PhotoDocumentationSection";

// FormItemRHF komponenta podle evidence obyvatel
interface FormItemRHFProps<T extends FieldValues = any> {
  label: string;
  name: FieldPath<T>;
  control: Control<T>;
  placeholder?: string;
  type?: string;
  disabled?: boolean;
  className?: string;
  as?: 'input' | 'textarea';
  rows?: number;
  smallLabel?: boolean;
}

const FormItemRHF = <T extends FieldValues>({ 
  label, 
  name, 
  control, 
  placeholder, 
  type = "text", 
  disabled = false, 
  className, 
  as = 'input', 
  rows = 3, 
  smallLabel = false 
}: FormItemRHFProps<T>) => (
  <FormField
    control={control} 
    name={name} 
    disabled={disabled}
    render={({ field, fieldState: { error } }) => (
      <FormItem className={className}>
        <FormLabel className={smallLabel ? "text-xs font-medium" : "font-semibold text-sm"}>
          {label}
        </FormLabel>
        <FormControl>
          {as === 'input' ? (
            <Input 
              {...field} 
              value={field.value || ''} 
              placeholder={placeholder} 
              type={type} 
              disabled={disabled}
              className={error ? "border-destructive" : ""} 
            />
          ) : (
            <Textarea 
              {...field} 
              value={field.value || ''} 
              placeholder={placeholder} 
              rows={rows} 
              disabled={disabled}
              className={error ? "border-destructive" : ""} 
            />
          )}
        </FormControl>
        {error && <FormMessage />}
      </FormItem>
    )}
  />
);

// Schema pro validaci
const facebookFriendSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Jméno je povinné"),
  profileUrl: z.string().optional(),
  extraInfo: z.array(z.string()).optional(),
  isPersonOfInterest: z.boolean().optional(),
  notes: z.string().optional(),
});

const facebookPostSchema = z.object({
  id: z.string(),
  date: z.string().optional(),
  text: z.string().optional(),
  likesCount: z.number().optional(),
  sharesCount: z.number().optional(),
  reactionsList: z.string().optional(),
  sharesList: z.string().optional(),
  comments: z.string().optional(),
  notes: z.string().optional(),
  photos: z.array(z.any()).optional(),
});

const facebookEventSchema = z.object({
  id: z.string(),
  eventName: z.string().optional(),
  organizer: z.string().optional(),
  eventDate: z.string().optional(),
  eventTime: z.string().optional(),
  location: z.string().optional(),
  participation: z.enum(["going", "interested", "invited", "declined", "unknown"]).optional(),
  description: z.string().optional(),
  eventUrl: z.string().optional(),
  participantsCount: z.number().optional(),
  interestedCount: z.number().optional(),
  notes: z.string().optional(),
});

const facebookModuleSchema = z.object({
  profileName: z.string().optional(),
  username: z.string().optional(),
  profileUrl: z.string().optional(),
  currentLocation: z.string().optional(),
  currentJob: z.string().optional(),
  education: z.string().optional(),
  relationshipStatus: z.string().optional(),
  aboutMe: z.string().optional(),
  contactInfo: z.string().optional(),
  otherPersonalInfo: z.string().optional(),
  friends: z.array(facebookFriendSchema).optional(),
  friendsRawData: z.string().optional(),
  likedPagesRawData: z.string().optional(),
  groupsRawData: z.string().optional(),
  posts: z.array(facebookPostSchema).optional(),
  events: z.array(facebookEventSchema).optional(),
  photos: z.array(z.any()).optional(),
  osintNotes: z.string().optional(),
  investigationNotes: z.string().optional(),
});

type FacebookFormValues = z.infer<typeof facebookModuleSchema>;

interface FacebookFormProps {
  caseId: string;
  subject: {
    id: string;
    firstName: string;
    lastName: string;
    type: "person" | "company";
  };
  moduleId: string;
  existingData: FacebookModuleData | null;
  onSave: (data: FacebookModuleData, wasNew: boolean) => void;
  onCancel: () => void;
}

export default function FacebookForm({ caseId, subject, moduleId, existingData, onSave, onCancel }: FacebookFormProps) {
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [showFriendsList, setShowFriendsList] = useState(false);
  const [friendsProcessed, setFriendsProcessed] = useState(!!existingData?.friends?.length);
  const [expandedComments, setExpandedComments] = useState<{[key: number]: boolean}>({});

  const defaultValues = useMemo(() => ({
    profileName: existingData?.profileName || "",
    username: existingData?.username || "",
    profileUrl: existingData?.profileUrl || "",
    currentLocation: existingData?.currentLocation || "",
    currentJob: existingData?.currentJob || "",
    education: existingData?.education || "",
    relationshipStatus: existingData?.relationshipStatus || "",
    aboutMe: existingData?.aboutMe || "",
    contactInfo: existingData?.contactInfo || "",
    otherPersonalInfo: existingData?.otherPersonalInfo || "",
    friends: existingData?.friends || [],
    friendsRawData: existingData?.friendsRawData || "",
    likedPagesRawData: existingData?.likedPagesRawData || "",
    groupsRawData: existingData?.groupsRawData || "",
    posts: existingData?.posts?.map((post: any) => ({
      ...post,
      date: typeof post.date === 'string' ? post.date : post.date?.toDate?.()?.toISOString().split('T')[0] || '',
      photos: post.photos || [],
    })) || [],
    events: existingData?.events?.map((event: any) => ({
      ...event,
      eventDate: typeof event.eventDate === 'string' ? event.eventDate : event.eventDate?.toDate?.()?.toISOString().split('T')[0] || '',
    })) || [],
    photos: existingData?.photos || [],
    osintNotes: existingData?.osintNotes || "",
    investigationNotes: existingData?.investigationNotes || "",
  }), [existingData]);

  const form = useForm<FacebookFormValues>({
    resolver: zodResolver(facebookModuleSchema),
    defaultValues,
  });

  const { fields: postFields, append: appendPost, remove: removePost } = useFieldArray({
    control: form.control,
    name: "posts"
  });

  const { fields: eventFields, append: appendEvent, remove: removeEvent } = useFieldArray({
    control: form.control,
    name: "events"
  });

  // Funkce pro zpracování přátel z Facebook textu
  const processFriends = () => {
    const friendsText = form.watch("friendsRawData");
    if (!friendsText?.trim()) {
      toast({
        title: "Chyba",
        description: "Nejprve vložte seznam přátel z Facebooku",
        variant: "destructive"
      });
      return;
    }

    const lines = friendsText.split('\n');
    const friends: FacebookFriend[] = [];

    for (let i = 0; i < lines.length; i++) {
      let line = lines[i].trim();
      if (!line) continue;

      const friend: FacebookFriend = {
        id: crypto.randomUUID(),
        name: line,
        extraInfo: [],
        isPersonOfInterest: false
      };

      // Zpracování dodatečných informací
      while (i + 1 < lines.length) {
        const nextLine = lines[i + 1].trim();
        if (!nextLine) break;

        const isExtraInfo = 
          /^(pracuje|studuje|žije|bydlí|společnost|fakulta|univerzit|škola|dovolená|corporation)/i.test(nextLine) ||
          nextLine.toLowerCase().includes('s.r.o') ||
          nextLine.includes('SOŠ') ||
          nextLine.includes('SOU') ||
          nextLine.includes('PSŠ') ||
          nextLine.includes('VUT') ||
          nextLine.includes('ČZU') ||
          /^[a-záčďéěíňóřšťúůýž0-9]/.test(nextLine) ||
          /^[^a-zA-ZáčďéěíňóřšťúůýžÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ]/.test(nextLine);

        if (isExtraInfo) {
          friend.extraInfo!.push(nextLine);
          i++;
        } else {
          break;
        }
      }

      friends.push(friend);
    }

    form.setValue("friends", friends);
    setFriendsProcessed(true);
    setShowFriendsList(true);
    
    toast({
      title: "Přátelé zpracováni",
      description: `Zpracováno ${friends.length} přátel z Facebooku`
    });
  };

  // Funkce pro označení přítele jako zájmovou osobu
  const togglePersonOfInterest = (friendIndex: number) => {
    const friends = form.watch("friends") || [];
    const updatedFriends = [...friends];
    updatedFriends[friendIndex].isPersonOfInterest = !updatedFriends[friendIndex].isPersonOfInterest;
    form.setValue("friends", updatedFriends);
  };

  // Funkce pro vyčištění Facebook komentářů
  const cleanFacebookComments = (commentText: string): string => {
    return commentText
      .replace(/\d+ r/g, '')
      .replace(/Odpovědět/g, '')
      .replace(/To se mi líbí/g, '')
      .split('\n')
      .filter(line => line.trim() !== '')
      .join('\n\n');
  };

  // Funkce pro kopírování textu do schránky
  const copyToClipboard = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Zkopírováno",
        description: `${type} byl zkopírován do schránky`,
      });
    } catch (error) {
      toast({
        title: "Chyba kopírování",
        description: "Nepodařilo se zkopírovat text do schránky",
        variant: "destructive"
      });
    }
  };

  // Funkce pro rozbalení/sbalení komentářů
  const toggleCommentExpansion = (postIndex: number) => {
    setExpandedComments(prev => ({
      ...prev,
      [postIndex]: !prev[postIndex]
    }));
  };

  const onSubmitHandler = async (data: FacebookFormValues) => {
    setIsSaving(true);
    try {
      const moduleDocRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);

      // Funkce pro odstranění undefined hodnot z objektu (rekurzivně)
      const removeUndefinedValues = (obj: any): any => {
        if (obj === null || obj === undefined) {
          return null;
        }
        
        if (Array.isArray(obj)) {
          return obj.map(removeUndefinedValues).filter(item => item !== undefined);
        }
        
        if (typeof obj === 'object') {
          const cleaned: any = {};
          for (const [key, value] of Object.entries(obj)) {
            if (value !== undefined) {
              const cleanedValue = removeUndefinedValues(value);
              if (cleanedValue !== undefined) {
                cleaned[key] = cleanedValue;
              }
            }
          }
          return cleaned;
        }
        
        return obj;
      };

      const saveData: FacebookModuleData = {
        subjectId: subject.id,
        profileName: data.profileName || "",
        username: data.username || "",
        profileUrl: data.profileUrl || "",
        currentLocation: data.currentLocation || "",
        currentJob: data.currentJob || "",
        education: data.education || "",
        relationshipStatus: data.relationshipStatus || "",
        aboutMe: data.aboutMe || "",
        contactInfo: data.contactInfo || "",
        otherPersonalInfo: data.otherPersonalInfo || "",
        friends: data.friends || [],
        friendsCount: data.friends?.length || 0,
        friendsRawData: data.friendsRawData || "",
        likedPagesRawData: data.likedPagesRawData || "",
        groupsRawData: data.groupsRawData || "",
        posts: data.posts || [],
        events: data.events || [],
        photos: data.photos || [],
        osintNotes: data.osintNotes || "",
        investigationNotes: data.investigationNotes || "",
        lastUpdatedAt: Timestamp.now(),
        createdAt: existingData?.createdAt || Timestamp.now(),
      };

      const cleanedData = removeUndefinedValues(saveData);
      await setDoc(moduleDocRef, cleanedData);
      
      toast({
        title: "Data uložena",
        description: "Facebook modul byl úspěšně uložen"
      });

      const wasNew = !existingData || !existingData.createdAt;
      onSave(cleanedData, wasNew);

    } catch (error: any) {
      console.error("Error saving Facebook data:", error);
      toast({
        title: "Chyba ukládání",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmitHandler)} className="space-y-6">
        {/* Základní údaje profilu */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Facebook className="mr-2 h-5 w-5 text-blue-600" />
              Základní údaje Facebook profilu
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemRHF<FacebookFormValues>
                label="Jméno na profilu"
                name="profileName"
                control={form.control}
                placeholder="Jméno zobrazené na profilu"
              />
              <FormItemRHF<FacebookFormValues>
                label="Uživatelské jméno"
                name="username"
                control={form.control}
                placeholder="@uživatelské_jméno"
              />
            </div>
            <FormItemRHF<FacebookFormValues>
              label="URL profilu"
              name="profileUrl"
              control={form.control}
              placeholder="https://facebook.com/..."
            />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemRHF<FacebookFormValues>
                label="Současné bydliště"
                name="currentLocation"
                control={form.control}
                placeholder="Město, země"
              />
              <FormItemRHF<FacebookFormValues>
                label="Současné zaměstnání"
                name="currentJob"
                control={form.control}
                placeholder="Pozice, společnost"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormItemRHF<FacebookFormValues>
                label="Vzdělání"
                name="education"
                control={form.control}
                placeholder="Škola, obor"
              />
              <FormItemRHF<FacebookFormValues>
                label="Vztahový stav"
                name="relationshipStatus"
                control={form.control}
                placeholder="Single, zadaný/á, ženatý/vdaná"
              />
            </div>
            <FormItemRHF<FacebookFormValues>
              label="O mně"
              name="aboutMe"
              control={form.control}
              placeholder="Informace z sekce O mně"
              as="textarea"
              rows={3}
            />
            <FormItemRHF<FacebookFormValues>
              label="Kontaktní údaje"
              name="contactInfo"
              control={form.control}
              placeholder="E-mail, telefon, web, atd."
              as="textarea"
              rows={2}
            />
            <FormItemRHF<FacebookFormValues>
              label="Další osobní informace"
              name="otherPersonalInfo"
              control={form.control}
              placeholder="Jiné osobní informace uvedené v profilu"
              as="textarea"
              rows={2}
            />
          </CardContent>
        </Card>

        {/* Přátelé */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="mr-2 h-5 w-5 text-primary" />
              Facebook přátelé
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormItemRHF<FacebookFormValues>
              label="Seznam přátel (zkopírovaný z Facebooku)"
              name="friendsRawData"
              control={form.control}
              placeholder="Vložte seznam přátel zkopírovaný z Facebooku..."
              as="textarea"
              rows={8}
            />
            
            <div className="flex gap-4">
              <Button
                type="button"
                onClick={processFriends}
                variant="outline"
                className="flex items-center"
              >
                <Users className="mr-2 h-4 w-4" />
                Zpracovat přátele
              </Button>
              
              {friendsProcessed && (
                <Button
                  type="button"
                  onClick={() => setShowFriendsList(!showFriendsList)}
                  variant="outline"
                  className="flex items-center"
                >
                  {showFriendsList ? <Minimize2 className="mr-2 h-4 w-4" /> : <Expand className="mr-2 h-4 w-4" />}
                  {showFriendsList ? 'Skrýt' : 'Zobrazit'} seznam přátel
                </Button>
              )}
            </div>

            {showFriendsList && form.watch("friends") && form.watch("friends")!.length > 0 && (
              <div className="mt-4">
                <h4 className="font-medium mb-2">Zpracovaní přátelé ({form.watch("friends")!.length})</h4>
                <div className="max-h-96 overflow-y-auto space-y-2">
                  {form.watch("friends")!.map((friend: FacebookFriend, index: number) => (
                    <Card key={friend.id} className={`p-3 ${friend.isPersonOfInterest ? 'border-yellow-500 bg-yellow-50' : ''}`}>
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{friend.name}</span>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => togglePersonOfInterest(index)}
                              className={friend.isPersonOfInterest ? "text-yellow-600" : "text-gray-400"}
                              title={friend.isPersonOfInterest ? "Odstranit z zájmových osob" : "Označit jako zájmovou osobu"}
                            >
                              {friend.isPersonOfInterest ? (
                                <Star className="h-4 w-4 fill-current" />
                              ) : (
                                <StarOff className="h-4 w-4" />
                              )}
                            </Button>
                          </div>
                          {friend.extraInfo && friend.extraInfo.length > 0 && (
                            <div className="text-sm text-gray-600 mt-1">
                              {friend.extraInfo.map((info, infoIndex) => (
                                <div key={infoIndex}>{info}</div>
                              ))}
                            </div>
                          )}
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(friend.name, "Jméno přítele")}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Příspěvky */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <MessageSquare className="mr-2 h-5 w-5 text-primary" />
                Klíčové příspěvky
              </span>
              <Button
                type="button"
                onClick={() => appendPost({
                  id: crypto.randomUUID(),
                  date: "",
                  text: "",
                  likesCount: 0,
                  sharesCount: 0,
                  reactionsList: "",
                  sharesList: "",
                  comments: "",
                  notes: "",
                  photos: []
                })}
                size="sm"
              >
                <PlusCircle className="mr-2 h-4 w-4" />
                Přidat příspěvek
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {postFields.map((field, index) => (
              <Card key={field.id} className="p-4">
                <div className="flex justify-between items-center mb-4">
                  <h4 className="font-medium">Příspěvek {index + 1}</h4>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removePost(index)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                
                <div className="space-y-4">
                  <FormItemRHF<FacebookFormValues>
                    label="Datum příspěvku"
                    name={`posts.${index}.date` as any}
                    control={form.control}
                    type="date"
                  />
                  
                  <FormItemRHF<FacebookFormValues>
                    label="Text příspěvku"
                    name={`posts.${index}.text` as any}
                    control={form.control}
                    placeholder="Zadejte text příspěvku..."
                    as="textarea"
                    rows={3}
                  />
                  
                  <div className="grid grid-cols-2 gap-4">
                    <FormItemRHF<FacebookFormValues>
                      label="Počet reakcí"
                      name={`posts.${index}.likesCount` as any}
                      control={form.control}
                      type="number"
                      placeholder="Počet to se mi líbí"
                    />
                    <FormItemRHF<FacebookFormValues>
                      label="Počet sdílení"
                      name={`posts.${index}.sharesCount` as any}
                      control={form.control}
                      type="number"
                      placeholder="Počet sdílení"
                    />
                  </div>
                  
                  <FormItemRHF<FacebookFormValues>
                    label="Seznam reakcí"
                    name={`posts.${index}.reactionsList` as any}
                    control={form.control}
                    placeholder="Jména lidí, kteří reagovali na příspěvek..."
                    as="textarea"
                    rows={3}
                  />
                  
                  <FormItemRHF<FacebookFormValues>
                    label="Seznam sdílení"
                    name={`posts.${index}.sharesList` as any}
                    control={form.control}
                    placeholder="Jména lidí, kteří sdíleli příspěvek..."
                    as="textarea"
                    rows={2}
                  />
                  
                  <div>
                    <div className="flex items-center justify-between mb-2">
                      <Label>Komentáře</Label>
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const comments = form.watch(`posts.${index}.comments`);
                            if (comments) {
                              const cleaned = cleanFacebookComments(comments);
                              form.setValue(`posts.${index}.comments` as any, cleaned);
                              toast({
                                title: "Komentáře vyčištěny",
                                description: "Odstraněny Facebook texty a čísla"
                              });
                            }
                          }}
                        >
                          Vyčistit
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => toggleCommentExpansion(index)}
                        >
                          {expandedComments[index] ? <Minimize2 className="h-4 w-4" /> : <Expand className="h-4 w-4" />}
                        </Button>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            const comments = form.watch(`posts.${index}.comments`);
                            if (comments) {
                              copyToClipboard(comments, "Komentáře");
                            }
                          }}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <Textarea
                      {...form.register(`posts.${index}.comments` as any)}
                      placeholder="Vložte komentáře z příspěvku..."
                      rows={expandedComments[index] ? 12 : 4}
                    />
                  </div>
                  
                  <FormItemRHF<FacebookFormValues>
                    label="Poznámky k příspěvku"
                    name={`posts.${index}.notes` as any}
                    control={form.control}
                    placeholder="Další důležité informace o příspěvku..."
                    as="textarea"
                    rows={2}
                  />

                  {/* Fotodokumentace pro příspěvek */}
                  <div className="border-t pt-4">
                    <PhotoDocumentationSection
                      form={form as any}
                      title={`Fotodokumentace příspěvku ${index + 1}`}
                      description="Nahrajte snímky obrazovky tohoto příspěvku"
                      namePrefix={`posts.${index}.photos`}
                      photoHint="facebook post social media screenshot"
                      caseId={caseId}
                      subjectId={subject.id}
                      moduleId={moduleId}
                    />
                  </div>
                </div>
              </Card>
            ))}
          </CardContent>
        </Card>

        {/* Líbí se mi stránky */}
        <Card>
          <CardHeader>
            <CardTitle>Stránky, které se mu líbí</CardTitle>
          </CardHeader>
          <CardContent>
            <FormItemRHF<FacebookFormValues>
              label="Seznam stránek (zkopírovaný z Facebooku)"
              name="likedPagesRawData"
              control={form.control}
              placeholder="Vložte seznam stránek, které se uživateli líbí..."
              as="textarea"
              rows={6}
            />
          </CardContent>
        </Card>

        {/* Skupiny */}
        <Card>
          <CardHeader>
            <CardTitle>Facebook skupiny</CardTitle>
          </CardHeader>
          <CardContent>
            <FormItemRHF<FacebookFormValues>
              label="Seznam skupin (zkopírovaný z Facebooku)"
              name="groupsRawData"
              control={form.control}
              placeholder="Vložte seznam Facebook skupin, kde je uživatel členem..."
              as="textarea"
              rows={6}
            />
          </CardContent>
        </Card>

        {/* Fotodokumentace celého profilu */}
        <Card className="shadow-md">
          <PhotoDocumentationSection
            form={form as any}
            namePrefix="photos"
            title="Fotodokumentace Facebook profilu"
            description="Nahrajte a spravujte snímky obrazovky Facebook profilu. Fotografie se ukládají trvale do aplikace."
            photoHint="facebook profile social media screenshot"
            caseId={caseId}
            subjectId={subject.id}
            moduleId={moduleId}
          />
        </Card>

        {/* OSINT poznámky */}
        <Card>
          <CardHeader>
            <CardTitle>OSINT poznámky</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <FormItemRHF<FacebookFormValues>
              label="OSINT poznámky"
              name="osintNotes"
              control={form.control}
              placeholder="Poznámky z OSINT vyšetřování Facebook profilu..."
              as="textarea"
              rows={4}
            />
            <FormItemRHF<FacebookFormValues>
              label="Vyšetřovací poznámky"
              name="investigationNotes"
              control={form.control}
              placeholder="Interní poznámky vyšetřovatele..."
              as="textarea"
              rows={3}
            />
          </CardContent>
        </Card>

        {/* Tlačítko pro uložení */}
        <div className="flex justify-end pt-6">
          <Button type="submit" disabled={isSaving}>
            {isSaving ? (
              <>
                <LoadingSpinner size="sm" className="mr-2" />
                Ukládání...
              </>
            ) : (
              "Uložit Facebook modul"
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
} 