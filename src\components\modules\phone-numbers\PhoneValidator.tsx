"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Separator } from "@/components/ui/separator";
import { 
  Phone, 
  RefreshCw, 
  CheckCircle2, 
  XCircle, 
  AlertCircle, 
  Globe, 
  MapPin, 
  Building, 
  Calendar, 
  Info 
} from "lucide-react";
import { PhoneValidationResult, PhoneType } from "@/types";

interface PhoneValidatorProps {
  initialPhone: string;
  onValidationComplete: (results: PhoneValidationResult) => void;
}

export function PhoneValidator({ initialPhone, onValidationComplete }: PhoneValidatorProps) {
  const [phone, setPhone] = useState(initialPhone);
  const [isValidating, setIsValidating] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [validationProgress, setValidationProgress] = useState(0);
  const [validationStep, setValidationStep] = useState("");
  const [validationResults, setValidationResults] = useState<PhoneValidationResult | null>(null);
  const [activeTab, setActiveTab] = useState("summary");

  useEffect(() => {
    setPhone(initialPhone);
  }, [initialPhone]);

  // Simulace postupu validace pro demonstrační účely
  const simulateValidationStep = async (progressIncrement: number, stepName: string) => {
    setValidationStep(stepName);
    return new Promise<void>((resolve) => {
      const startProgress = validationProgress;
      const targetProgress = Math.min(startProgress + progressIncrement, 100);
      const interval = setInterval(() => {
        setValidationProgress((prev) => {
          const newProgress = prev + 1;
          if (newProgress >= targetProgress) {
            clearInterval(interval);
            resolve();
          }
          return newProgress;
        });
      }, 20);
    });
  };

  const validatePhone = async () => {
    if (!phone.trim()) {
      setValidationError("Zadejte telefonní číslo");
      return;
    }

    setIsValidating(true);
    setValidationError(null);
    setValidationProgress(0);
    setValidationStep("Kontrola formátu telefonního čísla...");

    try {
      // Simulace postupu validace
      await simulateValidationStep(15, "Kontrola formátu telefonního čísla...");
      
      // Základní validace formátu telefonního čísla
      const phoneRegex = /^(\+\d{1,3}\s?)?(\d{3}\s?){2,4}\d{0,3}$/;
      const formatValid = phoneRegex.test(phone);
      
      if (!formatValid) {
        throw new Error("Neplatný formát telefonního čísla");
      }

      await simulateValidationStep(20, "Kontrola mezinárodního formátu...");
      
      // Převod na mezinárodní formát
      let internationalFormat = phone;
      if (!phone.startsWith("+")) {
        if (phone.startsWith("00")) {
          internationalFormat = "+" + phone.substring(2);
        } else if (phone.length === 9 && (phone.startsWith("6") || phone.startsWith("7") || phone.startsWith("9"))) {
          // České mobilní číslo bez předvolby
          internationalFormat = "+420 " + phone;
        }
      }

      await simulateValidationStep(20, "Identifikace země a operátora...");
      
      // Identifikace země a operátora
      let countryCode = "CZ";
      let countryName = "Česká republika";
      let carrier = "unknown";
      let phoneType: PhoneType = "mobile";
      
      if (internationalFormat.startsWith("+420")) {
        countryCode = "CZ";
        countryName = "Česká republika";
        
        // Identifikace operátora podle předčíslí
        const prefix = internationalFormat.replace(/\s+/g, "").substring(4, 7);
        if (["601", "602", "606", "607", "720", "721", "722", "723", "724", "725", "726", "727", "728", "729"].includes(prefix)) {
          carrier = "o2";
        } else if (["603", "604", "605", "730", "731", "732", "733", "734", "735", "736", "737", "738", "739"].includes(prefix)) {
          carrier = "t-mobile";
        } else if (["608", "609", "773", "774", "775", "776", "777", "778", "779"].includes(prefix)) {
          carrier = "vodafone";
        }
        
        // Identifikace typu telefonního čísla
        if (internationalFormat.replace(/\s+/g, "").substring(4, 5) === "6" || 
            internationalFormat.replace(/\s+/g, "").substring(4, 5) === "7") {
          phoneType = "mobile";
        } else if (internationalFormat.replace(/\s+/g, "").substring(4, 5) === "2") {
          phoneType = "landline";
        } else if (internationalFormat.replace(/\s+/g, "").substring(4, 5) === "9") {
          phoneType = "business";
        }
      } else if (internationalFormat.startsWith("+421")) {
        countryCode = "SK";
        countryName = "Slovensko";
      }

      await simulateValidationStep(25, "Kontrola aktivního stavu...");
      
      // Simulace kontroly aktivního stavu (v reálné implementaci by zde bylo API volání)
      const isActive = Math.random() > 0.2; // 80% šance, že číslo je aktivní
      
      await simulateValidationStep(20, "Dokončování validace...");
      
      // Vytvoření výsledku validace
      const results: PhoneValidationResult = {
        isValid: isActive,
        internationalFormat,
        countryCode,
        countryName,
        carrier,
        phoneType,
        validationDate: new Date().toISOString().split('T')[0]
      };
      
      setValidationResults(results);
      setActiveTab("summary");
      onValidationComplete(results);
      
    } catch (error: any) {
      console.error("Validation error:", error);
      setValidationError(error.message || "Chyba při validaci telefonního čísla");
      setValidationResults(null);
    } finally {
      setIsValidating(false);
      setValidationProgress(100);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Phone className="mr-2 h-5 w-5 text-primary" />
          Validace telefonního čísla
        </CardTitle>
        <CardDescription>
          Ověřte platnost a funkčnost telefonního čísla
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col space-y-2">
          <Label htmlFor="phone-input">Telefonní číslo</Label>
          <div className="flex space-x-2">
            <Input
              id="phone-input"
              placeholder="+420 123 456 789"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              disabled={isValidating}
              className="flex-1"
            />
            <Button 
              onClick={validatePhone} 
              disabled={isValidating || !phone.trim()}
            >
              {isValidating ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Validuji...
                </>
              ) : (
                <>
                  <Phone className="mr-2 h-4 w-4" />
                  Validovat
                </>
              )}
            </Button>
          </div>
        </div>

        {isValidating && (
          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span>{validationStep}</span>
              <span>{validationProgress}%</span>
            </div>
            <Progress value={validationProgress} className="h-2" />
          </div>
        )}

        {validationError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Chyba validace</AlertTitle>
            <AlertDescription>{validationError}</AlertDescription>
          </Alert>
        )}

        {validationResults && (
          <div className="space-y-4">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="summary">Souhrn</TabsTrigger>
                <TabsTrigger value="details">Detaily</TabsTrigger>
                <TabsTrigger value="carrier">Operátor</TabsTrigger>
              </TabsList>

              <TabsContent value="summary" className="space-y-4">
                <div className="flex justify-center my-4">
                  {validationResults.isValid ? (
                    <Badge variant="outline" className="px-4 py-2 text-lg bg-green-50 text-green-700 border-green-200">
                      <CheckCircle2 className="mr-2 h-5 w-5 text-green-500" />
                      Platné číslo
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="px-4 py-2 text-lg bg-red-50 text-red-700 border-red-200">
                      <XCircle className="mr-2 h-5 w-5 text-red-500" />
                      Neplatné číslo
                    </Badge>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-xs text-muted-foreground">Mezinárodní formát</Label>
                    <div className="font-medium">{validationResults.internationalFormat}</div>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-xs text-muted-foreground">Země</Label>
                    <div className="font-medium flex items-center">
                      <Globe className="mr-2 h-4 w-4 text-primary" />
                      {validationResults.countryName} ({validationResults.countryCode})
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="text-xs text-muted-foreground">Operátor</Label>
                    <div className="font-medium flex items-center">
                      <Building className="mr-2 h-4 w-4 text-primary" />
                      {validationResults.carrier === "o2" ? "O2" : 
                       validationResults.carrier === "t-mobile" ? "T-Mobile" : 
                       validationResults.carrier === "vodafone" ? "Vodafone" : 
                       "Neznámý operátor"}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label className="text-xs text-muted-foreground">Typ čísla</Label>
                    <div className="font-medium">
                      {validationResults.phoneType === "mobile" ? "Mobilní" : 
                       validationResults.phoneType === "landline" ? "Pevná linka" : 
                       validationResults.phoneType === "business" ? "Firemní" : 
                       validationResults.phoneType === "fax" ? "Fax" : 
                       validationResults.phoneType === "voip" ? "VoIP" : 
                       "Jiný"}
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="details" className="space-y-4">
                <Alert>
                  <Info className="h-4 w-4" />
                  <AlertTitle>Detailní informace</AlertTitle>
                  <AlertDescription>
                    Podrobné informace o telefonním čísle získané během validace.
                  </AlertDescription>
                </Alert>

                <div className="space-y-2">
                  <div className="flex justify-between py-2 border-b">
                    <span className="font-medium">Formát zadání</span>
                    <span>{phone}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span className="font-medium">Mezinárodní formát</span>
                    <span>{validationResults.internationalFormat}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span className="font-medium">Kód země</span>
                    <span>{validationResults.countryCode}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span className="font-medium">Země</span>
                    <span>{validationResults.countryName}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span className="font-medium">Typ čísla</span>
                    <span>
                      {validationResults.phoneType === "mobile" ? "Mobilní" : 
                       validationResults.phoneType === "landline" ? "Pevná linka" : 
                       validationResults.phoneType === "business" ? "Firemní" : 
                       validationResults.phoneType === "fax" ? "Fax" : 
                       validationResults.phoneType === "voip" ? "VoIP" : 
                       "Jiný"}
                    </span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span className="font-medium">Datum validace</span>
                    <span>{validationResults.validationDate}</span>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="carrier" className="space-y-4">
                <div className="flex items-center justify-center my-4">
                  <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center">
                    <Building className="h-8 w-8 text-primary" />
                  </div>
                </div>

                <div className="text-center mb-4">
                  <h3 className="text-lg font-semibold">
                    {validationResults.carrier === "o2" ? "O2 Czech Republic" : 
                     validationResults.carrier === "t-mobile" ? "T-Mobile Czech Republic" : 
                     validationResults.carrier === "vodafone" ? "Vodafone Czech Republic" : 
                     "Neznámý operátor"}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    Informace o operátorovi telefonního čísla
                  </p>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between py-2 border-b">
                    <span className="font-medium">Operátor</span>
                    <span>
                      {validationResults.carrier === "o2" ? "O2" : 
                       validationResults.carrier === "t-mobile" ? "T-Mobile" : 
                       validationResults.carrier === "vodafone" ? "Vodafone" : 
                       "Neznámý operátor"}
                    </span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span className="font-medium">Typ sítě</span>
                    <span>
                      {validationResults.phoneType === "mobile" ? "Mobilní" : "Pevná"}
                    </span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span className="font-medium">Předvolba</span>
                    <span>
                      {validationResults.internationalFormat?.replace(/\s+/g, "").substring(4, 7)}
                    </span>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <p className="text-xs text-muted-foreground">
          Validace telefonních čísel je prováděna pomocí interních databází a API.
        </p>
      </CardFooter>
    </Card>
  );
}
