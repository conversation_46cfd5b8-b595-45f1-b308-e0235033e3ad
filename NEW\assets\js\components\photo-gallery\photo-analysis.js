/**
 * Fotografická analýza - funkce pro práci s fotografiemi a jejich analýzu
 */

// Inicializace funkcí pro fotografickou analýzu
function initPhotoAnalysis() {
    console.log('Inicializace modulu fotografické analýzy');

    // Najít aktuální modul fotografické analýzy
    const photoModule = document.querySelector('.module[id^="module-foto-analyza"]');
    if (!photoModule) {
        console.error('Modul fotografické analýzy nebyl nalezen');
        return;
    }

    console.log('Nalezen modul fotografické analýzy:', photoModule.id);

    // Přidání event listenerů pro tlačítka v modulu fotografické analýzy
    // Použijeme delegaci událostí pouze pro aktuální modul
    photoModule.addEventListener('click', function(event) {
        // Extrakce EXIF dat
        if (event.target.closest('.extract-exif')) {
            extractExifData();
        }

        // Reverzní vyhledávání obrázků
        if (event.target.closest('.reverse-image-search')) {
            reverseImageSearch();
        }

        // Detekce obličejů
        if (event.target.closest('.detect-faces')) {
            detectFaces();
        }

        // Objektová analýza
        if (event.target.closest('.analyze-objects')) {
            analyzeObjects();
        }

        // Geolokace
        if (event.target.closest('.geolocate-image')) {
            geolocateImage();
        }

        // OCR - rozpoznání textu
        if (event.target.closest('.extract-text')) {
            extractText();
        }

        // Nahrání fotografie z počítače
        if (event.target.closest('.upload-photo-analysis')) {
            const uploadInput = photoModule.querySelector('#photo-analysis-upload');
            if (uploadInput) {
                uploadInput.click();
            }
        }

        // Přidání fotografie z URL
        if (event.target.closest('.add-photo-url-analysis')) {
            addPhotoFromUrl();
        }
    });

    // Změna event listenery - pouze pro input v aktuálním modulu
    const uploadInput = photoModule.querySelector('#photo-analysis-upload');
    if (uploadInput) {
        uploadInput.addEventListener('change', function(event) {
            handlePhotoUpload(event);
        });
    }

    // Vložení fotografie ze schránky (Ctrl+V) - globální, ale kontroluje aktivní modul
    // Odstraníme existující event listener, pokud existuje
    if (window.photoAnalysisPasteHandler) {
        document.removeEventListener('paste', window.photoAnalysisPasteHandler);
    }

    // Vytvoříme nový event listener
    window.photoAnalysisPasteHandler = function(event) {
        const activeElement = document.activeElement;
        const currentPhotoModule = document.querySelector('.module[id^="module-foto-analyza"]');

        // Kontrola, zda je aktivní modul fotografické analýzy a kurzor není v textovém poli
        if (currentPhotoModule &&
            currentPhotoModule.contains(activeElement) &&
            !activeElement.tagName.match(/input|textarea/i)) {
            // Zabránit výchozímu chování
            event.preventDefault();
            event.stopPropagation();

            handlePasteImage(event);
        }
    };

    // Přidáme nový event listener
    document.addEventListener('paste', window.photoAnalysisPasteHandler);

    // Inicializace fotogalerie
    initPhotoGallery(photoModule);
}

/**
 * Inicializace fotogalerie pro fotografickou analýzu
 * @param {HTMLElement} moduleElement - Element modulu fotografické analýzy
 */
function initPhotoGallery(moduleElement) {
    console.log('Inicializace fotogalerie pro fotografickou analýzu');

    if (!moduleElement) {
        console.error('Modul fotografické analýzy nebyl předán do initPhotoGallery');
        return;
    }

    // Přidání event listeneru pro tlačítko přidání fotografie
    const addButton = moduleElement.querySelector('.photo-gallery-add-analysis');
    if (addButton) {
        console.log('Nalezeno tlačítko pro přidání fotografie');

        // Odstranění existujících event listenerů
        const newAddButton = addButton.cloneNode(true);
        addButton.parentNode.replaceChild(newAddButton, addButton);

        // Přidání nového event listeneru
        newAddButton.addEventListener('click', function() {
            const uploadInput = moduleElement.querySelector('#photo-analysis-upload');
            if (uploadInput) {
                uploadInput.click();
            }
        });
    }

    // Přidání event listeneru pro tlačítko vložení ze schránky
    const pasteButton = moduleElement.querySelector('.paste-photo-analysis');
    if (pasteButton) {
        console.log('Nalezeno tlačítko pro vložení ze schránky');

        // Odstranění existujících event listenerů
        const newPasteButton = pasteButton.cloneNode(true);
        pasteButton.parentNode.replaceChild(newPasteButton, pasteButton);

        // Přidání nového event listeneru
        newPasteButton.addEventListener('click', function() {
            alert('Pro vložení obrázku ze schránky stiskněte Ctrl+V kdekoli v modulu fotografické analýzy (mimo textová pole).');
        });
    }
}

/**
 * Zpracování nahrání fotografie z počítače
 * @param {Event} event - Událost změny input[type=file]
 */
function handlePhotoUpload(event) {
    console.log('Zpracování nahrání fotografie z počítače');

    // Resetování input[type=file] pro možnost nahrání stejného souboru znovu
    const input = event.target;

    const file = input.files[0];
    if (!file) {
        console.error('Nebyl vybrán žádný soubor');
        return;
    }

    console.log('Vybraný soubor:', file.name, file.type, file.size);

    // Kontrola, zda jde o obrázek
    if (!file.type.match('image.*')) {
        alert('Vybraný soubor není obrázek.');
        input.value = ''; // Reset input
        return;
    }

    // Vytvoření URL pro náhled
    const imageUrl = URL.createObjectURL(file);

    // Přidání fotografie do galerie
    addPhotoToGallery(file.name, imageUrl);

    // Automatická extrakce EXIF dat
    extractExifDataFromFile(file);

    // Reset input pro možnost nahrání stejného souboru znovu
    input.value = '';
}

/**
 * Zpracování vložení obrázku ze schránky
 * @param {Event} event - Událost vložení
 */
function handlePasteImage(event) {
    console.log('Zpracování vložení obrázku ze schránky');

    // Najít aktuální modul fotografické analýzy
    const photoModule = document.querySelector('.module[id^="module-foto-analyza"]');
    if (!photoModule) {
        console.error('Modul fotografické analýzy nebyl nalezen');
        return;
    }

    // Kontrola, zda schránka obsahuje obrázek
    const clipboardData = event.clipboardData || window.clipboardData;
    if (!clipboardData) {
        console.error('Clipboard data nejsou k dispozici');
        return;
    }

    const items = clipboardData.items;
    if (!items) {
        console.error('Clipboard items nejsou k dispozici');
        return;
    }

    let blob = null;

    // Procházení položek ve schránce
    for (let i = 0; i < items.length; i++) {
        console.log('Clipboard item:', items[i].type);
        if (items[i].type.indexOf('image') === 0) {
            blob = items[i].getAsFile();
            console.log('Nalezen obrázek ve schránce:', blob);
            break;
        }
    }

    if (!blob) {
        console.error('Schránka neobsahuje obrázek');
        alert('Schránka neobsahuje obrázek. Zkopírujte obrázek do schránky a zkuste to znovu.');
        return;
    }

    // Vytvoření URL pro náhled
    const imageUrl = URL.createObjectURL(blob);
    console.log('Vytvořeno URL pro náhled:', imageUrl);

    // Přidání fotografie do galerie
    addPhotoToGallery('Vložený obrázek', imageUrl);

    // Automatická extrakce EXIF dat
    extractExifDataFromFile(blob);
}

/**
 * Přidání fotografie z URL
 */
function addPhotoFromUrl() {
    console.log('Přidání fotografie z URL');

    // Najít aktuální modul fotografické analýzy
    const photoModule = document.querySelector('.module[id^="module-foto-analyza"]');
    if (!photoModule) {
        console.error('Modul fotografické analýzy nebyl nalezen');
        return;
    }

    // Zobrazení dialogu pro zadání URL
    const url = prompt('Zadejte URL obrázku:');
    if (!url) return;

    // Kontrola, zda URL je platná
    if (!url.match(/^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i)) {
        alert('Zadejte platnou URL obrázku (končící na .jpg, .png, .gif nebo .webp).');
        return;
    }

    console.log('Přidání obrázku z URL:', url);

    // Přidání fotografie do galerie
    addPhotoToGallery('Obrázek z URL', url);

    // Automatická extrakce EXIF dat
    // Poznámka: Pro obrázky z URL je extrakce EXIF dat omezená
    fetchImageAndExtractExif(url);
}

/**
 * Stažení obrázku z URL a extrakce EXIF dat
 * @param {string} url - URL obrázku
 */
function fetchImageAndExtractExif(url) {
    console.log('Stažení obrázku z URL a extrakce EXIF dat:', url);

    // Pro obrázky z URL je extrakce EXIF dat omezená
    // Většina webových serverů odstraňuje EXIF data z obrázků
    // Místo toho použijeme funkci pro extrakci EXIF dat z URL
    extractExifDataFromUrl(url);
}

/**
 * Přidání fotografie do galerie
 * @param {string} title - Název fotografie
 * @param {string} imageUrl - URL obrázku
 */
function addPhotoToGallery(title, imageUrl) {
    console.log('Přidání fotografie do galerie:', title, imageUrl);

    // Najít aktuální modul fotografické analýzy
    const photoModule = document.querySelector('.module[id^="module-foto-analyza"]');
    if (!photoModule) {
        console.error('Modul fotografické analýzy nebyl nalezen');
        return;
    }

    // Najít galerii v aktuálním modulu
    const gallery = photoModule.querySelector('.photo-gallery-analysis');
    if (!gallery) {
        console.error('Galerie nebyla nalezena v modulu fotografické analýzy');
        return;
    }

    // Vytvoření nové fotografie
    const photoId = `photo-${Date.now()}`;
    const photoHTML = `
        <div class="photo-item" id="${photoId}" data-image-url="${imageUrl}">
            <div class="photo-preview">
                <img src="${imageUrl}" alt="${title}" onerror="this.src='https://via.placeholder.com/150x150?text=Chyba+načítání'">
            </div>
            <div class="photo-info">
                <div class="photo-title">${title}</div>
                <div class="photo-actions">
                    <button type="button" class="photo-action-btn analyze-photo" data-photo-id="${photoId}" title="Analyzovat fotografii">
                        <i class="fas fa-search"></i>
                    </button>
                    <button type="button" class="photo-action-btn view-photo" data-photo-id="${photoId}" title="Zobrazit fotografii">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button type="button" class="photo-action-btn delete-photo" data-photo-id="${photoId}" title="Odstranit fotografii">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;

    // Přidání fotografie do galerie
    const addButton = gallery.querySelector('.photo-gallery-add-analysis');
    if (addButton) {
        gallery.insertBefore(document.createRange().createContextualFragment(photoHTML), addButton);
    } else {
        gallery.insertAdjacentHTML('beforeend', photoHTML);
    }

    // Přidání event listenerů pro tlačítka
    const photoElement = document.getElementById(photoId);
    if (!photoElement) {
        console.error('Nově přidaná fotografie nebyla nalezena v DOM');
        return;
    }

    // Přidání event listeneru pro výběr fotografie kliknutím
    photoElement.addEventListener('click', function(event) {
        // Ignorujeme kliknutí na tlačítka
        if (event.target.closest('.photo-action-btn')) {
            return;
        }

        // Odznačíme všechny fotografie
        const photoModule = document.querySelector('.module[id^="module-foto-analyza"]');
        if (photoModule) {
            photoModule.querySelectorAll('.photo-item').forEach(item => {
                item.classList.remove('selected');
            });
        }

        // Označíme kliknutou fotografii
        this.classList.add('selected');
    });

    const analyzeButton = photoElement.querySelector('.analyze-photo');
    if (analyzeButton) {
        analyzeButton.addEventListener('click', function() {
            const photoId = this.getAttribute('data-photo-id');
            const photoElement = document.getElementById(photoId);
            if (photoElement) {
                const imageUrl = photoElement.getAttribute('data-image-url');
                analyzePhoto(photoId, imageUrl);
            }
        });
    }

    const viewButton = photoElement.querySelector('.view-photo');
    if (viewButton) {
        viewButton.addEventListener('click', function() {
            const photoId = this.getAttribute('data-photo-id');
            const photoElement = document.getElementById(photoId);
            if (photoElement) {
                const imageUrl = photoElement.getAttribute('data-image-url');
                const photoTitle = photoElement.querySelector('.photo-title').textContent;
                showPhotoViewer(imageUrl, photoTitle);
            }
        });
    }

    const deleteButton = photoElement.querySelector('.delete-photo');
    if (deleteButton) {
        deleteButton.addEventListener('click', function() {
            const photoId = this.getAttribute('data-photo-id');
            const photoElement = document.getElementById(photoId);
            if (photoElement && confirm('Opravdu chcete odstranit tuto fotografii?')) {
                photoElement.remove();
            }
        });
    }
}

/**
 * Zobrazení prohlížeče fotografií
 * @param {string} imageUrl - URL obrázku
 * @param {string} title - Název fotografie
 */
function showPhotoViewer(imageUrl, title) {
    console.log('Zobrazení prohlížeče fotografií:', title, imageUrl);

    // Vytvoření prohlížeče fotografií
    const viewer = document.createElement('div');
    viewer.className = 'photo-viewer';
    viewer.innerHTML = `
        <div class="photo-viewer-content">
            <div class="photo-viewer-header">
                <div class="photo-viewer-title">${title}</div>
                <div class="photo-viewer-actions">
                    <button type="button" class="photo-viewer-action-btn extract-exif-viewer" title="Extrahovat EXIF data">
                        <i class="fas fa-info-circle"></i>
                    </button>
                    <button type="button" class="photo-viewer-action-btn reverse-search-viewer" title="Reverzní vyhledávání">
                        <i class="fas fa-search"></i>
                    </button>
                    <button type="button" class="photo-viewer-action-btn close-viewer" title="Zavřít">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="photo-viewer-body">
                <img src="${imageUrl}" alt="${title}" onerror="this.src='https://via.placeholder.com/800x600?text=Chyba+načítání'">
            </div>
        </div>
    `;

    // Přidání prohlížeče do stránky
    document.body.appendChild(viewer);

    // Přidání event listenerů pro tlačítka
    viewer.querySelector('.close-viewer').addEventListener('click', function() {
        viewer.remove();
    });

    viewer.querySelector('.extract-exif-viewer').addEventListener('click', function() {
        extractExifDataFromUrl(imageUrl);
    });

    viewer.querySelector('.reverse-search-viewer').addEventListener('click', function() {
        reverseImageSearchUrl(imageUrl);
    });

    // Přidání event listeneru pro kliknutí mimo obsah prohlížeče
    viewer.addEventListener('click', function(event) {
        if (event.target === viewer) {
            viewer.remove();
        }
    });
}

// Inicializace modulu se provádí v osint-modules.js při přidání modulu do dokumentu
// Není potřeba inicializovat modul při načtení stránky
