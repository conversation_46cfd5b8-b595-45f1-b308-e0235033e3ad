"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { MapOverlaysModuleData, Subject } from "@/types";
import { MapOverlaysModuleFormValues, mapOverlaysModuleSchema } from "./schemas";
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { MapPointsTab } from "./tabs/MapPointsTab";
import { MapAreasTab } from "./tabs/MapAreasTab";
import { MapRoutesTab } from "./tabs/MapRoutesTab";
import { MapExternalLayersTab } from "./tabs/MapExternalLayersTab";
import { MapAnalysisTab } from "./tabs/MapAnalysisTab";
import { MapSettingsTab } from "./tabs/MapSettingsTab";
import { MapViewTab } from "./tabs/MapViewTab";
import { ArrowLeft, HelpCircle, Save } from "lucide-react";

interface MapOverlaysFormProps {
  caseId: string;
  subject: Subject;
  initialData?: MapOverlaysModuleData | null;
  existingData?: MapOverlaysModuleData | null;
  onSave: (data: MapOverlaysModuleFormValues) => Promise<void>;
  onBack?: () => void;
}

export function MapOverlaysForm({
  caseId,
  subject,
  initialData,
  existingData,
  onSave,
  onBack
}: MapOverlaysFormProps) {
  const [activeTab, setActiveTab] = useState("view");

  // Funkce pro změnu aktivní záložky
  const handleTabChange = (value: string) => {
    setActiveTab(value);

    // Pokud přepínáme na záložku s mapou, počkáme chvíli a pak vyvoláme událost pro obnovení mapy
    if (value === "view") {
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
        console.log("Resize event dispatched to refresh map");
      }, 200);
    }
  };
  const [isSaving, setIsSaving] = useState(false);

  // Použijeme existingData, pokud je k dispozici, jinak initialData
  const data = existingData || initialData;

  // Výchozí hodnoty pro formulář
  const defaultValues: Partial<MapOverlaysModuleFormValues> = {
    points: data?.points || [],
    areas: data?.areas || [],
    routes: data?.routes || [],
    externalLayers: data?.externalLayers || [],
    analysisResults: data?.analysisResults || [],
    defaultCenter: data?.defaultCenter || { lat: 50.0755, lng: 14.4378 }, // Praha jako výchozí
    defaultZoom: data?.defaultZoom || 10,
    defaultBasemap: data?.defaultBasemap || "streets",
    visibleLayers: data?.visibleLayers || ["points", "areas", "routes"],
    mapSettings: data?.mapSettings || {},
    generalNotes: data?.generalNotes || "",
  };

  const form = useForm<MapOverlaysModuleFormValues>({
    resolver: zodResolver(mapOverlaysModuleSchema),
    defaultValues,
  });



  const handleSubmit = async (values: MapOverlaysModuleFormValues, event?: React.BaseSyntheticEvent) => {
    console.log("handleSubmit called - manuální uložení");

    // Zastavíme výchozí chování formuláře
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }

    // Pokud je tlačítko stisknuto během ukládání, ignorujeme ho
    if (isSaving) {
      alert("Ukládání již probíhá, prosím počkejte...");
      return;
    }

    setIsSaving(true);
    try {
      // Zajistíme, že máme výchozí hodnoty pro všechny potřebné vlastnosti
      const dataToSave = {
        ...values,
        points: values.points || [],
        areas: values.areas || [],
        routes: values.routes || [],
        externalLayers: values.externalLayers || [],
        analysisResults: values.analysisResults || [],
        defaultCenter: values.defaultCenter || { lat: 50.0755, lng: 14.4378 },
        defaultZoom: values.defaultZoom || 10,
        defaultBasemap: values.defaultBasemap || "streets",
        visibleLayers: values.visibleLayers || ["points", "areas", "routes"],
      };

      // Uložíme data
      await onSave(dataToSave);

      // Zobrazíme potvrzení
      alert("Data byla úspěšně uložena. Můžete pokračovat v práci s mapou.");

      // Zůstaneme na aktuální záložce
      return true;
    } catch (error) {
      console.error("Error saving map overlays data:", error);
      alert("Při ukládání dat došlo k chybě. Zkuste to prosím znovu.");
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  return (
      <Form {...form}>
        <form
          onSubmit={(e) => {
            console.log("Form submit event");
            e.preventDefault();
            e.stopPropagation();
            // Nebudeme automaticky odesílat formulář
            // form.handleSubmit(handleSubmit)(e);
            return false;
          }}
          className="space-y-6"
          onClick={(e) => {
            // Zastavíme propagaci kliknutí, aby se formulář neodeslal
            e.stopPropagation();
          }}
          onKeyDown={(e) => {
            // Zabráníme odeslání formuláře při stisknutí klávesy Enter
            if (e.key === 'Enter' && e.target instanceof HTMLInputElement) {
              e.preventDefault();
              e.stopPropagation();
              return false;
            }
          }}
        >
          <Tabs value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="grid grid-cols-7">
              <TabsTrigger value="view">Mapa</TabsTrigger>
              <TabsTrigger value="points">Body</TabsTrigger>
              <TabsTrigger value="areas">Oblasti</TabsTrigger>
              <TabsTrigger value="routes">Trasy</TabsTrigger>
              <TabsTrigger value="layers">Externí vrstvy</TabsTrigger>
              <TabsTrigger value="analysis">Analýza</TabsTrigger>
              <TabsTrigger value="settings">Nastavení</TabsTrigger>
            </TabsList>

          <TabsContent value="view" className="space-y-4 pt-4">
            <MapViewTab form={form} />
          </TabsContent>

          <TabsContent value="points" className="space-y-4 pt-4">
            <MapPointsTab form={form} />
          </TabsContent>

          <TabsContent value="areas" className="space-y-4 pt-4">
            <MapAreasTab form={form} />
          </TabsContent>

          <TabsContent value="routes" className="space-y-4 pt-4">
            <MapRoutesTab form={form} />
          </TabsContent>

          <TabsContent value="layers" className="space-y-4 pt-4">
            <MapExternalLayersTab form={form} />
          </TabsContent>

          <TabsContent value="analysis" className="space-y-4 pt-4">
            <MapAnalysisTab form={form} />
          </TabsContent>

          <TabsContent value="settings" className="space-y-4 pt-4">
            <MapSettingsTab form={form} />
          </TabsContent>
        </Tabs>

        <Card>
          <CardHeader>
            <CardTitle>Obecné poznámky</CardTitle>
            <CardDescription>
              Zde můžete přidat obecné poznámky k mapovým overlapům
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea
              {...form.register("generalNotes")}
              placeholder="Zadejte obecné poznámky..."
              rows={5}
            />
          </CardContent>
        </Card>

        <div className="flex justify-between">
          {onBack && (
            <Button type="button" variant="outline" onClick={onBack}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Zpět
            </Button>
          )}
          <Button
            type="button"
            disabled={isSaving}
            id="save-map-overlays-form"
            onClick={(e) => {
              console.log("Save button clicked");
              e.preventDefault();
              e.stopPropagation();

              // Uložíme data
              const values = form.getValues();
              handleSubmit(values, e);

              // Zabráníme dalšímu šíření události
              return false;
            }}
          >
            <Save className="mr-2 h-4 w-4" />
            {isSaving ? "Ukládání..." : "Uložit"}
          </Button>

          <Button
            type="button"
            variant="outline"
            className="ml-2"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();

              // Zobrazíme instrukce pro práci s mapou
              alert("Instrukce pro práci s mapou:\n\n" +
                "1. Pro přidání bodu klikněte na ikonu bodu a pak na mapu\n" +
                "2. Pro kreslení oblasti klikněte na ikonu čtverce a pak klikejte na mapu (min. 3 body)\n" +
                "3. Pro kreslení trasy klikněte na ikonu trasy a pak klikejte na mapu (min. 2 body)\n" +
                "4. Po dokončení kreslení oblasti nebo trasy klikněte na tlačítko 'Dokončit'\n" +
                "5. Nezapomeňte uložit změny tlačítkem 'Uložit'");
            }}
          >
            <HelpCircle className="mr-2 h-4 w-4" />
            Nápověda
          </Button>
        </div>
      </form>
    </Form>
  );
}
