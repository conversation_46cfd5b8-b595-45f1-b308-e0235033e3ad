"use client";

import { useState, useEffect } from "react";
import { doc, getDoc, deleteDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { InstagramModuleData } from "@/types/instagram";
import InstagramForm from "./InstagramForm";
import { LoadingSpinner } from "@/components/ui/loading";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Trash2, Edit, Instagram } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface InstagramModuleProps {
  caseId: string;
  subject: {
    id: string;
    firstName: string;
    lastName: string;
    type: "person" | "company";
  };
  moduleId: string;
  onSave: (data: InstagramModuleData, wasNew: boolean) => void;
  onCancel: () => void;
}

export default function InstagramModule({ caseId, subject, moduleId, onSave, onCancel }: InstagramModuleProps) {
  const [existingData, setExistingData] = useState<InstagramModuleData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const loadExistingData = async () => {
      try {
        const moduleDocRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);
        const moduleSnap = await getDoc(moduleDocRef);
        
        if (moduleSnap.exists()) {
          setExistingData(moduleSnap.data() as InstagramModuleData);
          setIsEditing(false);
        }
      } catch (error) {
        console.error("Error loading Instagram module data:", error);
        toast({
          title: "Chyba načítání",
          description: "Nepodařilo se načíst data Instagram modulu",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadExistingData();
  }, [caseId, subject.id, moduleId, toast]);

  const handleSaveComplete = (data: InstagramModuleData, wasNew: boolean) => {
    setExistingData(data);
    setIsEditing(false);
    onSave(data, wasNew);
  };

  const handleDelete = async () => {
    if (!confirm("Opravdu chcete smazat tento Instagram profil?")) {
      return;
    }
    
    setIsLoading(true);
    try {
      const docRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);
      await deleteDoc(docRef);
      
      toast({
        title: "Profil smazán",
        description: "Instagram profil byl úspěšně smazán.",
      });
      
      onCancel();
    } catch (error) {
      console.error("Error deleting Instagram profile:", error);
      toast({
        title: "Chyba při mazání",
        description: "Nepodařilo se smazat Instagram profil.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (isEditing) {
    return (
      <InstagramForm
        caseId={caseId}
        subject={subject}
        moduleId={moduleId}
        existingData={existingData}
        onSave={handleSaveComplete}
        onCancel={onCancel}
      />
    );
  }

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="flex items-center">
          <Instagram className="mr-2 h-5 w-5 text-pink-600" />
          Instagram
        </CardTitle>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
            <Edit className="h-4 w-4 mr-1" /> Upravit
          </Button>
          <Button variant="destructive" size="sm" onClick={handleDelete}>
            <Trash2 className="h-4 w-4 mr-1" /> Smazat
          </Button>
          <Button variant="ghost" size="sm" onClick={onCancel}>
            Zavřít
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-center py-8">
          <p>Vyberte akci pro Instagram profil uživatele {subject.firstName} {subject.lastName}</p>
        </div>
      </CardContent>
    </Card>
  );
} 