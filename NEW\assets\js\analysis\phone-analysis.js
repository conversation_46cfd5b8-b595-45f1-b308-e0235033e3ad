/**
 * Telefonní analýza - OSINT nástroj
 * Funkce pro práci s modulem telefonní analýzy
 */

// API klíč pro NumVerify
const NUMVERIFY_API_KEY = '********************************';
const NUMVERIFY_API_URL = 'http://apilayer.net/api/validate';

document.addEventListener('DOMContentLoaded', function() {
    // Inicializace funkcí pro telefonní analýzu
    initPhoneAnalysis();

    // Inicializace fotogalerie pro telefonní analýzu
    initPhonePhotoGallery();
});

/**
 * Inicializace funkcí pro telefonní analýzu
 */
function initPhoneAnalysis() {
    // Přidání přímých event listenerů pro tlačítka v modulu telefonní analýzy
    addDirectButtonListeners();

    // Změna zdroje telefonního čísla
    document.addEventListener('change', function(event) {
        if (event.target.id === 'phone-source') {
            togglePhoneOtherSourceField(event.target.value);
        }

        // Změna typu zdroje telefonního čísla
        if (event.target.id === 'phone-source-type') {
            toggleSourceSections(event.target.value);
        }

        // Nahrání fotografie z počítače
        if (event.target.id === 'phone-photo-upload') {
            handlePhotoUpload(event);
        }
    });

    // Vložení fotografie ze schránky (Ctrl+V)
    document.addEventListener('paste', function(event) {
        const activeElement = document.activeElement;
        const pasteButton = document.querySelector('.paste-photo');

        // Kontrola, zda je kurzor v modulu telefonní analýzy
        if (pasteButton && !activeElement.tagName.match(/input|textarea/i)) {
            handlePasteImage(event, pasteButton.getAttribute('data-gallery'));
        }
    });

    // Skrytí nefunkčních tlačítek
    hideNonFunctionalButtons();
}

/**
 * Přidání přímých event listenerů pro tlačítka v modulu telefonní analýzy
 */
function addDirectButtonListeners() {
    // Přidání globálního event listeneru pro tlačítka v modulu telefonní analýzy
    document.addEventListener('click', function(event) {
        // Validace telefonního čísla
        if (event.target.closest('.validate-phone:not(.module-action-btn)')) {
            event.stopPropagation();
            event.preventDefault();
            validatePhone();
            return;
        }

        // Přidání aktivity
        if (event.target.closest('.add-phone-activity:not(.module-action-btn)')) {
            event.stopPropagation();
            event.preventDefault();
            addPhoneActivity();
            return;
        }

        // Přidání připojeného zařízení
        if (event.target.closest('.add-connected-device:not(.module-action-btn)')) {
            event.stopPropagation();
            event.preventDefault();
            addConnectedDevice();
            return;
        }

        // Přidání kontaktu
        if (event.target.closest('.add-phone-contact:not(.module-action-btn)')) {
            event.stopPropagation();
            event.preventDefault();
            addPhoneContact();
            return;
        }

        // Přidání lokace
        if (event.target.closest('.add-phone-location:not(.module-action-btn)')) {
            event.stopPropagation();
            event.preventDefault();
            addPhoneLocation();
            return;
        }
    });

    // Nahrání fotografie z počítače, přidání z URL a vložení ze schránky
    // jsou řešeny v initPhonePhotoGallery
}

/**
 * Skrytí nefunkčních tlačítek a sekcí
 */
function hideNonFunctionalButtons() {
    // Skrytí tlačítka pro reverzní vyhledávání
    const reverseLookupBtn = document.querySelector('.reverse-phone-lookup');
    if (reverseLookupBtn) {
        reverseLookupBtn.closest('.form-row').style.display = 'none';
    }

    // Skrytí tlačítek pro kontrolu služeb
    const serviceButtons = document.querySelectorAll('.btn-check-service');
    serviceButtons.forEach(button => {
        button.style.display = 'none';
    });

    // Přidání informace o nedostupnosti funkcí
    const lookupSection = document.querySelector('h4.mt-3 + .form-row');
    if (lookupSection) {
        const infoElement = document.createElement('div');
        infoElement.className = 'info-message';
        infoElement.innerHTML = '<i class="fas fa-info-circle"></i> Reverzní vyhledávání vyžaduje přístup k policejním databázím.';
        lookupSection.parentNode.insertBefore(infoElement, lookupSection);
    }

    // Přidání informace o nedostupnosti kontroly služeb
    const serviceContainer = document.getElementById('service-registration-container');
    if (serviceContainer) {
        const infoElement = document.createElement('div');
        infoElement.className = 'info-message';
        infoElement.innerHTML = '<i class="fas fa-info-circle"></i> Kontrola registrace ve službách vyžaduje přístup k API poskytovatelů.';
        serviceContainer.parentNode.insertBefore(infoElement, serviceContainer);
    }
}

/**
 * Validace telefonního čísla pomocí NumVerify API
 */
function validatePhone() {
    const phoneInput = document.getElementById('phone-number');
    const phone = phoneInput.value.trim();

    if (!phone) {
        alert('Zadejte telefonní číslo pro validaci.');
        return;
    }

    // Zobrazení výsledků validace
    const validationResults = phoneInput.closest('.module-content').querySelector('.phone-validation-results');
    validationResults.style.display = 'block';

    // Resetování výsledků
    document.getElementById('phone-format-result').innerHTML = '<span class="pending"><i class="fas fa-spinner fa-spin"></i> Probíhá validace...</span>';
    document.getElementById('phone-international-format').innerHTML = '<span class="pending"><i class="fas fa-spinner fa-spin"></i> Probíhá validace...</span>';
    document.getElementById('phone-country').innerHTML = '<span class="pending"><i class="fas fa-spinner fa-spin"></i> Probíhá validace...</span>';
    document.getElementById('phone-carrier').innerHTML = '<span class="pending"><i class="fas fa-spinner fa-spin"></i> Probíhá validace...</span>';
    document.getElementById('phone-type').innerHTML = '<span class="pending"><i class="fas fa-spinner fa-spin"></i> Probíhá validace...</span>';

    // Volání NumVerify API
    callNumVerifyAPI(phone);
}

/**
 * Volání NumVerify API pro validaci telefonního čísla
 * @param {string} phone - Telefonní číslo k validaci
 */
function callNumVerifyAPI(phone) {
    // Odstranění mezer a dalších znaků z telefonního čísla
    const cleanPhone = phone.replace(/\s+/g, '').replace(/[()-]/g, '');

    // Kontrola, zda číslo začíná na + (mezinárodní formát)
    let apiPhone;
    let countryCode = '';

    if (cleanPhone.startsWith('+')) {
        // Odstranění + ze začátku čísla pro API volání
        apiPhone = cleanPhone.substring(1);

        // Získání kódu země z telefonního čísla
        if (cleanPhone.startsWith('+420')) {
            countryCode = 'CZ';
        } else if (cleanPhone.startsWith('+421')) {
            countryCode = 'SK';
        } else if (cleanPhone.startsWith('+48')) {
            countryCode = 'PL';
        } else if (cleanPhone.startsWith('+49')) {
            countryCode = 'DE';
        } else if (cleanPhone.startsWith('+43')) {
            countryCode = 'AT';
        } else if (cleanPhone.startsWith('+36')) {
            countryCode = 'HU';
        }
    } else {
        // Pokud nezačíná na +, předpokládáme české číslo
        if (cleanPhone.startsWith('00420')) {
            apiPhone = cleanPhone.replace('00420', '');
            countryCode = 'CZ';
        } else if (cleanPhone.startsWith('420')) {
            apiPhone = cleanPhone.replace('420', '');
            countryCode = 'CZ';
        } else {
            // Předpokládáme, že jde o české číslo bez předvolby
            apiPhone = cleanPhone;
            countryCode = 'CZ';
        }
    }

    // Sestavení URL pro API volání
    const apiUrl = `${NUMVERIFY_API_URL}?access_key=${NUMVERIFY_API_KEY}&number=${apiPhone}&country_code=${countryCode}&format=1`;

    // Volání API
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            processNumVerifyResponse(data);
        })
        .catch(error => {
            console.error('Chyba při volání NumVerify API:', error);

            // Zobrazení chyby uživateli
            document.getElementById('phone-format-result').innerHTML =
                '<span class="invalid"><i class="fas fa-times-circle"></i> Chyba při validaci</span>';
            document.getElementById('phone-international-format').textContent = 'Nedostupné';
            document.getElementById('phone-country').textContent = 'Nedostupné';
            document.getElementById('phone-carrier').textContent = 'Nedostupné';
            document.getElementById('phone-type').textContent = 'Nedostupné';

            alert('Došlo k chybě při validaci telefonního čísla. Zkuste to prosím znovu.');
        });
}

/**
 * Zpracování odpovědi z NumVerify API
 * @param {Object} data - Odpověď z API
 */
function processNumVerifyResponse(data) {
    // Formát čísla
    if (data.valid) {
        document.getElementById('phone-format-result').innerHTML =
            '<span class="valid"><i class="fas fa-check-circle"></i> Platné číslo</span>';
    } else {
        document.getElementById('phone-format-result').innerHTML =
            '<span class="invalid"><i class="fas fa-times-circle"></i> Neplatné číslo</span>';
    }

    // Mezinárodní formát
    document.getElementById('phone-international-format').textContent = data.international_format || 'Neznámý';

    // Země
    const countryInfo = data.country_name ? `${data.country_name} (${data.country_code})` : 'Neznámá';
    document.getElementById('phone-country').textContent = countryInfo;

    // Operátor
    document.getElementById('phone-carrier').textContent = data.carrier || 'Neznámý';

    // Typ čísla
    let lineType = 'Neznámý';
    if (data.line_type === 'mobile') {
        lineType = 'Mobilní';
    } else if (data.line_type === 'landline') {
        lineType = 'Pevná linka';
    } else if (data.line_type === 'voip') {
        lineType = 'VoIP';
    } else if (data.line_type === 'premium') {
        lineType = 'Prémiové číslo';
    } else if (data.line_type === 'tollfree') {
        lineType = 'Bezplatné číslo';
    }
    document.getElementById('phone-type').textContent = lineType;
}

/**
 * Přepínání pole pro jiný zdroj telefonního čísla
 * @param {string} sourceValue - Hodnota vybraného zdroje
 */
function togglePhoneOtherSourceField(sourceValue) {
    const otherSourceContainer = document.getElementById('phone-other-source-container');
    if (sourceValue === 'other') {
        otherSourceContainer.style.display = 'block';
    } else {
        otherSourceContainer.style.display = 'none';
    }
}

/**
 * Přidání aktivity telefonního čísla
 */
function addPhoneActivity() {
    const activityTimeline = document.getElementById('activity-timeline');
    if (!activityTimeline) return;

    // Odstranění placeholderu, pokud existuje
    const placeholder = activityTimeline.querySelector('.timeline-placeholder');
    if (placeholder) {
        placeholder.remove();
    }

    // Vytvoření dialogu pro přidání aktivity
    const dialog = document.createElement('div');
    dialog.className = 'activity-dialog';
    dialog.innerHTML = `
        <div class="activity-dialog-content">
            <h4>Přidat aktivitu</h4>
            <div class="form-row">
                <div class="form-group">
                    <label>Datum aktivity</label>
                    <input type="date" class="form-control" id="activity-date" value="${new Date().toISOString().split('T')[0]}">
                </div>
                <div class="form-group">
                    <label>Typ aktivity</label>
                    <select class="form-control" id="activity-type">
                        <option value="call">Hovor</option>
                        <option value="sms">SMS</option>
                        <option value="registration">Registrace</option>
                        <option value="location">Lokace</option>
                        <option value="other">Jiná aktivita</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group long">
                    <label>Popis aktivity</label>
                    <textarea class="form-control" id="activity-description" rows="3" placeholder="Popište aktivitu..."></textarea>
                </div>
            </div>
            <div class="dialog-buttons">
                <button type="button" class="btn-inline cancel-activity">Zrušit</button>
                <button type="button" class="btn-inline save-activity">Uložit</button>
            </div>
        </div>
    `;

    // Přidání dialogu do stránky
    document.body.appendChild(dialog);

    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.cancel-activity').addEventListener('click', function() {
        dialog.remove();
    });

    dialog.querySelector('.save-activity').addEventListener('click', function() {
        const date = dialog.querySelector('#activity-date').value;
        const type = dialog.querySelector('#activity-type').value;
        const description = dialog.querySelector('#activity-description').value;

        if (!date || !description) {
            alert('Vyplňte všechna povinná pole.');
            return;
        }

        // Vytvoření nové aktivity
        const activityId = `activity-${Date.now()}`;
        const activityHTML = `
            <div class="timeline-item" id="${activityId}">
                <div class="timeline-date">${formatDate(date)}</div>
                <div class="timeline-content">
                    <div class="timeline-title">${getActivityTypeLabel(type)}</div>
                    <div class="timeline-description">${description}</div>
                </div>
                <div class="timeline-actions">
                    <button type="button" class="timeline-delete" data-activity-id="${activityId}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        // Přidání aktivity do časové osy
        activityTimeline.insertAdjacentHTML('beforeend', activityHTML);

        // Přidání event listeneru pro odstranění aktivity
        const deleteButton = document.querySelector(`#${activityId} .timeline-delete`);
        if (deleteButton) {
            deleteButton.addEventListener('click', function() {
                const activityId = this.getAttribute('data-activity-id');
                const activityElement = document.getElementById(activityId);
                if (activityElement) {
                    activityElement.remove();

                    // Pokud byla odstraněna poslední aktivita, zobrazit placeholder
                    if (activityTimeline.querySelectorAll('.timeline-item').length === 0) {
                        activityTimeline.innerHTML = `
                            <div class="timeline-placeholder">
                                <i class="fas fa-history"></i>
                                <p>Klikněte na "Přidat aktivitu" pro zaznamenání historie</p>
                            </div>
                        `;
                    }
                }
            });
        }

        // Zavření dialogu
        dialog.remove();
    });
}

/**
 * Formátování data
 * @param {string} dateString - Datum ve formátu YYYY-MM-DD
 * @returns {string} Formátované datum
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('cs-CZ');
}

/**
 * Získání popisku typu aktivity
 * @param {string} type - Typ aktivity
 * @returns {string} Popisek typu aktivity
 */
function getActivityTypeLabel(type) {
    const types = {
        'call': 'Hovor',
        'sms': 'SMS',
        'registration': 'Registrace',
        'location': 'Lokace',
        'other': 'Jiná aktivita'
    };
    return types[type] || 'Aktivita';
}

/**
 * Přidání připojeného zařízení
 */
function addConnectedDevice() {
    const connectedDevicesContainer = document.getElementById('connected-devices');
    if (!connectedDevicesContainer) return;

    const deviceId = `device-${Date.now()}`;
    const deviceHTML = `
        <div class="connected-device" id="${deviceId}">
            <div class="form-row">
                <div class="form-group">
                    <label>Typ zařízení</label>
                    <select class="form-control device-type-select">
                        <option value="">-- Vyberte typ --</option>
                        <option value="smartphone">Smartphone</option>
                        <option value="tablet">Tablet</option>
                        <option value="laptop">Notebook</option>
                        <option value="desktop">Stolní počítač</option>
                        <option value="other">Jiné</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Výrobce a model</label>
                    <input type="text" class="form-control" placeholder="Např. Apple iPhone 13, Samsung Galaxy S21...">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>Operační systém</label>
                    <input type="text" class="form-control" placeholder="Např. iOS 15, Android 12, Windows 11...">
                </div>
                <div class="form-group">
                    <label>IMEI / Sériové číslo</label>
                    <input type="text" class="form-control" placeholder="Zadejte IMEI nebo sériové číslo">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group long">
                    <label>Poznámka</label>
                    <textarea class="form-control" rows="2" placeholder="Další informace o zařízení..."></textarea>
                </div>
            </div>
            <button type="button" class="btn-inline remove-device" data-device-id="${deviceId}">
                <i class="fas fa-trash"></i> Odstranit zařízení
            </button>
        </div>
    `;

    connectedDevicesContainer.insertAdjacentHTML('beforeend', deviceHTML);

    // Přidání event listeneru pro odstranění zařízení
    const removeButton = document.querySelector(`#${deviceId} .remove-device`);
    if (removeButton) {
        removeButton.addEventListener('click', function() {
            const deviceId = this.getAttribute('data-device-id');
            const deviceElement = document.getElementById(deviceId);
            if (deviceElement) {
                deviceElement.remove();
            }
        });
    }
}

/**
 * Přidání kontaktu
 */
function addPhoneContact() {
    const phoneContactsContainer = document.getElementById('phone-contacts');
    if (!phoneContactsContainer) return;

    const contactId = `contact-${Date.now()}`;
    const contactHTML = `
        <div class="phone-contact" id="${contactId}">
            <div class="form-row">
                <div class="form-group">
                    <label>Jméno kontaktu</label>
                    <input type="text" class="form-control" placeholder="Zadejte jméno kontaktu">
                </div>
                <div class="form-group">
                    <label>Telefonní číslo</label>
                    <input type="tel" class="form-control" placeholder="Zadejte telefonní číslo">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>Vztah</label>
                    <select class="form-control">
                        <option value="">-- Vyberte vztah --</option>
                        <option value="family">Rodina</option>
                        <option value="friend">Přítel</option>
                        <option value="colleague">Kolega</option>
                        <option value="business">Obchodní partner</option>
                        <option value="other">Jiný</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Frekvence komunikace</label>
                    <select class="form-control">
                        <option value="">-- Vyberte frekvenci --</option>
                        <option value="daily">Denně</option>
                        <option value="weekly">Týdně</option>
                        <option value="monthly">Měsíčně</option>
                        <option value="rarely">Zřídka</option>
                        <option value="unknown">Neznámá</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group long">
                    <label>Poznámka</label>
                    <textarea class="form-control" rows="2" placeholder="Další informace o kontaktu..."></textarea>
                </div>
            </div>
            <button type="button" class="btn-inline remove-contact" data-contact-id="${contactId}">
                <i class="fas fa-trash"></i> Odstranit kontakt
            </button>
        </div>
    `;

    phoneContactsContainer.insertAdjacentHTML('beforeend', contactHTML);

    // Přidání event listeneru pro odstranění kontaktu
    const removeButton = document.querySelector(`#${contactId} .remove-contact`);
    if (removeButton) {
        removeButton.addEventListener('click', function() {
            const contactId = this.getAttribute('data-contact-id');
            const contactElement = document.getElementById(contactId);
            if (contactElement) {
                contactElement.remove();
            }
        });
    }
}

/**
 * Přidání lokace
 */
function addPhoneLocation() {
    const phoneLocationsContainer = document.getElementById('phone-locations');
    if (!phoneLocationsContainer) return;

    const locationId = `location-${Date.now()}`;
    const locationHTML = `
        <div class="phone-location" id="${locationId}">
            <div class="form-row">
                <div class="form-group">
                    <label>Datum a čas</label>
                    <input type="datetime-local" class="form-control">
                </div>
                <div class="form-group">
                    <label>Adresa / Místo</label>
                    <input type="text" class="form-control" placeholder="Zadejte adresu nebo místo">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>GPS souřadnice</label>
                    <input type="text" class="form-control" placeholder="Např. 50.0755, 14.4378">
                </div>
                <div class="form-group">
                    <label>Zdroj lokace</label>
                    <select class="form-control">
                        <option value="">-- Vyberte zdroj --</option>
                        <option value="gps">GPS</option>
                        <option value="cell">Mobilní síť</option>
                        <option value="wifi">Wi-Fi</option>
                        <option value="manual">Manuální zadání</option>
                        <option value="other">Jiný</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group long">
                    <label>Poznámka</label>
                    <textarea class="form-control" rows="2" placeholder="Další informace o lokaci..."></textarea>
                </div>
            </div>
            <button type="button" class="btn-inline remove-location" data-location-id="${locationId}">
                <i class="fas fa-trash"></i> Odstranit lokaci
            </button>
        </div>
    `;

    phoneLocationsContainer.insertAdjacentHTML('beforeend', locationHTML);

    // Přidání event listeneru pro odstranění lokace
    const removeButton = document.querySelector(`#${locationId} .remove-location`);
    if (removeButton) {
        removeButton.addEventListener('click', function() {
            const locationId = this.getAttribute('data-location-id');
            const locationElement = document.getElementById(locationId);
            if (locationElement) {
                locationElement.remove();
            }
        });
    }
}

/**
 * Inicializace fotogalerie pro telefonní analýzu
 */
function initPhonePhotoGallery() {
    // Přidání event listenerů pro tlačítka v galerii je již v initPhoneAnalysis

    // Zajistíme, že event listenery pro fotogalerii nebudou interferovat s tlačítky modulu
    document.querySelectorAll('.upload-photo, .paste-photo, .add-photo-url').forEach(button => {
        // Odstranění existujících event listenerů
        const newButton = button.cloneNode(true);
        button.parentNode.replaceChild(newButton, button);

        // Přidání nového event listeneru
        newButton.addEventListener('click', function(event) {
            // Zastavíme propagaci události, aby se nespustily jiné event listenery
            event.stopPropagation();
            event.preventDefault();

            // Zpracování podle typu tlačítka
            if (newButton.classList.contains('upload-photo')) {
                const uploadInput = document.getElementById('phone-photo-upload');
                if (uploadInput) {
                    uploadInput.click();
                }
            } else if (newButton.classList.contains('add-photo-url')) {
                const galleryId = newButton.getAttribute('data-gallery');
                addPhotoFromUrl(galleryId);
            } else if (newButton.classList.contains('paste-photo')) {
                alert('Pro vložení obrázku ze schránky stiskněte Ctrl+V kdekoli v modulu telefonní analýzy (mimo textová pole).');
            }
        });
    });
}

/**
 * Přepínání sekcí podle typu zdroje telefonního čísla
 * @param {string} sourceType - Typ zdroje
 */
function toggleSourceSections(sourceType) {
    const caseFileSection = document.getElementById('case-file-section');
    const isCdoSection = document.getElementById('is-cdo-section');

    if (caseFileSection) {
        caseFileSection.style.display = sourceType === 'case-file' ? 'block' : 'none';
    }

    if (isCdoSection) {
        isCdoSection.style.display = sourceType === 'is-cdo' ? 'block' : 'none';
    }
}

/**
 * Přidání fotografie z URL
 * @param {string} galleryId - ID galerie
 */
function addPhotoFromUrl(galleryId) {
    const gallery = document.getElementById(galleryId);
    if (!gallery) return;

    // Vytvoření dialogu pro přidání fotografie
    const dialog = document.createElement('div');
    dialog.className = 'photo-dialog';
    dialog.innerHTML = `
        <div class="photo-dialog-content">
            <h4>Přidat fotografii z URL</h4>
            <div class="form-row">
                <div class="form-group long">
                    <label>Název fotografie</label>
                    <input type="text" class="form-control" id="photo-title" placeholder="Zadejte název fotografie">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group long">
                    <label>URL fotografie</label>
                    <input type="text" class="form-control" id="photo-url" placeholder="Zadejte URL fotografie">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>Datum pořízení</label>
                    <input type="date" class="form-control" id="photo-date" value="${new Date().toISOString().split('T')[0]}">
                </div>
                <div class="form-group">
                    <label>Zdroj fotografie</label>
                    <select class="form-control" id="photo-source">
                        <option value="">-- Vyberte zdroj --</option>
                        <option value="social-media">Sociální sítě</option>
                        <option value="surveillance">Sledování</option>
                        <option value="evidence">Důkazní materiál</option>
                        <option value="public-source">Veřejný zdroj</option>
                        <option value="other">Jiný zdroj</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group long">
                    <label>Popis fotografie</label>
                    <textarea class="form-control" id="photo-description" rows="3" placeholder="Popište fotografii..."></textarea>
                </div>
            </div>
            <div class="dialog-buttons">
                <button type="button" class="btn-inline cancel-photo">Zrušit</button>
                <button type="button" class="btn-inline save-photo" data-gallery="${galleryId}">Uložit</button>
            </div>
        </div>
    `;

    // Přidání dialogu do stránky
    document.body.appendChild(dialog);

    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.cancel-photo').addEventListener('click', function() {
        dialog.remove();
    });

    dialog.querySelector('.save-photo').addEventListener('click', function() {
        const galleryId = this.getAttribute('data-gallery');
        const title = dialog.querySelector('#photo-title').value;
        const url = dialog.querySelector('#photo-url').value;
        const date = dialog.querySelector('#photo-date').value;
        const source = dialog.querySelector('#photo-source').value;
        const description = dialog.querySelector('#photo-description').value;

        if (!title || !url) {
            alert('Vyplňte název a URL fotografie.');
            return;
        }

        // Přidání fotografie do galerie
        addPhotoToGallery(galleryId, title, url, date, description, source);

        // Zavření dialogu
        dialog.remove();
    });
}

/**
 * Zpracování nahrání fotografie z počítače
 * @param {Event} event - Událost změny input[type=file]
 */
function handlePhotoUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    // Kontrola, zda jde o obrázek
    if (!file.type.match('image.*')) {
        alert('Vybraný soubor není obrázek.');
        return;
    }

    // Vytvoření URL pro náhled
    const imageUrl = URL.createObjectURL(file);

    // Otevření dialogu pro zadání informací o fotografii
    openPhotoInfoDialog('phone-photo-gallery', file.name, imageUrl);
}

/**
 * Zpracování vložení obrázku ze schránky
 * @param {Event} event - Událost vložení
 * @param {string} galleryId - ID galerie
 */
function handlePasteImage(event, galleryId) {
    // Kontrola, zda schránka obsahuje obrázek
    const items = (event.clipboardData || event.originalEvent.clipboardData).items;
    let blob = null;

    for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf('image') === 0) {
            blob = items[i].getAsFile();
            break;
        }
    }

    if (!blob) {
        alert('Schránka neobsahuje obrázek.');
        return;
    }

    // Vytvoření URL pro náhled
    const imageUrl = URL.createObjectURL(blob);

    // Otevření dialogu pro zadání informací o fotografii
    openPhotoInfoDialog(galleryId, 'Vložený obrázek', imageUrl);
}

/**
 * Otevření dialogu pro zadání informací o fotografii
 * @param {string} galleryId - ID galerie
 * @param {string} fileName - Název souboru
 * @param {string} imageUrl - URL obrázku
 */
function openPhotoInfoDialog(galleryId, fileName, imageUrl) {
    // Vytvoření dialogu pro zadání informací o fotografii
    const dialog = document.createElement('div');
    dialog.className = 'photo-dialog';
    dialog.innerHTML = `
        <div class="photo-dialog-content">
            <h4>Informace o fotografii</h4>
            <div class="form-row">
                <div class="form-group long">
                    <div class="photo-preview-dialog">
                        <img src="${imageUrl}" alt="${fileName}" style="max-width: 100%; max-height: 200px;">
                    </div>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group long">
                    <label>Název fotografie</label>
                    <input type="text" class="form-control" id="photo-title" value="${fileName.replace(/\.[^/.]+$/, '')}" placeholder="Zadejte název fotografie">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>Datum pořízení</label>
                    <input type="date" class="form-control" id="photo-date" value="${new Date().toISOString().split('T')[0]}">
                </div>
                <div class="form-group">
                    <label>Zdroj fotografie</label>
                    <select class="form-control" id="photo-source">
                        <option value="">-- Vyberte zdroj --</option>
                        <option value="social-media">Sociální sítě</option>
                        <option value="surveillance">Sledování</option>
                        <option value="evidence">Důkazní materiál</option>
                        <option value="public-source">Veřejný zdroj</option>
                        <option value="other">Jiný zdroj</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group long">
                    <label>Popis fotografie</label>
                    <textarea class="form-control" id="photo-description" rows="3" placeholder="Popište fotografii..."></textarea>
                </div>
            </div>
            <div class="dialog-buttons">
                <button type="button" class="btn-inline cancel-photo">Zrušit</button>
                <button type="button" class="btn-inline save-photo" data-gallery="${galleryId}" data-image-url="${imageUrl}">Uložit</button>
            </div>
        </div>
    `;

    // Přidání dialogu do stránky
    document.body.appendChild(dialog);

    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.cancel-photo').addEventListener('click', function() {
        dialog.remove();
        URL.revokeObjectURL(imageUrl); // Uvolnění URL
    });

    dialog.querySelector('.save-photo').addEventListener('click', function() {
        const galleryId = this.getAttribute('data-gallery');
        const imageUrl = this.getAttribute('data-image-url');
        const title = dialog.querySelector('#photo-title').value;
        const date = dialog.querySelector('#photo-date').value;
        const source = dialog.querySelector('#photo-source').value;
        const description = dialog.querySelector('#photo-description').value;

        if (!title) {
            alert('Vyplňte název fotografie.');
            return;
        }

        // Přidání fotografie do galerie
        addPhotoToGallery(galleryId, title, imageUrl, date, description, source);

        // Zavření dialogu
        dialog.remove();
    });
}

/**
 * Přidání fotografie do galerie
 * @param {string} galleryId - ID galerie
 * @param {string} title - Název fotografie
 * @param {string} imageUrl - URL obrázku
 * @param {string} date - Datum pořízení
 * @param {string} description - Popis fotografie
 * @param {string} source - Zdroj fotografie
 */
function addPhotoToGallery(galleryId, title, imageUrl, date, description, source) {
    const gallery = document.getElementById(galleryId);
    if (!gallery) return;

    // Vytvoření nové fotografie
    const photoId = `photo-${Date.now()}`;
    const photoHTML = `
        <div class="photo-item" id="${photoId}">
            <div class="photo-preview">
                <img src="${imageUrl}" alt="${title}" onerror="this.src='https://via.placeholder.com/150x150?text=Chyba+načítání'">
            </div>
            <div class="photo-info">
                <div class="photo-title">${title}</div>
                <div class="photo-date">${formatDate(date)}</div>
                <div class="photo-description">${description || 'Bez popisu'}</div>
            </div>
            <div class="photo-actions">
                <button type="button" class="photo-view" data-photo-url="${imageUrl}" data-photo-title="${title}">
                    <i class="fas fa-search-plus"></i>
                </button>
                <button type="button" class="photo-delete" data-photo-id="${photoId}">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;

    // Přidání fotografie do galerie
    gallery.insertAdjacentHTML('beforeend', photoHTML);

    // Přidání event listenerů pro tlačítka
    const viewButton = document.querySelector(`#${photoId} .photo-view`);
    if (viewButton) {
        viewButton.addEventListener('click', function() {
            const photoUrl = this.getAttribute('data-photo-url');
            const photoTitle = this.getAttribute('data-photo-title');
            showPhotoViewer(photoUrl, photoTitle);
        });
    }

    const deleteButton = document.querySelector(`#${photoId} .photo-delete`);
    if (deleteButton) {
        deleteButton.addEventListener('click', function() {
            const photoId = this.getAttribute('data-photo-id');
            const photoElement = document.getElementById(photoId);
            if (photoElement) {
                photoElement.remove();
            }
        });
    }
}

/**
 * Zobrazení prohlížeče fotografií
 * @param {string} photoUrl - URL fotografie
 * @param {string} photoTitle - Název fotografie
 */
function showPhotoViewer(photoUrl, photoTitle) {
    // Vytvoření prohlížeče fotografií
    const viewer = document.createElement('div');
    viewer.className = 'photo-viewer';
    viewer.innerHTML = `
        <div class="photo-viewer-content">
            <div class="photo-viewer-header">
                <div class="photo-viewer-title">${photoTitle}</div>
                <button type="button" class="photo-viewer-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="photo-viewer-body">
                <img src="${photoUrl}" alt="${photoTitle}" onerror="this.src='https://via.placeholder.com/800x600?text=Chyba+načítání'">
            </div>
        </div>
    `;

    // Přidání prohlížeče do stránky
    document.body.appendChild(viewer);

    // Přidání event listeneru pro tlačítko zavření
    viewer.querySelector('.photo-viewer-close').addEventListener('click', function() {
        viewer.remove();
    });

    // Přidání event listeneru pro kliknutí mimo obsah prohlížeče
    viewer.addEventListener('click', function(event) {
        if (event.target === viewer) {
            viewer.remove();
        }
    });
}

/**
 * Přidání aktivity telefonního čísla
 */
function addPhoneActivity() {
    const activityTimeline = document.getElementById('activity-timeline');
    if (!activityTimeline) return;

    // Odstranění placeholderu, pokud existuje
    const placeholder = activityTimeline.querySelector('.timeline-placeholder');
    if (placeholder) {
        placeholder.remove();
    }

    // Vytvoření dialogu pro přidání aktivity
    const dialog = document.createElement('div');
    dialog.className = 'activity-dialog';
    dialog.innerHTML = `
        <div class="activity-dialog-content">
            <h4>Přidat aktivitu</h4>
            <div class="form-row">
                <div class="form-group">
                    <label>Datum aktivity</label>
                    <input type="date" class="form-control" id="activity-date" value="${new Date().toISOString().split('T')[0]}">
                </div>
                <div class="form-group">
                    <label>Typ aktivity</label>
                    <select class="form-control" id="activity-type">
                        <option value="call">Hovor</option>
                        <option value="sms">SMS</option>
                        <option value="registration">Registrace</option>
                        <option value="location">Lokace</option>
                        <option value="other">Jiná aktivita</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group long">
                    <label>Popis aktivity</label>
                    <textarea class="form-control" id="activity-description" rows="3" placeholder="Popište aktivitu..."></textarea>
                </div>
            </div>
            <div class="dialog-buttons">
                <button type="button" class="btn-inline cancel-activity">Zrušit</button>
                <button type="button" class="btn-inline save-activity">Uložit</button>
            </div>
        </div>
    `;

    // Přidání dialogu do stránky
    document.body.appendChild(dialog);

    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.cancel-activity').addEventListener('click', function() {
        dialog.remove();
    });

    dialog.querySelector('.save-activity').addEventListener('click', function() {
        const date = dialog.querySelector('#activity-date').value;
        const type = dialog.querySelector('#activity-type').value;
        const description = dialog.querySelector('#activity-description').value;

        if (!date || !description) {
            alert('Vyplňte všechna povinná pole.');
            return;
        }

        // Vytvoření nové aktivity
        const activityId = `activity-${Date.now()}`;
        const activityHTML = `
            <div class="timeline-item" id="${activityId}">
                <div class="timeline-date">${formatDate(date)}</div>
                <div class="timeline-content">
                    <div class="timeline-title">${getActivityTypeLabel(type)}</div>
                    <div class="timeline-description">${description}</div>
                </div>
                <div class="timeline-actions">
                    <button type="button" class="timeline-delete" data-activity-id="${activityId}">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;

        // Přidání aktivity do časové osy
        activityTimeline.insertAdjacentHTML('beforeend', activityHTML);

        // Přidání event listeneru pro odstranění aktivity
        const deleteButton = document.querySelector(`#${activityId} .timeline-delete`);
        if (deleteButton) {
            deleteButton.addEventListener('click', function() {
                const activityId = this.getAttribute('data-activity-id');
                const activityElement = document.getElementById(activityId);
                if (activityElement) {
                    activityElement.remove();

                    // Pokud byla odstraněna poslední aktivita, zobrazit placeholder
                    if (activityTimeline.querySelectorAll('.timeline-item').length === 0) {
                        activityTimeline.innerHTML = `
                            <div class="timeline-placeholder">
                                <i class="fas fa-history"></i>
                                <p>Klikněte na "Přidat aktivitu" pro zaznamenání historie</p>
                            </div>
                        `;
                    }
                }
            });
        }

        // Zavření dialogu
        dialog.remove();
    });
}

/**
 * Formátování data
 * @param {string} dateString - Datum ve formátu YYYY-MM-DD
 * @returns {string} Formátované datum
 */
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('cs-CZ');
}

/**
 * Získání popisku typu aktivity
 * @param {string} type - Typ aktivity
 * @returns {string} Popisek typu aktivity
 */
function getActivityTypeLabel(type) {
    const types = {
        'call': 'Hovor',
        'sms': 'SMS',
        'registration': 'Registrace',
        'location': 'Lokace',
        'other': 'Jiná aktivita'
    };
    return types[type] || 'Aktivita';
}

/**
 * Přidání připojeného zařízení
 */
function addConnectedDevice() {
    const connectedDevicesContainer = document.getElementById('connected-devices');
    if (!connectedDevicesContainer) return;

    const deviceId = `device-${Date.now()}`;
    const deviceHTML = `
        <div class="connected-device" id="${deviceId}">
            <div class="form-row">
                <div class="form-group">
                    <label>Typ zařízení</label>
                    <select class="form-control device-type-select">
                        <option value="">-- Vyberte typ --</option>
                        <option value="smartphone">Smartphone</option>
                        <option value="tablet">Tablet</option>
                        <option value="laptop">Notebook</option>
                        <option value="desktop">Stolní počítač</option>
                        <option value="other">Jiné</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Výrobce a model</label>
                    <input type="text" class="form-control" placeholder="Např. Apple iPhone 13, Samsung Galaxy S21...">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>Operační systém</label>
                    <input type="text" class="form-control" placeholder="Např. iOS 15, Android 12, Windows 11...">
                </div>
                <div class="form-group">
                    <label>IMEI / Sériové číslo</label>
                    <input type="text" class="form-control" placeholder="Zadejte IMEI nebo sériové číslo">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group long">
                    <label>Poznámka</label>
                    <textarea class="form-control" rows="2" placeholder="Další informace o zařízení..."></textarea>
                </div>
            </div>
            <button type="button" class="btn-inline remove-device" data-device-id="${deviceId}">
                <i class="fas fa-trash"></i> Odstranit zařízení
            </button>
        </div>
    `;

    connectedDevicesContainer.insertAdjacentHTML('beforeend', deviceHTML);

    // Přidání event listeneru pro odstranění zařízení
    const removeButton = document.querySelector(`#${deviceId} .remove-device`);
    if (removeButton) {
        removeButton.addEventListener('click', function() {
            const deviceId = this.getAttribute('data-device-id');
            const deviceElement = document.getElementById(deviceId);
            if (deviceElement) {
                deviceElement.remove();
            }
        });
    }
}

/**
 * Přidání kontaktu
 */
function addPhoneContact() {
    const phoneContactsContainer = document.getElementById('phone-contacts');
    if (!phoneContactsContainer) return;

    const contactId = `contact-${Date.now()}`;
    const contactHTML = `
        <div class="phone-contact" id="${contactId}">
            <div class="form-row">
                <div class="form-group">
                    <label>Jméno kontaktu</label>
                    <input type="text" class="form-control" placeholder="Zadejte jméno kontaktu">
                </div>
                <div class="form-group">
                    <label>Telefonní číslo</label>
                    <input type="tel" class="form-control" placeholder="Zadejte telefonní číslo">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>Vztah</label>
                    <select class="form-control">
                        <option value="">-- Vyberte vztah --</option>
                        <option value="family">Rodina</option>
                        <option value="friend">Přítel</option>
                        <option value="colleague">Kolega</option>
                        <option value="business">Obchodní partner</option>
                        <option value="other">Jiný</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Frekvence komunikace</label>
                    <select class="form-control">
                        <option value="">-- Vyberte frekvenci --</option>
                        <option value="daily">Denně</option>
                        <option value="weekly">Týdně</option>
                        <option value="monthly">Měsíčně</option>
                        <option value="rarely">Zřídka</option>
                        <option value="unknown">Neznámá</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group long">
                    <label>Poznámka</label>
                    <textarea class="form-control" rows="2" placeholder="Další informace o kontaktu..."></textarea>
                </div>
            </div>
            <button type="button" class="btn-inline remove-contact" data-contact-id="${contactId}">
                <i class="fas fa-trash"></i> Odstranit kontakt
            </button>
        </div>
    `;

    phoneContactsContainer.insertAdjacentHTML('beforeend', contactHTML);

    // Přidání event listeneru pro odstranění kontaktu
    const removeButton = document.querySelector(`#${contactId} .remove-contact`);
    if (removeButton) {
        removeButton.addEventListener('click', function() {
            const contactId = this.getAttribute('data-contact-id');
            const contactElement = document.getElementById(contactId);
            if (contactElement) {
                contactElement.remove();
            }
        });
    }
}

/**
 * Přidání lokace
 */
function addPhoneLocation() {
    const phoneLocationsContainer = document.getElementById('phone-locations');
    if (!phoneLocationsContainer) return;

    const locationId = `location-${Date.now()}`;
    const locationHTML = `
        <div class="phone-location" id="${locationId}">
            <div class="form-row">
                <div class="form-group">
                    <label>Datum a čas</label>
                    <input type="datetime-local" class="form-control">
                </div>
                <div class="form-group">
                    <label>Adresa / Místo</label>
                    <input type="text" class="form-control" placeholder="Zadejte adresu nebo místo">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>GPS souřadnice</label>
                    <input type="text" class="form-control" placeholder="Např. 50.0755, 14.4378">
                </div>
                <div class="form-group">
                    <label>Zdroj lokace</label>
                    <select class="form-control">
                        <option value="">-- Vyberte zdroj --</option>
                        <option value="gps">GPS</option>
                        <option value="cell">Mobilní síť</option>
                        <option value="wifi">Wi-Fi</option>
                        <option value="manual">Manuální zadání</option>
                        <option value="other">Jiný</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group long">
                    <label>Poznámka</label>
                    <textarea class="form-control" rows="2" placeholder="Další informace o lokaci..."></textarea>
                </div>
            </div>
            <button type="button" class="btn-inline remove-location" data-location-id="${locationId}">
                <i class="fas fa-trash"></i> Odstranit lokaci
            </button>
        </div>
    `;

    phoneLocationsContainer.insertAdjacentHTML('beforeend', locationHTML);

    // Přidání event listeneru pro odstranění lokace
    const removeButton = document.querySelector(`#${locationId} .remove-location`);
    if (removeButton) {
        removeButton.addEventListener('click', function() {
            const locationId = this.getAttribute('data-location-id');
            const locationElement = document.getElementById(locationId);
            if (locationElement) {
                locationElement.remove();
            }
        });
    }
}