"use client";

import type { SubmitHand<PERSON> } from 'react-hook-form';
import { useForm, useField<PERSON><PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import type {
  BusinessActivityModuleData, OsvcInfo, CompanyInvolvementRecord,
  PhotoMetadata, Subject, PhysicalPersonSubject, LegalEntitySubject,
  BusinessActivityStatus, MainBusinessType, YesNoUnknown
} from '@/types';
import {
  businessActivityExtendedSchema,
  foreignBusinessInvolvementSchema,
  insolvencyRecordSchema,
  realEstateRecordSchema,
  personnelConnectionSchema,
  businessPhotoSchema
} from "./schemas";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { PlusCircle, Trash2, Briefcase, Building, Loader2, Search, UserCircle as UserCircleIcon, List } from 'lucide-react'; // Renamed UserCircle to avoid conflict
import { useToast } from '@/hooks/use-toast';
import { db } from '@/lib/firebase';
import { doc, setDoc, serverTimestamp } from 'firebase/firestore';
import { cn } from '@/lib/utils';
import { useState } from "react";
import {
  Form,
  FormField,
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { lookupCompanyInAresDirectly } from "@/ai/flows/ares-direct-lookup-flow";
import { CompanySearchDialog } from "./CompanySearchDialog";
import { OsvcSearchDialog } from "./OsvcSearchDialog";
import { StatutoryRepresentativesList } from "./StatutoryRepresentativesList";
import { CompanyStatusBadge } from "./CompanyStatusBadge";
import { ForeignBusinessSection } from "./ForeignBusinessSection";
import { InsolvencySection } from "./InsolvencySection";
import { PhotoDocumentationSection } from "./PhotoDocumentationSection";
import { RealEstateSection } from "./RealEstateSection";
import { PersonnelConnectionsSection } from "./PersonnelConnectionsSection";


// --- Zod Schemas ---
const osvcInfoSchema = z.object({
  ico: z.string()
    .regex(/^[0-9]{8}$/, "IČO musí obsahovat přesně 8 číslic.")
    .optional()
    .or(z.literal('')),
  dic: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  fieldsOfActivity: z.string().optional(),
  actualBusinessScope: z.string().optional(),
  businessSeat: z.string().optional(),
  establishmentAddress: z.string().optional(),
  estimatedAnnualTurnover: z.preprocess(
    (val) => (val === "" || val === null || val === undefined || Number.isNaN(Number(val))) ? null : Number(val),
    z.number().nullable().optional()
  ),
  estimatedCollaboratorsCount: z.preprocess(
    (val) => (val === "" || val === null || val === undefined || Number.isNaN(Number(val))) ? null : Number(val),
    z.number().nullable().optional()
  ),
});

const companyInvolvementRecordSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Název společnosti je povinný.").optional(),
  ico: z.string().length(8, "IČO musí mít přesně 8 číslic.").optional().or(z.literal('')),
  address: z.string().optional(),
  legalForm: z.string().optional(),
  establishmentDate: z.string().optional(),
  role: z.string().optional(),
  ownership: z.string().optional(),
  dic: z.string().optional(),
  businessScope: z.string().optional(),
  status: z.string().optional(),
  statutoryRepresentatives: z.array(z.string()).optional(),
  notes: z.string().optional(),
});


const businessActivityStatusEnum: [BusinessActivityStatus, ...BusinessActivityStatus[]] = ["active", "passive", "former", "no", "unknown"];
const mainBusinessTypeEnum: [MainBusinessType, ...MainBusinessType[]] = ["osvc", "sro", "as", "ks", "vos", "druzstvo", "spolecenstvi", "neziskova", "zahranicni", "other"];


const businessActivityModuleSchema = z.object({
  activityStatus: z.enum(businessActivityStatusEnum).optional(),
  mainBusinessType: z.enum(mainBusinessTypeEnum).optional(),
  otherBusinessTypeDetail: z.string().optional(),
  osvcInfo: osvcInfoSchema.optional(),
  companyInvolvements: z.array(companyInvolvementRecordSchema).optional().default([]),
  generalNotes: z.string().optional(),

  // Foreign Business Activities
  foreignBusinessSummary: z.string().optional(),
  foreignInvolvements: z.array(foreignBusinessInvolvementSchema).optional().default([]),

  // Insolvency and Execution
  insolvencyStatus: z.enum(["yes", "no", "unknown"] as [YesNoUnknown, ...YesNoUnknown[]]).optional(),
  insolvencyDetails: z.string().optional(),
  insolvencyRecords: z.array(insolvencyRecordSchema).optional().default([]),

  // Real Estate
  realEstateRecords: z.array(realEstateRecordSchema).optional().default([]),

  // Personnel Connections
  personnelConnections: z.string().optional(),
  personnelConnectionRecords: z.array(personnelConnectionSchema).optional().default([]),

  // Photo Documentation
  businessPhotos: z.array(businessPhotoSchema).optional().default([]),
}).superRefine((data, ctx) => {
  if (data.mainBusinessType === 'other' && !data.otherBusinessTypeDetail?.trim()) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Upřesnění formy podnikání je povinné.", path: ['otherBusinessTypeDetail'] });
  }

  // Validate IČO format for OSVČ
  if (data.mainBusinessType === 'osvc' && data.osvcInfo?.ico && data.osvcInfo.ico.length > 0) {
    const icoRegex = /^[0-9]{8}$/;
    if (!icoRegex.test(data.osvcInfo.ico)) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: "IČO pro OSVČ musí obsahovat přesně 8 číslic.",
        path: ['osvcInfo.ico']
      });
    }
  }
});

type BusinessActivityFormValues = z.infer<typeof businessActivityModuleSchema>;

interface BusinessActivityFormProps {
  caseId: string;
  subject: Subject;
  existingData: BusinessActivityModuleData | null;
  onSave: (moduleId: string, wasNew: boolean) => void;
}

const activityStatusOptions = [
  { value: "active", label: "Aktivně podniká" },
  { value: "passive", label: "Pasivně podniká (tiché společnictví)" },
  { value: "former", label: "Dříve podnikal(a)" },
  { value: "no", label: "Nepodniká" },
  { value: "unknown", label: "Nezjištěno" },
];

const businessTypeOptions = [
  { value: "osvc", label: "OSVČ" },
  { value: "sro", label: "Společnost s ručením omezeným (s.r.o.)" },
  { value: "as", label: "Akciová společnost (a.s.)" },
  { value: "ks", label: "Komanditní společnost (k.s.)" },
  { value: "vos", label: "Veřejná obchodní společnost (v.o.s.)" },
  { value: "druzstvo", label: "Družstvo" },
  { value: "spolecenstvi", label: "Společenství vlastníků jednotek (SVJ)" },
  { value: "neziskova", label: "Nezisková organizace" },
  { value: "zahranicni", label: "Zahraniční subjekt" },
  { value: "other", label: "Jiná forma" },
];


export function BusinessActivityForm({ caseId, subject, existingData, onSave }: BusinessActivityFormProps) {
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [isAresLoading, setIsAresLoading] = useState<number | null>(null);
  const moduleId = "business_activity";

  const form = useForm<BusinessActivityFormValues>({
    resolver: zodResolver(businessActivityModuleSchema),
    defaultValues: {
      activityStatus: existingData?.activityStatus || undefined,
      mainBusinessType: existingData?.mainBusinessType || undefined,
      otherBusinessTypeDetail: existingData?.otherBusinessTypeDetail || "",
      osvcInfo: existingData?.osvcInfo || { ico: "", dic: "", startDate: "", endDate: "", fieldsOfActivity: "", actualBusinessScope: "", businessSeat: "", establishmentAddress: "", estimatedAnnualTurnover: null, estimatedCollaboratorsCount: null },
      companyInvolvements: existingData?.companyInvolvements?.map(c => ({...c, id: c.id || crypto.randomUUID()})) || [],
      generalNotes: existingData?.generalNotes || "",

      // Foreign Business Activities
      foreignBusinessSummary: existingData?.foreignBusinessSummary || "",
      foreignInvolvements: existingData?.foreignInvolvements?.map(f => ({...f, id: f.id || crypto.randomUUID()})) || [],

      // Insolvency and Execution
      insolvencyStatus: existingData?.insolvencyStatus || undefined,
      insolvencyDetails: existingData?.insolvencyDetails || "",
      insolvencyRecords: existingData?.insolvencyRecords?.map(i => ({...i, id: i.id || crypto.randomUUID()})) || [],

      // Real Estate
      realEstateRecords: existingData?.realEstateRecords?.map(r => ({...r, id: r.id || crypto.randomUUID()})) || [],

      // Personnel Connections
      personnelConnections: existingData?.personnelConnections || "",
      personnelConnectionRecords: existingData?.personnelConnectionRecords?.map(p => ({...p, id: p.id || crypto.randomUUID()})) || [],

      // Photo Documentation
      businessPhotos: existingData?.businessPhotos?.map(p => ({...p, id: p.id || crypto.randomUUID()})) || [],
    },
  });

  const { fields: companyFields, append: appendCompany, remove: removeCompany } = useFieldArray({
    control: form.control,
    name: "companyInvolvements",
  });

  const watchedMainBusinessType = form.watch("mainBusinessType");

  const handleAresLookupForCompany = async (index: number) => {
    const companyIco = form.getValues(`companyInvolvements.${index}.ico`);
    if (!companyIco || companyIco.trim().length !== 8) {
      toast({ title: "Chybné IČO", description: "Zadejte platné 8místné IČO pro vyhledání v ARES.", variant: "destructive" });
      form.setError(`companyInvolvements.${index}.ico`, { type: "manual", message: "IČO musí mít 8 číslic." });
      return;
    }
    setIsAresLoading(index);
    try {
      const aresData = await lookupCompanyInAresDirectly({ companyId: companyIco });
      if (aresData) {
        // Set all available data from ARES
        form.setValue(`companyInvolvements.${index}.name`, aresData.name, { shouldValidate: true });
        form.setValue(`companyInvolvements.${index}.address`, aresData.address, { shouldValidate: true });
        form.setValue(`companyInvolvements.${index}.legalForm`, aresData.legalForm, { shouldValidate: true });
        form.setValue(`companyInvolvements.${index}.establishmentDate`, aresData.establishmentDate, { shouldValidate: true });
        form.setValue(`companyInvolvements.${index}.dic`, aresData.dic, { shouldValidate: true });
        form.setValue(`companyInvolvements.${index}.businessScope`, aresData.businessScope, { shouldValidate: true });
        form.setValue(`companyInvolvements.${index}.status`, aresData.status, { shouldValidate: true });
        form.setValue(`companyInvolvements.${index}.statutoryRepresentatives`, aresData.statutoryRepresentatives, { shouldValidate: true });

        // Add a note about VAT status if available
        const vatStatus = aresData.isVatPayer ? "Plátce DPH" : "Není plátce DPH";
        const currentNotes = form.getValues(`companyInvolvements.${index}.notes`) || "";
        if (aresData.isVatPayer !== undefined) {
          form.setValue(
            `companyInvolvements.${index}.notes`,
            currentNotes ? `${currentNotes}\n${vatStatus}` : vatStatus,
            { shouldValidate: true }
          );
        }

        toast({
          title: "Data z ARES načtena",
          description: `Nalezen subjekt: ${aresData.name}${aresData.status ? ` (${aresData.status})` : ''}`
        });
      } else {
        toast({ title: "Data z ARES", description: "Pro zadané IČO nebyla nalezena žádná data.", variant: "default" });
      }
    } catch (error: any) {
      toast({ title: "Chyba při načítání z ARES", description: error.message, variant: "destructive" });
    } finally {
      setIsAresLoading(null);
    }
  };

  // Handler for selecting a company from the search dialog
  const handleSelectCompanyFromSearch = async (ico: string) => {
    // Create a new company involvement with the selected ICO
    const newCompanyId = crypto.randomUUID();
    appendCompany({ id: newCompanyId, name: "", ico });

    // Wait for the next render cycle to ensure the new field is available
    setTimeout(() => {
      // Get the index of the newly added company
      const newIndex = companyFields.length;
      // Lookup the company details in ARES
      handleAresLookupForCompany(newIndex);
    }, 100);
  };

  const onSubmitHandler: SubmitHandler<BusinessActivityFormValues> = async (data) => {
    setIsSaving(true);
    try {
      const moduleDocRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);

      // Funkce pro odstranění undefined hodnot z objektu (rekurzivně)
      const removeUndefinedValues = (obj: any): any => {
        if (obj === null || obj === undefined) {
          return null;
        }
        
        if (Array.isArray(obj)) {
          return obj.map(removeUndefinedValues).filter(item => item !== undefined);
        }
        
        if (typeof obj === 'object') {
          const cleaned: any = {};
          for (const [key, value] of Object.entries(obj)) {
            if (value !== undefined) {
              const cleanedValue = removeUndefinedValues(value);
              if (cleanedValue !== undefined) {
                cleaned[key] = cleanedValue;
              }
            }
          }
          return cleaned;
        }
        
        return obj;
      };

      // Odstranit undefined hodnoty před uložením
      const cleanedData = removeUndefinedValues(data);

      const dataToSave: BusinessActivityModuleData = {
        ...existingData, // zachováme existující data z nezobrazených sekcí
        ...cleanedData, // přepíšeme daty z formuláře
        subjectId: subject.id,
        lastUpdatedAt: serverTimestamp(),
        createdAt: existingData?.createdAt || serverTimestamp(),
      };
      await setDoc(moduleDocRef, dataToSave, { merge: true });
      toast({ title: "Data modulu Podnikatelské aktivity uložena." });
      const wasNew = !existingData || !existingData.createdAt;
      onSave(moduleId, wasNew);
    } catch (error: any) {
      toast({ title: "Chyba ukládání dat modulu", description: error.message, variant: "destructive" });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmitHandler)} className="space-y-8">
        <Card className="shadow-lg border border-primary/30">
          <CardHeader>
            <CardTitle className="text-xl flex items-center"><Briefcase className="mr-3 h-6 w-6 text-primary"/>Podnikatelské aktivity: {subject.type === 'physical' ? `${(subject as PhysicalPersonSubject).firstName} ${(subject as PhysicalPersonSubject).lastName}` : (subject as LegalEntitySubject).name}</CardTitle>
            <CardDescription>Zadejte informace o podnikatelských aktivitách subjektu.</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6 pt-4">

            <Card>
              <CardHeader><CardTitle className="text-lg">Základní informace o podnikání</CardTitle></CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
                <FormItemSelectRHF label="Podnikatelská aktivita" name="activityStatus" control={form.control} options={activityStatusOptions} placeholder="-- Vyberte status --" />
                <FormItemSelectRHF label="Hlavní forma podnikání" name="mainBusinessType" control={form.control} options={businessTypeOptions} placeholder="-- Vyberte formu --" />
                {watchedMainBusinessType === 'other' && (
                  <FormItemRHF label="Upřesnění formy podnikání" name="otherBusinessTypeDetail" control={form.control} placeholder="Uveďte jinou formu" className="md:col-span-2" />
                )}
              </CardContent>
            </Card>

            {watchedMainBusinessType === 'osvc' && (
              <Card>
                <CardHeader><CardTitle className="text-lg flex items-center"><UserCircleIcon className="mr-2 h-5 w-5"/>Údaje o OSVČ</CardTitle></CardHeader>
                <CardContent className="space-y-4 pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-3 items-end">
                      <FormItemRHF label="IČO" name="osvcInfo.ico" control={form.control} placeholder="IČO pro OSVČ (8 číslic)" className="md:col-span-2" />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="self-end mb-1 h-10 w-full md:w-auto"
                        onClick={() => {
                          const ico = form.getValues("osvcInfo.ico");
                          // Validate IČO format
                          if (!ico || ico.length !== 8 || !/^\d{8}$/.test(ico)) {
                            toast({
                              title: "Chybné IČO",
                              description: "IČO musí obsahovat přesně 8 číslic.",
                              variant: "destructive"
                            });
                            form.setError("osvcInfo.ico", {
                              type: "manual",
                              message: "IČO musí obsahovat přesně 8 číslic."
                            });
                            return;
                          }

                          lookupCompanyInAresDirectly({ companyId: ico })
                            .then(data => {
                              form.setValue("osvcInfo.dic", data.dic || "", { shouldValidate: true });
                              form.setValue("osvcInfo.businessSeat", data.address, { shouldValidate: true });
                              form.setValue("osvcInfo.fieldsOfActivity", data.businessScope || "", { shouldValidate: true });
                              toast({
                                title: "Data z ARES načtena",
                                description: `Nalezen subjekt: ${data.name}`
                              });
                            })
                            .catch(error => {
                              toast({
                                title: "Chyba při načítání z ARES",
                                description: error.message,
                                variant: "destructive"
                              });
                            });
                        }}
                      >
                        <Search className="mr-2 h-4 w-4" />
                        Načíst z ARES
                      </Button>
                    </div>
                    <FormItemRHF label="DIČ" name="osvcInfo.dic" control={form.control} placeholder="Daňové identifikační číslo" />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF label="Datum zahájení činnosti" name="osvcInfo.startDate" control={form.control} type="date" />
                    <FormItemRHF label="Datum ukončení činnosti" name="osvcInfo.endDate" control={form.control} type="date" />
                  </div>
                  <div className="relative">
                    <FormItemRHF label="Obory činnosti (dle živn. rejstříku)" name="osvcInfo.fieldsOfActivity" control={form.control} as="textarea" rows={3} placeholder="Seznam oborů činnosti..." />
                    <div className="absolute right-0 top-0">
                      <OsvcSearchDialog
                        onSelectOsvc={(ico) => {
                          form.setValue("osvcInfo.ico", ico, { shouldValidate: true });
                          // Trigger the ARES lookup after setting the ICO
                          setTimeout(() => {
                            const ico = form.getValues("osvcInfo.ico");
                            // Validate IČO format
                            if (!ico || ico.length !== 8 || !/^\d{8}$/.test(ico)) {
                              toast({
                                title: "Chybné IČO",
                                description: "IČO musí obsahovat přesně 8 číslic.",
                                variant: "destructive"
                              });
                              form.setError("osvcInfo.ico", {
                                type: "manual",
                                message: "IČO musí obsahovat přesně 8 číslic."
                              });
                              return;
                            }

                            lookupCompanyInAresDirectly({ companyId: ico })
                              .then(data => {
                                form.setValue("osvcInfo.dic", data.dic || "", { shouldValidate: true });
                                form.setValue("osvcInfo.businessSeat", data.address, { shouldValidate: true });
                                form.setValue("osvcInfo.fieldsOfActivity", data.businessScope || "", { shouldValidate: true });
                                toast({
                                  title: "Data z ARES načtena",
                                  description: `Nalezen subjekt: ${data.name}`
                                });
                              })
                              .catch(error => {
                                toast({
                                  title: "Chyba při načítání z ARES",
                                  description: error.message,
                                  variant: "destructive"
                                });
                              });
                          }, 100);
                        }}
                        trigger={
                          <Button type="button" variant="outline" size="sm">
                            <List className="mr-2 h-4 w-4"/>Vyhledat OSVČ
                          </Button>
                        }
                      />
                    </div>
                  </div>
                  <FormItemRHF label="Skutečný předmět podnikání" name="osvcInfo.actualBusinessScope" control={form.control} as="textarea" rows={3} placeholder="Reálný předmět podnikání..." />
                   <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF label="Sídlo podnikání" name="osvcInfo.businessSeat" control={form.control} placeholder="Adresa sídla" />
                    <FormItemRHF label="Provozovna" name="osvcInfo.establishmentAddress" control={form.control} placeholder="Adresa provozovny" />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF label="Odhadovaný roční obrat (Kč)" name="osvcInfo.estimatedAnnualTurnover" control={form.control} type="number" placeholder="Odhad obratu" />
                    <FormItemRHF label="Odhadovaný počet spolupracovníků" name="osvcInfo.estimatedCollaboratorsCount" control={form.control} type="number" placeholder="Počet spolupracovníků" />
                  </div>
                </CardContent>
              </Card>
            )}

            <Card>
              <CardHeader><CardTitle className="text-lg flex items-center"><Building className="mr-2 h-5 w-5"/>Účast ve společnostech</CardTitle></CardHeader>
              <CardContent className="space-y-4 pt-4">
                {companyFields.map((company, index) => (
                  <Card key={company.id} className="p-4 shadow-sm bg-card-foreground/5 relative">
                     <Button type="button" variant="ghost" size="icon" onClick={() => removeCompany(index)} className="absolute top-2 right-2 text-destructive hover:bg-destructive/10"><Trash2 className="h-5 w-5"/></Button>
                     <CardHeader className="px-0 pt-0 pb-3">
                       <div className="flex items-center justify-between">
                         <CardTitle className="text-md">Společnost {index + 1}</CardTitle>
                         {form.getValues(`companyInvolvements.${index}.status`) && (
                           <CompanyStatusBadge status={form.getValues(`companyInvolvements.${index}.status`) || ""} />
                         )}
                       </div>
                     </CardHeader>
                     <CardContent className="px-0 pb-0 space-y-3">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 items-end">
                            <FormItemRHF label="IČO společnosti" name={`companyInvolvements.${index}.ico`} control={form.control} placeholder="8místné IČO" className="md:col-span-2"/>
                            <Button type="button" variant="outline" size="sm" onClick={() => handleAresLookupForCompany(index)} disabled={isAresLoading === index} className="self-end mb-1 h-10 w-full md:w-auto">
                                {isAresLoading === index ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Search className="mr-2 h-4 w-4" />}
                                Načíst z ARES
                            </Button>
                        </div>
                        <FormItemRHF label="Název společnosti" name={`companyInvolvements.${index}.name`} control={form.control} placeholder="Název společnosti" />

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <FormItemRHF label="Právní forma" name={`companyInvolvements.${index}.legalForm`} control={form.control} placeholder="Právní forma" />
                          <FormItemRHF label="DIČ" name={`companyInvolvements.${index}.dic`} control={form.control} placeholder="Daňové identifikační číslo" />
                        </div>

                        <FormItemRHF label="Adresa sídla" name={`companyInvolvements.${index}.address`} control={form.control} placeholder="Adresa sídla společnosti" />

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <FormItemRHF label="Datum založení" name={`companyInvolvements.${index}.establishmentDate`} control={form.control} placeholder="Datum založení" />
                          <FormItemRHF label="Stav společnosti" name={`companyInvolvements.${index}.status`} control={form.control} placeholder="Stav společnosti" />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <FormItemRHF label="Role subjektu" name={`companyInvolvements.${index}.role`} control={form.control} placeholder="Např. jednatel, společník..." />
                          <FormItemRHF label="Podíl" name={`companyInvolvements.${index}.ownership`} control={form.control} placeholder="Např. 50%, většinový..." />
                        </div>

                        <FormItemRHF label="Předmět podnikání" name={`companyInvolvements.${index}.businessScope`} control={form.control} as="textarea" rows={2} placeholder="Předmět podnikání společnosti" />

                        <FormItemRHF label="Poznámky" name={`companyInvolvements.${index}.notes`} control={form.control} as="textarea" rows={2} placeholder="Další poznámky ke společnosti" />

                        {form.getValues(`companyInvolvements.${index}.statutoryRepresentatives`) && (
                          <StatutoryRepresentativesList
                            representatives={form.getValues(`companyInvolvements.${index}.statutoryRepresentatives`) || []}
                          />
                        )}

                        <div className="mt-2 pt-2 border-t border-border">
                          <h4 className="text-sm font-semibold mb-2">Externí odkazy</h4>
                          <div className="flex flex-wrap gap-2">
                            <a href={`https://or.justice.cz/ias/ui/rejstrik-firma.vysledky?subjektId=&ico=${company.ico}&obchodniJmeno=&typHledani=STARTS_WITH&lokalita=`} target="_blank" rel="noopener noreferrer" className="text-xs px-2 py-1 bg-primary/10 hover:bg-primary/20 rounded-md transition-colors">
                              Obchodní rejstřík
                            </a>
                            <a href={`https://apl.czso.cz/res/detail?ico=${company.ico}`} target="_blank" rel="noopener noreferrer" className="text-xs px-2 py-1 bg-primary/10 hover:bg-primary/20 rounded-md transition-colors">
                              RES ČSÚ
                            </a>
                            <a href={`https://www.mfcr.cz/cs/o-ministerstvu/informacni-systemy/ares/ekonomicke-subjekty/vyhledavani-ekonomickeho-subjektu-podle-ico/${company.ico}`} target="_blank" rel="noopener noreferrer" className="text-xs px-2 py-1 bg-primary/10 hover:bg-primary/20 rounded-md transition-colors">
                              ARES
                            </a>
                            <a href={`https://esm.justice.cz/ias/issm/rejstrik-$firma?ico=${company.ico}`} target="_blank" rel="noopener noreferrer" className="text-xs px-2 py-1 bg-primary/10 hover:bg-primary/20 rounded-md transition-colors">
                              Evidence skutečných majitelů
                            </a>
                            <a href={`https://www.vestnikverejnychzakazek.cz/SearchForm/SearchContract?contractNumber=&contractName=&contractType=&cpvCode=&dateFrom=&dateTo=&price=&deadline=&documentType=&submitterName=&submitterIco=${company.ico}&sortBy=0`} target="_blank" rel="noopener noreferrer" className="text-xs px-2 py-1 bg-primary/10 hover:bg-primary/20 rounded-md transition-colors">
                              Veřejné zakázky
                            </a>
                            <a href={`https://smlouvy.gov.cz/vyhledavani?q=${company.ico}`} target="_blank" rel="noopener noreferrer" className="text-xs px-2 py-1 bg-primary/10 hover:bg-primary/20 rounded-md transition-colors">
                              Registr smluv
                            </a>
                          </div>
                        </div>
                     </CardContent>
                  </Card>
                ))}
                <div className="flex flex-wrap gap-2 mt-2">
                  <Button type="button" variant="outline" onClick={() => appendCompany({ id: crypto.randomUUID(), name: "", ico: ""})} className="flex-1 md:flex-none">
                    <PlusCircle className="mr-2 h-4 w-4"/>Přidat účast ve společnosti
                  </Button>
                  <CompanySearchDialog onSelectCompany={handleSelectCompanyFromSearch} trigger={
                    <Button type="button" variant="outline" className="flex-1 md:flex-none">
                      <List className="mr-2 h-4 w-4"/>Vyhledat společnost podle názvu
                    </Button>
                  } />
                </div>
              </CardContent>
            </Card>

            <Card>
                <CardHeader><CardTitle className="text-lg">Obecné poznámky k podnikání</CardTitle></CardHeader>
                <CardContent className="pt-4">
                    <FormItemRHF label="Další relevantní informace" name="generalNotes" control={form.control} as="textarea" rows={4} placeholder="Jakékoli další důležité informace o podnikatelských aktivitách subjektu..."/>
                </CardContent>
            </Card>
            <div className="space-y-6 pt-4">
              <ForeignBusinessSection control={form.control} />
              <InsolvencySection control={form.control} />
              <RealEstateSection control={form.control} />
              <PersonnelConnectionsSection control={form.control} />
              <PhotoDocumentationSection 
                form={form} 
                title="Fotodokumentace podnikání"
                description="Fotografie související s podnikatelskými aktivitami"
                namePrefix="businessPhotos" 
                photoHint="business company premises"
                caseId={caseId}
                subjectId={subject.id}
                moduleId={moduleId}
              />
            </div>

          </CardContent>
        </Card>

        <div className="flex justify-end pt-8 mt-8 border-t border-border">
          <Button type="submit" disabled={isSaving} className="w-full md:w-auto text-lg py-3 px-6 shadow-md">
            {isSaving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}Uložit data modulu
          </Button>
        </div>
      </form>
    </Form>
  );
}

// Helper components
interface FormItemRHFProps {
  label: string; name: string; control: any; placeholder?: string;
  type?: string; disabled?: boolean; className?: string;
  as?: 'input' | 'textarea'; rows?: number; smallLabel?: boolean;
}
const FormItemRHF = ({ label, name, control, placeholder, type = "text", disabled = false, className, as = 'input', rows, smallLabel }: FormItemRHFProps) => (
  <FormField
    control={control} name={name as any} disabled={disabled}
    render={({ field, fieldState: { error } }) => (
      <FormItem className={cn("w-full", className)}>
        <FormLabel className={cn("font-semibold", smallLabel ? "text-xs" : "text-sm")}>{label}</FormLabel>
        <FormControl>
          {as === 'input' ? (
            <Input {...field} value={field.value ?? ''} placeholder={placeholder} type={type} className={cn(smallLabel ? "text-xs h-8 py-1" : "text-sm h-10", error ? "border-destructive" : "")} />
          ) : (
            <Textarea {...field} value={field.value ?? ''} placeholder={placeholder} rows={rows} className={cn(smallLabel ? "text-xs py-1" : "text-sm", error ? "border-destructive" : "")} />
          )}
        </FormControl>
        {error && <FormMessage className="text-xs" />}
      </FormItem>
    )}
  />
);

interface FormItemSelectRHFProps {
  label: string; name: string; control: any; placeholder?: string;
  options: {value: string; label: string}[];
  disabled?: boolean; className?: string; smallLabel?: boolean;
}
const FormItemSelectRHF = ({ label, name, control, placeholder, options, disabled = false, className, smallLabel }: FormItemSelectRHFProps) => (
  <FormField
    control={control} name={name as any} disabled={disabled}
    render={({ field, fieldState: { error } }) => (
      <FormItem className={cn("w-full", className)}>
        <FormLabel className={cn("font-semibold", smallLabel ? "text-xs" : "text-sm")}>{label}</FormLabel>
        <Select onValueChange={field.onChange} value={field.value || undefined} defaultValue={field.value || undefined}>
          <FormControl>
            <SelectTrigger className={cn(smallLabel ? "text-xs h-8 py-1" : "text-sm h-10", error ? "border-destructive" : "")}><SelectValue placeholder={placeholder} /></SelectTrigger>
          </FormControl>
          <SelectContent>{options.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}</SelectContent>
        </Select>
        {error && <FormMessage className="text-xs" />}
      </FormItem>
    )}
  />
);
