/**
 * <PERSON><PERSON><PERSON> analýza - OSINT nástroj
 * Funkce pro práci s modulem emailové analýzy
 */

document.addEventListener('DOMContentLoaded', function() {
    // Inicializace funkcí pro emailovou analýzu
    initEmailAnalysis();
});

/**
 * Inicializace funkcí pro emailovou analýzu
 */
function initEmailAnalysis() {
    // Přidání event listenerů pro tlačítka v modulu emailové analýzy
    document.addEventListener('click', function(event) {
        // Validace emailu
        if (event.target.closest('.validate-email')) {
            validateEmail();
        }

        // Kontrola úniků dat
        if (event.target.closest('.check-data-breach')) {
            checkDataBreach();
        }

        // Analýza hlaviček emailu
        if (event.target.closest('.analyze-headers')) {
            analyzeEmailHeaders();
        }

        // Přidání spojeného profilu
        if (event.target.closest('.add-connected-profile')) {
            addConnectedProfile();
        }
    });

    // Změna zdroje emailu
    document.addEventListener('change', function(event) {
        if (event.target.id === 'email-source') {
            toggleOtherSourceField(event.target.value);
        }
    });

    // Automatické odvození jména z emailu
    document.addEventListener('input', function(event) {
        if (event.target.id === 'email-adresa') {
            deriveNameFromEmail(event.target.value);
        }
    });
}

/**
 * Validace emailové adresy
 */
function validateEmail() {
    const emailInput = document.getElementById('email-adresa');
    const email = emailInput.value.trim();

    if (!email) {
        alert('Zadejte emailovou adresu pro validaci.');
        return;
    }

    // Zobrazení výsledků validace
    const validationResults = emailInput.closest('.module-content').querySelector('.validation-results');
    validationResults.style.display = 'block';

    // Simulace validace emailu (v reálné aplikaci by zde bylo API volání)
    simulateEmailValidation(email);
}

/**
 * Simulace validace emailu
 * @param {string} email - Emailová adresa k validaci
 */
function simulateEmailValidation(email) {
    // Resetování výsledků
    document.getElementById('email-format-result').innerHTML = '<span class="pending"><i class="fas fa-spinner fa-spin"></i> Probíhá validace...</span>';
    document.getElementById('domain-existence-result').innerHTML = '<span class="pending"><i class="fas fa-spinner fa-spin"></i> Probíhá validace...</span>';
    document.getElementById('smtp-validation-result').innerHTML = '<span class="pending"><i class="fas fa-spinner fa-spin"></i> Probíhá validace...</span>';
    document.getElementById('disposable-email-result').innerHTML = '<span class="pending"><i class="fas fa-spinner fa-spin"></i> Probíhá validace...</span>';

    // Validace formátu emailu
    setTimeout(() => {
        const formatValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
        document.getElementById('email-format-result').innerHTML = formatValid ?
            '<span class="valid"><i class="fas fa-check-circle"></i> Platný formát</span>' :
            '<span class="invalid"><i class="fas fa-times-circle"></i> Neplatný formát</span>';

        // Validace existence domény
        setTimeout(() => {
            const domain = email.split('@')[1];
            const commonDomains = ['gmail.com', 'seznam.cz', 'email.cz', 'centrum.cz', 'yahoo.com', 'hotmail.com', 'outlook.com'];
            const domainValid = commonDomains.includes(domain) || Math.random() > 0.3;

            document.getElementById('domain-existence-result').innerHTML = domainValid ?
                '<span class="valid"><i class="fas fa-check-circle"></i> Doména existuje</span>' :
                '<span class="invalid"><i class="fas fa-times-circle"></i> Doména neexistuje</span>';

            // SMTP validace
            setTimeout(() => {
                const smtpValid = domainValid && Math.random() > 0.4;
                document.getElementById('smtp-validation-result').innerHTML = smtpValid ?
                    '<span class="valid"><i class="fas fa-check-circle"></i> Schránka existuje</span>' :
                    '<span class="invalid"><i class="fas fa-times-circle"></i> Schránka neexistuje</span>';

                // Disposable email validace
                setTimeout(() => {
                    const disposableDomains = ['tempmail.com', 'temp-mail.org', 'guerrillamail.com', 'mailinator.com'];
                    const isDisposable = disposableDomains.includes(domain);

                    document.getElementById('disposable-email-result').innerHTML = isDisposable ?
                        '<span class="invalid"><i class="fas fa-times-circle"></i> Jednorázový email</span>' :
                        '<span class="valid"><i class="fas fa-check-circle"></i> Standardní email</span>';

                }, 500);
            }, 700);
        }, 500);
    }, 1000);
}

/**
 * Přepínání pole pro jiný zdroj emailu
 * @param {string} sourceValue - Hodnota vybraného zdroje
 */
function toggleOtherSourceField(sourceValue) {
    const otherSourceContainer = document.getElementById('other-source-container');
    if (sourceValue === 'other') {
        otherSourceContainer.style.display = 'block';
    } else {
        otherSourceContainer.style.display = 'none';
    }
}

/**
 * Odvození jména z emailové adresy
 * @param {string} email - Emailová adresa
 */
function deriveNameFromEmail(email) {
    if (!email || !email.includes('@')) return;

    const localPart = email.split('@')[0];
    const domain = email.split('@')[1];

    // Odvození jména z lokální části emailu
    let probableName = '';

    // Odstranění čísel a speciálních znaků
    const cleanLocalPart = localPart.replace(/[0-9_\-.]/g, ' ').trim();

    if (cleanLocalPart.includes(' ')) {
        // Pokud obsahuje mezery, pravděpodobně jde o jméno a příjmení
        probableName = cleanLocalPart.split(' ').map(part =>
            part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
        ).join(' ');
    } else if (cleanLocalPart.includes('.')) {
        // Pokud obsahuje tečky, pravděpodobně jde o jméno.příjmení
        probableName = cleanLocalPart.split('.').map(part =>
            part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
        ).join(' ');
    } else {
        // Jinak zkusíme najít velká písmena jako oddělovače (camelCase)
        const camelCaseParts = cleanLocalPart.replace(/([A-Z])/g, ' $1').trim();
        if (camelCaseParts !== cleanLocalPart) {
            probableName = camelCaseParts.split(' ').map(part =>
                part.charAt(0).toUpperCase() + part.slice(1).toLowerCase()
            ).join(' ');
        } else {
            // Pokud nic jiného, použijeme lokální část jako jméno
            probableName = cleanLocalPart.charAt(0).toUpperCase() + cleanLocalPart.slice(1).toLowerCase();
        }
    }

    // Nastavení pravděpodobného jména
    const probableNameInput = document.getElementById('probable-name');
    if (probableNameInput) {
        probableNameInput.value = probableName;
    }

    // Nastavení spojené organizace
    const associatedOrgInput = document.getElementById('associated-organization');
    if (associatedOrgInput && domain) {
        // Odstranění TLD a získání názvu organizace
        const orgName = domain.split('.')[0];
        if (orgName && !['gmail', 'seznam', 'email', 'centrum', 'yahoo', 'hotmail', 'outlook'].includes(orgName)) {
            associatedOrgInput.value = orgName.charAt(0).toUpperCase() + orgName.slice(1);
        }
    }
}

/**
 * Kontrola úniků dat
 */
function checkDataBreach() {
    const emailInput = document.getElementById('email-adresa');
    const email = emailInput.value.trim();

    if (!email) {
        alert('Zadejte emailovou adresu pro kontrolu úniků dat.');
        return;
    }

    // Zobrazení výsledků kontroly úniků dat
    const dataBreachResults = emailInput.closest('.module-content').querySelector('.data-breach-results');
    dataBreachResults.style.display = 'block';

    // Simulace kontroly úniků dat (v reálné aplikaci by zde bylo API volání)
    simulateDataBreachCheck(email);
}

/**
 * Simulace kontroly úniků dat
 * @param {string} email - Emailová adresa k kontrole
 */
function simulateDataBreachCheck(email) {
    // Resetování výsledků
    document.getElementById('breach-count').textContent = '...';
    document.getElementById('breach-severity').innerHTML = '<span class="severity-unknown">Probíhá kontrola...</span>';
    document.getElementById('data-breach-list').innerHTML = '<div class="breach-loading"><i class="fas fa-spinner fa-spin"></i> Probíhá vyhledávání v databázích úniků dat...</div>';

    // Simulace načítání
    setTimeout(() => {
        // Náhodný počet úniků dat (1-5)
        const breachCount = Math.floor(Math.random() * 5) + 1;
        document.getElementById('breach-count').textContent = breachCount;

        // Nastavení závažnosti
        let severityClass = 'severity-low';
        let severityText = 'Nízká';

        if (breachCount >= 4) {
            severityClass = 'severity-critical';
            severityText = 'Kritická';
        } else if (breachCount >= 3) {
            severityClass = 'severity-high';
            severityText = 'Vysoká';
        } else if (breachCount >= 2) {
            severityClass = 'severity-medium';
            severityText = 'Střední';
        }

        document.getElementById('breach-severity').innerHTML = `<span class="${severityClass}">${severityText}</span>`;

        // Generování seznamu úniků dat
        let breachListHTML = '';

        for (let i = 0; i < breachCount; i++) {
            breachListHTML += generateDataBreachItem(email, i);
        }

        document.getElementById('data-breach-list').innerHTML = breachListHTML;
    }, 2000);
}

/**
 * Generování položky úniku dat
 * @param {string} email - Emailová adresa
 * @param {number} index - Index úniku dat
 * @returns {string} HTML kód položky úniku dat
 */
function generateDataBreachItem(email, index) {
    const breachSources = [
        { name: 'LinkedIn', icon: 'fab fa-linkedin', date: '2016-05-18', records: '164 milionů' },
        { name: 'Adobe', icon: 'fab fa-adobe', date: '2013-10-04', records: '153 milionů' },
        { name: 'Dropbox', icon: 'fab fa-dropbox', date: '2012-07-01', records: '68 milionů' },
        { name: 'Yahoo', icon: 'fab fa-yahoo', date: '2013-08-01', records: '3 miliardy' },
        { name: 'MySpace', icon: 'fas fa-user-friends', date: '2008-07-01', records: '360 milionů' },
        { name: 'Canva', icon: 'fas fa-palette', date: '2019-05-24', records: '137 milionů' },
        { name: 'Tumblr', icon: 'fab fa-tumblr', date: '2013-02-28', records: '65 milionů' },
        { name: 'VK', icon: 'fab fa-vk', date: '2012-01-01', records: '100 milionů' },
        { name: 'Zynga', icon: 'fas fa-gamepad', date: '2019-09-01', records: '218 milionů' },
        { name: 'Marriott', icon: 'fas fa-hotel', date: '2018-11-30', records: '500 milionů' }
    ];

    const source = breachSources[index % breachSources.length];
    const compromisedDataTypes = [
        { name: 'Email', icon: 'fas fa-envelope', sensitive: false },
        { name: 'Heslo', icon: 'fas fa-key', sensitive: true },
        { name: 'Jméno', icon: 'fas fa-user', sensitive: false },
        { name: 'Telefon', icon: 'fas fa-phone', sensitive: true },
        { name: 'Adresa', icon: 'fas fa-home', sensitive: true },
        { name: 'Datum narození', icon: 'fas fa-birthday-cake', sensitive: true },
        { name: 'IP adresa', icon: 'fas fa-network-wired', sensitive: false },
        { name: 'Geolokace', icon: 'fas fa-map-marker-alt', sensitive: true },
        { name: 'Platební údaje', icon: 'fas fa-credit-card', sensitive: true },
        { name: 'Historie nákupů', icon: 'fas fa-shopping-cart', sensitive: false }
    ];

    // Náhodný výběr kompromitovaných dat
    const dataCount = Math.floor(Math.random() * 5) + 2;
    const compromisedData = [];

    for (let i = 0; i < dataCount; i++) {
        const randomIndex = Math.floor(Math.random() * compromisedDataTypes.length);
        if (!compromisedData.some(item => item.name === compromisedDataTypes[randomIndex].name)) {
            compromisedData.push(compromisedDataTypes[randomIndex]);
        }
    }

    // Generování HTML pro kompromitovaná data
    let dataTagsHTML = '';
    compromisedData.forEach(data => {
        dataTagsHTML += `<div class="data-tag ${data.sensitive ? 'sensitive' : ''}">
            <i class="${data.icon}"></i> ${data.name}
        </div>`;
    });

    return `
        <div class="data-breach-item">
            <div class="breach-header">
                <div class="breach-title">
                    <i class="${source.icon}"></i> ${source.name}
                </div>
                <div class="breach-date">
                    <i class="fas fa-calendar-alt"></i> ${source.date}
                </div>
            </div>
            <div class="breach-details">
                <div class="breach-detail-item">
                    <div class="breach-detail-label">Počet záznamů:</div>
                    <div class="breach-detail-value">${source.records}</div>
                </div>
                <div class="breach-detail-item">
                    <div class="breach-detail-label">Kompromitovaná data:</div>
                    <div class="breach-detail-value">
                        <div class="compromised-data">
                            ${dataTagsHTML}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

/**
 * Analýza hlaviček emailu
 */
function analyzeEmailHeaders() {
    const headersTextarea = document.getElementById('email-headers');
    const headers = headersTextarea.value.trim();

    if (!headers) {
        alert('Vložte hlavičky emailu pro analýzu.');
        return;
    }

    // Zobrazení výsledků analýzy hlaviček
    const headersAnalysisResults = headersTextarea.closest('.module-content').querySelector('.headers-analysis-results');
    headersAnalysisResults.style.display = 'block';

    // Simulace analýzy hlaviček (v reálné aplikaci by zde byla skutečná analýza)
    simulateHeadersAnalysis(headers);
}

/**
 * Simulace analýzy hlaviček emailu
 * @param {string} headers - Hlavičky emailu k analýze
 */
function simulateHeadersAnalysis(headers) {
    // Resetování výsledků
    document.getElementById('sender-ip').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Probíhá analýza...';
    document.getElementById('ip-geolocation').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Probíhá analýza...';
    document.getElementById('email-client').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Probíhá analýza...';
    document.getElementById('email-authentication').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Probíhá analýza...';
    document.getElementById('email-route').innerHTML = '<i class="fas fa-spinner fa-spin"></i> Probíhá analýza...';

    // Simulace analýzy
    setTimeout(() => {
        // Extrakce IP adresy
        const ipRegex = /\b(?:\d{1,3}\.){3}\d{1,3}\b/;
        const ipMatch = headers.match(ipRegex);
        const ip = ipMatch ? ipMatch[0] : generateRandomIP();

        document.getElementById('sender-ip').textContent = ip;

        // Geolokace IP
        setTimeout(() => {
            const countries = ['Česká republika', 'Slovensko', 'Německo', 'USA', 'Rusko', 'Čína', 'Velká Británie'];
            const randomCountry = countries[Math.floor(Math.random() * countries.length)];
            const cities = ['Praha', 'Brno', 'Bratislava', 'Berlín', 'New York', 'Moskva', 'Peking', 'Londýn'];
            const randomCity = cities[Math.floor(Math.random() * cities.length)];

            document.getElementById('ip-geolocation').textContent = `${randomCity}, ${randomCountry}`;

            // Emailový klient
            setTimeout(() => {
                const clients = [
                    'Microsoft Outlook',
                    'Gmail',
                    'Apple Mail',
                    'Mozilla Thunderbird',
                    'Yahoo Mail',
                    'iPhone Mail',
                    'Android Mail',
                    'Webový prohlížeč'
                ];
                const randomClient = clients[Math.floor(Math.random() * clients.length)];

                document.getElementById('email-client').textContent = randomClient;

                // SPF/DKIM/DMARC
                setTimeout(() => {
                    const authResults = [
                        '<span style="color: #28a745;"><i class="fas fa-check-circle"></i> Všechny kontroly v pořádku</span>',
                        '<span style="color: #ffc107;"><i class="fas fa-exclamation-triangle"></i> SPF v pořádku, DKIM selhalo</span>',
                        '<span style="color: #ffc107;"><i class="fas fa-exclamation-triangle"></i> DKIM v pořádku, SPF selhalo</span>',
                        '<span style="color: #dc3545;"><i class="fas fa-times-circle"></i> Všechny kontroly selhaly</span>'
                    ];
                    const randomAuth = authResults[Math.floor(Math.random() * authResults.length)];

                    document.getElementById('email-authentication').innerHTML = randomAuth;

                    // Cesta emailu
                    setTimeout(() => {
                        const route = generateEmailRoute();
                        document.getElementById('email-route').innerHTML = route;
                    }, 500);
                }, 500);
            }, 500);
        }, 500);
    }, 1000);
}

/**
 * Generování náhodné IP adresy
 * @returns {string} Náhodná IP adresa
 */
function generateRandomIP() {
    return `${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}.${Math.floor(Math.random() * 256)}`;
}

/**
 * Generování cesty emailu
 * @returns {string} HTML kód cesty emailu
 */
function generateEmailRoute() {
    const servers = [
        'mail-server.odesílatel.cz',
        'smtp-relay.provider.com',
        'mx1.transit-server.net',
        'spam-filter.security.org',
        'mx-in.příjemce.cz'
    ];

    let routeHTML = '';

    servers.forEach((server, index) => {
        routeHTML += `<div class="route-step">
            <div class="step-number">${index + 1}</div>
            <div class="step-server">${server}</div>
            <div class="step-time">${Math.floor(Math.random() * 500) + 10} ms</div>
        </div>`;

        if (index < servers.length - 1) {
            routeHTML += '<div class="route-arrow"><i class="fas fa-long-arrow-alt-down"></i></div>';
        }
    });

    return `<div class="email-route-container">${routeHTML}</div>`;
}

/**
 * Přidání spojeného profilu
 */
function addConnectedProfile() {
    const connectedProfilesContainer = document.getElementById('connected-profiles');
    if (!connectedProfilesContainer) return;

    const profileId = `profile-${Date.now()}`;
    const profileHTML = `
        <div class="connected-profile" id="${profileId}">
            <div class="form-row">
                <div class="form-group">
                    <label>Typ profilu</label>
                    <select class="form-control profile-type-select">
                        <option value="">-- Vyberte typ --</option>
                        <option value="social">Sociální síť</option>
                        <option value="forum">Fórum</option>
                        <option value="ecommerce">E-shop</option>
                        <option value="service">Online služba</option>
                        <option value="other">Jiný</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Název platformy</label>
                    <input type="text" class="form-control" placeholder="Např. Facebook, Reddit, Amazon...">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>Uživatelské jméno / ID</label>
                    <input type="text" class="form-control" placeholder="Uživatelské jméno nebo ID">
                </div>
                <div class="form-group">
                    <label>URL profilu</label>
                    <input type="text" class="form-control" placeholder="https://...">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group long">
                    <label>Poznámka</label>
                    <textarea class="form-control" rows="2" placeholder="Další informace o profilu..."></textarea>
                </div>
            </div>
            <button type="button" class="btn-inline remove-profile" data-profile-id="${profileId}">
                <i class="fas fa-trash"></i> Odstranit profil
            </button>
        </div>
    `;

    connectedProfilesContainer.insertAdjacentHTML('beforeend', profileHTML);

    // Přidání event listeneru pro odstranění profilu
    const removeButton = document.querySelector(`#${profileId} .remove-profile`);
    if (removeButton) {
        removeButton.addEventListener('click', function() {
            const profileId = this.getAttribute('data-profile-id');
            const profileElement = document.getElementById(profileId);
            if (profileElement) {
                profileElement.remove();
            }
        });
    }
}