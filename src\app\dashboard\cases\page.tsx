
"use client";

import { useState, useMemo, useEffect } from "react";
import type { Case, CasePriority, CaseStatus } from "@/types";
import { CaseCard } from "@/components/cases/CaseCard";
import { CaseListControls } from "@/components/cases/CaseListControls";
import { <PERSON><PERSON><PERSON>t, Loader2 } from "lucide-react";
import { LoadingSpinner, LoadingDots } from "@/components/ui/loading";
import { useRouter } from "next/navigation";
import { db, auth } from "@/lib/firebase";
import { collection, query, orderBy, onSnapshot, addDoc, serverTimestamp, deleteDoc, doc, getDocs, writeBatch } from "firebase/firestore";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export default function CasesPage() {
  const router = useRouter();
  const { user, userProfile } = useAuth();
  const { toast } = useToast();

  const [cases, setCases] = useState<Case[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCreatingCase, setIsCreatingCase] = useState(false);

  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<CaseStatus | "all">("all");
  const [priorityFilter, setPriorityFilter] = useState<CasePriority | "all">("all");

  // State for delete confirmation dialog
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [caseToDelete, setCaseToDelete] = useState<Case | null>(null);
  const [deleteConfirmInput, setDeleteConfirmInput] = useState("");
  const [isDeletingCase, setIsDeletingCase] = useState(false);


  useEffect(() => {
    if (!user) {
      setLoading(false);
      return;
    }
    setLoading(true);
    const q = query(collection(db, "cases"), orderBy("createdAt", "desc"));

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const casesData: Case[] = [];
      querySnapshot.forEach((docSnap) => {
        const data = docSnap.data();
        casesData.push({
          id: docSnap.id,
          ...data,
          creationDate: data.createdAt?.toDate ? data.createdAt.toDate().toLocaleDateString('cs-CZ') : 'Neznámo',
          createdAt: data.createdAt,
          updatedAt: data.updatedAt,
        } as Case);
      });
      setCases(casesData);
      setLoading(false);
    }, (error) => {
      console.error("Error fetching cases: ", error);
      toast({
        title: "Chyba při načítání případů",
        description: error.message || "Nepodařilo se načíst seznam případů.",
        variant: "destructive",
      });
      setLoading(false);
    });

    return () => unsubscribe();
  }, [user, toast]);

  const handleSearchChange = (term: string) => setSearchTerm(term.toLowerCase());
  const handleStatusFilterChange = (status: string) => setStatusFilter(status as CaseStatus | "all");
  const handlePriorityFilterChange = (priority: string) => setPriorityFilter(priority as CasePriority | "all");

  const handleCreateNewCase = async () => {
    if (!user || !userProfile) {
      toast({
        title: "Přihlášení vyžadováno",
        description: "Pro vytvoření případu musíte být přihlášen a mít kompletní profil.",
        variant: "destructive",
      });
      setIsCreatingCase(false);
      return;
    }
    setIsCreatingCase(true);
    try {
      const investigatorFullName = `${userProfile.title || ''} ${userProfile.firstName || ''} ${userProfile.lastName || ''} ${userProfile.rank || ''}`.replace(/\s+/g, ' ').trim();

      const newCaseData = {
        title: "Nový případ (klikněte pro úpravu)",
        status: "pending" as CaseStatus,
        priority: "medium" as CasePriority,
        subjectsCount: 0,
        investigatorId: user.uid,
        investigatorName: investigatorFullName || user.email || "Nepřiřazeno",
        deadlineInfo: "Nespecifikováno",
        description: "Zadejte popis nového případu.",
        progress: 0,
        modulesCompleted: 0,
        modulesTotal: 0,
        referenceNumber: "",
        internalId: "",
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      };
      const docRef = await addDoc(collection(db, "cases"), newCaseData);
      toast({ title: "Případ vytvořen", description: `Případ s ID ${docRef.id} byl úspěšně vytvořen.`});
      router.push(`/dashboard/cases/${docRef.id}`);
    } catch (error: any) {
      console.error("Error creating new case: ", error);
      toast({
        title: "Chyba při vytváření případu",
        description: error.message || "Nepodařilo se vytvořit nový případ.",
        variant: "destructive",
      });
    } finally {
      setIsCreatingCase(false);
    }
  };

  const openDeleteDialog = (caseItem: Case) => {
    setCaseToDelete(caseItem);
    setIsDeleteConfirmOpen(true);
    setDeleteConfirmInput(""); // Reset input
  };

  const closeDeleteDialog = () => {
    setIsDeleteConfirmOpen(false);
    setCaseToDelete(null);
    setDeleteConfirmInput("");
  };

  const handleConfirmDelete = async () => {
    if (!caseToDelete || deleteConfirmInput !== "SMAZAT") {
      toast({
        title: "Potvrzení nesouhlasí",
        description: 'Pro smazání případu musíte do pole zadat přesně "SMAZAT".',
        variant: "destructive",
      });
      return;
    }

    setIsDeletingCase(true);
    try {
      const caseId = caseToDelete.id;
      
      // 1. Delete subjects and their moduleData subcollections
      const subjectsCollectionRef = collection(db, "cases", caseId, "subjects");
      const subjectsSnapshot = await getDocs(subjectsCollectionRef);
      
      const batch = writeBatch(db);

      for (const subjectDoc of subjectsSnapshot.docs) {
        const subjectId = subjectDoc.id;
        // Delete moduleData for each subject
        const moduleDataCollectionRef = collection(db, "cases", caseId, "subjects", subjectId, "moduleData");
        const moduleDataSnapshot = await getDocs(moduleDataCollectionRef);
        moduleDataSnapshot.forEach(mdDoc => {
          batch.delete(doc(db, "cases", caseId, "subjects", subjectId, "moduleData", mdDoc.id));
        });
        // Delete the subject itself
        batch.delete(doc(db, "cases", caseId, "subjects", subjectId));
      }
      await batch.commit(); // Commit batch for deleting subjects and modules

      // 2. Delete the main case document
      await deleteDoc(doc(db, "cases", caseId));

      toast({ title: "Případ smazán", description: `Případ "${caseToDelete.title}" byl úspěšně smazán.` });
      closeDeleteDialog();
    } catch (error: any) {
      console.error("Error deleting case: ", error);
      toast({
        title: "Chyba při mazání případu",
        description: error.message || "Nepodařilo se smazat případ.",
        variant: "destructive",
      });
    } finally {
      setIsDeletingCase(false);
    }
  };


  const filteredCases = useMemo(() => {
    return cases.filter((caseItem) => {
      const matchesSearch =
        caseItem.title.toLowerCase().includes(searchTerm) ||
        caseItem.id.toLowerCase().includes(searchTerm) ||
        (caseItem.description && caseItem.description.toLowerCase().includes(searchTerm)) ||
        (caseItem.investigatorName && caseItem.investigatorName.toLowerCase().includes(searchTerm)) ||
        (caseItem.referenceNumber && caseItem.referenceNumber.toLowerCase().includes(searchTerm)) ||
        (caseItem.internalId && caseItem.internalId.toLowerCase().includes(searchTerm));
      const matchesStatus = statusFilter === "all" || caseItem.status === statusFilter;
      const matchesPriority = priorityFilter === "all" || caseItem.priority === priorityFilter;
      return matchesSearch && matchesStatus && matchesPriority;
    });
  }, [cases, searchTerm, statusFilter, priorityFilter]);

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col items-center justify-center py-20 space-y-6">
          <LoadingSpinner size="lg" className="text-primary" />
          <div className="text-center">
            <h2 className="text-2xl font-semibold text-gray-900">Načítání případů</h2>
            <p className="text-gray-600 mt-2">Získáváme seznam všech OSINT případů</p>
            <LoadingDots className="mt-4 text-gray-400" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 md:px-6">
      <div className="mb-8 text-center md:text-left">
        <h1 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
          Seznam případů
        </h1>
        <p className="mt-2 text-lg text-muted-foreground">
          Přehled aktuálních i archivovaných OSINT analýz.
        </p>
      </div>

      <CaseListControls
        onSearchChange={handleSearchChange}
        onStatusFilterChange={handleStatusFilterChange}
        onPriorityFilterChange={handlePriorityFilterChange}
        onNewCaseClick={handleCreateNewCase}
        isCreatingCase={isCreatingCase}
      />

      {(filteredCases.length > 0 || isCreatingCase) ? (
         <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3">
          <CaseCard
            key="add-new-case-card"
            caseData={{} as Case}
            isAddNew={true}
            onClick={handleCreateNewCase}
            isProcessing={isCreatingCase}
          />
          {filteredCases.map((caseData) => (
            <CaseCard 
              key={caseData.id} 
              caseData={caseData} 
              onDeleteRequest={openDeleteDialog}
            />
          ))}
        </div>
      ) : (
        <div className="mt-16 flex flex-col items-center justify-center rounded-xl border border-dashed border-border bg-card p-12 text-center shadow-sm">
          <ShieldAlert className="mx-auto h-16 w-16 text-muted-foreground opacity-50" />
          <h3 className="mt-4 text-xl font-semibold text-foreground">Žádné případy nenalezeny</h3>
          <p className="mt-2 text-sm text-muted-foreground">
            Zkuste upravit filtry nebo vytvořte nový případ.
          </p>
           <CaseCard
            caseData={{} as Case}
            isAddNew={true}
            onClick={handleCreateNewCase}
            isProcessing={isCreatingCase}
            className="mt-6"
           />
        </div>
      )}

      <AlertDialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Opravdu smazat případ?</AlertDialogTitle>
            <AlertDialogDescription>
              Chystáte se smazat případ: <span className="font-semibold text-foreground">{caseToDelete?.title}</span> (ID: {caseToDelete?.id}).
              Tato akce je nevratná a smaže veškerá související data, včetně všech subjektů a jejich modulů.
              <br />
              Pro potvrzení zadejte <strong className="text-destructive">SMAZAT</strong> do pole níže.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <div className="py-2">
            <Label htmlFor="deleteConfirm" className="sr-only">Potvrzení smazání</Label>
            <Input
              id="deleteConfirm"
              type="text"
              value={deleteConfirmInput}
              onChange={(e) => setDeleteConfirmInput(e.target.value)}
              placeholder='Napište "SMAZAT"'
              className="border-destructive focus-visible:ring-destructive"
            />
          </div>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={closeDeleteDialog} disabled={isDeletingCase}>Zrušit</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleConfirmDelete}
              disabled={deleteConfirmInput !== "SMAZAT" || isDeletingCase}
              className="bg-destructive hover:bg-destructive/90"
            >
              {isDeletingCase ? (
                <div className="flex items-center">
                  <LoadingSpinner size="sm" className="mr-2 text-white" />
                  Mazání...
                </div>
              ) : (
                "Smazat případ"
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

    </div>
  );
}
