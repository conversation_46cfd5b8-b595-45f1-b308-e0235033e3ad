"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, Card<PERSON><PERSON>le, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Tabs, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Mail, CheckCircle, XCircle, AlertTriangle, Shield, 
  Search, Server, Globe, RefreshCw, AlertCircle, 
  CheckSquare, Clock, Zap, Database, Route, MapPin
} from "lucide-react";

interface EmailHeaderAnalyzerProps {
  onAnalysisComplete?: (results: EmailHeaderAnalysisResults) => void;
  initialHeaders?: string;
}

export interface EmailHeaderAnalysisResults {
  senderIp: string;
  ipGeolocation: string;
  emailClient: string;
  authResults: {
    spf: "pass" | "fail" | "neutral" | "none";
    dkim: "pass" | "fail" | "neutral" | "none";
    dmarc: "pass" | "fail" | "neutral" | "none";
  };
  emailRoute: {
    server: string;
    ip: string;
    timestamp: string;
  }[];
  rawHeaders: string;
  analysisDate: string;
  securityScore: number;
  securityIssues: string[];
  originalSender: string;
  returnPath: string;
  subject: string;
  receivedDate: string;
  messageId: string;
}

export function EmailHeaderAnalyzer({ onAnalysisComplete, initialHeaders = "" }: EmailHeaderAnalyzerProps) {
  const [headers, setHeaders] = useState(initialHeaders);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<EmailHeaderAnalysisResults | null>(null);
  const [analysisError, setAnalysisError] = useState<string | null>(null);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [analysisStep, setAnalysisStep] = useState("");

  const analyzeHeaders = async () => {
    if (!headers.trim()) {
      setAnalysisError("Zadejte hlavičky emailu");
      return;
    }

    setIsAnalyzing(true);
    setAnalysisError(null);
    setAnalysisProgress(0);
    setAnalysisStep("Parsování hlaviček...");

    try {
      // Simulace postupu analýzy
      await simulateAnalysisStep(15, "Parsování hlaviček...");
      await simulateAnalysisStep(30, "Extrakce IP adres...");
      await simulateAnalysisStep(50, "Analýza autentizace...");
      await simulateAnalysisStep(70, "Rekonstrukce cesty emailu...");
      await simulateAnalysisStep(85, "Geolokace IP adres...");
      await simulateAnalysisStep(95, "Finalizace výsledků...");
      
      // Simulace výsledků analýzy
      const results: EmailHeaderAnalysisResults = {
        senderIp: "***********",
        ipGeolocation: "Praha, Česká republika",
        emailClient: "Microsoft Outlook",
        authResults: {
          spf: "pass",
          dkim: "pass",
          dmarc: "pass"
        },
        emailRoute: [
          { server: "mail.example.com", ip: "***********", timestamp: "2023-06-01T12:00:00Z" },
          { server: "relay.example.net", ip: "***********", timestamp: "2023-06-01T12:01:00Z" },
          { server: "mx.recipient.org", ip: "***********", timestamp: "2023-06-01T12:02:00Z" }
        ],
        rawHeaders: headers,
        analysisDate: new Date().toISOString(),
        securityScore: 90,
        securityIssues: [],
        originalSender: "<EMAIL>",
        returnPath: "<EMAIL>",
        subject: "Testovací email",
        receivedDate: "2023-06-01T12:02:00Z",
        messageId: "<<EMAIL>>"
      };
      
      setAnalysisResults(results);
      setAnalysisProgress(100);
      setAnalysisStep("Analýza dokončena");
      
      if (onAnalysisComplete) {
        onAnalysisComplete(results);
      }
    } catch (error: any) {
      setAnalysisError(error.message || "Chyba při analýze hlaviček");
      setAnalysisProgress(0);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const simulateAnalysisStep = async (progress: number, step: string) => {
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        setAnalysisProgress(progress);
        setAnalysisStep(step);
        resolve();
      }, 500);
    });
  };

  const getAuthStatusIcon = (status: string) => {
    switch (status) {
      case "pass":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "fail":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "neutral":
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSecurityScoreColor = (score: number): string => {
    if (score >= 80) return "text-green-500";
    if (score >= 60) return "text-yellow-500";
    if (score >= 40) return "text-orange-500";
    return "text-red-500";
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Server className="mr-2 h-5 w-5 text-primary" />
          Analýza hlaviček emailu
        </CardTitle>
        <CardDescription>
          Analyzujte hlavičky emailu pro zjištění původu a autenticity
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex flex-col space-y-2">
          <Label htmlFor="headers-input">Hlavičky emailu</Label>
          <Textarea
            id="headers-input"
            placeholder="Vložte hlavičky emailu..."
            value={headers}
            onChange={(e) => setHeaders(e.target.value)}
            disabled={isAnalyzing}
            className="min-h-[200px] font-mono text-xs"
          />
          <div className="flex justify-end">
            <Button 
              onClick={analyzeHeaders} 
              disabled={isAnalyzing || !headers.trim()}
            >
              {isAnalyzing ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Analyzuji...
                </>
              ) : (
                <>
                  <Search className="mr-2 h-4 w-4" />
                  Analyzovat
                </>
              )}
            </Button>
          </div>
        </div>

        {analysisError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Chyba analýzy</AlertTitle>
            <AlertDescription>{analysisError}</AlertDescription>
          </Alert>
        )}

        {isAnalyzing && (
          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span>{analysisStep}</span>
              <span>{analysisProgress}%</span>
            </div>
            <Progress value={analysisProgress} />
          </div>
        )}

        {analysisResults && (
          <div className="space-y-4 mt-4">
            <Separator />
            
            <div className="flex justify-between items-center">
              <h3 className="text-lg font-medium">Výsledky analýzy</h3>
              <Badge variant="outline">
                {new Date(analysisResults.analysisDate).toLocaleString('cs-CZ')}
              </Badge>
            </div>
            
            <Tabs defaultValue="summary" className="w-full">
              <TabsList className="grid grid-cols-4 mb-4">
                <TabsTrigger value="summary">Souhrn</TabsTrigger>
                <TabsTrigger value="authentication">Autentizace</TabsTrigger>
                <TabsTrigger value="route">Cesta emailu</TabsTrigger>
                <TabsTrigger value="details">Detaily</TabsTrigger>
              </TabsList>
              
              <TabsContent value="summary" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex flex-col space-y-1">
                    <span className="text-sm text-muted-foreground">Odesílatel</span>
                    <span className="font-medium">{analysisResults.originalSender}</span>
                  </div>
                  
                  <div className="flex flex-col space-y-1">
                    <span className="text-sm text-muted-foreground">IP adresa odesílatele</span>
                    <div className="flex items-center">
                      <Globe className="h-4 w-4 mr-1 text-blue-500" />
                      <span>{analysisResults.senderIp}</span>
                    </div>
                  </div>
                  
                  <div className="flex flex-col space-y-1">
                    <span className="text-sm text-muted-foreground">Geolokace IP</span>
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-1 text-red-500" />
                      <span>{analysisResults.ipGeolocation}</span>
                    </div>
                  </div>
                  
                  <div className="flex flex-col space-y-1">
                    <span className="text-sm text-muted-foreground">Emailový klient</span>
                    <span>{analysisResults.emailClient}</span>
                  </div>
                  
                  <div className="flex flex-col space-y-1">
                    <span className="text-sm text-muted-foreground">Předmět</span>
                    <span className="font-medium">{analysisResults.subject}</span>
                  </div>
                  
                  <div className="flex flex-col space-y-1">
                    <span className="text-sm text-muted-foreground">Datum přijetí</span>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1 text-gray-500" />
                      <span>{new Date(analysisResults.receivedDate).toLocaleString('cs-CZ')}</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex flex-col space-y-1">
                  <span className="text-sm text-muted-foreground">Skóre bezpečnosti</span>
                  <div className="flex items-center">
                    <span className={`text-2xl font-bold ${getSecurityScoreColor(analysisResults.securityScore)}`}>
                      {analysisResults.securityScore}%
                    </span>
                    {analysisResults.securityScore >= 80 && (
                      <Badge variant="outline" className="ml-2 bg-green-50 text-green-700 border-green-200">
                        <Shield className="h-3 w-3 mr-1" /> Bezpečný
                      </Badge>
                    )}
                    {analysisResults.securityScore < 80 && analysisResults.securityScore >= 60 && (
                      <Badge variant="outline" className="ml-2 bg-yellow-50 text-yellow-700 border-yellow-200">
                        <AlertCircle className="h-3 w-3 mr-1" /> Podezřelý
                      </Badge>
                    )}
                    {analysisResults.securityScore < 60 && (
                      <Badge variant="outline" className="ml-2 bg-red-50 text-red-700 border-red-200">
                        <AlertTriangle className="h-3 w-3 mr-1" /> Nebezpečný
                      </Badge>
                    )}
                  </div>
                </div>
                
                {analysisResults.securityIssues.length > 0 && (
                  <div className="flex flex-col space-y-1">
                    <span className="text-sm text-muted-foreground">Bezpečnostní problémy</span>
                    <ul className="list-disc list-inside text-sm">
                      {analysisResults.securityIssues.map((issue, index) => (
                        <li key={index} className="text-red-500">{issue}</li>
                      ))}
                    </ul>
                  </div>
                )}
              </TabsContent>
              
              <TabsContent value="authentication" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <Card>
                    <CardHeader className="py-4">
                      <CardTitle className="text-base flex items-center">
                        {getAuthStatusIcon(analysisResults.authResults.spf)}
                        <span className="ml-2">SPF</span>
                        <Badge variant="outline" className="ml-auto">
                          {analysisResults.authResults.spf.toUpperCase()}
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="py-2">
                      <p className="text-sm text-muted-foreground">
                        Sender Policy Framework - ověřuje, zda je odesílající server autorizován k odesílání emailů za doménu.
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="py-4">
                      <CardTitle className="text-base flex items-center">
                        {getAuthStatusIcon(analysisResults.authResults.dkim)}
                        <span className="ml-2">DKIM</span>
                        <Badge variant="outline" className="ml-auto">
                          {analysisResults.authResults.dkim.toUpperCase()}
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="py-2">
                      <p className="text-sm text-muted-foreground">
                        DomainKeys Identified Mail - digitálně podepisuje email, aby bylo možné ověřit jeho autenticitu.
                      </p>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader className="py-4">
                      <CardTitle className="text-base flex items-center">
                        {getAuthStatusIcon(analysisResults.authResults.dmarc)}
                        <span className="ml-2">DMARC</span>
                        <Badge variant="outline" className="ml-auto">
                          {analysisResults.authResults.dmarc.toUpperCase()}
                        </Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="py-2">
                      <p className="text-sm text-muted-foreground">
                        Domain-based Message Authentication, Reporting & Conformance - kombinuje SPF a DKIM pro lepší ochranu.
                      </p>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
              
              <TabsContent value="route" className="space-y-4">
                <div className="relative">
                  <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-gray-200"></div>
                  <div className="space-y-6 ml-4 pl-6">
                    {analysisResults.emailRoute.map((hop, index) => (
                      <div key={index} className="relative">
                        <div className="absolute -left-10 top-1/2 transform -translate-y-1/2 w-4 h-4 rounded-full bg-primary"></div>
                        <Card>
                          <CardHeader className="py-3">
                            <CardTitle className="text-base flex items-center">
                              <Server className="h-4 w-4 mr-2 text-primary" />
                              {hop.server}
                              <Badge variant="outline" className="ml-auto">
                                Hop #{index + 1}
                              </Badge>
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="py-2">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                              <div className="flex items-center">
                                <Globe className="h-4 w-4 mr-1 text-blue-500" />
                                <span>IP: {hop.ip}</span>
                              </div>
                              <div className="flex items-center">
                                <Clock className="h-4 w-4 mr-1 text-gray-500" />
                                <span>{new Date(hop.timestamp).toLocaleString('cs-CZ')}</span>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    ))}
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="details" className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Message ID</span>
                    <span className="font-mono text-xs">{analysisResults.messageId}</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Return-Path</span>
                    <span className="font-medium">{analysisResults.returnPath}</span>
                  </div>
                  
                  <Separator />
                  
                  <div className="space-y-2">
                    <Label>Původní hlavičky</Label>
                    <ScrollArea className="h-[200px] w-full rounded-md border p-4">
                      <pre className="text-xs font-mono whitespace-pre-wrap">
                        {analysisResults.rawHeaders}
                      </pre>
                    </ScrollArea>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-end">
        {analysisResults && (
          <Button 
            variant="outline" 
            onClick={() => {
              setAnalysisResults(null);
              setAnalysisError(null);
              setAnalysisProgress(0);
            }}
          >
            <RefreshCw className="mr-2 h-4 w-4" />
            Nová analýza
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
