"use client";

import { z } from "zod";
import { YesNoUnknown } from "@/types";

// Photo Metadata Schema
export const photoMetadataSchema = z.object({
  id: z.string(),
  fileName: z.string().optional(),
  storagePath: z.string().optional(),
  downloadURL: z.string().optional(),
  description: z.string().optional(),
  dateTaken: z.string().optional(),
  sourceURL: z.string().optional(),
  location: z.string().optional(),
  notes: z.string().optional(),
});
export type PhotoMetadata = z.infer<typeof photoMetadataSchema>;

// Property Types
export const propertyTypes = [
  "residential_house", 
  "residential_apartment", 
  "commercial", 
  "industrial", 
  "agricultural", 
  "land", 
  "forest", 
  "other"
] as const;

export const ownershipTypes = [
  "full_ownership", 
  "co_ownership", 
  "shared_ownership", 
  "cooperative", 
  "leased", 
  "other"
] as const;

export const acquisitionMethods = [
  "purchase", 
  "inheritance", 
  "gift", 
  "construction", 
  "exchange", 
  "privatization", 
  "restitution", 
  "other"
] as const;

export const usageTypes = [
  "personal_use", 
  "rental", 
  "business", 
  "vacant", 
  "agricultural", 
  "recreational", 
  "other"
] as const;

export const propertySources = [
  "cadastre", 
  "land_registry", 
  "public_records", 
  "witness", 
  "surveillance", 
  "social_media", 
  "other"
] as const;

// Property Record Schema
export const propertyRecordSchema = z.object({
  id: z.string(),
  
  // Basic information
  name: z.string().optional(),
  propertyType: z.enum(propertyTypes).optional(),
  otherPropertyTypeDetail: z.string().optional(),
  
  // Location information
  address: z.string().optional(),
  gpsCoordinates: z.string().optional().refine(val => {
    if (!val || val.trim() === "") return true; // Allow empty
    const parts = val.split(',').map(p => parseFloat(p.trim()));
    return parts.length === 2 && !isNaN(parts[0]) && !isNaN(parts[1]) && 
           parts[0] >= -90 && parts[0] <= 90 && parts[1] >= -180 && parts[1] <= 180;
  }, { message: "Neplatný formát GPS (např. 50.08, 14.42)" }),
  cadastralArea: z.string().optional(),
  cadastralNumber: z.string().optional(),
  landRegistryNumber: z.string().optional(),
  parcelNumbers: z.array(z.string()).optional().default([]),
  
  // Ownership information
  ownershipType: z.enum(ownershipTypes).optional(),
  otherOwnershipTypeDetail: z.string().optional(),
  ownershipPercentage: z.preprocess(
    (val) => (val === "" || val === null || val === undefined || Number.isNaN(Number(val))) ? null : Number(val),
    z.number().min(0).max(100).nullable().optional()
  ),
  coOwners: z.array(z.string()).optional().default([]),
  acquisitionMethod: z.enum(acquisitionMethods).optional(),
  otherAcquisitionMethodDetail: z.string().optional(),
  acquisitionDate: z.string().optional(),
  
  // Financial information
  purchasePrice: z.preprocess(
    (val) => (val === "" || val === null || val === undefined || Number.isNaN(Number(val))) ? null : Number(val),
    z.number().nullable().optional()
  ),
  purchaseCurrency: z.string().optional().default("CZK"),
  currentEstimatedValue: z.preprocess(
    (val) => (val === "" || val === null || val === undefined || Number.isNaN(Number(val))) ? null : Number(val),
    z.number().nullable().optional()
  ),
  valueCurrency: z.string().optional().default("CZK"),
  mortgageInfo: z.string().optional(),
  otherEncumbrances: z.string().optional(),
  
  // Usage information
  usageType: z.enum(usageTypes).optional(),
  otherUsageTypeDetail: z.string().optional(),
  rentalIncome: z.preprocess(
    (val) => (val === "" || val === null || val === undefined || Number.isNaN(Number(val))) ? null : Number(val),
    z.number().nullable().optional()
  ),
  rentalCurrency: z.string().optional().default("CZK"),
  tenants: z.string().optional(),
  
  // Physical characteristics
  totalArea: z.preprocess(
    (val) => (val === "" || val === null || val === undefined || Number.isNaN(Number(val))) ? null : Number(val),
    z.number().nullable().optional()
  ),
  buildingArea: z.preprocess(
    (val) => (val === "" || val === null || val === undefined || Number.isNaN(Number(val))) ? null : Number(val),
    z.number().nullable().optional()
  ),
  landArea: z.preprocess(
    (val) => (val === "" || val === null || val === undefined || Number.isNaN(Number(val))) ? null : Number(val),
    z.number().nullable().optional()
  ),
  numberOfRooms: z.preprocess(
    (val) => (val === "" || val === null || val === undefined || Number.isNaN(Number(val))) ? null : Number(val),
    z.number().nullable().optional()
  ),
  numberOfFloors: z.preprocess(
    (val) => (val === "" || val === null || val === undefined || Number.isNaN(Number(val))) ? null : Number(val),
    z.number().nullable().optional()
  ),
  constructionYear: z.preprocess(
    (val) => (val === "" || val === null || val === undefined || Number.isNaN(Number(val))) ? null : Number(val),
    z.number().nullable().optional()
  ),
  reconstructionYear: z.preprocess(
    (val) => (val === "" || val === null || val === undefined || Number.isNaN(Number(val))) ? null : Number(val),
    z.number().nullable().optional()
  ),
  
  // Source information
  source: z.enum(propertySources).optional(),
  otherSourceDetail: z.string().optional(),
  verificationStatus: z.enum(["yes", "no", "unknown"] as [YesNoUnknown, ...YesNoUnknown[]]).optional(),
  verificationNotes: z.string().optional(),
  
  // Additional information
  description: z.string().optional(),
  notes: z.string().optional(),
  photos: z.array(photoMetadataSchema).optional().default([]),
  
  // Related documents
  documentReferences: z.string().optional(),
}).superRefine((data, ctx) => {
  if (data.propertyType === 'other' && !data.otherPropertyTypeDetail?.trim()) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Upřesnění typu nemovitosti je povinné.", path: ['otherPropertyTypeDetail'] });
  }
  if (data.ownershipType === 'other' && !data.otherOwnershipTypeDetail?.trim()) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Upřesnění typu vlastnictví je povinné.", path: ['otherOwnershipTypeDetail'] });
  }
  if (data.acquisitionMethod === 'other' && !data.otherAcquisitionMethodDetail?.trim()) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Upřesnění způsobu nabytí je povinné.", path: ['otherAcquisitionMethodDetail'] });
  }
  if (data.usageType === 'other' && !data.otherUsageTypeDetail?.trim()) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Upřesnění způsobu využití je povinné.", path: ['otherUsageTypeDetail'] });
  }
  if (data.source === 'other' && !data.otherSourceDetail?.trim()) {
    ctx.addIssue({ code: z.ZodIssueCode.custom, message: "Upřesnění zdroje je povinné.", path: ['otherSourceDetail'] });
  }
});

export type PropertyRecord = z.infer<typeof propertyRecordSchema>;

// Cadastre Module Schema
export const cadastreModuleSchema = z.object({
  properties: z.array(propertyRecordSchema).optional().default([]),
  generalNotes: z.string().optional(),
});

export type CadastreModuleFormValues = z.infer<typeof cadastreModuleSchema>;
