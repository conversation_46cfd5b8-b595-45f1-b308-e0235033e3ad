/**
 * Fotografická analýza - pokročilé analytické <PERSON>
 */

/**
 * Reverzní vyhledávání obrázků
 */
function reverseImageSearch() {
    console.log('Reverzní vyhledávání obrázků');
    
    // Kontrola, zda je vybrána fotografie
    const selectedPhoto = document.querySelector('.photo-item.selected');
    if (!selectedPhoto) {
        alert('Nejprve vyberte fotografii pro analýzu.');
        return;
    }
    
    const imageUrl = selectedPhoto.getAttribute('data-image-url');
    if (!imageUrl) {
        alert('Nelze získat URL fotografie.');
        return;
    }
    
    // Reverzní vyhledávání podle URL
    reverseImageSearchUrl(imageUrl);
}

/**
 * Reverzní vyhledávání obrázků podle URL
 * @param {string} imageUrl - URL obrázku
 */
function reverseImageSearchUrl(imageUrl) {
    console.log('Reverzní vyhledávání obrázků podle URL:', imageUrl);
    
    // Vytvoření dialogu pro reverzní vyhledávání
    const dialog = document.createElement('div');
    dialog.className = 'reverse-search-dialog';
    dialog.innerHTML = `
        <div class="reverse-search-dialog-content">
            <div class="reverse-search-dialog-header">
                <h3>Reverzní vyhledávání obrázků</h3>
                <button type="button" class="reverse-search-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="reverse-search-dialog-body">
                <div class="reverse-search-preview">
                    <img src="${imageUrl}" alt="Vyhledávaný obrázek" onerror="this.src='https://via.placeholder.com/400x300?text=Chyba+načítání'">
                </div>
                <div class="reverse-search-engines">
                    <h4>Vyberte vyhledávač</h4>
                    <div class="reverse-search-engine-list">
                        <div class="reverse-search-engine" data-engine="google">
                            <div class="reverse-search-engine-icon">
                                <i class="fab fa-google"></i>
                            </div>
                            <div class="reverse-search-engine-name">Google Images</div>
                        </div>
                        <div class="reverse-search-engine" data-engine="bing">
                            <div class="reverse-search-engine-icon">
                                <i class="fab fa-microsoft"></i>
                            </div>
                            <div class="reverse-search-engine-name">Bing Visual Search</div>
                        </div>
                        <div class="reverse-search-engine" data-engine="yandex">
                            <div class="reverse-search-engine-icon">
                                <i class="fas fa-search"></i>
                            </div>
                            <div class="reverse-search-engine-name">Yandex Images</div>
                        </div>
                        <div class="reverse-search-engine" data-engine="tineye">
                            <div class="reverse-search-engine-icon">
                                <i class="fas fa-eye"></i>
                            </div>
                            <div class="reverse-search-engine-name">TinEye</div>
                        </div>
                    </div>
                </div>
                <div class="reverse-search-note">
                    <p><i class="fas fa-info-circle"></i> Poznámka: Pro lokální soubory budete přesměrováni na stránku vyhledávače, kde budete muset soubor nahrát ručně.</p>
                </div>
            </div>
        </div>
    `;
    
    // Přidání dialogu do stránky
    document.body.appendChild(dialog);
    
    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.reverse-search-dialog-close').addEventListener('click', function() {
        dialog.remove();
    });
    
    // Přidání event listenerů pro vyhledávače
    dialog.querySelectorAll('.reverse-search-engine').forEach(engine => {
        engine.addEventListener('click', function() {
            const engineType = this.getAttribute('data-engine');
            
            // Vytvoření URL pro reverzní vyhledávání
            let searchUrl = '';
            
            // Pro lokální soubory (blob URL) je potřeba jiný přístup
            if (imageUrl.startsWith('blob:')) {
                // Pro blob URL nemůžeme přímo použít URL obrázku
                // Musíme přesměrovat na stránku vyhledávače
                switch (engineType) {
                    case 'google':
                        searchUrl = 'https://images.google.com/';
                        break;
                    case 'bing':
                        searchUrl = 'https://www.bing.com/images/discover?FORM=ILPMFT';
                        break;
                    case 'yandex':
                        searchUrl = 'https://yandex.com/images/';
                        break;
                    case 'tineye':
                        searchUrl = 'https://tineye.com/';
                        break;
                }
            } else {
                // Pro online obrázky můžeme použít URL
                switch (engineType) {
                    case 'google':
                        searchUrl = `https://images.google.com/searchbyimage?image_url=${encodeURIComponent(imageUrl)}`;
                        break;
                    case 'bing':
                        searchUrl = `https://www.bing.com/images/search?view=detailv2&iss=sbi&form=SBIVSP&q=imgurl:${encodeURIComponent(imageUrl)}`;
                        break;
                    case 'yandex':
                        searchUrl = `https://yandex.com/images/search?url=${encodeURIComponent(imageUrl)}&rpt=imageview`;
                        break;
                    case 'tineye':
                        searchUrl = `https://tineye.com/search?url=${encodeURIComponent(imageUrl)}`;
                        break;
                }
            }
            
            // Otevření URL v novém okně
            window.open(searchUrl, '_blank');
            
            // Zavření dialogu
            dialog.remove();
        });
    });
    
    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}

/**
 * Detekce obličejů
 */
function detectFaces() {
    console.log('Detekce obličejů');
    
    // Kontrola, zda je vybrána fotografie
    const selectedPhoto = document.querySelector('.photo-item.selected');
    if (!selectedPhoto) {
        alert('Nejprve vyberte fotografii pro analýzu.');
        return;
    }
    
    const imageUrl = selectedPhoto.getAttribute('data-image-url');
    if (!imageUrl) {
        alert('Nelze získat URL fotografie.');
        return;
    }
    
    // Detekce obličejů v obrázku
    detectFacesInImage(imageUrl);
}

/**
 * Detekce obličejů v obrázku
 * @param {string} imageUrl - URL obrázku
 */
function detectFacesInImage(imageUrl) {
    console.log('Detekce obličejů v obrázku:', imageUrl);
    
    // Zobrazení dialogu s informacemi o externích službách
    const dialog = document.createElement('div');
    dialog.className = 'face-detection-dialog';
    dialog.innerHTML = `
        <div class="face-detection-dialog-content">
            <div class="face-detection-dialog-header">
                <h3>Detekce obličejů</h3>
                <button type="button" class="face-detection-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="face-detection-dialog-body">
                <div class="face-detection-preview">
                    <img src="${imageUrl}" alt="Analyzovaný obrázek" onerror="this.src='https://via.placeholder.com/400x300?text=Chyba+načítání'">
                </div>
                <div class="face-detection-info">
                    <p>Pro detekci obličejů a biometrickou analýzu můžete použít následující online služby:</p>
                    <div class="external-links">
                        <a href="https://pimeyes.com/en" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> PimEyes
                        </a>
                        <a href="https://azure.microsoft.com/en-us/services/cognitive-services/face/" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> Microsoft Face API
                        </a>
                        <a href="https://cloud.google.com/vision/docs/detecting-faces" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> Google Cloud Vision
                        </a>
                    </div>
                    <div class="face-detection-note">
                        <p><i class="fas fa-info-circle"></i> Poznámka: Tyto služby mohou vyžadovat registraci a mohou být zpoplatněny. Při používání těchto služeb dbejte na dodržování zákonů a předpisů týkajících se ochrany osobních údajů.</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Přidání dialogu do stránky
    document.body.appendChild(dialog);
    
    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.face-detection-dialog-close').addEventListener('click', function() {
        dialog.remove();
    });
    
    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}

/**
 * Analýza objektů
 */
function analyzeObjects() {
    console.log('Analýza objektů');
    
    // Kontrola, zda je vybrána fotografie
    const selectedPhoto = document.querySelector('.photo-item.selected');
    if (!selectedPhoto) {
        alert('Nejprve vyberte fotografii pro analýzu.');
        return;
    }
    
    const imageUrl = selectedPhoto.getAttribute('data-image-url');
    if (!imageUrl) {
        alert('Nelze získat URL fotografie.');
        return;
    }
    
    // Analýza objektů v obrázku
    analyzeObjectsInImage(imageUrl);
}

/**
 * Analýza objektů v obrázku
 * @param {string} imageUrl - URL obrázku
 */
function analyzeObjectsInImage(imageUrl) {
    console.log('Analýza objektů v obrázku:', imageUrl);
    
    // Zobrazení dialogu s informacemi o externích službách
    const dialog = document.createElement('div');
    dialog.className = 'object-detection-dialog';
    dialog.innerHTML = `
        <div class="object-detection-dialog-content">
            <div class="object-detection-dialog-header">
                <h3>Analýza objektů</h3>
                <button type="button" class="object-detection-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="object-detection-dialog-body">
                <div class="object-detection-preview">
                    <img src="${imageUrl}" alt="Analyzovaný obrázek" onerror="this.src='https://via.placeholder.com/400x300?text=Chyba+načítání'">
                </div>
                <div class="object-detection-info">
                    <p>Pro analýzu objektů a scén můžete použít následující online služby:</p>
                    <div class="external-links">
                        <a href="https://cloud.google.com/vision" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> Google Cloud Vision
                        </a>
                        <a href="https://azure.microsoft.com/en-us/services/cognitive-services/computer-vision/" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> Microsoft Computer Vision
                        </a>
                        <a href="https://aws.amazon.com/rekognition/" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> Amazon Rekognition
                        </a>
                    </div>
                    <div class="object-detection-note">
                        <p><i class="fas fa-info-circle"></i> Poznámka: Tyto služby mohou vyžadovat registraci a mohou být zpoplatněny.</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Přidání dialogu do stránky
    document.body.appendChild(dialog);
    
    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.object-detection-dialog-close').addEventListener('click', function() {
        dialog.remove();
    });
    
    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}

/**
 * Geolokace obrázku
 */
function geolocateImage() {
    console.log('Geolokace obrázku');
    
    // Kontrola, zda je vybrána fotografie
    const selectedPhoto = document.querySelector('.photo-item.selected');
    if (!selectedPhoto) {
        alert('Nejprve vyberte fotografii pro analýzu.');
        return;
    }
    
    const imageUrl = selectedPhoto.getAttribute('data-image-url');
    if (!imageUrl) {
        alert('Nelze získat URL fotografie.');
        return;
    }
    
    // Geolokace obrázku podle obsahu
    geolocateImageByContent(imageUrl);
}

/**
 * Geolokace obrázku podle obsahu
 * @param {string} imageUrl - URL obrázku
 */
function geolocateImageByContent(imageUrl) {
    console.log('Geolokace obrázku podle obsahu:', imageUrl);
    
    // Zobrazení dialogu s informacemi o externích službách
    const dialog = document.createElement('div');
    dialog.className = 'geolocation-dialog';
    dialog.innerHTML = `
        <div class="geolocation-dialog-content">
            <div class="geolocation-dialog-header">
                <h3>Geolokace obrázku</h3>
                <button type="button" class="geolocation-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="geolocation-dialog-body">
                <div class="geolocation-preview">
                    <img src="${imageUrl}" alt="Analyzovaný obrázek" onerror="this.src='https://via.placeholder.com/400x300?text=Chyba+načítání'">
                </div>
                <div class="geolocation-info">
                    <p>Pro geolokaci obrázku podle vizuálních prvků můžete použít následující online služby a techniky:</p>
                    <div class="external-links">
                        <a href="https://www.geoguessr.com/" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> GeoGuessr
                        </a>
                        <a href="https://lens.google.com/" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> Google Lens
                        </a>
                        <a href="https://www.google.com/maps" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> Google Maps
                        </a>
                    </div>
                    <div class="geolocation-techniques">
                        <h4>Techniky pro manuální geolokaci:</h4>
                        <ul>
                            <li>Analýza architektonických prvků a staveb</li>
                            <li>Identifikace dopravního značení a nápisů</li>
                            <li>Rozpoznání typické vegetace a krajiny</li>
                            <li>Analýza stínů pro určení orientace a času</li>
                            <li>Porovnání s referenčními fotografiemi a satelitními snímky</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Přidání dialogu do stránky
    document.body.appendChild(dialog);
    
    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.geolocation-dialog-close').addEventListener('click', function() {
        dialog.remove();
    });
    
    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}

/**
 * Extrakce textu z obrázku (OCR)
 */
function extractText() {
    console.log('Extrakce textu z obrázku');
    
    // Kontrola, zda je vybrána fotografie
    const selectedPhoto = document.querySelector('.photo-item.selected');
    if (!selectedPhoto) {
        alert('Nejprve vyberte fotografii pro analýzu.');
        return;
    }
    
    const imageUrl = selectedPhoto.getAttribute('data-image-url');
    if (!imageUrl) {
        alert('Nelze získat URL fotografie.');
        return;
    }
    
    // Extrakce textu z obrázku
    extractTextFromImage(imageUrl);
}

/**
 * Extrakce textu z obrázku
 * @param {string} imageUrl - URL obrázku
 */
function extractTextFromImage(imageUrl) {
    console.log('Extrakce textu z obrázku:', imageUrl);
    
    // Zobrazení dialogu s informacemi o externích službách
    const dialog = document.createElement('div');
    dialog.className = 'ocr-dialog';
    dialog.innerHTML = `
        <div class="ocr-dialog-content">
            <div class="ocr-dialog-header">
                <h3>Extrakce textu (OCR)</h3>
                <button type="button" class="ocr-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="ocr-dialog-body">
                <div class="ocr-preview">
                    <img src="${imageUrl}" alt="Analyzovaný obrázek" onerror="this.src='https://via.placeholder.com/400x300?text=Chyba+načítání'">
                </div>
                <div class="ocr-info">
                    <p>Pro extrakci textu z obrázku můžete použít následující online služby:</p>
                    <div class="external-links">
                        <a href="https://www.onlineocr.net/" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> OnlineOCR.net
                        </a>
                        <a href="https://www.newocr.com/" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> NewOCR.com
                        </a>
                        <a href="https://cloud.google.com/vision/docs/ocr" target="_blank" class="btn-inline">
                            <i class="fas fa-external-link-alt"></i> Google Cloud Vision OCR
                        </a>
                    </div>
                    <div class="ocr-note">
                        <p><i class="fas fa-info-circle"></i> Poznámka: Pro nejlepší výsledky použijte obrázek s vysokým rozlišením a dobrým kontrastem mezi textem a pozadím.</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Přidání dialogu do stránky
    document.body.appendChild(dialog);
    
    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.ocr-dialog-close').addEventListener('click', function() {
        dialog.remove();
    });
    
    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}
