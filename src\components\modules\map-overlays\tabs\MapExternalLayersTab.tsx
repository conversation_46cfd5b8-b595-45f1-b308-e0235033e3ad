"use client";

import { useState } from "react";
import { UseFormReturn, useFieldArray } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { MapOverlaysModuleFormValues } from "../schemas";
import { MapExternalLayer } from "@/types";
import { Plus, Trash2, Search, Edit, Layers, Eye, EyeOff, ExternalLink } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface MapExternalLayersTabProps {
  form: UseFormReturn<MapOverlaysModuleFormValues>;
}

export function MapExternalLayersTab({ form }: MapExternalLayersTabProps) {
  const [selectedLayerId, setSelectedLayerId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  const { control, register, watch } = form;
  const { fields, append, remove, update } = useFieldArray({
    control,
    name: "externalLayers",
  });

  const externalLayers = watch("externalLayers") || [];
  const selectedLayerIndex = externalLayers.findIndex(l => l.id === selectedLayerId);

  // Filtrování vrstev podle vyhledávacího výrazu
  const filteredLayers = externalLayers.filter(layer => 
    layer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    layer.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    layer.url.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Přidání nové vrstvy
  const handleAddLayer = () => {
    const newLayer: MapExternalLayer = {
      id: uuidv4(),
      name: "Nová vrstva",
      type: "WMS",
      url: "https://example.com/wms",
      isVisible: true,
      opacity: 0.7,
      zIndex: externalLayers.length,
    };
    append(newLayer);
    setSelectedLayerId(newLayer.id);
  };

  // Odstranění vrstvy
  const handleRemoveLayer = (index: number) => {
    if (window.confirm("Opravdu chcete smazat tuto vrstvu?")) {
      remove(index);
      if (selectedLayerId === externalLayers[index].id) {
        setSelectedLayerId(null);
      }
    }
  };

  // Přepnutí viditelnosti vrstvy
  const toggleLayerVisibility = (index: number) => {
    const layer = externalLayers[index];
    const updatedLayer = { ...layer, isVisible: !layer.isVisible };
    update(index, updatedLayer);
  };

  // Změna průhlednosti vrstvy
  const handleOpacityChange = (index: number, value: number[]) => {
    const layer = externalLayers[index];
    const updatedLayer = { ...layer, opacity: value[0] };
    update(index, updatedLayer);
  };

  // Změna Z-indexu vrstvy
  const handleZIndexChange = (index: number, value: number) => {
    const layer = externalLayers[index];
    const updatedLayer = { ...layer, zIndex: value };
    update(index, updatedLayer);
  };

  // Otevření URL vrstvy v novém okně
  const openLayerUrl = (url: string) => {
    window.open(url, "_blank");
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Externí mapové vrstvy</CardTitle>
              <CardDescription>
                Správa externích mapových vrstev (WMS, GeoJSON, KML, atd.)
              </CardDescription>
            </div>
            <Button onClick={handleAddLayer}>
              <Plus className="mr-2 h-4 w-4" />
              Přidat vrstvu
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Vyhledat vrstvy..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>

            <ScrollArea className="h-[400px] rounded-md border">
              {filteredLayers.length === 0 ? (
                <div className="p-4 text-center text-muted-foreground">
                  Nebyly nalezeny žádné externí vrstvy
                </div>
              ) : (
                <div className="p-4 space-y-4">
                  {filteredLayers.map((layer, index) => (
                    <Card key={layer.id} className={`border ${selectedLayerId === layer.id ? 'border-primary' : ''}`}>
                      <CardHeader className="p-4 pb-2">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => toggleLayerVisibility(index)}
                            >
                              {layer.isVisible ? (
                                <Eye className="h-4 w-4 text-primary" />
                              ) : (
                                <EyeOff className="h-4 w-4 text-muted-foreground" />
                              )}
                            </Button>
                            <div>
                              <div className="font-medium">{layer.name}</div>
                              <div className="text-xs text-muted-foreground">
                                {layer.type}
                              </div>
                            </div>
                          </div>
                          <div className="flex space-x-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => openLayerUrl(layer.url)}
                            >
                              <ExternalLink className="h-4 w-4 text-muted-foreground" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => setSelectedLayerId(selectedLayerId === layer.id ? null : layer.id)}
                            >
                              <Edit className="h-4 w-4 text-muted-foreground" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={() => handleRemoveLayer(index)}
                            >
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </Button>
                          </div>
                        </div>
                      </CardHeader>
                      
                      {selectedLayerId === layer.id && (
                        <CardContent className="p-4 pt-0">
                          <Separator className="my-2" />
                          <div className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <Label htmlFor={`externalLayers.${index}.name`}>Název vrstvy</Label>
                                <Input
                                  id={`externalLayers.${index}.name`}
                                  {...register(`externalLayers.${index}.name`)}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor={`externalLayers.${index}.type`}>Typ vrstvy</Label>
                                <Input
                                  id={`externalLayers.${index}.type`}
                                  {...register(`externalLayers.${index}.type`)}
                                  placeholder="Např. WMS, GeoJSON, KML, ..."
                                />
                              </div>
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`externalLayers.${index}.url`}>URL vrstvy</Label>
                              <Input
                                id={`externalLayers.${index}.url`}
                                {...register(`externalLayers.${index}.url`)}
                                placeholder="https://example.com/wms"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`externalLayers.${index}.apiKey`}>API klíč (volitelné)</Label>
                              <Input
                                id={`externalLayers.${index}.apiKey`}
                                {...register(`externalLayers.${index}.apiKey`)}
                                placeholder="Zadejte API klíč, pokud je vyžadován"
                                type="password"
                              />
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div className="space-y-2">
                                <div className="flex justify-between">
                                  <Label htmlFor={`externalLayers.${index}.opacity`}>Průhlednost</Label>
                                  <span className="text-xs text-muted-foreground">
                                    {Math.round(layer.opacity * 100)}%
                                  </span>
                                </div>
                                <Slider
                                  id={`externalLayers.${index}.opacity`}
                                  min={0}
                                  max={1}
                                  step={0.01}
                                  value={[layer.opacity]}
                                  onValueChange={(value) => handleOpacityChange(index, value)}
                                />
                              </div>
                              <div className="space-y-2">
                                <Label htmlFor={`externalLayers.${index}.zIndex`}>Z-index</Label>
                                <Input
                                  id={`externalLayers.${index}.zIndex`}
                                  type="number"
                                  min={0}
                                  {...register(`externalLayers.${index}.zIndex`, {
                                    valueAsNumber: true,
                                  })}
                                />
                              </div>
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`externalLayers.${index}.attribution`}>Atribuce</Label>
                              <Input
                                id={`externalLayers.${index}.attribution`}
                                {...register(`externalLayers.${index}.attribution`)}
                                placeholder="Např. © OpenStreetMap contributors"
                              />
                            </div>

                            <div className="space-y-2">
                              <Label htmlFor={`externalLayers.${index}.notes`}>Poznámky</Label>
                              <Textarea
                                id={`externalLayers.${index}.notes`}
                                {...register(`externalLayers.${index}.notes`)}
                                placeholder="Zadejte poznámky k vrstvě..."
                                rows={3}
                              />
                            </div>

                            <div className="flex items-center space-x-2">
                              <Switch
                                id={`externalLayers.${index}.isVisible`}
                                checked={layer.isVisible}
                                onCheckedChange={() => toggleLayerVisibility(index)}
                              />
                              <Label htmlFor={`externalLayers.${index}.isVisible`}>
                                Vrstva je viditelná
                              </Label>
                            </div>
                          </div>
                        </CardContent>
                      )}
                    </Card>
                  ))}
                </div>
              )}
            </ScrollArea>
          </div>
        </CardContent>
        <CardFooter className="border-t pt-4">
          <div className="flex justify-between w-full">
            <div className="text-sm text-muted-foreground">
              Celkem vrstev: {externalLayers.length}
            </div>
            <div className="flex space-x-2">
              <Badge variant="outline" className="flex items-center">
                <Layers className="h-3 w-3 mr-1" />
                Viditelné: {externalLayers.filter(l => l.isVisible).length}
              </Badge>
            </div>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
