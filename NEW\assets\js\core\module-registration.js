/**
 * <PERSON>trace modulů pro OSINT systém
 * Tento soubor obsahuje funkce pro registraci a správu modulů v OSINT systému.
 */

// Pole dostupných modulů
const availableModules = [
    {
        id: 'email-analysis',
        name: 'Analýza e-mailů',
        description: 'Modul pro analýzu e-mailových adres, včetně ověření existence, vyhledávání v únicích dat a vizualizace.',
        icon: 'fa-envelope',
        category: 'analysis',
        jsFile: 'email-analysis.js',
        cssFile: 'email-analysis-styles.css',
        enabled: true
    },
    {
        id: 'phone-analysis',
        name: 'Analýza telefonních čísel',
        description: 'Modul pro analýzu telefonních čísel, včetně ověření platnosti, geolokace a vyhledávání v databázích.',
        icon: 'fa-phone',
        category: 'analysis',
        jsFile: 'phone-analysis.js',
        cssFile: 'phone-analysis-styles.css',
        enabled: true
    },
    {
        id: 'ip-analysis',
        name: 'Analýza IP/sítí',
        description: 'Modul pro analýzu IP adres a sítí, včetně geolokace, whois informací a skenování portů.',
        icon: 'fa-network-wired',
        category: 'analysis',
        jsFile: 'ip-analysis.js',
        cssFile: 'ip-analysis-styles.css',
        enabled: true
    },
    {
        id: 'darkweb-monitoring',
        name: 'Monitoring darkwebu',
        description: 'Modul pro monitoring darkwebu, včetně vyhledávání na darkwebových fórech a tržištích.',
        icon: 'fa-user-secret',
        category: 'monitoring',
        jsFile: 'darkweb-monitoring.js',
        cssFile: 'darkweb-monitoring-styles.css',
        enabled: true
    },
    {
        id: 'photo-analysis',
        name: 'Analýza fotografií',
        description: 'Modul pro analýzu fotografií, včetně extrakce metadat, rozpoznávání obličejů a vyhledávání podobných obrázků.',
        icon: 'fa-image',
        category: 'analysis',
        jsFile: 'photo-analysis.js',
        cssFile: 'photo-analysis-styles.css',
        enabled: true
    },
    {
        id: 'financial-monitoring',
        name: 'Finanční monitoring',
        description: 'Modul pro monitoring finančních transakcí, včetně sledování bankovních účtů a kryptoměnových adres.',
        icon: 'fa-money-bill-wave',
        category: 'monitoring',
        jsFile: 'financial-monitoring.js',
        cssFile: 'financial-monitoring-styles.css',
        enabled: true
    },
    {
        id: 'map-overlays',
        name: 'Mapové překryvy',
        description: 'Modul pro zobrazení dat na mapě, včetně geolokace osob, událostí a objektů.',
        icon: 'fa-map-marked-alt',
        category: 'visualization',
        jsFile: 'map-overlays.js',
        cssFile: 'map-overlays-styles.css',
        enabled: true
    },
    {
        id: 'automated-crawler',
        name: 'Automatizovaný crawler',
        description: 'Modul pro automatizované procházení webových stránek a extrakci dat.',
        icon: 'fa-spider',
        category: 'collection',
        jsFile: 'automated-crawler.js',
        cssFile: 'automated-crawler-styles.css',
        enabled: true
    },
    {
        id: 'news-monitoring',
        name: 'Monitoring zpráv',
        description: 'Modul pro monitoring zpravodajských webů a sociálních médií.',
        icon: 'fa-newspaper',
        category: 'monitoring',
        jsFile: 'news-monitoring.js',
        cssFile: 'news-monitoring-styles.css',
        enabled: true
    },
    {
        id: 'api-connectors',
        name: 'API konektory',
        description: 'Modul pro připojení k externím API a získávání dat.',
        icon: 'fa-plug',
        category: 'collection',
        jsFile: 'api-connectors.js',
        cssFile: 'api-connectors-styles.css',
        enabled: true
    },
    {
        id: 'messaging-platforms',
        name: 'Monitoring komunikačních platforem',
        description: 'Modul pro monitoring komunikačních platforem jako Telegram, Discord a WhatsApp.',
        icon: 'fa-comments',
        category: 'monitoring',
        jsFile: 'messaging-platforms.js',
        cssFile: 'messaging-platforms-styles.css',
        enabled: true
    },
    {
        id: 'correlation-tool',
        name: 'Korelační nástroj',
        description: 'Modul pro korelaci dat z různých zdrojů a vizualizaci vztahů mezi entitami.',
        icon: 'fa-project-diagram',
        category: 'analysis',
        jsFile: 'correlation-tool.js',
        cssFile: 'correlation-tool-styles.css',
        enabled: true
    },
    {
        id: 'timeline',
        name: 'Časová osa',
        description: 'Modul pro vizualizaci událostí na časové ose.',
        icon: 'fa-clock',
        category: 'visualization',
        jsFile: 'timeline.js',
        cssFile: 'timeline-styles.css',
        enabled: true
    },
    {
        id: 'complex-search',
        name: 'Komplexní vyhledávání',
        description: 'Modul pro komplexní vyhledávání entit na internetu pomocí Google dorků a dalších nástrojů.',
        icon: 'fa-search',
        category: 'collection',
        jsFile: 'complex-search.js',
        cssFile: 'complex-search-styles.css',
        enabled: true
    }
];

/**
 * Získání seznamu dostupných modulů
 * @returns {Array} - Pole dostupných modulů
 */
function getAvailableModules() {
    return availableModules;
}

/**
 * Získání modulu podle ID
 * @param {string} moduleId - ID modulu
 * @returns {Object|null} - Objekt modulu nebo null, pokud modul nebyl nalezen
 */
function getModuleById(moduleId) {
    return availableModules.find(module => module.id === moduleId) || null;
}

/**
 * Načtení skriptů a stylů modulu
 * @param {string} moduleId - ID modulu
 * @returns {Promise} - Promise, který se vyřeší po načtení všech souborů
 */
function loadModuleResources(moduleId) {
    const module = getModuleById(moduleId);
    if (!module) {
        return Promise.reject(new Error(`Modul s ID ${moduleId} nebyl nalezen.`));
    }
    
    return Promise.all([
        loadScript(module.jsFile),
        loadStylesheet(module.cssFile)
    ]);
}

/**
 * Načtení JavaScript souboru
 * @param {string} src - Cesta k souboru
 * @returns {Promise} - Promise, který se vyřeší po načtení souboru
 */
function loadScript(src) {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = () => resolve(script);
        script.onerror = () => reject(new Error(`Nepodařilo se načíst skript: ${src}`));
        document.head.appendChild(script);
    });
}

/**
 * Načtení CSS souboru
 * @param {string} href - Cesta k souboru
 * @returns {Promise} - Promise, který se vyřeší po načtení souboru
 */
function loadStylesheet(href) {
    return new Promise((resolve, reject) => {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        link.onload = () => resolve(link);
        link.onerror = () => reject(new Error(`Nepodařilo se načíst stylesheet: ${href}`));
        document.head.appendChild(link);
    });
}

// Exportovat funkce pro použití v jiných modulech
window.getAvailableModules = getAvailableModules;
window.getModuleById = getModuleById;
window.loadModuleResources = loadModuleResources;
