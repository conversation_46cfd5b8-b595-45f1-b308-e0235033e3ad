/**
 * Mapové overlapy - funkce pro export a fotodokumentaci
 */

/**
 * Export mapy
 */
function exportMap() {
    console.log('Export mapy');
    
    if (!map) {
        console.error('Mapa není inicializována');
        return;
    }
    
    // Najít aktuální modul mapových overlapů
    const mapModule = document.querySelector('.module[id^="module-mapove-overlapy"]');
    if (!mapModule) {
        console.error('Modul mapových overlapů nebyl nalezen');
        return;
    }
    
    // Zobrazení dialogu pro export
    const exportDialog = document.createElement('div');
    exportDialog.className = 'export-dialog';
    exportDialog.innerHTML = `
        <div class="export-dialog-content">
            <div class="export-dialog-header">
                <h3>Export mapy</h3>
                <button type="button" class="export-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="export-dialog-body">
                <div class="export-options">
                    <div class="export-option">
                        <h4>Export jako obrázek</h4>
                        <p>Export aktuálního pohledu mapy jako obrázek PNG.</p>
                        <button type="button" class="btn-inline export-as-image">
                            <i class="fas fa-image"></i> Exportovat jako PNG
                        </button>
                    </div>
                    <div class="export-option">
                        <h4>Export dat</h4>
                        <p>Export všech dat z mapy ve formátu GeoJSON.</p>
                        <button type="button" class="btn-inline export-as-geojson">
                            <i class="fas fa-file-code"></i> Exportovat jako GeoJSON
                        </button>
                    </div>
                    <div class="export-option">
                        <h4>Tisk mapy</h4>
                        <p>Tisk aktuálního pohledu mapy.</p>
                        <button type="button" class="btn-inline print-map">
                            <i class="fas fa-print"></i> Tisknout mapu
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Přidání dialogu do stránky
    document.body.appendChild(exportDialog);
    
    // Přidání event listenerů pro tlačítka
    exportDialog.querySelector('.export-dialog-close').addEventListener('click', function() {
        exportDialog.remove();
    });
    
    exportDialog.querySelector('.export-as-image').addEventListener('click', function() {
        exportMapAsImage();
        exportDialog.remove();
    });
    
    exportDialog.querySelector('.export-as-geojson').addEventListener('click', function() {
        exportMapAsGeoJSON();
        exportDialog.remove();
    });
    
    exportDialog.querySelector('.print-map').addEventListener('click', function() {
        printMap();
        exportDialog.remove();
    });
    
    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    exportDialog.addEventListener('click', function(event) {
        if (event.target === exportDialog) {
            exportDialog.remove();
        }
    });
}

/**
 * Export mapy jako obrázek
 */
function exportMapAsImage() {
    console.log('Export mapy jako obrázek');
    
    if (!map) {
        console.error('Mapa není inicializována');
        return;
    }
    
    // Kontrola, zda je načtena knihovna leaflet-image
    if (typeof leafletImage === 'undefined') {
        alert('Pro export mapy jako obrázek je potřeba načíst knihovnu leaflet-image.');
        console.error('Knihovna leaflet-image není načtena');
        
        // Alternativní řešení - screenshot viditelné části mapy
        html2canvas(document.querySelector('#map-container')).then(canvas => {
            // Vytvoření odkazu pro stažení
            const link = document.createElement('a');
            link.download = 'mapa-export.png';
            link.href = canvas.toDataURL('image/png');
            link.click();
        });
        
        return;
    }
    
    // Export mapy jako obrázek pomocí leaflet-image
    leafletImage(map, function(err, canvas) {
        if (err) {
            console.error('Chyba při exportu mapy jako obrázek:', err);
            alert('Nepodařilo se exportovat mapu jako obrázek.');
            return;
        }
        
        // Vytvoření odkazu pro stažení
        const link = document.createElement('a');
        link.download = 'mapa-export.png';
        link.href = canvas.toDataURL('image/png');
        link.click();
    });
}

/**
 * Export mapy jako GeoJSON
 */
function exportMapAsGeoJSON() {
    console.log('Export mapy jako GeoJSON');
    
    if (!map || !mapLayers || !mapLayers.dataLayers) {
        console.error('Mapa nebo vrstvy nejsou inicializovány');
        return;
    }
    
    // Vytvoření GeoJSON objektu
    const geojson = {
        type: 'FeatureCollection',
        features: []
    };
    
    // Přidání markerů do GeoJSON
    if (mapLayers.dataLayers.markers) {
        mapLayers.dataLayers.markers.eachLayer(layer => {
            if (layer instanceof L.Marker) {
                const feature = layer.toGeoJSON();
                
                // Přidání vlastností z popup
                if (layer.getPopup()) {
                    const popupContent = layer.getPopup().getContent();
                    feature.properties.popupContent = popupContent;
                }
                
                geojson.features.push(feature);
            }
        });
    }
    
    // Přidání oblastí do GeoJSON
    if (mapLayers.dataLayers.areas) {
        mapLayers.dataLayers.areas.eachLayer(layer => {
            if (layer instanceof L.Polygon) {
                const feature = layer.toGeoJSON();
                
                // Přidání vlastností z popup
                if (layer.getPopup()) {
                    const popupContent = layer.getPopup().getContent();
                    feature.properties.popupContent = popupContent;
                }
                
                geojson.features.push(feature);
            }
        });
    }
    
    // Přidání tras do GeoJSON
    if (mapLayers.dataLayers.routes) {
        mapLayers.dataLayers.routes.eachLayer(layer => {
            if (layer instanceof L.Polyline) {
                const feature = layer.toGeoJSON();
                
                // Přidání vlastností z popup
                if (layer.getPopup()) {
                    const popupContent = layer.getPopup().getContent();
                    feature.properties.popupContent = popupContent;
                }
                
                geojson.features.push(feature);
            }
        });
    }
    
    // Konverze GeoJSON na string
    const geojsonString = JSON.stringify(geojson, null, 2);
    
    // Vytvoření Blob objektu
    const blob = new Blob([geojsonString], { type: 'application/json' });
    
    // Vytvoření odkazu pro stažení
    const link = document.createElement('a');
    link.download = 'mapa-export.geojson';
    link.href = URL.createObjectURL(blob);
    link.click();
}

/**
 * Tisk mapy
 */
function printMap() {
    console.log('Tisk mapy');
    
    if (!map) {
        console.error('Mapa není inicializována');
        return;
    }
    
    // Kontrola, zda je načtena knihovna leaflet-easyPrint
    if (typeof L.easyPrint === 'undefined') {
        alert('Pro tisk mapy je potřeba načíst knihovnu leaflet-easyPrint.');
        console.error('Knihovna leaflet-easyPrint není načtena');
        
        // Alternativní řešení - otevření nového okna s mapou
        const mapContainer = document.querySelector('#map-container');
        const printWindow = window.open('', '_blank');
        
        printWindow.document.write(`
            <html>
                <head>
                    <title>Tisk mapy</title>
                    <style>
                        body { margin: 0; padding: 0; }
                        .map-container { width: 100%; height: 100vh; }
                    </style>
                </head>
                <body>
                    <div class="map-container">
                        ${mapContainer.innerHTML}
                    </div>
                    <script>
                        window.onload = function() {
                            window.print();
                        };
                    </script>
                </body>
            </html>
        `);
        
        return;
    }
    
    // Tisk mapy pomocí leaflet-easyPrint
    const printPlugin = L.easyPrint({
        title: 'Tisk mapy',
        position: 'topleft',
        sizeModes: ['A4Landscape'],
        exportOnly: true,
        filename: 'mapa-tisk'
    });
    
    printPlugin.printMap('A4Landscape', 'mapa-tisk');
}

/**
 * Přidání dat z externího zdroje
 */
function addExternalData() {
    console.log('Přidání dat z externího zdroje');
    
    // Najít aktuální modul mapových overlapů
    const mapModule = document.querySelector('.module[id^="module-mapove-overlapy"]');
    if (!mapModule) {
        console.error('Modul mapových overlapů nebyl nalezen');
        return;
    }
    
    // Zobrazení dialogu pro přidání externích dat
    const externalDataDialog = document.createElement('div');
    externalDataDialog.className = 'external-data-dialog';
    externalDataDialog.innerHTML = `
        <div class="external-data-dialog-content">
            <div class="external-data-dialog-header">
                <h3>Přidání dat z externího zdroje</h3>
                <button type="button" class="external-data-dialog-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="external-data-dialog-body">
                <div class="external-data-options">
                    <div class="external-data-option">
                        <h4>Nahrát GeoJSON soubor</h4>
                        <p>Nahrání dat ve formátu GeoJSON.</p>
                        <input type="file" id="geojson-file-input" accept=".geojson,.json" style="display: none;">
                        <button type="button" class="btn-inline upload-geojson">
                            <i class="fas fa-upload"></i> Nahrát GeoJSON
                        </button>
                    </div>
                    <div class="external-data-option">
                        <h4>Přidat WMS vrstvu</h4>
                        <p>Přidání vrstvy z WMS serveru.</p>
                        <div class="form-group">
                            <label>URL WMS serveru:</label>
                            <input type="text" id="wms-url-input" class="form-control" placeholder="https://example.com/wms">
                        </div>
                        <div class="form-group">
                            <label>Název vrstvy:</label>
                            <input type="text" id="wms-layer-input" class="form-control" placeholder="layer_name">
                        </div>
                        <button type="button" class="btn-inline add-wms-layer">
                            <i class="fas fa-plus"></i> Přidat WMS vrstvu
                        </button>
                    </div>
                    <div class="external-data-option">
                        <h4>Přidat data z API</h4>
                        <p>Přidání dat z externího API.</p>
                        <div class="form-group">
                            <label>URL API:</label>
                            <input type="text" id="api-url-input" class="form-control" placeholder="https://example.com/api">
                        </div>
                        <button type="button" class="btn-inline add-api-data">
                            <i class="fas fa-plus"></i> Přidat data z API
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Přidání dialogu do stránky
    document.body.appendChild(externalDataDialog);
    
    // Přidání event listenerů pro tlačítka
    externalDataDialog.querySelector('.external-data-dialog-close').addEventListener('click', function() {
        externalDataDialog.remove();
    });
    
    externalDataDialog.querySelector('.upload-geojson').addEventListener('click', function() {
        const fileInput = externalDataDialog.querySelector('#geojson-file-input');
        fileInput.click();
    });
    
    externalDataDialog.querySelector('#geojson-file-input').addEventListener('change', function(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const geojson = JSON.parse(e.target.result);
                    addGeoJSONToMap(geojson);
                    externalDataDialog.remove();
                } catch (error) {
                    console.error('Chyba při parsování GeoJSON:', error);
                    alert('Nepodařilo se načíst GeoJSON soubor. Zkontrolujte formát souboru.');
                }
            };
            reader.readAsText(file);
        }
    });
    
    externalDataDialog.querySelector('.add-wms-layer').addEventListener('click', function() {
        const wmsUrl = externalDataDialog.querySelector('#wms-url-input').value.trim();
        const wmsLayer = externalDataDialog.querySelector('#wms-layer-input').value.trim();
        
        if (wmsUrl && wmsLayer) {
            addWMSLayerToMap(wmsUrl, wmsLayer);
            externalDataDialog.remove();
        } else {
            alert('Zadejte URL WMS serveru a název vrstvy.');
        }
    });
    
    externalDataDialog.querySelector('.add-api-data').addEventListener('click', function() {
        const apiUrl = externalDataDialog.querySelector('#api-url-input').value.trim();
        
        if (apiUrl) {
            addAPIDataToMap(apiUrl);
            externalDataDialog.remove();
        } else {
            alert('Zadejte URL API.');
        }
    });
    
    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    externalDataDialog.addEventListener('click', function(event) {
        if (event.target === externalDataDialog) {
            externalDataDialog.remove();
        }
    });
}

/**
 * Přidání GeoJSON dat na mapu
 * @param {Object} geojson - GeoJSON objekt
 */
function addGeoJSONToMap(geojson) {
    console.log('Přidání GeoJSON dat na mapu');
    
    if (!map) {
        console.error('Mapa není inicializována');
        return;
    }
    
    // Vytvoření vrstvy z GeoJSON
    const geojsonLayer = L.geoJSON(geojson, {
        style: function(feature) {
            return {
                color: '#3388ff',
                weight: 3,
                opacity: 0.7,
                fillOpacity: 0.3
            };
        },
        pointToLayer: function(feature, latlng) {
            return L.marker(latlng);
        },
        onEachFeature: function(feature, layer) {
            // Přidání popup s vlastnostmi
            if (feature.properties) {
                let popupContent = '<div class="popup-content">';
                
                // Přidání názvu, pokud existuje
                if (feature.properties.name) {
                    popupContent += `<h4>${feature.properties.name}</h4>`;
                } else {
                    popupContent += '<h4>GeoJSON Feature</h4>';
                }
                
                // Přidání vlastností
                popupContent += '<table class="popup-properties">';
                for (const key in feature.properties) {
                    if (key !== 'name' && key !== 'popupContent') {
                        popupContent += `
                            <tr>
                                <td>${key}:</td>
                                <td>${feature.properties[key]}</td>
                            </tr>
                        `;
                    }
                }
                popupContent += '</table></div>';
                
                layer.bindPopup(popupContent);
            }
        }
    });
    
    // Přidání vrstvy na mapu
    geojsonLayer.addTo(map);
    
    // Přidání vrstvy do seznamu vrstev
    mapLayers.dataLayers.geojson = geojsonLayer;
    
    // Přizpůsobení pohledu mapy
    map.fitBounds(geojsonLayer.getBounds());
    
    // Aktualizace UI
    updateLayersUI();
}
