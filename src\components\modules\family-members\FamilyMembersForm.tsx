"use client";

import type { Submit<PERSON>and<PERSON> } from 'react-hook-form';
import type { ChangeEvent } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import type { 
  FamilyMembersModuleData, 
  FamilyMemberRecord, 
  PhysicalPersonSubject, 
  PhotoMetadata, 
  RelationshipType,
  RelationshipStatus,
  RelationshipIntensity,
  InformationReliability,
  InformationSource,
  ThreatLevel,
  ContactInfo,
  PhysicalDescription,
  ProfessionalInfo,
  SecurityAssessment
} from '@/types';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  PlusCircle, Trash2, ImageUp, UserPlus, Users, CalendarDays, Home, 
  LinkIcon, StickyNote, Languages, HeartHandshake, Skull, HelpCircle, 
  Shield, Briefcase, Phone, Mail, Share2, AlertTriangle, Eye, User,
  ChevronUp, ChevronDown, Building, MapPin
} from 'lucide-react';
import { LoadingSpinner } from '@/components/ui/loading';
import { useToast } from '@/hooks/use-toast';
import { db } from '@/lib/firebase';
import { doc, setDoc, Timestamp } from 'firebase/firestore';
import { useState, useEffect, useCallback } from 'react';
import { cn } from '@/lib/utils';
import {
  Form,
  FormField,
  FormControl,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

// Schemas for nested objects
const contactInfoSchema = z.object({
  id: z.string().optional(),
  type: z.enum(["phone", "email", "social", "address"]),
  value: z.string().min(1, "Hodnota kontaktu je povinná"),
  platform: z.string().optional().default(''),
  verified: z.boolean().optional().default(false),
  active: z.boolean().optional().default(true),
  lastContact: z.string().optional().default(''),
  notes: z.string().optional().default(''),
});

const physicalDescriptionSchema = z.object({
  id: z.string().optional(),
  height: z.string().optional().default(''),
  weight: z.string().optional().default(''),
  hairColor: z.string().optional().default(''),
  eyeColor: z.string().optional().default(''),
  build: z.string().optional().default(''),
  distinguishingMarks: z.string().optional().default(''),
  otherFeatures: z.string().optional().default(''),
});

const professionalInfoSchema = z.object({
  id: z.string().optional(),
  currentJob: z.string().optional().default(''),
  employer: z.string().optional().default(''),
  workAddress: z.string().optional().default(''),
  previousJobs: z.string().optional().default(''),
  education: z.string().optional().default(''),
  skills: z.string().optional().default(''),
  income: z.string().optional().default(''),
});

const securityAssessmentSchema = z.object({
  id: z.string().optional(),
  weaponAccess: z.boolean().optional().default(false),
  weaponTraining: z.boolean().optional().default(false),
  criminalHistory: z.string().optional().default(''),
  threatLevel: z.enum(["none", "low", "medium", "high", "critical", "unknown"]).optional().default("unknown"),
  threatAssessment: z.string().optional().default(''),
  surveillanceNotes: z.string().optional().default(''),
});

const photoMetadataSchema = z.object({
  id: z.string().optional(),
  fileName: z.string().optional().default(''),
  downloadURL: z.string().optional().default(''),
  description: z.string().optional().default(''),
  dateTaken: z.string().optional().default(''),
  sourceURL: z.string().optional().default(''),
  storagePath: z.string().optional().default(''),
  location: z.string().optional().default(''),
  photographerInfo: z.string().optional().default(''),
  tags: z.string().optional().default(''),
  verified: z.boolean().optional().default(false),
});

const familyMemberSchema = z.object({
  id: z.string(),
  
  // Základní údaje
  fullName: z.string().min(1, "Jméno a příjmení je povinné"),
  originalScriptName: z.string().optional().default(''),
  aliases: z.string().optional().default(''),
  dateOfBirth: z.string().optional().default(''),
  placeOfBirth: z.string().optional().default(''),
  personalIdNumber: z.string().optional().default(''),
  nationality: z.string().optional().default(''),
  
  // Vztah k hlavnímu subjektu
  relationshipType: z.enum(["mother", "father", "sibling", "spouse", "girlfriend", "boyfriend", "child", "child-in-law", "parent-in-law", "grandparent", "grandchild", "other"]).optional(),
  relationshipOtherDetail: z.string().optional().default(''),
  relationshipStatus: z.enum(["active", "inactive", "strained", "hostile", "unknown"]).optional().default("unknown"),
  relationshipIntensity: z.enum(["very_close", "close", "regular", "occasional", "minimal", "unknown"]).optional().default("unknown"),
  relationshipStartDate: z.string().optional().default(''),
  relationshipEndDate: z.string().optional().default(''),
  lastContact: z.string().optional().default(''),
  contactFrequency: z.string().optional().default(''),
  
  // Bydliště a kontakty
  permanentAddress: z.string().optional().default(''),
  currentAddress: z.string().optional().default(''),
  contactInfo: z.array(contactInfoSchema).optional().default([]),
  
  // Fyzické charakteristiky
  physicalDescription: physicalDescriptionSchema.optional(),
  
  // Profesní informace
  professionalInfo: professionalInfoSchema.optional(),
  
  // Úmrtí
  isDeceased: z.boolean().default(false),
  dateOfDeath: z.string().optional().default(''),
  placeOfDeath: z.string().optional().default(''),
  causeOfDeath: z.string().optional().default(''),
  
  // OSINT informace
  informationSource: z.enum(["social_media", "surveillance", "witness", "documents", "phone_analysis", "investigation", "open_source", "other"]).optional().default("investigation"),
  sourceDetail: z.string().optional().default(''),
  informationReliability: z.enum(["verified", "probable", "possible", "unconfirmed", "unknown"]).optional().default("unknown"),
  verificationDate: z.string().optional().default(''),
  lastSeen: z.string().optional().default(''),
  
  // Bezpečnostní hodnocení
  securityAssessment: securityAssessmentSchema.optional(),
  
  // Poznámky a fotografie
  investigationNotes: z.string().optional().default(''),
  behaviorNotes: z.string().optional().default(''),
  notes: z.string().optional().default(''),
  photos: z.array(photoMetadataSchema).optional().default([]),
}).refine(data => {
  if (data.relationshipType === 'other' && !data.relationshipOtherDetail?.trim()) {
    return false;
  }
  return true;
}, {
  message: "Upřesnění vztahu je povinné, pokud je vybrán typ 'Jiný'.",
  path: ["relationshipOtherDetail"],
}).refine(data => {
  if (data.isDeceased && !data.dateOfDeath?.trim()) {
    return false;
  }
  return true;
}, {
  message: "Datum úmrtí je povinné, pokud je osoba označena jako zemřelá.",
  path: ["dateOfDeath"],
});

const familyMembersModuleSchema = z.object({
  familyMembers: z.array(familyMemberSchema).optional().default([]),
  familyOverview: z.string().optional().default(''),
  familyDynamics: z.string().optional().default(''),
  keyRelationships: z.string().optional().default(''),
  securityConcerns: z.string().optional().default(''),
  investigationPriorities: z.string().optional().default(''),
  generalNotes: z.string().optional().default(''),
});

type FamilyMembersFormValues = z.infer<typeof familyMembersModuleSchema>;

interface FamilyMembersFormProps {
  caseId: string;
  subject: PhysicalPersonSubject;
  existingData: FamilyMembersModuleData | null;
  onSave: (moduleId: string, wasNew: boolean) => void;
}

// Options for select fields
const relationshipOptions: { value: RelationshipType; label: string }[] = [
  { value: "mother", label: "Matka" }, { value: "father", label: "Otec" },
  { value: "sibling", label: "Sourozenec" }, { value: "spouse", label: "Manžel/ka" },
  { value: "girlfriend", label: "Přítelkyně" }, { value: "boyfriend", label: "Přítel" },
  { value: "child", label: "Dítě" }, { value: "child-in-law", label: "Zeť/Snacha" },
  { value: "parent-in-law", label: "Tchán/Tchyně" }, { value: "grandparent", label: "Prarodič" },
  { value: "grandchild", label: "Vnouče" }, { value: "other", label: "Jiný" },
];

const relationshipStatusOptions: { value: RelationshipStatus; label: string }[] = [
  { value: "active", label: "Aktivní" }, { value: "inactive", label: "Neaktivní" },
  { value: "strained", label: "Napjatý" }, { value: "hostile", label: "Nepřátelský" },
  { value: "unknown", label: "Neznámý" },
];

const relationshipIntensityOptions: { value: RelationshipIntensity; label: string }[] = [
  { value: "very_close", label: "Velmi blízký" }, { value: "close", label: "Blízký" },
  { value: "regular", label: "Pravidelný" }, { value: "occasional", label: "Občasný" },
  { value: "minimal", label: "Minimální" }, { value: "unknown", label: "Neznámý" },
];

const informationSourceOptions: { value: InformationSource; label: string }[] = [
  { value: "social_media", label: "Sociální média" }, { value: "surveillance", label: "Sledování" },
  { value: "witness", label: "Svědek" }, { value: "documents", label: "Dokumenty" },
  { value: "phone_analysis", label: "Analýza telefonu" }, { value: "investigation", label: "Vyšetřování" },
  { value: "open_source", label: "Otevřené zdroje" }, { value: "other", label: "Jiný" },
];

const informationReliabilityOptions: { value: InformationReliability; label: string }[] = [
  { value: "verified", label: "Ověřeno" }, { value: "probable", label: "Pravděpodobné" },
  { value: "possible", label: "Možné" }, { value: "unconfirmed", label: "Nepotvrzeno" },
  { value: "unknown", label: "Neznámo" },
];

const threatLevelOptions: { value: ThreatLevel; label: string }[] = [
  { value: "none", label: "Žádná" }, { value: "low", label: "Nízká" },
  { value: "medium", label: "Střední" }, { value: "high", label: "Vysoká" },
  { value: "critical", label: "Kritická" }, { value: "unknown", label: "Neznámá" },
];

const contactTypeOptions = [
  { value: "phone", label: "Telefon" }, { value: "email", label: "E-mail" },
  { value: "social", label: "Sociální síť" }, { value: "address", label: "Adresa" },
];

// Helper to generate initial form values
const getInitialFormValues = (subject: PhysicalPersonSubject, existingData: FamilyMembersModuleData | null): FamilyMembersFormValues => {
  return {
    familyMembers: existingData?.familyMembers?.map(fm => ({
      ...fm,
      id: fm.id || crypto.randomUUID(),
      originalScriptName: fm.originalScriptName || '',
      aliases: fm.aliases || '',
      dateOfBirth: fm.dateOfBirth || '',
      placeOfBirth: fm.placeOfBirth || '',
      personalIdNumber: fm.personalIdNumber || '',
      nationality: fm.nationality || '',
      relationshipOtherDetail: fm.relationshipOtherDetail || '',
      relationshipStatus: fm.relationshipStatus || "unknown",
      relationshipIntensity: fm.relationshipIntensity || "unknown",
      relationshipStartDate: fm.relationshipStartDate || '',
      relationshipEndDate: fm.relationshipEndDate || '',
      lastContact: fm.lastContact || '',
      contactFrequency: fm.contactFrequency || '',
      permanentAddress: fm.permanentAddress || '',
      currentAddress: fm.currentAddress || '',
      contactInfo: fm.contactInfo?.map(c => ({
        ...c,
        id: c.id || crypto.randomUUID(),
        platform: c.platform || '',
        verified: c.verified || false,
        active: c.active !== undefined ? c.active : true,
        lastContact: c.lastContact || '',
        notes: c.notes || '',
      })) || [],
      physicalDescription: fm.physicalDescription ? {
        ...fm.physicalDescription,
        id: fm.physicalDescription.id || crypto.randomUUID(),
        height: fm.physicalDescription.height || '',
        weight: fm.physicalDescription.weight || '',
        hairColor: fm.physicalDescription.hairColor || '',
        eyeColor: fm.physicalDescription.eyeColor || '',
        build: fm.physicalDescription.build || '',
        distinguishingMarks: fm.physicalDescription.distinguishingMarks || '',
        otherFeatures: fm.physicalDescription.otherFeatures || '',
      } : {
        id: crypto.randomUUID(),
        height: '', weight: '', hairColor: '', eyeColor: '', build: '', distinguishingMarks: '', otherFeatures: ''
      },
      professionalInfo: fm.professionalInfo ? {
        ...fm.professionalInfo,
        id: fm.professionalInfo.id || crypto.randomUUID(),
        currentJob: fm.professionalInfo.currentJob || '',
        employer: fm.professionalInfo.employer || '',
        workAddress: fm.professionalInfo.workAddress || '',
        previousJobs: fm.professionalInfo.previousJobs || '',
        education: fm.professionalInfo.education || '',
        skills: fm.professionalInfo.skills || '',
        income: fm.professionalInfo.income || '',
      } : {
        id: crypto.randomUUID(),
        currentJob: '', employer: '', workAddress: '', previousJobs: '', education: '', skills: '', income: ''
      },
      dateOfDeath: fm.dateOfDeath || '',
      placeOfDeath: fm.placeOfDeath || '',
      causeOfDeath: fm.causeOfDeath || '',
      informationSource: fm.informationSource || "investigation",
      sourceDetail: fm.sourceDetail || '',
      informationReliability: fm.informationReliability || "unknown",
      verificationDate: fm.verificationDate || '',
      lastSeen: fm.lastSeen || '',
      securityAssessment: fm.securityAssessment ? {
        ...fm.securityAssessment,
        id: fm.securityAssessment.id || crypto.randomUUID(),
        weaponAccess: fm.securityAssessment.weaponAccess || false,
        weaponTraining: fm.securityAssessment.weaponTraining || false,
        criminalHistory: fm.securityAssessment.criminalHistory || '',
        threatLevel: fm.securityAssessment.threatLevel || "unknown",
        threatAssessment: fm.securityAssessment.threatAssessment || '',
        surveillanceNotes: fm.securityAssessment.surveillanceNotes || '',
      } : {
        id: crypto.randomUUID(),
        weaponAccess: false, weaponTraining: false, criminalHistory: '', threatLevel: "unknown" as ThreatLevel, threatAssessment: '', surveillanceNotes: ''
      },
      investigationNotes: fm.investigationNotes || '',
      behaviorNotes: fm.behaviorNotes || '',
      notes: fm.notes || '',
      photos: fm.photos?.map(p => ({
        ...p,
        id: p.id || crypto.randomUUID(),
        fileName: p.fileName || '',
        downloadURL: p.downloadURL || '',
        description: p.description || '',
        dateTaken: p.dateTaken || '',
        sourceURL: p.sourceURL || '',
        storagePath: p.storagePath || '',
        location: p.location || '',
        photographerInfo: p.photographerInfo || '',
        tags: p.tags || '',
        verified: p.verified || false,
      })) || [],
    })) || [],
    familyOverview: existingData?.familyOverview || '',
    familyDynamics: existingData?.familyDynamics || '',
    keyRelationships: existingData?.keyRelationships || '',
    securityConcerns: existingData?.securityConcerns || '',
    investigationPriorities: existingData?.investigationPriorities || '',
    generalNotes: existingData?.generalNotes || '',
  };
};

export function FamilyMembersForm({ caseId, subject, existingData, onSave }: FamilyMembersFormProps) {
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [uploadingPhotos, setUploadingPhotos] = useState<Record<string, boolean>>({});
  const [photoUrls, setPhotoUrls] = useState<Record<string, string>>({});
  const moduleId = "family_members";

  const form = useForm<FamilyMembersFormValues>({
    resolver: zodResolver(familyMembersModuleSchema),
    defaultValues: getInitialFormValues(subject, existingData),
  });

  // Field arrays
  const { fields: familyMemberFields, append: appendFamilyMember, remove: removeFamilyMember } = useFieldArray({ 
    control: form.control, 
    name: "familyMembers" 
  });

  // Efekt pro synchronizaci photoUrls s form data
  useEffect(() => {
    const currentMembers = form.getValues('familyMembers') || [];
    const newPhotoUrls: Record<string, string> = {};
    
    currentMembers.forEach((member, memberIndex) => {
      member.photos?.forEach((photo, photoIndex) => {
        if (photo.downloadURL) {
          const key = `${memberIndex}-${photoIndex}`;
          newPhotoUrls[key] = photo.downloadURL;
        }
      });
    });
    
    setPhotoUrls(newPhotoUrls);
  }, [form, existingData]);

  const addNewFamilyMember = () => {
    const newMember: FamilyMemberRecord = {
      id: crypto.randomUUID(),
      fullName: '',
      originalScriptName: '',
      aliases: '',
      dateOfBirth: '',
      placeOfBirth: '',
      personalIdNumber: '',
      nationality: '',
      relationshipOtherDetail: '',
      relationshipStatus: "unknown",
      relationshipIntensity: "unknown",
      relationshipStartDate: '',
      relationshipEndDate: '',
      lastContact: '',
      contactFrequency: '',
      permanentAddress: '',
      currentAddress: '',
      contactInfo: [],
      physicalDescription: {
        id: crypto.randomUUID(),
        height: '', weight: '', hairColor: '', eyeColor: '', build: '', distinguishingMarks: '', otherFeatures: ''
      },
      professionalInfo: {
        id: crypto.randomUUID(),
        currentJob: '', employer: '', workAddress: '', previousJobs: '', education: '', skills: '', income: ''
      },
      isDeceased: false,
      dateOfDeath: '',
      placeOfDeath: '',
      causeOfDeath: '',
      informationSource: "investigation",
      sourceDetail: '',
      informationReliability: "unknown",
      verificationDate: '',
      lastSeen: '',
      securityAssessment: {
        id: crypto.randomUUID(),
        weaponAccess: false, 
        weaponTraining: false, 
        criminalHistory: '', 
        threatLevel: "unknown", 
        threatAssessment: '', 
        surveillanceNotes: ''
      },
      investigationNotes: '',
      behaviorNotes: '',
      notes: '',
      photos: [],
    };
    appendFamilyMember(newMember as any);
  };

  const onSubmitHandler: SubmitHandler<FamilyMembersFormValues> = async (data) => {
    setIsSaving(true);
    try {
      const moduleDocRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);
      
      // Zpracování fotografií
      const processedFamilyMembers = data.familyMembers?.map(member => ({
        ...member,
        photos: member.photos?.filter(p => p.downloadURL && p.downloadURL.trim()).map(photo => ({
          id: photo.id || crypto.randomUUID(),
          downloadURL: photo.downloadURL,
          description: photo.description || '',
          dateTaken: photo.dateTaken || '',
          fileName: photo.fileName || '',
          sourceURL: photo.sourceURL || '',
          storagePath: photo.downloadURL,
          location: photo.location || '',
          photographerInfo: photo.photographerInfo || '',
          tags: photo.tags || '',
          verified: photo.verified || false,
        })) || []
      })) || [];
      
      const dataToSave: FamilyMembersModuleData = {
        ...data,
        familyMembers: processedFamilyMembers,
        subjectId: subject.id,
        lastUpdatedAt: Timestamp.now(),
        createdAt: existingData?.createdAt || Timestamp.now(),
      };

      await setDoc(moduleDocRef, dataToSave);
      toast({ title: "Data modulu uložena", description: "Informace z modulu Rodinní příslušníci byly úspěšně uloženy." });
      
      const wasNew = !existingData || !existingData.createdAt;
      onSave(moduleId, wasNew);

    } catch (error: any) {
      console.error("Error saving Family Members data:", error);
      toast({ title: "Chyba ukládání", description: error.message, variant: "destructive" });
    } finally {
      setIsSaving(false);
    }
  };

  // Funkce pro konverzi File na Base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  };

  // Funkce pro zpracování obrázku
  const processImageFile = useCallback(async (file: File, memberIndex: number, photoIndex: number) => {
    if (file.size > 10 * 1024 * 1024) {
      toast({
        title: "Soubor je příliš velký",
        description: "Maximální velikost fotografie je 10MB.",
        variant: "destructive"
      });
      return;
    }

    if (!file.type.startsWith('image/')) {
      toast({
        title: "Neplatný formát souboru",
        description: "Podporované jsou pouze obrázky (JPEG, PNG, GIF, WEBP).",
        variant: "destructive"
      });
      return;
    }

    const uploadKey = `${memberIndex}-${photoIndex}`;
    setUploadingPhotos(prev => ({ ...prev, [uploadKey]: true }));

    try {
      const base64 = await fileToBase64(file);

      const response = await fetch('/api/upload-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          base64Data: base64,
          caseId: caseId,
          subjectId: subject.id,
          moduleId: moduleId,
          fileName: file.name
        })
      });

      if (!response.ok) {
        throw new Error('Nepodařilo se uložit fotografii na server');
      }

      const { imagePath } = await response.json();

      setPhotoUrls(prev => ({
        ...prev,
        [uploadKey]: imagePath
      }));
      
      form.setValue(`familyMembers.${memberIndex}.photos.${photoIndex}.downloadURL`, imagePath);
      form.setValue(`familyMembers.${memberIndex}.photos.${photoIndex}.fileName`, file.name);
      form.setValue(`familyMembers.${memberIndex}.photos.${photoIndex}.description`, `Nahraná fotografie: ${file.name}`);
      form.setValue(`familyMembers.${memberIndex}.photos.${photoIndex}.id`, crypto.randomUUID());

      toast({
        title: "Fotografie uložena",
        description: `Fotografie byla úspěšně uložena do aplikace: ${imagePath}`,
      });
    } catch (error) {
      console.error('Chyba při načítání fotografie:', error);
      toast({
        title: "Chyba při načítání fotografie",
        description: "Nepodařilo se načíst fotografii. Zkuste to prosím znovu.",
        variant: "destructive"
      });
    } finally {
      setUploadingPhotos(prev => ({ ...prev, [uploadKey]: false }));
    }
  }, [caseId, subject.id, moduleId, form, toast]);

  // Funkce pro paste obrázku ze schránky
  const handlePaste = useCallback(async (event: React.ClipboardEvent, memberIndex: number, photoIndex: number) => {
    event.preventDefault();
    const items = event.clipboardData?.items;
    
    if (!items) return;
    
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (item.type.startsWith('image/')) {
        const file = item.getAsFile();
        if (file) {
          await processImageFile(file, memberIndex, photoIndex);
        }
        break;
      }
    }
  }, [processImageFile]);

  // Funkce pro zpracování nahraného obrázku
  const handlePhotoUpload = useCallback(async (event: ChangeEvent<HTMLInputElement>, memberIndex: number, photoIndex: number) => {
    const file = event.target.files?.[0];
    if (file) {
      await processImageFile(file, memberIndex, photoIndex);
    }
  }, [processImageFile]);

  const getPlaceholderImage = (memberIndex: number, photoIndex: number) => `https://placehold.co/150x150.png`;

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmitHandler)} className="space-y-8">
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-xl flex items-center">
              <Users className="mr-3 h-6 w-6 text-primary" />
              Rodinní příslušníci subjektu: {subject.firstName} {subject.lastName}
            </CardTitle>
            <CardDescription>
              Komplexní OSINT analýza rodinných příslušníků a blízkých osob vyšetřovaného subjektu.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {familyMemberFields.map((field, memberIndex) => {
              const watchedRelationshipType = form.watch(`familyMembers.${memberIndex}.relationshipType`);
              const watchedIsDeceased = form.watch(`familyMembers.${memberIndex}.isDeceased`);

              return (
                <Card key={field.id} className="p-6 shadow-inner bg-card-foreground/5 relative">
                  <Button 
                    type="button" 
                    variant="ghost" 
                    size="icon" 
                    onClick={() => removeFamilyMember(memberIndex)}
                    className="absolute top-4 right-4 text-destructive hover:bg-destructive/10"
                    aria-label="Odebrat příslušníka"
                  >
                    <Trash2 className="h-5 w-5" />
                  </Button>
                  
                  <CardHeader className="px-0 pt-0 pb-6">
                    <CardTitle className="text-lg flex items-center">
                      <User className="mr-2 h-5 w-5 text-primary" />
                      Příslušník {memberIndex + 1}
                    </CardTitle>
                  </CardHeader>
                  
                  <CardContent className="px-0 pb-0 space-y-8">
                    {/* Základní údaje */}
                    <Card className="p-4 shadow-sm">
                      <CardHeader className="px-0 pt-0 pb-4">
                        <CardTitle className="text-md flex items-center">
                          <User className="mr-2 h-4 w-4 text-primary/80" />
                          Základní údaje
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="px-0 pb-0 space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormItemRHF label="Jméno a příjmení" name={`familyMembers.${memberIndex}.fullName`} control={form.control} placeholder="Celé jméno příslušníka" />
                          <FormItemRHF label="Originální skript (pokud jiný)" name={`familyMembers.${memberIndex}.originalScriptName`} control={form.control} placeholder="např. Иван Иванов, اسم الشخص" />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormItemRHF label="Aliasy a přezdívky" name={`familyMembers.${memberIndex}.aliases`} control={form.control} placeholder="Jiná jména, přezdívky" />
                          <FormItemRHF label="Datum narození" name={`familyMembers.${memberIndex}.dateOfBirth`} control={form.control} type="date" />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormItemRHF label="Místo narození" name={`familyMembers.${memberIndex}.placeOfBirth`} control={form.control} placeholder="Město, stát" />
                          <FormItemRHF label="Rodné číslo / ID" name={`familyMembers.${memberIndex}.personalIdNumber`} control={form.control} placeholder="RRMMDD/XXXX" />
                        </div>
                        <FormItemRHF label="Státní příslušnost" name={`familyMembers.${memberIndex}.nationality`} control={form.control} placeholder="Např. Česká republika" />
                      </CardContent>
                    </Card>

                    {/* Vztah k subjektu */}
                    <Card className="p-4 shadow-sm">
                      <CardHeader className="px-0 pt-0 pb-4">
                        <CardTitle className="text-md flex items-center">
                          <HeartHandshake className="mr-2 h-4 w-4 text-primary/80" />
                          Vztah k hlavnímu subjektu
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="px-0 pb-0 space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormItemSelectRHF label="Typ vztahu" name={`familyMembers.${memberIndex}.relationshipType`} control={form.control} options={relationshipOptions} placeholder="-- Vyberte vztah --" />
                          <FormItemSelectRHF label="Stav vztahu" name={`familyMembers.${memberIndex}.relationshipStatus`} control={form.control} options={relationshipStatusOptions} placeholder="-- Vyberte stav --" />
                        </div>
                        
                        {watchedRelationshipType === 'other' && (
                          <FormItemRHF label="Upřesnění vztahu" name={`familyMembers.${memberIndex}.relationshipOtherDetail`} control={form.control} placeholder="Popište vztah (např. bývalý kolega, soused)" />
                        )}
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormItemSelectRHF label="Intenzita vztahu" name={`familyMembers.${memberIndex}.relationshipIntensity`} control={form.control} options={relationshipIntensityOptions} placeholder="-- Vyberte intenzitu --" />
                          <FormItemRHF label="Frekvence kontaktu" name={`familyMembers.${memberIndex}.contactFrequency`} control={form.control} placeholder="např. denně, týdně, měsíčně" />
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormItemRHF label="Vztah od (datum)" name={`familyMembers.${memberIndex}.relationshipStartDate`} control={form.control} type="date" />
                          <FormItemRHF label="Vztah do (datum)" name={`familyMembers.${memberIndex}.relationshipEndDate`} control={form.control} type="date" />
                        </div>
                        
                        <FormItemRHF label="Poslední kontakt" name={`familyMembers.${memberIndex}.lastContact`} control={form.control} type="date" />
                      </CardContent>
                    </Card>

                    {/* Bydliště a kontakty */}
                    <Card className="p-4 shadow-sm">
                      <CardHeader className="px-0 pt-0 pb-4">
                        <CardTitle className="text-md flex items-center">
                          <Home className="mr-2 h-4 w-4 text-primary/80" />
                          Bydliště a kontakty
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="px-0 pb-0 space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <FormItemRHF label="Trvalé bydliště" name={`familyMembers.${memberIndex}.permanentAddress`} control={form.control} placeholder="Adresa trvalého pobytu" as="textarea" rows={2} />
                          <FormItemRHF label="Současné bydliště" name={`familyMembers.${memberIndex}.currentAddress`} control={form.control} placeholder="Pokud jiné než trvalé" as="textarea" rows={2} />
                        </div>
                        
                        {/* Kontaktní informace */}
                        <ContactInfoFields control={form.control} memberIndex={memberIndex} />
                      </CardContent>
                    </Card>

                    {/* Fotografie */}
                    <Card className="p-4 shadow-sm">
                      <CardHeader className="px-0 pt-0 pb-4">
                        <CardTitle className="text-md flex items-center">
                          <ImageUp className="mr-2 h-4 w-4 text-primary/80" />
                          Fotografie příslušníka
                        </CardTitle>
                        <CardDescription>
                          Nahrajte a spravujte fotografie rodinného příslušníka. Fotografie se ukládají trvale do aplikace.
                        </CardDescription>
                      </CardHeader>
                      <CardContent className="px-0 pb-0">
                        <PhotoArrayField 
                          control={form.control} 
                          memberIndex={memberIndex}
                          uploadingPhotos={uploadingPhotos}
                          photoUrls={photoUrls}
                          handlePhotoUpload={handlePhotoUpload}
                          handlePaste={handlePaste}
                          getPlaceholderImage={getPlaceholderImage}
                        />
                      </CardContent>
                    </Card>

                    {/* Poznámky */}
                    <Card className="p-4 shadow-sm">
                      <CardHeader className="px-0 pt-0 pb-4">
                        <CardTitle className="text-md flex items-center">
                          <StickyNote className="mr-2 h-4 w-4 text-primary/80" />
                          Poznámky
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="px-0 pb-0 space-y-4">
                        <FormItemRHF label="Obecné poznámky" name={`familyMembers.${memberIndex}.notes`} control={form.control} placeholder="Ostatní zajímavé informace" as="textarea" rows={3} />
                      </CardContent>
                    </Card>
                  </CardContent>
                </Card>
              );
            })}

            <Button type="button" variant="outline" onClick={addNewFamilyMember} className="w-full md:w-auto">
              <UserPlus className="mr-2 h-4 w-4" /> Přidat rodinného příslušníka
            </Button>
          </CardContent>
        </Card>

        {/* Přehledové informace o rodině */}
        <Card className="shadow-md">
          <CardHeader>
            <CardTitle className="text-xl flex items-center">
              <Users className="mr-3 h-6 w-6 text-primary" />
              Přehledové informace o rodině
            </CardTitle>
            <CardDescription>
              Celkové hodnocení rodinné situace a vztahů pro účely vyšetřování.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <FormItemRHF label="Přehled rodiny" name="familyOverview" control={form.control} placeholder="Celkový přehled rodinné situace" as="textarea" rows={3} />
            <FormItemRHF label="Rodinná dynamika" name="familyDynamics" control={form.control} placeholder="Vztahy mezi členy rodiny, konflikty, aliance" as="textarea" rows={3} />
            <FormItemRHF label="Klíčové vztahy" name="keyRelationships" control={form.control} placeholder="Nejdůležitější vztahy pro vyšetřování" as="textarea" rows={3} />
            <FormItemRHF label="Bezpečnostní obavy" name="securityConcerns" control={form.control} placeholder="Potenciální rizika související s rodinou" as="textarea" rows={3} />
            <FormItemRHF label="Priority vyšetřování" name="investigationPriorities" control={form.control} placeholder="Na co se zaměřit v dalším vyšetřování" as="textarea" rows={3} />
            <FormItemRHF label="Obecné poznámky" name="generalNotes" control={form.control} placeholder="Ostatní důležité informace" as="textarea" rows={3} />
          </CardContent>
        </Card>

        <div className="flex justify-end pt-8 mt-8 border-t border-border">
          <Button type="submit" disabled={isSaving} className="w-full md:w-auto text-lg py-3 px-6 shadow-md hover:shadow-lg transition-shadow">
            {isSaving ? (
              <div className="flex items-center">
                <LoadingSpinner size="sm" className="mr-2 text-white" />
                Ukládání dat...
              </div>
            ) : (
              "Uložit data modulu"
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}

// Komponenta pro kontaktní informace
interface ContactInfoFieldsProps {
  control: any;
  memberIndex: number;
}

const ContactInfoFields: React.FC<ContactInfoFieldsProps> = ({ control, memberIndex }) => {
  const { fields: contactFields, append: appendContact, remove: removeContact } = useFieldArray({
    control,
    name: `familyMembers.${memberIndex}.contactInfo`
  });

  const addNewContact = () => {
    appendContact({
      id: crypto.randomUUID(),
      type: "phone",
      value: '',
      platform: '',
      verified: false,
      active: true,
      lastContact: '',
      notes: '',
    });
  };

  return (
    <div className="space-y-4">
      <Label className="text-sm font-medium">Kontaktní informace</Label>
      {contactFields.map((contactField, contactIndex) => (
        <Card key={contactField.id} className="p-3 bg-background shadow-xs">
          <div className="flex justify-between items-center mb-3">
            <h5 className="font-medium text-sm">Kontakt {contactIndex + 1}</h5>
            <Button 
              type="button" 
              variant="ghost" 
              size="icon" 
              onClick={() => removeContact(contactIndex)}
              className="text-destructive hover:bg-destructive/10 h-6 w-6"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
          <div className="space-y-3">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <FormItemSelectRHF 
                label="Typ kontaktu" 
                name={`familyMembers.${memberIndex}.contactInfo.${contactIndex}.type`} 
                control={control} 
                options={contactTypeOptions} 
                placeholder="-- Vyberte typ --"
                smallLabel
              />
              <FormItemRHF 
                label="Hodnota" 
                name={`familyMembers.${memberIndex}.contactInfo.${contactIndex}.value`} 
                control={control} 
                placeholder="Telefonní číslo, e-mail, adresa..." 
                smallLabel
              />
            </div>
            <FormItemRHF 
              label="Platforma (pro sociální sítě)" 
              name={`familyMembers.${memberIndex}.contactInfo.${contactIndex}.platform`} 
              control={control} 
              placeholder="Facebook, Instagram, Twitter..." 
              smallLabel
            />
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
              <FormField
                control={control}
                name={`familyMembers.${memberIndex}.contactInfo.${contactIndex}.verified`}
                render={({ field: checkboxField }) => (
                  <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={checkboxField.value}
                        onCheckedChange={checkboxField.onChange}
                        id={`verified-${memberIndex}-${contactIndex}`}
                      />
                    </FormControl>
                    <Label htmlFor={`verified-${memberIndex}-${contactIndex}`} className="text-sm font-normal cursor-pointer">
                      Ověřeno
                    </Label>
                  </FormItem>
                )}
              />
              <FormField
                control={control}
                name={`familyMembers.${memberIndex}.contactInfo.${contactIndex}.active`}
                render={({ field: checkboxField }) => (
                  <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={checkboxField.value}
                        onCheckedChange={checkboxField.onChange}
                        id={`active-${memberIndex}-${contactIndex}`}
                      />
                    </FormControl>
                    <Label htmlFor={`active-${memberIndex}-${contactIndex}`} className="text-sm font-normal cursor-pointer">
                      Aktivní
                    </Label>
                  </FormItem>
                )}
              />
            </div>
          </div>
        </Card>
      ))}
      <Button type="button" variant="outline" size="sm" onClick={addNewContact}>
        <PlusCircle className="mr-2 h-4 w-4" /> Přidat kontakt
      </Button>
    </div>
  );
};

// Komponenta pro pole fotografií
interface PhotoArrayFieldProps {
  control: any;
  memberIndex: number;
  uploadingPhotos: Record<string, boolean>;
  photoUrls: Record<string, string>;
  handlePhotoUpload: (event: ChangeEvent<HTMLInputElement>, memberIndex: number, photoIndex: number) => void;
  handlePaste: (event: React.ClipboardEvent, memberIndex: number, photoIndex: number) => void;
  getPlaceholderImage: (memberIndex: number, photoIndex: number) => string;
}

const PhotoArrayField: React.FC<PhotoArrayFieldProps> = ({ 
  control, 
  memberIndex, 
  uploadingPhotos, 
  photoUrls, 
  handlePhotoUpload, 
  handlePaste, 
  getPlaceholderImage 
}) => {
  const { fields: photoFields, append: appendPhoto, remove: removePhoto } = useFieldArray({
    control,
    name: `familyMembers.${memberIndex}.photos`
  });

  const addNewPhoto = () => {
    appendPhoto({
      id: crypto.randomUUID(),
      fileName: '',
      downloadURL: '',
      description: '',
      dateTaken: '',
      sourceURL: '',
      storagePath: '',
      location: '',
      photographerInfo: '',
      tags: '',
      verified: false,
    });
  };

  const movePhoto = useCallback((fromIndex: number, toIndex: number) => {
    if (toIndex < 0 || toIndex >= photoFields.length) return;
    
    // Implementace přesunutí fotografií - stejná logika jako v Evidence obyvatel
    // Pro jednoduchost zatím vynecháno
  }, [photoFields.length]);

  return (
    <div className="space-y-4">
      {photoFields.map((photoField, photoIndex) => {
        const uploadKey = `${memberIndex}-${photoIndex}`;
        return (
          <Card key={photoField.id} className="p-4 space-y-4 shadow-sm bg-card-foreground/5">
            <div className="flex justify-between items-center mb-2">
              <p className="font-semibold text-md">Fotografie {photoIndex + 1}</p>
              <div className="flex items-center gap-1">
                <Button 
                  type="button" 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => movePhoto(photoIndex, photoIndex - 1)}
                  disabled={photoIndex === 0}
                  className="h-8 w-8"
                  title="Posunout nahoru"
                >
                  <ChevronUp className="h-4 w-4" />
                </Button>
                <Button 
                  type="button" 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => movePhoto(photoIndex, photoIndex + 1)}
                  disabled={photoIndex === photoFields.length - 1}
                  className="h-8 w-8"
                  title="Posunout dolů"
                >
                  <ChevronDown className="h-4 w-4" />
                </Button>
                <Button 
                  type="button" 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => removePhoto(photoIndex)} 
                  className="text-destructive hover:bg-destructive/10 h-8 w-8" 
                  title="Smazat fotografii"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            <div className="flex flex-col md:flex-row items-start gap-6">
              <div className="relative">
                <img
                  src={photoUrls[uploadKey] || getPlaceholderImage(memberIndex, photoIndex)}
                  alt={`Náhled fotografie ${photoIndex + 1}`}
                  className="w-[250px] h-[250px] object-cover rounded-lg border-2 border-muted shadow-md bg-background mx-auto"
                  onError={(e) => (e.currentTarget.src = getPlaceholderImage(memberIndex, photoIndex))}
                />
                {uploadingPhotos[uploadKey] && (
                  <div className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-lg">
                    <LoadingSpinner size="md" className="text-white" />
                  </div>
                )}
              </div>
              
              <div className="flex-grow space-y-4 w-full">
                <FormItemRHF 
                  label="Popis fotografie" 
                  name={`familyMembers.${memberIndex}.photos.${photoIndex}.description`} 
                  control={control} 
                  placeholder="Popis fotografie..." 
                  as="textarea" 
                  rows={3}
                />
                <FormItemRHF 
                  label="Cesta k souboru (automaticky vyplněno)" 
                  name={`familyMembers.${memberIndex}.photos.${photoIndex}.downloadURL`} 
                  control={control} 
                  disabled={true} 
                  placeholder="Automaticky se vyplní po nahrání fotografie"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <FormItemRHF 
                label="Datum pořízení" 
                name={`familyMembers.${memberIndex}.photos.${photoIndex}.dateTaken`} 
                control={control} 
                type="text" 
                placeholder="Např. Leden 2023" 
              />
              <FormItemRHF 
                label="Název souboru" 
                name={`familyMembers.${memberIndex}.photos.${photoIndex}.fileName`} 
                control={control} 
                placeholder="foto.jpg" 
              />
            </div>
            
            <FormItemRHF 
              label="URL zdroje (kde byla nalezena)" 
              name={`familyMembers.${memberIndex}.photos.${photoIndex}.sourceURL`} 
              control={control} 
              placeholder="https://socialmedia.com/..." 
              type="text"
            />

            <div className="mt-4">
              <Label 
                htmlFor={`photo-upload-${memberIndex}-${photoIndex}`} 
                className={cn(
                  "flex items-center justify-center h-32 w-full border-2 border-dashed rounded-md transition-colors",
                  uploadingPhotos[uploadKey] 
                    ? "cursor-not-allowed border-muted bg-muted/30" 
                    : "cursor-pointer hover:border-primary/50 hover:bg-accent/30"
                )}
                onPaste={(e) => handlePaste(e, memberIndex, photoIndex)}
                tabIndex={0}
              >
                <div className="flex flex-col items-center text-muted-foreground">
                  {uploadingPhotos[uploadKey] ? (
                    <>
                      <LoadingSpinner size="md" className="text-primary" />
                      <p className="text-sm mt-1">Ukládání fotografie na server...</p>
                    </>
                  ) : (
                    <>
                      <ImageUp className="h-8 w-8"/>
                      <p className="text-sm mt-1">Klikněte pro nahrání nebo stiskněte Ctrl+V</p>
                      <p className="text-xs text-muted-foreground/70 mt-1">Max. 10MB, formáty: JPEG, PNG, GIF, WEBP</p>
                    </>
                  )}
                </div>
              </Label>
              <Input
                id={`photo-upload-${memberIndex}-${photoIndex}`}
                type="file"
                accept="image/*"
                className="sr-only"
                onChange={(e) => handlePhotoUpload(e, memberIndex, photoIndex)}
                disabled={uploadingPhotos[uploadKey]}
              />
            </div>
          </Card>
        );
      })}
      
      <Button type="button" variant="outline" size="sm" onClick={addNewPhoto}>
        <PlusCircle className="mr-2 h-4 w-4" /> Přidat záznam o fotografii
      </Button>
    </div>
  );
};

// Helper komponenty pro react-hook-form s ShadCN
interface FormItemRHFProps {
  label: string;
  name: string;
  control: any;
  placeholder?: string;
  type?: string;
  disabled?: boolean;
  className?: string;
  as?: 'input' | 'textarea';
  rows?: number;
  smallLabel?: boolean;
}

const FormItemRHF = ({ label, name, control, placeholder, type = "text", disabled = false, className, as = 'input', rows, smallLabel = false }: FormItemRHFProps) => (
  <FormField
    control={control}
    name={name as any}
    disabled={disabled}
    render={({ field, fieldState: { error } }) => (
      <FormItem className={cn("w-full", className)}>
        <FormLabel className={cn(smallLabel ? "text-xs font-medium" : "font-semibold text-sm")}>{label}</FormLabel>
        <FormControl>
          {as === 'input' ? (
            <Input 
              name={field.name}
              ref={field.ref}
              value={field.value || ''} 
              onChange={field.onChange}
              onBlur={field.onBlur}
              placeholder={placeholder} 
              type={type} 
              className={cn("text-base md:text-sm", error ? "border-destructive focus-visible:ring-destructive" : "")} 
            />
          ) : (
            <Textarea 
              name={field.name}
              ref={field.ref}
              value={field.value || ''} 
              onChange={field.onChange}
              onBlur={field.onBlur}
              placeholder={placeholder} 
              rows={rows} 
              className={cn("text-base md:text-sm", error ? "border-destructive focus-visible:ring-destructive" : "")} 
            />
          )}
        </FormControl>
        {error && <FormMessage className={cn("mt-1", smallLabel ? "text-xs" : "text-xs")} />}
      </FormItem>
    )}
  />
);

interface FormItemSelectRHFProps {
  label: string;
  name: string;
  control: any;
  placeholder?: string;
  options: {value: string; label: string}[];
  disabled?: boolean;
  className?: string;
  smallLabel?: boolean;
}

const FormItemSelectRHF = ({ label, name, control, placeholder, options, disabled = false, className, smallLabel = false }: FormItemSelectRHFProps) => (
  <FormField
    control={control}
    name={name as any}
    disabled={disabled}
    render={({ field, fieldState: { error } }) => (
      <FormItem className={cn("w-full", className)}>
        <FormLabel className={cn(smallLabel ? "text-xs font-medium" : "font-semibold text-sm")}>{label}</FormLabel>
        <Select onValueChange={field.onChange} value={field.value || ''}>
          <FormControl>
            <SelectTrigger className={cn("w-full text-base md:text-sm", error ? "border-destructive focus:ring-destructive" : "")}>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
          </FormControl>
          <SelectContent>
            {options.map(opt => <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>)}
          </SelectContent>
        </Select>
        {error && <FormMessage className={cn("mt-1", smallLabel ? "text-xs" : "text-xs")} />}
      </FormItem>
    )}
  />
); 