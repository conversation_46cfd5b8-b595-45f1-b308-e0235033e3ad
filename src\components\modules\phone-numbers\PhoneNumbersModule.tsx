"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import {
  Phone,
  Plus,
  Edit,
  Trash2,
  AlertCircle,
  CheckCircle,
  XCircle,
  Building,
  User
} from "lucide-react";
import { PhoneNumbersForm } from "./PhoneNumbersForm";
import { PhoneNumbersModuleFormValues } from "./schemas";
import { PhoneNumbersModuleData, Subject } from "@/types";
import {
  collection,
  doc,
  getDoc,
  setDoc,
  serverTimestamp
} from "firebase/firestore";
import { db } from "@/lib/firebase";

interface PhoneNumbersModuleProps {
  caseId: string;
  subject: Subject;
}

export function PhoneNumbersModule({ caseId, subject }: PhoneNumbersModuleProps) {
  const router = useRouter();
  const [isEditing, setIsEditing] = useState(false);
  const [moduleData, setModuleData] = useState<PhoneNumbersModuleData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);

      try {
        // Opravená cesta k modulu - používáme moduleData místo modules pro konzistenci
        const moduleRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", "phone-numbers");

        const moduleSnap = await getDoc(moduleRef);

        if (moduleSnap.exists()) {
          setModuleData(moduleSnap.data() as PhoneNumbersModuleData);
        } else {
          // Modul ještě neexistuje, vytvoříme prázdný
          setModuleData({
            subjectId: subject.id,
            phones: [],
            generalNotes: "",
          });
        }
      } catch (err) {
        console.error("Error fetching phone numbers module data:", err);
        setError("Nepodařilo se načíst data modulu telefonních čísel");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [caseId, subject.id]);

  const handleSave = async (data: PhoneNumbersModuleFormValues) => {
    try {
      // Opravená cesta k modulu - používáme moduleData místo modules pro konzistenci
      const moduleRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", "phone-numbers");

      await setDoc(moduleRef, {
        ...data,
        subjectId: subject.id,
        lastUpdatedAt: serverTimestamp(),
        createdAt: moduleData?.createdAt || serverTimestamp(),
      });

      // Aktualizace lokálních dat
      setModuleData({
        ...data,
        subjectId: subject.id,
      });

      setIsEditing(false);
      router.refresh();
    } catch (err) {
      console.error("Error saving phone numbers module data:", err);
      throw err;
    }
  };

  const handleDelete = async () => {
    if (!confirm("Opravdu chcete odstranit tento modul? Tato akce je nevratná.")) {
      return;
    }

    try {
      // Opravená cesta k modulu - používáme moduleData místo modules pro konzistenci
      const moduleRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", "phone-numbers");

      await setDoc(moduleRef, {
        subjectId: subject.id,
        phones: [],
        generalNotes: "",
        lastUpdatedAt: serverTimestamp(),
        createdAt: moduleData?.createdAt || serverTimestamp(),
      });

      setModuleData({
        subjectId: subject.id,
        phones: [],
        generalNotes: "",
      });

      router.refresh();
    } catch (err) {
      console.error("Error deleting phone numbers module data:", err);
    }
  };

  if (isEditing && moduleData) {
    return (
      <PhoneNumbersForm
        caseId={caseId}
        subject={subject}
        initialData={moduleData}
        onSave={handleSave}
        onBack={() => setIsEditing(false)}
      />
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div className="flex items-center">
          <Phone className="mr-2 h-5 w-5 text-primary" />
          <div>
            <CardTitle>Telefonní čísla</CardTitle>
            <CardDescription>
              Správa telefonních čísel spojených se subjektem
            </CardDescription>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            onClick={() => setIsEditing(true)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            onClick={handleDelete}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
          </div>
        ) : error ? (
          <div className="flex items-center justify-center py-8 text-destructive">
            <AlertCircle className="mr-2 h-5 w-5" />
            <p>{error}</p>
          </div>
        ) : moduleData?.phones && moduleData.phones.length > 0 ? (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {moduleData.phones.slice(0, 6).map((phone) => (
                <Card key={phone.id} className="overflow-hidden">
                  <CardHeader className="p-4 pb-2">
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="text-base">{phone.phoneNumber}</CardTitle>
                        <CardDescription className="text-xs">
                          {phone.discoveryDate && `Objeveno: ${phone.discoveryDate}`}
                        </CardDescription>
                      </div>
                      {phone.verificationStatus && (
                        <Badge variant="outline" className={
                          phone.verificationStatus === "verified" ? "bg-green-50 text-green-700 border-green-200" :
                          phone.verificationStatus === "invalid" ? "bg-red-50 text-red-700 border-red-200" :
                          phone.verificationStatus === "disconnected" ? "bg-gray-50 text-gray-700 border-gray-200" :
                          "bg-yellow-50 text-yellow-700 border-yellow-200"
                        }>
                          {phone.verificationStatus === "verified" ? (
                            <CheckCircle className="mr-1 h-3 w-3" />
                          ) : phone.verificationStatus === "invalid" ? (
                            <XCircle className="mr-1 h-3 w-3" />
                          ) : (
                            <AlertCircle className="mr-1 h-3 w-3" />
                          )}
                          {phone.verificationStatus === "verified" ? "Ověřeno" :
                           phone.verificationStatus === "unverified" ? "Neověřeno" :
                           phone.verificationStatus === "invalid" ? "Neplatné" :
                           phone.verificationStatus === "disconnected" ? "Odpojeno" :
                           "Neznámý stav"}
                        </Badge>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="p-4 pt-0">
                    <div className="space-y-2 text-sm">
                      {phone.owner && (
                        <div className="flex items-center">
                          <User className="h-3.5 w-3.5 mr-2 text-muted-foreground" />
                          <span>{phone.owner}</span>
                        </div>
                      )}
                      {phone.associatedOrganization && (
                        <div className="flex items-center">
                          <Building className="h-3.5 w-3.5 mr-2 text-muted-foreground" />
                          <span>{phone.associatedOrganization}</span>
                        </div>
                      )}
                      {phone.phoneType && (
                        <div className="flex items-center">
                          <Phone className="h-3.5 w-3.5 mr-2 text-muted-foreground" />
                          <span>
                            {phone.phoneType === "mobile" ? "Mobilní" :
                             phone.phoneType === "landline" ? "Pevná linka" :
                             phone.phoneType === "business" ? "Firemní" :
                             phone.phoneType === "fax" ? "Fax" :
                             phone.phoneType === "voip" ? "VoIP" :
                             "Jiný"}
                          </span>
                        </div>
                      )}
                      {phone.carrier && (
                        <div className="flex items-center">
                          <Building className="h-3.5 w-3.5 mr-2 text-muted-foreground" />
                          <span>
                            {phone.carrier === "o2" ? "O2" :
                             phone.carrier === "t-mobile" ? "T-Mobile" :
                             phone.carrier === "vodafone" ? "Vodafone" :
                             phone.carrier === "other" ? "Jiný operátor" :
                             "Neznámý operátor"}
                          </span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {moduleData.phones.length > 6 && (
              <div className="text-center">
                <Button
                  variant="outline"
                  onClick={() => setIsEditing(true)}
                >
                  Zobrazit všech {moduleData.phones.length} telefonních čísel
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <Phone className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground mb-4">
              Zatím nebyla přidána žádná telefonní čísla
            </p>
            <Button onClick={() => setIsEditing(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Přidat telefonní čísla
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
