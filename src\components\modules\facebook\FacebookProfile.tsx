import { FacebookModuleData } from "@/types/facebook";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, Calendar, Users, CheckCircle, Lock, Unlock, ExternalLink } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import { formatDate } from "@/lib/utils";

interface FacebookProfileProps {
  data: FacebookModuleData | null;
}

export default function FacebookProfile({ data }: FacebookProfileProps) {
  if (!data) {
    return <div>Žádná data k zobrazení</div>;
  }

  return (
    <div className="space-y-6">
      {/* Cover Image */}
      {data.coverImageUrl && (
        <div className="relative w-full h-48 rounded-lg overflow-hidden">
          <Image
            src={data.coverImageUrl}
            alt="Cover"
            fill
            className="object-cover"
            unoptimized
          />
        </div>
      )}

      {/* Profile Header */}
      <div className="flex flex-col md:flex-row gap-4 items-center md:items-start">
        {/* Profile Image */}
        <div className="relative w-24 h-24 rounded-full overflow-hidden border-4 border-white shadow-md">
          {data.profileImageUrl ? (
            <Image
              src={data.profileImageUrl}
              alt="Profile"
              fill
              className="object-cover"
              unoptimized
            />
          ) : (
            <div className="w-full h-full bg-blue-100 flex items-center justify-center">
              <span className="text-blue-600 text-2xl font-bold">
                {data.displayName ? data.displayName.charAt(0) : "?"}
              </span>
            </div>
          )}
        </div>

        {/* Profile Info */}
        <div className="flex-1 text-center md:text-left">
          <div className="flex flex-wrap items-center gap-2 justify-center md:justify-start">
            <h2 className="text-2xl font-bold">{data.displayName || "Neznámé jméno"}</h2>
            {data.isVerified && (
              <Badge variant="outline" className="bg-blue-50">
                <CheckCircle className="h-3 w-3 mr-1 text-blue-600" /> Ověřený
              </Badge>
            )}
            {data.isPublic !== undefined && (
              <Badge variant="outline" className={data.isPublic ? "bg-green-50" : "bg-amber-50"}>
                {data.isPublic ? (
                  <><Unlock className="h-3 w-3 mr-1 text-green-600" /> Veřejný</>
                ) : (
                  <><Lock className="h-3 w-3 mr-1 text-amber-600" /> Soukromý</>
                )}
              </Badge>
            )}
          </div>

          {data.username && (
            <p className="text-muted-foreground">@{data.username}</p>
          )}

          <div className="flex flex-wrap gap-4 mt-2 justify-center md:justify-start">
            {data.location && (
              <div className="flex items-center text-sm text-muted-foreground">
                <MapPin className="h-4 w-4 mr-1" /> {data.location}
              </div>
            )}
            {data.friendsCount !== undefined && (
              <div className="flex items-center text-sm text-muted-foreground">
                <Users className="h-4 w-4 mr-1" /> {data.friendsCount} přátel
              </div>
            )}
            {data.followersCount !== undefined && (
              <div className="flex items-center text-sm text-muted-foreground">
                <Users className="h-4 w-4 mr-1" /> {data.followersCount} sledujících
              </div>
            )}
            {data.joinDate && (
              <div className="flex items-center text-sm text-muted-foreground">
                <Calendar className="h-4 w-4 mr-1" /> Připojil(a) se: {typeof data.joinDate === 'string' ? formatDate(data.joinDate) : 'Neuvedeno'}
              </div>
            )}
          </div>
        </div>

        {/* External Link */}
        {data.profileUrl && (
          <Button variant="outline" size="sm" className="mt-2 md:mt-0" asChild>
            <a href={data.profileUrl} target="_blank" rel="noopener noreferrer">
              <ExternalLink className="h-4 w-4 mr-2" /> Zobrazit profil
            </a>
          </Button>
        )}
      </div>

      {/* Bio */}
      {data.bio && (
        <Card>
          <CardContent className="pt-6">
            <h3 className="text-lg font-semibold mb-2">O mně</h3>
            <p className="whitespace-pre-line">{data.bio}</p>
          </CardContent>
        </Card>
      )}

      {/* Education & Work */}
      {(data.education?.length > 0 || data.work?.length > 0) && (
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {data.education?.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-2">Vzdělání</h3>
                  <ul className="space-y-2">
                    {data.education.map((edu, index) => (
                      <li key={index} className="text-sm">{edu}</li>
                    ))}
                  </ul>
                </div>
              )}

              {data.work?.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold mb-2">Práce</h3>
                  <ul className="space-y-2">
                    {data.work.map((job, index) => (
                      <li key={index} className="text-sm">{job}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Notes */}
      {data.notes && (
        <Card>
          <CardContent className="pt-6">
            <h3 className="text-lg font-semibold mb-2 text-amber-600">Interní poznámky</h3>
            <p className="whitespace-pre-line">{data.notes}</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
