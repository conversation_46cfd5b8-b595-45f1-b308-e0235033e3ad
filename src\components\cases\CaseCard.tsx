
"use client";

import type { Case, CasePriority, CaseStatus } from "@/types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ProgressRing } from "@/components/ui/ProgressRing";
import { Users, CalendarDays, UserCircle, Clock, PlusCircle, Eye, Loader2, Trash2 } from "lucide-react";
import { LoadingSpinner, LoadingDots } from "@/components/ui/loading";
import { cn } from "@/lib/utils";
import { usePathname, useRouter } from "next/navigation";

interface CaseCardProps {
  caseData: Case;
  isAddNew?: boolean;
  onClick?: () => void; // For the "Add New" card
  onDeleteRequest?: (caseData: Case) => void; // For deleting a case
  isProcessing?: boolean; // For "Add New" card loading state
  className?: string;
}

const statusStyles: Record<CaseStatus, string> = {
  active: "bg-green-100 text-green-700 border-green-200",
  pending: "bg-yellow-100 text-yellow-700 border-yellow-200",
  completed: "bg-blue-100 text-blue-700 border-blue-200",
  archived: "bg-gray-100 text-gray-700 border-gray-200",
};

const priorityStyles: Record<CasePriority, string> = {
  high: "bg-red-100 text-red-700",
  medium: "bg-yellow-100 text-yellow-700",
  low: "bg-green-100 text-green-700",
};

const progressColorMap: Record<CaseStatus, string> = {
    active: "stroke-green-500",
    pending: "stroke-yellow-500",
    completed: "stroke-blue-500",
    archived: "stroke-gray-500",
};


export function CaseCard({ caseData, isAddNew = false, onClick, onDeleteRequest, isProcessing = false, className }: CaseCardProps) {
  const router = useRouter();


  const handleOpenCase = (caseId: string) => {
    router.push(`/dashboard/cases/${caseId}`);
  };

  if (isAddNew) {
    return (
      <Card
        className={cn(
          "flex min-h-[420px] cursor-pointer flex-col items-center justify-center border-2 border-dashed border-primary bg-background hover:border-primary-foreground hover:bg-accent transition-all duration-300 ease-in-out shadow-lg hover:shadow-xl",
          className
        )}
        onClick={onClick}
      >
        <CardContent className="flex flex-col items-center justify-center text-center p-6">
          {isProcessing ? (
            <div className="flex flex-col items-center space-y-4">
              <LoadingSpinner size="lg" className="text-primary" />
              <div className="text-center">
                <p className="text-xl font-semibold text-foreground mb-2">Vytváření případu</p>
                <p className="text-sm text-muted-foreground mb-3">Příprava nové OSINT analýzy</p>
                <LoadingDots className="text-primary" />
              </div>
            </div>
          ) : (
            <>
              <PlusCircle className="h-16 w-16 text-primary mb-4" />
              <p className="text-xl font-semibold text-foreground mb-2">Vytvořit nový případ</p>
              <p className="text-sm text-muted-foreground mb-4">Začněte novou OSINT analýzu</p>
            </>
          )}
          <Button disabled={isProcessing}>
            {isProcessing ? "Vytváření..." : "Nový případ"}
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Ensure creationDate is a string for display
  const displayCreationDate = caseData.creationDate && typeof caseData.creationDate === 'object' && 'toLocaleDateString' in caseData.creationDate
    ? (caseData.creationDate as Date).toLocaleDateString('cs-CZ')
    : typeof caseData.creationDate === 'string'
      ? caseData.creationDate
      : (caseData.createdAt as any)?.toDate
        ? (caseData.createdAt as any).toDate().toLocaleDateString('cs-CZ')
        : 'Neznámo';

  return (
    <Card
        className={cn("flex flex-col overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 ease-in-out min-h-[420px]", className)}
        // onClick is removed from the whole card to allow specific button actions
    >
      <CardHeader className="bg-card-foreground/5 p-5 cursor-pointer" onClick={() => handleOpenCase(caseData.id)}>
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-lg font-semibold text-foreground">{caseData.title}</CardTitle>
            <p className="text-xs text-muted-foreground font-mono">{caseData.id}</p>
          </div>
          <Badge variant="outline" className={cn("text-xs font-medium py-1 px-3 rounded-full border", statusStyles[caseData.status])}>
            {caseData.status.charAt(0).toUpperCase() + caseData.status.slice(1)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="flex-grow p-5 space-y-3 cursor-pointer" onClick={() => handleOpenCase(caseData.id)}>
        <div>
          <Badge className={cn("text-xs font-medium py-1 px-3 rounded-full", priorityStyles[caseData.priority])}>
            {caseData.priority.charAt(0).toUpperCase() + caseData.priority.slice(1)} priorita
          </Badge>
        </div>
        <div className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-primary" />
            <span>{caseData.subjectsCount || 0} {caseData.subjectsCount === 1 ? 'subjekt' : (caseData.subjectsCount || 0) <=4 ? 'subjekty' : 'subjektů'}</span>
          </div>
          <div className="flex items-center gap-2">
            <CalendarDays className="h-4 w-4 text-primary" />
            <span>{displayCreationDate}</span>
          </div>
          <div className="flex items-center gap-2">
            <UserCircle className="h-4 w-4 text-primary" />
            <span>{caseData.investigatorName || "Nepřiřazeno"}</span>
          </div>
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-primary" />
            <span>{caseData.deadlineInfo}</span>
          </div>
        </div>
        <p className="text-sm text-foreground/80 line-clamp-3">{caseData.description}</p>
        <div className="flex items-center justify-between pt-3 border-t border-border">
          <div className="text-center">
            <span className="block text-lg font-semibold text-foreground">{caseData.progress || 0}%</span>
            <span className="text-xs text-muted-foreground">Dokončeno</span>
          </div>
          <ProgressRing
            progress={caseData.progress || 0}
            radius={26}
            strokeWidth={4}
            progressColorClass={progressColorMap[caseData.status]}
          />
          <div className="text-center">
            <span className="block text-lg font-semibold text-foreground">
              {caseData.modulesCompleted || 0}/{caseData.modulesTotal || 0}
            </span>
            <span className="text-xs text-muted-foreground">Moduly</span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="p-5 bg-card-foreground/5">
        <div className="flex gap-2 w-full">
          {caseData.status === 'completed' || caseData.status === 'archived' ? (
            <>
              <Button variant="outline" size="sm" className="flex-1" onClick={(e) => { e.stopPropagation(); handleOpenCase(caseData.id); }}>
                 <Eye className="mr-2 h-4 w-4" /> Zobrazit ({caseData.progress || 0}%)
              </Button>
              <Button variant="outline" size="sm" className="flex-1" onClick={(e) => { e.stopPropagation(); alert(`Exportovat případ ${caseData.id}`); }}>
                Export
              </Button>
               {caseData.status === 'archived' && (
                 <Button variant="outline" size="sm" className="flex-1" onClick={(e) => { e.stopPropagation(); alert(`Obnovit případ ${caseData.id}`); }}>
                  Obnovit
                </Button>
               )}
            </>
          ) : (
            <>
              <Button size="sm" className="flex-1" onClick={(e) => { e.stopPropagation(); handleOpenCase(caseData.id); }}>
                Otevřít případ
              </Button>
              <Button variant="outline" size="sm" className="flex-1" onClick={(e) => { e.stopPropagation(); alert(`Zobrazit rychlý detail případu ${caseData.id} (funkce bude doplněna)`); }}>
                Rychlý detail ({caseData.progress || 0}%)
              </Button>
            </>
          )}
           <Button variant="destructive" size="sm" onClick={(e) => { e.stopPropagation(); onDeleteRequest?.(caseData); }}>
             <Trash2 className="mr-2 h-4 w-4" /> Smazat
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
