"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Loader2, Search, Building } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { searchCompaniesInAres } from "@/ai/flows/ares-search-flow";
import type { AresSearchOutput } from "@/ai/flows/ares-search-flow";

interface CompanySearchDialogProps {
  onSelectCompany: (ico: string) => void;
  trigger?: React.ReactNode;
}

export function CompanySearchDialog({ onSelectCompany, trigger }: CompanySearchDialogProps) {
  const { toast } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<AresSearchOutput | null>(null);

  const handleSearch = async () => {
    if (searchTerm.trim().length < 3) {
      toast({ 
        title: "Příliš krátký vyhledávací dotaz", 
        description: "Zadejte alespoň 3 znaky pro vyhledání společnosti.", 
        variant: "destructive" 
      });
      return;
    }

    setIsSearching(true);
    try {
      const results = await searchCompaniesInAres({ companyName: searchTerm });
      setSearchResults(results);
      if (results.companies.length === 0) {
        toast({ 
          title: "Žádné výsledky", 
          description: "Pro zadaný název nebyly nalezeny žádné společnosti.", 
          variant: "default" 
        });
      }
    } catch (error: any) {
      toast({ 
        title: "Chyba při vyhledávání", 
        description: error.message || "Nepodařilo se vyhledat společnosti v ARES.", 
        variant: "destructive" 
      });
      setSearchResults(null);
    } finally {
      setIsSearching(false);
    }
  };

  const handleSelectCompany = (ico: string) => {
    onSelectCompany(ico);
    setIsOpen(false);
    setSearchTerm("");
    setSearchResults(null);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <Search className="mr-2 h-4 w-4" />
            Vyhledat společnost
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Vyhledání společnosti v ARES</DialogTitle>
          <DialogDescription>
            Zadejte název společnosti pro vyhledání v registru ARES.
          </DialogDescription>
        </DialogHeader>
        
        <div className="flex items-end gap-2 my-4">
          <div className="flex-1">
            <Input
              placeholder="Zadejte název společnosti..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleSearch()}
            />
          </div>
          <Button onClick={handleSearch} disabled={isSearching}>
            {isSearching ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Search className="mr-2 h-4 w-4" />}
            Vyhledat
          </Button>
        </div>

        {isSearching ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : searchResults && searchResults.companies.length > 0 ? (
          <div className="max-h-[400px] overflow-y-auto border rounded-md">
            <div className="text-xs text-muted-foreground p-2 bg-muted">
              Nalezeno {searchResults.totalCount} výsledků. Zobrazeno prvních {searchResults.companies.length}.
            </div>
            <div className="divide-y">
              {searchResults.companies.map((company) => (
                <div 
                  key={company.ico} 
                  className="p-3 hover:bg-accent cursor-pointer transition-colors"
                  onClick={() => handleSelectCompany(company.ico)}
                >
                  <div className="flex items-start gap-2">
                    <Building className="h-5 w-5 text-primary shrink-0 mt-0.5" />
                    <div>
                      <div className="font-medium">{company.name}</div>
                      <div className="text-sm text-muted-foreground">IČO: {company.ico}</div>
                      {company.legalForm && (
                        <div className="text-sm text-muted-foreground">{company.legalForm}</div>
                      )}
                      <div className="text-sm mt-1">{company.address}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : searchResults ? (
          <div className="py-8 text-center text-muted-foreground">
            Nebyly nalezeny žádné společnosti odpovídající vašemu dotazu.
          </div>
        ) : null}

        <DialogFooter>
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Zavřít
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
