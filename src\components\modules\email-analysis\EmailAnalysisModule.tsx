"use client";

import { useState, useEffect } from "react";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { useToast } from "@/hooks/use-toast";
import { Skeleton } from "@/components/ui/skeleton";
import { EmailAnalysisForm } from "./EmailAnalysisForm";
import { EmailAnalysisModuleData, Subject } from "@/types";
import { Mail } from "lucide-react";

interface EmailAnalysisModuleProps {
  caseId: string;
  subject: Subject;
  onModuleSaved: (moduleId: string, wasNew: boolean) => void;
}

export function EmailAnalysisModule({ caseId, subject, onModuleSaved }: EmailAnalysisModuleProps) {
  const { toast } = useToast();
  const [moduleData, setModuleData] = useState<EmailAnalysisModuleData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const moduleId = "email_analysis";
  
  useEffect(() => {
    const fetchModuleData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const moduleDocRef = doc(db, "cases", caseId, "subjects", subject.id, "moduleData", moduleId);
        const moduleDocSnap = await getDoc(moduleDocRef);
        
        if (moduleDocSnap.exists()) {
          setModuleData(moduleDocSnap.data() as EmailAnalysisModuleData);
        } else {
          setModuleData(null);
        }
      } catch (error: any) {
        console.error("Error fetching email analysis module data:", error);
        setError(error.message);
        toast({
          title: "Chyba načítání dat modulu",
          description: error.message,
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchModuleData();
  }, [caseId, subject.id, toast]);
  
  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Mail className="h-6 w-6 text-primary" />
          <h2 className="text-2xl font-bold">Emailová analýza</h2>
        </div>
        <div className="space-y-2">
          <Skeleton className="h-[40px] w-full" />
          <Skeleton className="h-[400px] w-full" />
        </div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="p-4 border border-destructive rounded-md bg-destructive/10">
        <h3 className="text-lg font-medium text-destructive">Chyba načítání dat modulu</h3>
        <p className="text-sm text-destructive">{error}</p>
      </div>
    );
  }
  
  return (
    <EmailAnalysisForm
      caseId={caseId}
      subject={subject}
      existingData={moduleData}
      onSave={onModuleSaved}
    />
  );
}
