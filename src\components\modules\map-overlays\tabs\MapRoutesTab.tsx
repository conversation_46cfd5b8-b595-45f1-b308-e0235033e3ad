"use client";

import React, { useState } from "react";
import { UseFormReturn, useFieldArray } from "react-hook-form";
import { v4 as uuidv4 } from "uuid";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { MapOverlaysModuleFormValues, mapRouteTypes } from "../schemas";
import { MapRoute } from "@/types";
import { Plus, Trash2, Search, Edit, Map, Calendar, Tag, FileText, Route, Pencil } from "lucide-react";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { PhotoUploadSection } from "../../network-analysis/PhotoUploadSection";

interface MapRoutesTabProps {
  form: UseFormReturn<MapOverlaysModuleFormValues>;
}

export function MapRoutesTab({ form }: MapRoutesTabProps) {
  const [selectedRouteId, setSelectedRouteId] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState("list");

  const { control, register, watch } = form;
  const { fields, append, remove, update } = useFieldArray({
    control,
    name: "routes",
  });

  const routes = watch("routes") || [];
  const selectedRouteIndex = routes.findIndex(r => r.id === selectedRouteId);

  // Filtrování tras podle vyhledávacího výrazu
  const filteredRoutes = routes.filter(route =>
    route.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (route.description && route.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (route.startAddress && route.startAddress.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (route.endAddress && route.endAddress.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  // Přidání nové trasy
  const handleAddRoute = () => {
    const newRoute: MapRoute = {
      id: uuidv4(),
      name: "Nová trasa",
      routeType: "general",
      coordinates: [
        { lat: 50.0755, lng: 14.4378 }, // Praha jako výchozí
        { lat: 50.0855, lng: 14.4478 },
      ],
      photos: [],
      relatedRoutes: [],
      relatedSubjects: [],
    };
    append(newRoute);
    setSelectedRouteId(newRoute.id);
    setActiveTab("detail");
  };

  // Odstranění trasy
  const handleRemoveRoute = (index: number) => {
    if (window.confirm("Opravdu chcete smazat tuto trasu?")) {
      remove(index);
      if (selectedRouteId === routes[index].id) {
        setSelectedRouteId(null);
        setActiveTab("list");
      }
    }
  };

  // Výběr trasy pro editaci
  const handleSelectRoute = (id: string) => {
    setSelectedRouteId(id);
    setActiveTab("detail");
  };

  // Poslouchání události pro otevření detailu trasy
  React.useEffect(() => {
    const handleOpenRouteDetail = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (customEvent.detail && customEvent.detail.routeId) {
        setSelectedRouteId(customEvent.detail.routeId);
        setActiveTab("detail");
      }
    };

    window.addEventListener('openRouteDetail', handleOpenRouteDetail);

    return () => {
      window.removeEventListener('openRouteDetail', handleOpenRouteDetail);
    };
  }, []);

  // Výpočet vzdálenosti trasy v km
  const calculateDistance = (coordinates: Array<{lat: number, lng: number}>) => {
    if (coordinates.length < 2) return 0;

    let distance = 0;
    for (let i = 0; i < coordinates.length - 1; i++) {
      const p1 = coordinates[i];
      const p2 = coordinates[i + 1];

      // Haversine formula pro výpočet vzdálenosti mezi dvěma body na Zemi
      const R = 6371; // Poloměr Země v km
      const dLat = (p2.lat - p1.lat) * Math.PI / 180;
      const dLon = (p2.lng - p1.lng) * Math.PI / 180;
      const a =
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(p1.lat * Math.PI / 180) * Math.cos(p2.lat * Math.PI / 180) *
        Math.sin(dLon/2) * Math.sin(dLon/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
      const d = R * c; // Vzdálenost v km

      distance += d;
    }

    return distance.toFixed(2);
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Trasy na mapě</CardTitle>
              <CardDescription>
                Správa tras a jejich vlastností
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button onClick={handleAddRoute}>
                <Plus className="mr-2 h-4 w-4" />
                Přidat trasu
              </Button>

              <Button
                variant="outline"
                onClick={() => {
                  // Přepneme na záložku s mapou
                  const tabsElement = document.querySelector('[role="tablist"]');
                  if (tabsElement) {
                    const viewTabButton = tabsElement.querySelector('[value="view"]') as HTMLButtonElement;
                    if (viewTabButton) {
                      viewTabButton.click();

                      // Spustíme režim kreslení trasy
                      setTimeout(() => {
                        window.dispatchEvent(new CustomEvent('startDrawingRoute'));
                      }, 100);
                    }
                  }
                }}
              >
                <Pencil className="mr-2 h-4 w-4" />
                Nakreslit trasu na mapě
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="list">Seznam tras</TabsTrigger>
              <TabsTrigger value="detail" disabled={selectedRouteId === null}>
                Detail trasy
              </TabsTrigger>
            </TabsList>

            <TabsContent value="list" className="pt-4">
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Vyhledat trasy..."
                    className="pl-8"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                <ScrollArea className="h-[400px] rounded-md border">
                  {filteredRoutes.length === 0 ? (
                    <div className="p-4 text-center text-muted-foreground">
                      Nebyly nalezeny žádné trasy
                    </div>
                  ) : (
                    <div className="p-4 space-y-2">
                      {filteredRoutes.map((route, index) => (
                        <div
                          key={route.id}
                          className="flex items-center justify-between p-2 rounded-md hover:bg-muted cursor-pointer"
                          onClick={() => handleSelectRoute(route.id)}
                        >
                          <div className="flex items-center space-x-2">
                            <div className={`w-1 h-6 rounded-full ${
                              route.routeType === "vehicle_route" ? "bg-blue-500" :
                              route.routeType === "person_route" ? "bg-green-500" :
                              route.routeType === "escape_route" ? "bg-red-500" :
                              route.routeType === "patrol" ? "bg-yellow-500" :
                              route.routeType === "surveillance" ? "bg-purple-500" :
                              "bg-gray-500"
                            }`}></div>
                            <div>
                              <div className="font-medium">{route.name}</div>
                              <div className="text-xs text-muted-foreground">
                                {route.startAddress && route.endAddress
                                  ? `${route.startAddress} → ${route.endAddress}`
                                  : `${route.coordinates.length} bodů, ${calculateDistance(route.coordinates)} km`}
                              </div>
                            </div>
                          </div>
                          <div className="flex space-x-1">
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleSelectRoute(route.id);
                              }}
                            >
                              <Edit className="h-4 w-4 text-muted-foreground" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRemoveRoute(index);
                              }}
                            >
                              <Trash2 className="h-4 w-4 text-destructive" />
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </div>
            </TabsContent>

            <TabsContent value="detail" className="pt-4">
              {selectedRouteId && selectedRouteIndex !== -1 && (
                <div className="space-y-4">
                  <Tabs defaultValue="basic">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="basic">Základní informace</TabsTrigger>
                      <TabsTrigger value="coordinates">Souřadnice</TabsTrigger>
                      <TabsTrigger value="photos">Fotodokumentace</TabsTrigger>
                    </TabsList>

                    <TabsContent value="basic" className="space-y-4 pt-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`routes.${selectedRouteIndex}.name`}>Název trasy</Label>
                          <Input
                            id={`routes.${selectedRouteIndex}.name`}
                            {...register(`routes.${selectedRouteIndex}.name`)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`routes.${selectedRouteIndex}.routeType`}>Typ trasy</Label>
                          <Select
                            defaultValue={routes[selectedRouteIndex].routeType}
                            onValueChange={(value) => {
                              const updatedRoute = { ...routes[selectedRouteIndex], routeType: value as any };
                              update(selectedRouteIndex, updatedRoute);
                            }}
                          >
                            <SelectTrigger id={`routes.${selectedRouteIndex}.routeType`}>
                              <SelectValue placeholder="Vyberte typ trasy" />
                            </SelectTrigger>
                            <SelectContent>
                              {mapRouteTypes.map((type) => (
                                <SelectItem key={type} value={type}>
                                  {type === "general" ? "Obecná" :
                                   type === "vehicle_route" ? "Trasa vozidla" :
                                   type === "person_route" ? "Trasa osoby" :
                                   type === "escape_route" ? "Úniková trasa" :
                                   type === "patrol" ? "Hlídková trasa" :
                                   type === "surveillance" ? "Trasa sledování" :
                                   type === "other" ? "Jiná" : type}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      {routes[selectedRouteIndex].routeType === "other" && (
                        <div className="space-y-2">
                          <Label htmlFor={`routes.${selectedRouteIndex}.otherRouteTypeDetail`}>
                            Upřesnění typu
                          </Label>
                          <Input
                            id={`routes.${selectedRouteIndex}.otherRouteTypeDetail`}
                            {...register(`routes.${selectedRouteIndex}.otherRouteTypeDetail`)}
                            placeholder="Zadejte vlastní typ trasy"
                          />
                        </div>
                      )}

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`routes.${selectedRouteIndex}.startDate`}>Datum začátku</Label>
                          <Input
                            id={`routes.${selectedRouteIndex}.startDate`}
                            type="date"
                            {...register(`routes.${selectedRouteIndex}.startDate`)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`routes.${selectedRouteIndex}.endDate`}>Datum konce</Label>
                          <Input
                            id={`routes.${selectedRouteIndex}.endDate`}
                            type="date"
                            {...register(`routes.${selectedRouteIndex}.endDate`)}
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor={`routes.${selectedRouteIndex}.startAddress`}>Adresa začátku</Label>
                          <Input
                            id={`routes.${selectedRouteIndex}.startAddress`}
                            {...register(`routes.${selectedRouteIndex}.startAddress`)}
                            placeholder="Zadejte adresu začátku trasy"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor={`routes.${selectedRouteIndex}.endAddress`}>Adresa konce</Label>
                          <Input
                            id={`routes.${selectedRouteIndex}.endAddress`}
                            {...register(`routes.${selectedRouteIndex}.endAddress`)}
                            placeholder="Zadejte adresu konce trasy"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`routes.${selectedRouteIndex}.category`}>Kategorie</Label>
                        <Input
                          id={`routes.${selectedRouteIndex}.category`}
                          {...register(`routes.${selectedRouteIndex}.category`)}
                          placeholder="Např. Důležitá, Podezřelá, ..."
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`routes.${selectedRouteIndex}.description`}>Popis</Label>
                        <Textarea
                          id={`routes.${selectedRouteIndex}.description`}
                          {...register(`routes.${selectedRouteIndex}.description`)}
                          placeholder="Zadejte popis trasy..."
                          rows={4}
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor={`routes.${selectedRouteIndex}.notes`}>Poznámky</Label>
                        <Textarea
                          id={`routes.${selectedRouteIndex}.notes`}
                          {...register(`routes.${selectedRouteIndex}.notes`)}
                          placeholder="Zadejte poznámky k trase..."
                          rows={4}
                        />
                      </div>

                      <div className="p-4 bg-muted rounded-md">
                        <div className="flex items-center justify-between">
                          <div className="text-sm font-medium">Statistiky trasy</div>
                          <Badge variant="outline">
                            {calculateDistance(routes[selectedRouteIndex].coordinates)} km
                          </Badge>
                        </div>
                        <div className="mt-2 text-xs text-muted-foreground">
                          Počet bodů: {routes[selectedRouteIndex].coordinates.length}
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="coordinates" className="space-y-4 pt-4">
                      <div className="w-full h-[200px] bg-muted rounded-md relative overflow-hidden">
                        <div
                          className="w-full h-full"
                          style={{
                            backgroundImage: `url(https://api.mapbox.com/styles/v1/mapbox/streets-v11/static/${routes[selectedRouteIndex].coordinates[0].lng},${routes[selectedRouteIndex].coordinates[0].lat},13,0/800x400?access_token=pk.eyJ1IjoiZGVtb3VzZXIiLCJhIjoiY2txOHkwdWUzMDkzcjJvcWk2ZzFzZ2h6ZSJ9.TxMGHqwYLO1QnfEUL8F7_A)`,
                            backgroundSize: 'cover',
                            backgroundPosition: 'center'
                          }}
                        >
                          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                            <Route className="h-8 w-8 text-blue-500" />
                          </div>
                        </div>
                      </div>

                      <Button
                        variant="outline"
                        className="w-full"
                        onClick={() => {
                          // Přepneme na záložku s mapou
                          const tabsElement = document.querySelector('[role="tablist"]');
                          if (tabsElement) {
                            const viewTabButton = tabsElement.querySelector('[value="view"]') as HTMLButtonElement;
                            if (viewTabButton) {
                              viewTabButton.click();

                              // Vybereme trasu na mapě
                              setTimeout(() => {
                                window.dispatchEvent(new CustomEvent('selectRouteOnMap', {
                                  detail: { routeId: routes[selectedRouteIndex].id }
                                }));
                              }, 100);
                            }
                          }
                        }}
                      >
                        <Map className="mr-2 h-4 w-4" />
                        Zobrazit trasu na mapě
                      </Button>

                      <ScrollArea className="h-[200px] rounded-md border">
                        <div className="p-4 space-y-2">
                          {routes[selectedRouteIndex].coordinates.map((coord, coordIndex) => (
                            <div key={coordIndex} className="flex items-center space-x-2">
                              <div className="font-mono text-xs">
                                Bod {coordIndex + 1}:
                              </div>
                              <div className="flex-1 grid grid-cols-2 gap-2">
                                <Input
                                  value={coord.lat}
                                  onChange={(e) => {
                                    const newCoordinates = [...routes[selectedRouteIndex].coordinates];
                                    newCoordinates[coordIndex] = {
                                      ...newCoordinates[coordIndex],
                                      lat: parseFloat(e.target.value) || 0,
                                    };
                                    const updatedRoute = {
                                      ...routes[selectedRouteIndex],
                                      coordinates: newCoordinates,
                                    };
                                    update(selectedRouteIndex, updatedRoute);
                                  }}
                                  placeholder="Šířka"
                                  type="number"
                                  step="0.000001"
                                />
                                <Input
                                  value={coord.lng}
                                  onChange={(e) => {
                                    const newCoordinates = [...routes[selectedRouteIndex].coordinates];
                                    newCoordinates[coordIndex] = {
                                      ...newCoordinates[coordIndex],
                                      lng: parseFloat(e.target.value) || 0,
                                    };
                                    const updatedRoute = {
                                      ...routes[selectedRouteIndex],
                                      coordinates: newCoordinates,
                                    };
                                    update(selectedRouteIndex, updatedRoute);
                                  }}
                                  placeholder="Délka"
                                  type="number"
                                  step="0.000001"
                                />
                              </div>
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => {
                                  if (routes[selectedRouteIndex].coordinates.length <= 2) {
                                    alert("Trasa musí mít alespoň 2 body");
                                    return;
                                  }
                                  const newCoordinates = [...routes[selectedRouteIndex].coordinates];
                                  newCoordinates.splice(coordIndex, 1);
                                  const updatedRoute = {
                                    ...routes[selectedRouteIndex],
                                    coordinates: newCoordinates,
                                  };
                                  update(selectedRouteIndex, updatedRoute);
                                }}
                              >
                                <Trash2 className="h-4 w-4 text-destructive" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      </ScrollArea>

                      <Button
                        variant="outline"
                        onClick={() => {
                          const lastCoord = routes[selectedRouteIndex].coordinates[routes[selectedRouteIndex].coordinates.length - 1];
                          const newCoordinates = [...routes[selectedRouteIndex].coordinates, {
                            lat: lastCoord.lat + 0.001,
                            lng: lastCoord.lng + 0.001,
                          }];
                          const updatedRoute = {
                            ...routes[selectedRouteIndex],
                            coordinates: newCoordinates,
                          };
                          update(selectedRouteIndex, updatedRoute);
                        }}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Přidat bod
                      </Button>
                    </TabsContent>

                    <TabsContent value="photos" className="pt-4">
                      <PhotoUploadSection
                        control={control}
                        entityIndex={selectedRouteIndex}
                        entityType="routes"
                      />
                    </TabsContent>
                  </Tabs>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="border-t pt-4">
          <div className="flex justify-between w-full">
            <div className="text-sm text-muted-foreground">
              Celkem tras: {routes.length}
            </div>
            {selectedRouteId && (
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedRouteId(null);
                  setActiveTab("list");
                }}
              >
                Zpět na seznam
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
