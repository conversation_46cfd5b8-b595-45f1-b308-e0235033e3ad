"use client";

import { useState } from "react";
import { v4 as uuidv4 } from "uuid";
import { Control, useFieldArray, useFormContext } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Phone,
  Calendar,
  Building,
  Activity,
  PlusCircle,
  X,
  MessageCircle,
  Users
} from "lucide-react";
import {
  PhoneNumbersModuleFormValues,
  phoneSources,
  phoneVerificationStatuses,
  phoneTypes,
  phoneCarriers,
  phoneServiceRegistrations
} from "./schemas";
import { PhotoUploadSection } from "./PhotoUploadSection";
import { PhoneValidator } from "./PhoneValidator";

interface PhoneDetailProps {
  control: Control<PhoneNumbersModuleFormValues>;
  phoneIndex: number;
}

interface FormItemRHFProps {
  label: string;
  name: string;
  control: Control<PhoneNumbersModuleFormValues>;
  placeholder?: string;
  type?: string;
  disabled?: boolean;
  className?: string;
  as?: 'input' | 'textarea';
  rows?: number;
  smallLabel?: boolean;
}

export function PhoneDetail({ control, phoneIndex }: PhoneDetailProps) {
  const [activeTab, setActiveTab] = useState("basic");
  const { watch, setValue } = useFormContext();

  // Kontakty
  const {
    fields: contactFields,
    append: appendContact,
    remove: removeContact
  } = useFieldArray({
    control,
    name: `phones.${phoneIndex}.contacts`,
  });

  // Aktivity
  const {
    fields: activityFields,
    append: appendActivity,
    remove: removeActivity
  } = useFieldArray({
    control,
    name: `phones.${phoneIndex}.activities`,
  });

  // Registrované služby
  const registeredServices = watch(`phones.${phoneIndex}.registeredServices`) || [];

  const handleAddContact = () => {
    appendContact({
      id: uuidv4(),
      name: "",
      phoneNumber: "",
      relationship: "",
      notes: ""
    });
  };

  const handleAddActivity = () => {
    appendActivity({
      id: uuidv4(),
      date: new Date().toISOString().split('T')[0],
      activityType: "",
      description: "",
      location: "",
      duration: "",
      notes: ""
    });
  };

  const handleServiceChange = (service: string, checked: boolean) => {
    const currentServices = [...registeredServices];

    if (checked) {
      if (!currentServices.includes(service as any)) {
        setValue(`phones.${phoneIndex}.registeredServices`, [...currentServices, service], { shouldDirty: true });
      }
    } else {
      setValue(
        `phones.${phoneIndex}.registeredServices`,
        currentServices.filter(s => s !== service),
        { shouldDirty: true }
      );
    }
  };

  const handleValidationComplete = (results: any) => {
    // Aktualizace formuláře s výsledky validace
    setValue(`phones.${phoneIndex}.verificationStatus`,
      results.isValid ? "verified" : "invalid",
      { shouldDirty: true }
    );
    setValue(`phones.${phoneIndex}.verificationDate`, new Date().toISOString().split('T')[0], { shouldDirty: true });
    setValue(`phones.${phoneIndex}.phoneType`, results.phoneType, { shouldDirty: true });
    setValue(`phones.${phoneIndex}.carrier`, results.carrier as any, { shouldDirty: true });
    setValue(`phones.${phoneIndex}.validationResult`, results, { shouldDirty: true });
  };

  const FormItemRHF = ({ label, name, control, placeholder, type = "text", disabled = false, className, as = 'input', rows, smallLabel }: FormItemRHFProps) => (
    <FormField
      control={control}
      name={name as any}
      disabled={disabled}
      render={({ field }) => (
        <FormItem className={className}>
          <FormLabel className={smallLabel ? "text-xs" : "text-sm"}>{label}</FormLabel>
          <FormControl>
            {as === 'input' ? (
              <Input
                {...field}
                placeholder={placeholder}
                type={type}
              />
            ) : (
              <Textarea
                {...field}
                placeholder={placeholder}
                rows={rows || 3}
              />
            )}
          </FormControl>
        </FormItem>
      )}
    />
  );

  return (
    <div className="border rounded-lg p-4">
      <div className="flex items-center mb-4">
        <Phone className="mr-2 h-5 w-5 text-primary" />
        <h3 className="text-lg font-semibold">Detail telefonního čísla</h3>
      </div>
      <div className="text-sm text-muted-foreground mb-4">
        Správa informací o telefonním čísle
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="basic">Základní informace</TabsTrigger>
          <TabsTrigger value="verification">Ověření</TabsTrigger>
          <TabsTrigger value="contacts">Kontakty</TabsTrigger>
          <TabsTrigger value="activities">Aktivity</TabsTrigger>
          <TabsTrigger value="services">Služby</TabsTrigger>
          <TabsTrigger value="photos">Fotodokumentace</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItemRHF
              control={control}
              name={`phones.${phoneIndex}.phoneNumber`}
              label="Telefonní číslo"
              placeholder="Např. +420 123 456 789"
            />

            <FormField
              control={control}
              name={`phones.${phoneIndex}.source`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Zdroj</FormLabel>
                  <Select
                    value={field.value || ""}
                    onValueChange={field.onChange}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Vyberte zdroj" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {phoneSources.map(source => (
                        <SelectItem key={source} value={source}>
                          {source === "osint" ? "OSINT" :
                           source === "investigation" ? "Vyšetřování" :
                           source === "public_data" ? "Veřejné zdroje" :
                           source === "social_media" ? "Sociální sítě" :
                           source === "direct_communication" ? "Přímá komunikace" :
                           source === "official_document" ? "Oficiální dokument" :
                           "Jiný zdroj"}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
          </div>

          {watch(`phones.${phoneIndex}.source`) === "other" && (
            <FormItemRHF
              control={control}
              name={`phones.${phoneIndex}.otherSourceDetail`}
              label="Upřesnění jiného zdroje"
              placeholder="Zadejte podrobnosti o zdroji"
            />
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItemRHF
              control={control}
              name={`phones.${phoneIndex}.discoveryDate`}
              label="Datum objevení"
              type="date"
            />

            <FormField
              control={control}
              name={`phones.${phoneIndex}.phoneType`}
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Typ telefonního čísla</FormLabel>
                  <Select
                    value={field.value || ""}
                    onValueChange={field.onChange}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Vyberte typ" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {phoneTypes.map(type => (
                        <SelectItem key={type} value={type}>
                          {type === "mobile" ? "Mobilní" :
                           type === "landline" ? "Pevná linka" :
                           type === "business" ? "Firemní" :
                           type === "fax" ? "Fax" :
                           type === "voip" ? "VoIP" :
                           "Jiný"}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItemRHF
              control={control}
              name={`phones.${phoneIndex}.owner`}
              label="Vlastník"
              placeholder="Jméno vlastníka telefonního čísla"
            />

            <FormItemRHF
              control={control}
              name={`phones.${phoneIndex}.associatedOrganization`}
              label="Přidružená organizace"
              placeholder="Název organizace"
            />
          </div>

          <FormItemRHF
            control={control}
            name={`phones.${phoneIndex}.notes`}
            label="Poznámky"
            placeholder="Zadejte poznámky k telefonnímu číslu"
            as="textarea"
            rows={4}
          />
        </TabsContent>

        <TabsContent value="verification" className="space-y-4">
          <PhoneValidator
            initialPhone={watch(`phones.${phoneIndex}.phoneNumber`) || ""}
            onValidationComplete={handleValidationComplete}
          />

          <div className="border rounded-lg p-4 space-y-4">
            <h3 className="text-lg font-semibold">Stav ověření</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={control}
                name={`phones.${phoneIndex}.verificationStatus`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Stav ověření</FormLabel>
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Vyberte stav ověření" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {phoneVerificationStatuses.map(status => (
                          <SelectItem key={status} value={status}>
                            {status === "verified" ? "Ověřeno" :
                             status === "unverified" ? "Neověřeno" :
                             status === "invalid" ? "Neplatné" :
                             status === "disconnected" ? "Odpojeno" :
                             "Neznámý stav"}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />

              <FormItemRHF
                control={control}
                name={`phones.${phoneIndex}.verificationDate`}
                label="Datum ověření"
                type="date"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={control}
                name={`phones.${phoneIndex}.carrier`}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Operátor</FormLabel>
                    <Select
                      value={field.value || ""}
                      onValueChange={field.onChange}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Vyberte operátora" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {phoneCarriers.map(carrier => (
                          <SelectItem key={carrier} value={carrier}>
                            {carrier === "o2" ? "O2" :
                             carrier === "t-mobile" ? "T-Mobile" :
                             carrier === "vodafone" ? "Vodafone" :
                             carrier === "other" ? "Jiný operátor" :
                             "Neznámý operátor"}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="contacts" className="space-y-4">
          <div className="border rounded-lg p-4 space-y-4">
            <div className="flex items-center mb-4">
              <Users className="mr-2 h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Kontakty</h3>
            </div>
            <div className="text-sm text-muted-foreground mb-4">
              Správa kontaktů spojených s telefonním číslem
            </div>

            {contactFields.length === 0 ? (
              <div className="text-center py-8 border rounded-md bg-muted/20">
                <Users className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">Zatím nebyly přidány žádné kontakty</p>
              </div>
            ) : (
              contactFields.map((field, index) => (
                <div key={field.id} className="border rounded-lg p-4 space-y-4 shadow-sm bg-card-foreground/5">
                  <div className="flex justify-between items-center mb-2">
                    <p className="font-semibold text-md">Kontakt {index + 1}</p>
                    <Button type="button" variant="ghost" size="icon" onClick={() => removeContact(index)} className="text-destructive hover:bg-destructive/10">
                      <X className="h-5 w-5" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF
                      control={control}
                      name={`phones.${phoneIndex}.contacts.${index}.name`}
                      label="Jméno kontaktu"
                      placeholder="Zadejte jméno kontaktu"
                    />

                    <FormItemRHF
                      control={control}
                      name={`phones.${phoneIndex}.contacts.${index}.phoneNumber`}
                      label="Telefonní číslo"
                      placeholder="Zadejte telefonní číslo"
                    />
                  </div>

                  <FormItemRHF
                    control={control}
                    name={`phones.${phoneIndex}.contacts.${index}.relationship`}
                    label="Vztah"
                    placeholder="Zadejte vztah ke kontaktu"
                  />

                  <FormItemRHF
                    control={control}
                    name={`phones.${phoneIndex}.contacts.${index}.notes`}
                    label="Poznámky"
                    placeholder="Zadejte poznámky ke kontaktu"
                    as="textarea"
                  />
                </div>
              ))
            )}

            <Button
              type="button"
              variant="outline"
              onClick={handleAddContact}
              className="w-full"
            >
              <PlusCircle className="mr-2 h-4 w-4" />
              Přidat kontakt
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="activities" className="space-y-4">
          <div className="border rounded-lg p-4 space-y-4">
            <div className="flex items-center mb-4">
              <Activity className="mr-2 h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Aktivity</h3>
            </div>
            <div className="text-sm text-muted-foreground mb-4">
              Správa aktivit spojených s telefonním číslem
            </div>

            {activityFields.length === 0 ? (
              <div className="text-center py-8 border rounded-md bg-muted/20">
                <Activity className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">Zatím nebyly přidány žádné aktivity</p>
              </div>
            ) : (
              activityFields.map((field, index) => (
                <div key={field.id} className="border rounded-lg p-4 space-y-4 shadow-sm bg-card-foreground/5">
                  <div className="flex justify-between items-center mb-2">
                    <p className="font-semibold text-md">Aktivita {index + 1}</p>
                    <Button type="button" variant="ghost" size="icon" onClick={() => removeActivity(index)} className="text-destructive hover:bg-destructive/10">
                      <X className="h-5 w-5" />
                    </Button>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF
                      control={control}
                      name={`phones.${phoneIndex}.activities.${index}.date`}
                      label="Datum"
                      type="date"
                    />

                    <FormItemRHF
                      control={control}
                      name={`phones.${phoneIndex}.activities.${index}.activityType`}
                      label="Typ aktivity"
                      placeholder="Např. Hovor, SMS, atd."
                    />
                  </div>

                  <FormItemRHF
                    control={control}
                    name={`phones.${phoneIndex}.activities.${index}.description`}
                    label="Popis"
                    placeholder="Zadejte popis aktivity"
                  />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormItemRHF
                      control={control}
                      name={`phones.${phoneIndex}.activities.${index}.location`}
                      label="Místo"
                      placeholder="Zadejte místo aktivity"
                    />

                    <FormItemRHF
                      control={control}
                      name={`phones.${phoneIndex}.activities.${index}.duration`}
                      label="Trvání"
                      placeholder="Např. 5 minut"
                    />
                  </div>

                  <FormItemRHF
                    control={control}
                    name={`phones.${phoneIndex}.activities.${index}.notes`}
                    label="Poznámky"
                    placeholder="Zadejte poznámky k aktivitě"
                    as="textarea"
                  />
                </div>
              ))
            )}

            <Button
              type="button"
              variant="outline"
              onClick={handleAddActivity}
              className="w-full"
            >
              <PlusCircle className="mr-2 h-4 w-4" />
              Přidat aktivitu
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="services" className="space-y-4">
          <div className="border rounded-lg p-4 space-y-4">
            <div className="flex items-center mb-4">
              <MessageCircle className="mr-2 h-5 w-5 text-primary" />
              <h3 className="text-lg font-semibold">Registrované služby</h3>
            </div>
            <div className="text-sm text-muted-foreground mb-4">
              Správa služeb, ve kterých je telefonní číslo registrováno
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {phoneServiceRegistrations.map(service => (
                <div key={service} className="flex items-center space-x-2">
                  <Checkbox
                    id={`service-${service}`}
                    checked={registeredServices.includes(service)}
                    onCheckedChange={(checked) => handleServiceChange(service, !!checked)}
                  />
                  <Label htmlFor={`service-${service}`} className="cursor-pointer">
                    {service === "whatsapp" ? "WhatsApp" :
                     service === "signal" ? "Signal" :
                     service === "telegram" ? "Telegram" :
                     service === "viber" ? "Viber" :
                     service === "facebook" ? "Facebook Messenger" :
                     "Jiná služba"}
                  </Label>
                </div>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="photos">
          <PhotoUploadSection control={control} phoneIndex={phoneIndex} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
