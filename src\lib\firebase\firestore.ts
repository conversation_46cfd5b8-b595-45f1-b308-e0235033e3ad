import { db } from '@/lib/firebase';
import {
  collection,
  doc,
  setDoc,
  getDoc,
  getDocs,
  query,
  where,
  serverTimestamp,
  updateDoc,
  deleteDoc
} from 'firebase/firestore';
import {
  NetworkAnalysisModuleData,
  NetworkAnalysisModuleFormValues,
  MapOverlaysModuleData
} from '@/types';

/**
 * Uloží data modulu síťové analýzy do Firestore
 */
export const saveNetworkAnalysisData = async (
  caseId: string,
  subjectId: string,
  data: NetworkAnalysisModuleFormValues
): Promise<void> => {
  try {
    // Nejprve zkontrolujeme, zda již existuje záznam pro tento subjekt
    const networkAnalysisRef = collection(db, 'cases', caseId, 'networkAnalysis');
    const q = query(networkAnalysisRef, where('subjectId', '==', subjectId));
    const querySnapshot = await getDocs(q);

    const networkAnalysisData: NetworkAnalysisModuleData = {
      subjectId,
      ...data,
      lastUpdatedAt: serverTimestamp(),
    };

    if (querySnapshot.empty) {
      // Vytvoření nového záznamu
      networkAnalysisData.createdAt = serverTimestamp();
      const newDocRef = doc(networkAnalysisRef);
      await setDoc(newDocRef, networkAnalysisData);
    } else {
      // Aktualizace existujícího záznamu
      const docId = querySnapshot.docs[0].id;
      networkAnalysisData.id = docId;
      await updateDoc(doc(networkAnalysisRef, docId), networkAnalysisData);
    }
  } catch (error) {
    console.error('Error saving network analysis data:', error);
    throw error;
  }
};

/**
 * Načte data modulu síťové analýzy z Firestore
 */
export const getNetworkAnalysisData = async (
  caseId: string,
  subjectId: string
): Promise<NetworkAnalysisModuleData | null> => {
  try {
    const networkAnalysisRef = collection(db, 'cases', caseId, 'networkAnalysis');
    const q = query(networkAnalysisRef, where('subjectId', '==', subjectId));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return null;
    }

    const docData = querySnapshot.docs[0].data() as NetworkAnalysisModuleData;
    docData.id = querySnapshot.docs[0].id;

    return docData;
  } catch (error) {
    console.error('Error getting network analysis data:', error);
    throw error;
  }
};

/**
 * Smaže data modulu síťové analýzy z Firestore
 */
export const deleteNetworkAnalysisData = async (
  caseId: string,
  networkAnalysisId: string
): Promise<void> => {
  try {
    const networkAnalysisRef = doc(db, 'cases', caseId, 'networkAnalysis', networkAnalysisId);
    await deleteDoc(networkAnalysisRef);
  } catch (error) {
    console.error('Error deleting network analysis data:', error);
    throw error;
  }
};

/**
 * Uloží data modulu mapových overlapů do Firestore
 */
export const saveMapOverlaysData = async (
  caseId: string,
  subjectId: string,
  data: MapOverlaysModuleData
): Promise<void> => {
  try {
    // Nejprve zkontrolujeme, zda již existuje záznam pro tento subjekt
    const mapOverlaysRef = collection(db, 'cases', caseId, 'mapOverlays');
    const q = query(mapOverlaysRef, where('subjectId', '==', subjectId));
    const querySnapshot = await getDocs(q);

    const mapOverlaysData: MapOverlaysModuleData = {
      subjectId,
      ...data,
      lastUpdatedAt: serverTimestamp(),
    };

    if (querySnapshot.empty) {
      // Vytvoření nového záznamu
      mapOverlaysData.createdAt = serverTimestamp();
      const newDocRef = doc(mapOverlaysRef);
      await setDoc(newDocRef, mapOverlaysData);
    } else {
      // Aktualizace existujícího záznamu
      const docId = querySnapshot.docs[0].id;
      mapOverlaysData.id = docId;
      await updateDoc(doc(mapOverlaysRef, docId), mapOverlaysData);
    }
  } catch (error) {
    console.error('Error saving map overlays data:', error);
    throw error;
  }
};

/**
 * Načte data modulu mapových overlapů z Firestore
 */
export const getMapOverlaysData = async (
  caseId: string,
  subjectId: string
): Promise<MapOverlaysModuleData | null> => {
  try {
    const mapOverlaysRef = collection(db, 'cases', caseId, 'mapOverlays');
    const q = query(mapOverlaysRef, where('subjectId', '==', subjectId));
    const querySnapshot = await getDocs(q);

    if (querySnapshot.empty) {
      return null;
    }

    const docData = querySnapshot.docs[0].data() as MapOverlaysModuleData;
    docData.id = querySnapshot.docs[0].id;

    return docData;
  } catch (error) {
    console.error('Error getting map overlays data:', error);
    throw error;
  }
};

/**
 * Smaže data modulu mapových overlapů z Firestore
 */
export const deleteMapOverlaysData = async (
  caseId: string,
  mapOverlaysId: string
): Promise<void> => {
  try {
    const mapOverlaysRef = doc(db, 'cases', caseId, 'mapOverlays', mapOverlaysId);
    await deleteDoc(mapOverlaysRef);
  } catch (error) {
    console.error('Error deleting map overlays data:', error);
    throw error;
  }
};
