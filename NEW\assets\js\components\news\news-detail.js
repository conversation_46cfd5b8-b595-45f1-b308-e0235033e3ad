/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ud<PERSON><PERSON> - funkce pro detail zpr<PERSON><PERSON>, monitoring a notifikace
 */

/**
 * Zobrazení detailu zprávy
 * @param {string} newsId - ID zprávy
 */
function showNewsDetail(newsId) {
    console.log('Zobrazení detailu zprávy:', newsId);

    // Kontrola, zda zpráva existuje v cache
    if (!newsCache[newsId]) {
        console.error('Zpráva nebyla nalezena v cache:', newsId);
        alert('Zpráva nebyla nalezena.');
        return;
    }

    // Získání zprávy z cache
    const news = newsCache[newsId];

    // Vytvoření dialogu pro detail zprávy
    const dialog = document.createElement('div');
    dialog.className = 'news-detail-dialog';

    // Formátování data publikace
    const publishedDate = news.publishedAt ? formatPublishedDate(news.publishedAt) : 'Neznámé datum';

    // Určení třídy pro sentiment
    let sentimentClass = 'sentiment-neutral';
    let sentimentText = 'Neutrální';
    let sentimentIcon = 'fa-meh';

    if (news.sentiment === 'positive') {
        sentimentClass = 'sentiment-positive';
        sentimentText = 'Pozitivní';
        sentimentIcon = 'fa-smile';
    } else if (news.sentiment === 'negative') {
        sentimentClass = 'sentiment-negative';
        sentimentText = 'Negativní';
        sentimentIcon = 'fa-frown';
    }

    // Vytvoření HTML pro dialog
    dialog.innerHTML = `
        <div class="news-detail-content">
            <div class="news-detail-header">
                <h3>${news.title}</h3>
                <button type="button" class="news-detail-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="news-detail-body">
                <div class="news-detail-meta">
                    <div class="news-detail-source">
                        <strong>Zdroj:</strong> ${news.source}
                    </div>
                    <div class="news-detail-date">
                        <strong>Publikováno:</strong> ${publishedDate}
                    </div>
                    <div class="news-detail-sentiment ${sentimentClass}">
                        <strong>Sentiment:</strong> ${sentimentText} <i class="far ${sentimentIcon}"></i>
                    </div>
                    <div class="news-detail-language">
                        <strong>Jazyk:</strong> ${news.language ? news.language.toUpperCase() : 'Neznámý'}
                        ${news.language && news.language !== 'cs' ? `
                        <button type="button" class="btn-inline btn-translate" data-news-id="${newsId}">
                            <i class="fas fa-language"></i> Přeložit
                        </button>
                        ` : ''}
                    </div>
                </div>

                ${news.imageUrl ? `
                <div class="news-detail-image">
                    <img src="${news.imageUrl}" alt="${news.title}" onerror="this.style.display='none'">
                </div>
                ` : ''}

                <div class="news-detail-description">
                    ${news.description}
                </div>

                <div class="news-detail-entities">
                    <h4>Entity</h4>
                    <div class="news-detail-entities-content">
    `;

    // Přidání entit (pokud existují)
    if (news.entities) {
        // Přidání osob
        if (news.entities.persons && news.entities.persons.length > 0) {
            dialog.innerHTML += `
                <div class="news-detail-entities-section">
                    <h5>Osoby</h5>
                    <div class="news-detail-entities-list">
            `;

            news.entities.persons.forEach(person => {
                dialog.innerHTML += `
                    <span class="entity entity-person">
                        <i class="fas fa-user"></i> ${person}
                        <button type="button" class="entity-add" data-entity="${person}" data-type="person">
                            <i class="fas fa-plus"></i>
                        </button>
                    </span>
                `;
            });

            dialog.innerHTML += `
                    </div>
                </div>
            `;
        }

        // Přidání organizací
        if (news.entities.organizations && news.entities.organizations.length > 0) {
            dialog.innerHTML += `
                <div class="news-detail-entities-section">
                    <h5>Organizace</h5>
                    <div class="news-detail-entities-list">
            `;

            news.entities.organizations.forEach(org => {
                dialog.innerHTML += `
                    <span class="entity entity-organization">
                        <i class="fas fa-building"></i> ${org}
                        <button type="button" class="entity-add" data-entity="${org}" data-type="organization">
                            <i class="fas fa-plus"></i>
                        </button>
                    </span>
                `;
            });

            dialog.innerHTML += `
                    </div>
                </div>
            `;
        }

        // Přidání lokací
        if (news.entities.locations && news.entities.locations.length > 0) {
            dialog.innerHTML += `
                <div class="news-detail-entities-section">
                    <h5>Lokace</h5>
                    <div class="news-detail-entities-list">
            `;

            news.entities.locations.forEach(location => {
                dialog.innerHTML += `
                    <span class="entity entity-location">
                        <i class="fas fa-map-marker-alt"></i> ${location}
                        <button type="button" class="entity-add" data-entity="${location}" data-type="location">
                            <i class="fas fa-plus"></i>
                        </button>
                    </span>
                `;
            });

            dialog.innerHTML += `
                    </div>
                </div>
            `;
        }
    } else {
        dialog.innerHTML += `<p>Nebyly nalezeny žádné entity.</p>`;
    }

    dialog.innerHTML += `
                    </div>
                </div>

                <div class="news-detail-actions">
                    <a href="${news.url}" target="_blank" class="btn-inline news-detail-source-link">
                        <i class="fas fa-external-link-alt"></i> Otevřít zdroj
                    </a>
                    <button type="button" class="btn-inline news-detail-add-monitoring" data-news-id="${newsId}">
                        <i class="fas fa-eye"></i> Přidat do monitoringu
                    </button>
                    <button type="button" class="btn-inline news-detail-add-photo" data-news-id="${newsId}">
                        <i class="fas fa-camera"></i> Přidat do fotogalerie
                    </button>
                </div>
            </div>
        </div>
    `;

    // Přidání dialogu do stránky
    document.body.appendChild(dialog);

    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.news-detail-close').addEventListener('click', function() {
        dialog.remove();
    });

    // Event listener pro přidání entity do monitoringu
    const entityAddButtons = dialog.querySelectorAll('.entity-add');
    entityAddButtons.forEach(button => {
        button.addEventListener('click', function() {
            const entity = this.getAttribute('data-entity');
            const type = this.getAttribute('data-type');

            if (entity && type) {
                addEntityToMonitoring(entity, type);
            }
        });
    });

    // Event listener pro přidání zprávy do monitoringu
    const addMonitoringButton = dialog.querySelector('.news-detail-add-monitoring');
    if (addMonitoringButton) {
        addMonitoringButton.addEventListener('click', function() {
            const newsId = this.getAttribute('data-news-id');
            if (newsId && newsCache[newsId]) {
                addNewsToMonitoring(newsCache[newsId]);
            }
        });
    }

    // Event listener pro přidání zprávy do fotogalerie
    const addPhotoButton = dialog.querySelector('.news-detail-add-photo');
    if (addPhotoButton) {
        addPhotoButton.addEventListener('click', function() {
            const newsId = this.getAttribute('data-news-id');
            if (newsId && newsCache[newsId]) {
                addNewsToPhotoGallery(newsCache[newsId]);

                // Zobrazení potvrzení
                showNotification('Zpráva byla přidána do fotogalerie', 'success');
            }
        });
    }

    // Event listener pro tlačítko překladu
    const translateButton = dialog.querySelector('.btn-translate');
    if (translateButton) {
        translateButton.addEventListener('click', function() {
            translateNews(newsId);
        });
    }

    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}

/**
 * Přidání entity do monitoringu
 * @param {string} entity - Název entity
 * @param {string} type - Typ entity (person, organization, location)
 */
function addEntityToMonitoring(entity, type) {
    console.log('Přidání entity do monitoringu:', entity, type);

    // Kontrola, zda entita již není v monitoringu
    if (activeNewsFilters.entities.includes(entity)) {
        alert(`Entita "${entity}" je již v monitoringu.`);
        return;
    }

    // Přidání entity do monitoringu
    activeNewsFilters.entities.push(entity);

    // Přidání entity do seznamu klíčových slov pro vyhledávání
    if (!activeNewsFilters.keywords.includes(entity)) {
        activeNewsFilters.keywords.push(entity);
    }

    // Aktualizace UI
    updateMonitoringUI();

    // Informace pro uživatele
    showNotification(`Entita "${entity}" byla přidána do monitoringu`, 'success');
}

/**
 * Přidání zprávy do monitoringu
 * @param {Object} news - Zpráva
 */
function addNewsToMonitoring(news) {
    console.log('Přidání zprávy do monitoringu:', news);

    // Přidání klíčových slov ze zprávy do monitoringu
    const keywords = extractKeywordsFromNews(news);

    // Přidání klíčových slov do monitoringu
    keywords.forEach(keyword => {
        if (!activeNewsFilters.keywords.includes(keyword)) {
            activeNewsFilters.keywords.push(keyword);
        }
    });

    // Přidání entit ze zprávy do monitoringu
    if (news.entities) {
        // Přidání osob
        if (news.entities.persons && news.entities.persons.length > 0) {
            news.entities.persons.forEach(person => {
                if (!activeNewsFilters.entities.includes(person)) {
                    activeNewsFilters.entities.push(person);
                }
            });
        }

        // Přidání organizací
        if (news.entities.organizations && news.entities.organizations.length > 0) {
            news.entities.organizations.forEach(org => {
                if (!activeNewsFilters.entities.includes(org)) {
                    activeNewsFilters.entities.push(org);
                }
            });
        }

        // Přidání lokací
        if (news.entities.locations && news.entities.locations.length > 0) {
            news.entities.locations.forEach(location => {
                if (!activeNewsFilters.entities.includes(location)) {
                    activeNewsFilters.entities.push(location);
                }
            });
        }
    }

    // Aktualizace UI
    updateMonitoringUI();

    // Informace pro uživatele
    showNotification('Zpráva byla přidána do monitoringu', 'success');
}

/**
 * Extrakce klíčových slov ze zprávy
 * @param {Object} news - Zpráva
 * @returns {Array} - Pole klíčových slov
 */
function extractKeywordsFromNews(news) {
    // Vytvoření textu pro extrakci klíčových slov
    const text = `${news.title} ${news.description}`;

    // Rozdělení textu na slova
    const words = text.toLowerCase()
        .replace(/[^\p{L}\p{N}\s]/gu, '') // Odstranění speciálních znaků
        .split(/\s+/) // Rozdělení podle mezer
        .filter(word => word.length > 3); // Odstranění krátkých slov

    // Odstranění stop slov
    const stopWords = [
        'a', 'aby', 'ale', 'ani', 'ano', 'asi', 'az', 'bez', 'bude', 'budem',
        'budes', 'by', 'byl', 'byla', 'byli', 'bylo', 'byt', 'ci', 'clanek',
        'clanku', 'clanky', 'co', 'coz', 'cz', 'dalsi', 'design', 'dnes', 'do',
        'email', 'ho', 'jak', 'jako', 'je', 'jeho', 'jej', 'jeji', 'jejich',
        'jen', 'jeste', 'ji', 'jine', 'jiz', 'jsem', 'jses', 'jsme', 'jsou',
        'jste', 'k', 'kam', 'kde', 'kdo', 'kdyz', 'ke', 'ktera', 'ktere',
        'kteri', 'kterou', 'ktery', 'ma', 'mate', 'me', 'mezi', 'mi', 'mit',
        'mnou', 'moje', 'moji', 'mozu', 'muj', 'musim', 'muze', 'my', 'na',
        'nad', 'nam', 'napiste', 'nas', 'nasi', 'ne', 'nebo', 'nebudu',
        'nebyl', 'nebyla', 'nebyli', 'nebyly', 'nechci', 'nejsou', 'nel',
        'nem', 'neni', 'nestaci', 'nevim', 'nic', 'nove', 'novy', 'nuz',
        'o', 'od', 'ode', 'on', 'ona', 'oni', 'ono', 'ony', 'pak', 'po',
        'pod', 'podle', 'pokud', 'pouze', 'prave', 'pred', 'pres', 'pri',
        'pro', 'proc', 'proto', 'protoze', 'prvni', 'pta', 're', 's', 'se',
        'si', 'sice', 'strana', 'sve', 'svych', 'svym', 'svymi', 'ta', 'tak',
        'take', 'takze', 'tato', 'te', 'tedy', 'tema', 'ten', 'tento', 'teto',
        'tim', 'timto', 'tipy', 'to', 'tohle', 'toho', 'tohoto', 'tom',
        'tomto', 'tomuto', 'tu', 'tuto', 'ty', 'tyto', 'u', 'uz', 'v', 've',
        'vice', 'vsak', 'vam', 'vas', 'vase', 've', 'vice', 'vsak', 'vsechno',
        'všechen', 'z', 'za', 'zda', 'zde', 'ze', 'zpet', 'zpravy'
    ];

    const filteredWords = words.filter(word => !stopWords.includes(word));

    // Počítání četnosti slov
    const wordCounts = {};

    filteredWords.forEach(word => {
        wordCounts[word] = (wordCounts[word] || 0) + 1;
    });

    // Seřazení slov podle četnosti
    const sortedWords = Object.entries(wordCounts)
        .sort((a, b) => b[1] - a[1])
        .slice(0, 5) // Omezení na 5 nejčastějších slov
        .map(([word]) => word);

    return sortedWords;
}

/**
 * Aktualizace UI pro monitoring
 */
function updateMonitoringUI() {
    // Najít aktuální modul aktuálních událostí
    const newsModule = document.querySelector('.module[id^="module-aktualni-udalosti"]');
    if (!newsModule) {
        console.error('Modul aktuálních událostí nebyl nalezen');
        return;
    }

    // Najít kontejner pro monitoring
    const monitoringContainer = newsModule.querySelector('#monitoring-container');
    if (!monitoringContainer) {
        console.error('Kontejner pro monitoring nebyl nalezen');
        return;
    }

    // Vytvoření HTML pro monitoring
    let monitoringHtml = `
        <div class="monitoring-header">
            <h4>Monitoring</h4>
            <div class="monitoring-actions">
                <button type="button" class="btn-inline setup-monitoring">
                    <i class="fas fa-cog"></i> Nastavení
                </button>
            </div>
        </div>
        <div class="monitoring-content">
    `;

    // Přidání klíčových slov
    if (activeNewsFilters.keywords && activeNewsFilters.keywords.length > 0) {
        monitoringHtml += `
            <div class="monitoring-section">
                <h5>Klíčová slova</h5>
                <div class="monitoring-items">
        `;

        activeNewsFilters.keywords.forEach(keyword => {
            monitoringHtml += `
                <div class="monitoring-item">
                    <span class="monitoring-item-name">${keyword}</span>
                    <button type="button" class="monitoring-item-remove" data-keyword="${keyword}">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        });

        monitoringHtml += `
                </div>
            </div>
        `;
    }

    // Přidání entit
    if (activeNewsFilters.entities && activeNewsFilters.entities.length > 0) {
        monitoringHtml += `
            <div class="monitoring-section">
                <h5>Entity</h5>
                <div class="monitoring-items">
        `;

        activeNewsFilters.entities.forEach(entity => {
            monitoringHtml += `
                <div class="monitoring-item">
                    <span class="monitoring-item-name">${entity}</span>
                    <button type="button" class="monitoring-item-remove" data-entity="${entity}">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
        });

        monitoringHtml += `
                </div>
            </div>
        `;
    }

    monitoringHtml += `</div>`;

    // Aktualizace kontejneru
    monitoringContainer.innerHTML = monitoringHtml;

    // Zobrazení kontejneru
    monitoringContainer.style.display = 'block';

    // Přidání event listenerů pro tlačítka odstranění
    const removeKeywordButtons = monitoringContainer.querySelectorAll('.monitoring-item-remove[data-keyword]');
    removeKeywordButtons.forEach(button => {
        button.addEventListener('click', function() {
            const keyword = this.getAttribute('data-keyword');
            if (keyword) {
                removeKeywordFromMonitoring(keyword);
            }
        });
    });

    const removeEntityButtons = monitoringContainer.querySelectorAll('.monitoring-item-remove[data-entity]');
    removeEntityButtons.forEach(button => {
        button.addEventListener('click', function() {
            const entity = this.getAttribute('data-entity');
            if (entity) {
                removeEntityFromMonitoring(entity);
            }
        });
    });
}

/**
 * Odstranění klíčového slova z monitoringu
 * @param {string} keyword - Klíčové slovo
 */
function removeKeywordFromMonitoring(keyword) {
    console.log('Odstranění klíčového slova z monitoringu:', keyword);

    // Odstranění klíčového slova z monitoringu
    activeNewsFilters.keywords = activeNewsFilters.keywords.filter(k => k !== keyword);

    // Aktualizace UI
    updateMonitoringUI();

    // Informace pro uživatele
    showNotification(`Klíčové slovo "${keyword}" bylo odstraněno z monitoringu`, 'success');
}

/**
 * Odstranění entity z monitoringu
 * @param {string} entity - Entita
 */
function removeEntityFromMonitoring(entity) {
    console.log('Odstranění entity z monitoringu:', entity);

    // Odstranění entity z monitoringu
    activeNewsFilters.entities = activeNewsFilters.entities.filter(e => e !== entity);

    // Aktualizace UI
    updateMonitoringUI();

    // Informace pro uživatele
    showNotification(`Entita "${entity}" byla odstraněna z monitoringu`, 'success');
}

/**
 * Odeslání testovacího emailového alertu
 * @param {string} email - Emailová adresa pro odeslání testu
 */
function sendTestEmailAlert(email) {
    console.log('Odeslání testovacího emailového alertu na:', email);

    // Validace emailové adresy
    if (!email || !validateEmailFormat(email)) {
        showNotification('Zadejte platnou emailovou adresu.', 'error');
        return;
    }

    // Zobrazení notifikace o zahájení odesílání
    showNotification(`Odesílám testovací email na adresu ${email}...`, 'info');

    // Najít tlačítko TEST a změnit jeho text na "Odesílám..."
    const testButton = document.querySelector('.test-email-alert');
    if (testButton) {
        const originalContent = testButton.innerHTML;
        testButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Odesílám...';
        testButton.disabled = true;

        // Simulace odeslání emailu (v reálné aplikaci by zde byl API požadavek na backend)
        setTimeout(() => {
            // Obnovení původního textu tlačítka
            testButton.innerHTML = originalContent;
            testButton.disabled = false;

            // Zobrazení potvrzení
            showNotification(`Testovací email byl úspěšně odeslán na adresu ${email}`, 'success');

            // Zobrazení detailů v konzoli
            console.log('Testovací email byl odeslán s následujícími parametry:');
            console.log('- Adresa:', email);
            console.log('- Předmět: [OSINT] Testovací email pro ověření alertů');
            console.log('- Obsah: Toto je testovací email pro ověření funkčnosti alertů v OSINT nástroji.');

            // Zobrazení detailního dialogu s informacemi o odeslaném emailu
            showEmailSentDialog(email);
        }, 2000); // Simulace zpoždění API požadavku
    } else {
        // Pokud tlačítko nebylo nalezeno, použijeme pouze notifikaci
        setTimeout(() => {
            showNotification(`Testovací email byl úspěšně odeslán na adresu ${email}`, 'success');
        }, 2000);
    }
}

/**
 * Zobrazení dialogu s informacemi o odeslaném emailu
 * @param {string} email - Emailová adresa, na kterou byl email odeslán
 */
function showEmailSentDialog(email) {
    // Vytvoření dialogu
    const dialog = document.createElement('div');
    dialog.className = 'email-sent-dialog';
    dialog.innerHTML = `
        <div class="email-sent-content">
            <div class="email-sent-header">
                <h3>Email byl odeslán</h3>
                <button type="button" class="email-sent-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="email-sent-body">
                <div class="email-sent-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="email-sent-message">
                    <p>Testovací email byl úspěšně odeslán na adresu:</p>
                    <p class="email-address">${email}</p>
                </div>
                <div class="email-sent-details">
                    <h4>Detaily emailu:</h4>
                    <ul>
                        <li><strong>Předmět:</strong> [OSINT] Testovací email pro ověření alertů</li>
                        <li><strong>Odesláno:</strong> ${new Date().toLocaleString()}</li>
                        <li><strong>Obsah:</strong> Toto je testovací email pro ověření funkčnosti alertů v OSINT nástroji.</li>
                    </ul>
                </div>
                <div class="email-sent-note">
                    <p>Poznámka: Pokud email nedorazí do vaší schránky, zkontrolujte složku spam nebo se obraťte na správce systému.</p>
                </div>
            </div>
            <div class="email-sent-footer">
                <button type="button" class="btn-primary email-sent-ok">OK</button>
            </div>
        </div>
    `;

    // Přidání dialogu do stránky
    document.body.appendChild(dialog);

    // Event listener pro zavření dialogu
    const closeButton = dialog.querySelector('.email-sent-close');
    if (closeButton) {
        closeButton.addEventListener('click', function() {
            dialog.remove();
        });
    }

    // Event listener pro tlačítko OK
    const okButton = dialog.querySelector('.email-sent-ok');
    if (okButton) {
        okButton.addEventListener('click', function() {
            dialog.remove();
        });
    }

    // Event listener pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}

/**
 * Validace formátu emailové adresy
 * @param {string} email - Emailová adresa k validaci
 * @returns {boolean} - True pokud je email validní, jinak false
 */
function validateEmailFormat(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * Vyhledání zpráv
 */
function searchNews() {
    console.log('Vyhledání zpráv');

    // Najít aktuální modul aktuálních událostí
    const newsModule = document.querySelector('.module[id^="module-aktualni-udalosti"]');
    if (!newsModule) {
        console.error('Modul aktuálních událostí nebyl nalezen');
        return;
    }

    // Najít kontejner pro výsledky
    const newsContainer = newsModule.querySelector('#news-results');
    if (!newsContainer) {
        console.error('Kontejner pro výsledky nebyl nalezen');
        return;
    }

    // Zobrazení načítání
    newsContainer.innerHTML = `
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Vyhledávání zpráv...</span>
        </div>
    `;

    // Aktualizace filtrů
    updateFiltersFromUI(newsModule);

    // Vyhledání zpráv
    fetchGdeltNews()
        .then(news => {
            displayNews(news);
        })
        .catch(error => {
            console.error('Chyba při vyhledávání zpráv:', error);

            // Pokud se nepodaří vyhledat zprávy z GDELT, zkusíme alternativní zdroj
            fetchAlternativeNews()
                .then(news => {
                    displayNews(news);
                })
                .catch(alternativeError => {
                    console.error('Chyba při vyhledávání alternativních zpráv:', alternativeError);

                    if (newsContainer) {
                        newsContainer.innerHTML = `
                            <div class="error-message">
                                <i class="fas fa-exclamation-circle"></i>
                                <span>Nepodařilo se vyhledat zprávy. Zkuste to prosím znovu.</span>
                            </div>
                        `;
                    }
                });
        });
}

/**
 * Aktualizace filtrů z UI
 * @param {HTMLElement} moduleElement - Element modulu aktuálních událostí
 */
function updateFiltersFromUI(moduleElement) {
    console.log('Aktualizace filtrů z UI');

    // Aktualizace klíčových slov
    const keywordsInput = moduleElement.querySelector('#news-keywords');
    if (keywordsInput) {
        const keywords = keywordsInput.value.trim().split(',').map(k => k.trim()).filter(k => k);
        activeNewsFilters.keywords = keywords;
    }

    // Aktualizace časového období
    const fromDateInput = moduleElement.querySelector('#news-from-date');
    const toDateInput = moduleElement.querySelector('#news-to-date');

    if (fromDateInput && fromDateInput.value) {
        activeNewsFilters.fromDate = fromDateInput.value;
    }

    if (toDateInput && toDateInput.value) {
        activeNewsFilters.toDate = toDateInput.value;
    }

    // Aktualizace zdrojů
    const sourcesSelect = moduleElement.querySelector('#news-sources');
    if (sourcesSelect) {
        const selectedSources = Array.from(sourcesSelect.selectedOptions).map(option => option.value);
        activeNewsFilters.sources = selectedSources;
    }

    // Aktualizace kategorií
    const categoriesSelect = moduleElement.querySelector('#news-categories');
    if (categoriesSelect) {
        const selectedCategories = Array.from(categoriesSelect.selectedOptions).map(option => option.value);
        activeNewsFilters.categories = selectedCategories;
    }

    // Aktualizace jazyků
    const languagesSelect = moduleElement.querySelector('#news-languages');
    if (languagesSelect) {
        const selectedLanguages = Array.from(languagesSelect.selectedOptions).map(option => option.value);
        activeNewsFilters.languages = selectedLanguages;
    }
}

/**
 * Nastavení alertů
 */
function setupAlerts() {
    console.log('Nastavení alertů');

    // Vytvoření dialogu pro nastavení alertů
    const dialog = document.createElement('div');
    dialog.className = 'monitoring-setup-dialog';
    dialog.innerHTML = `
        <div class="monitoring-setup-content">
            <div class="monitoring-setup-header">
                <h3>Nastavení alertů</h3>
                <button type="button" class="monitoring-setup-close">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="monitoring-setup-body">
                <div class="monitoring-setup-section">
                    <h4>E-mail pro notifikace</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <input type="email" id="alert-email" class="form-control" placeholder="Zadejte e-mail" value="${alertSettings.email}">
                        </div>
                        <button type="button" class="btn-inline test-email-alert">
                            <i class="fas fa-paper-plane"></i> TEST
                        </button>
                    </div>
                    <div class="form-hint">
                        Kliknutím na TEST odešlete testovací email pro ověření funkčnosti.
                    </div>
                </div>

                <div class="monitoring-setup-section">
                    <h4>Frekvence notifikací</h4>
                    <div class="form-group">
                        <select id="alert-frequency" class="form-control">
                            <option value="realtime" ${alertSettings.frequency === 'realtime' ? 'selected' : ''}>V reálném čase</option>
                            <option value="hourly" ${alertSettings.frequency === 'hourly' ? 'selected' : ''}>Každou hodinu</option>
                            <option value="daily" ${alertSettings.frequency === 'daily' ? 'selected' : ''}>Denně</option>
                        </select>
                    </div>
                </div>

                <div class="monitoring-setup-section">
                    <h4>Klíčová slova pro alerty</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <input type="text" id="alert-keyword" class="form-control" placeholder="Zadejte klíčové slovo">
                        </div>
                        <button type="button" class="btn-inline add-alert-keyword">
                            <i class="fas fa-plus"></i> Přidat
                        </button>
                    </div>
                    <div id="alert-keywords-container" class="monitoring-items">
                        ${alertSettings.keywords.map(keyword => `
                            <div class="monitoring-item">
                                <span class="monitoring-item-name">${keyword}</span>
                                <button type="button" class="monitoring-item-remove" data-alert-keyword="${keyword}">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="monitoring-setup-section">
                    <h4>Entity pro alerty</h4>
                    <div class="form-row">
                        <div class="form-group">
                            <input type="text" id="alert-entity" class="form-control" placeholder="Zadejte entitu">
                        </div>
                        <button type="button" class="btn-inline add-alert-entity">
                            <i class="fas fa-plus"></i> Přidat
                        </button>
                    </div>
                    <div id="alert-entities-container" class="monitoring-items">
                        ${alertSettings.entities.map(entity => `
                            <div class="monitoring-item">
                                <span class="monitoring-item-name">${entity}</span>
                                <button type="button" class="monitoring-item-remove" data-alert-entity="${entity}">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        `).join('')}
                    </div>
                </div>

                <div class="monitoring-setup-section">
                    <h4>Minimální počet zmínek pro odeslání alertu</h4>
                    <div class="form-group">
                        <input type="number" id="alert-threshold" class="form-control" min="1" max="100" value="${alertSettings.threshold}">
                    </div>
                </div>

                <div class="monitoring-setup-actions">
                    <button type="button" class="btn-inline save-alert-settings">
                        <i class="fas fa-save"></i> Uložit nastavení
                    </button>
                </div>
            </div>
        </div>
    `;

    // Přidání dialogu do stránky
    document.body.appendChild(dialog);

    // Přidání event listenerů pro tlačítka
    dialog.querySelector('.monitoring-setup-close').addEventListener('click', function() {
        dialog.remove();
    });

    // Event listener pro přidání klíčového slova pro alert
    const addAlertKeywordButton = dialog.querySelector('.add-alert-keyword');
    if (addAlertKeywordButton) {
        addAlertKeywordButton.addEventListener('click', function() {
            const keywordInput = dialog.querySelector('#alert-keyword');
            if (keywordInput && keywordInput.value.trim()) {
                const keyword = keywordInput.value.trim();

                // Přidání klíčového slova do alertů
                if (!alertSettings.keywords.includes(keyword)) {
                    alertSettings.keywords.push(keyword);

                    // Aktualizace UI
                    const keywordsContainer = dialog.querySelector('#alert-keywords-container');
                    if (keywordsContainer) {
                        const keywordItem = document.createElement('div');
                        keywordItem.className = 'monitoring-item';
                        keywordItem.innerHTML = `
                            <span class="monitoring-item-name">${keyword}</span>
                            <button type="button" class="monitoring-item-remove" data-alert-keyword="${keyword}">
                                <i class="fas fa-times"></i>
                            </button>
                        `;
                        keywordsContainer.appendChild(keywordItem);

                        // Přidání event listeneru pro tlačítko odstranění
                        keywordItem.querySelector('.monitoring-item-remove').addEventListener('click', function() {
                            const keyword = this.getAttribute('data-alert-keyword');
                            if (keyword) {
                                alertSettings.keywords = alertSettings.keywords.filter(k => k !== keyword);
                                keywordItem.remove();
                            }
                        });
                    }

                    // Vyčištění inputu
                    keywordInput.value = '';
                } else {
                    alert(`Klíčové slovo "${keyword}" je již v alertech.`);
                }
            } else {
                alert('Zadejte klíčové slovo.');
            }
        });
    }

    // Event listener pro přidání entity pro alert
    const addAlertEntityButton = dialog.querySelector('.add-alert-entity');
    if (addAlertEntityButton) {
        addAlertEntityButton.addEventListener('click', function() {
            const entityInput = dialog.querySelector('#alert-entity');
            if (entityInput && entityInput.value.trim()) {
                const entity = entityInput.value.trim();

                // Přidání entity do alertů
                if (!alertSettings.entities.includes(entity)) {
                    alertSettings.entities.push(entity);

                    // Aktualizace UI
                    const entitiesContainer = dialog.querySelector('#alert-entities-container');
                    if (entitiesContainer) {
                        const entityItem = document.createElement('div');
                        entityItem.className = 'monitoring-item';
                        entityItem.innerHTML = `
                            <span class="monitoring-item-name">${entity}</span>
                            <button type="button" class="monitoring-item-remove" data-alert-entity="${entity}">
                                <i class="fas fa-times"></i>
                            </button>
                        `;
                        entitiesContainer.appendChild(entityItem);

                        // Přidání event listeneru pro tlačítko odstranění
                        entityItem.querySelector('.monitoring-item-remove').addEventListener('click', function() {
                            const entity = this.getAttribute('data-alert-entity');
                            if (entity) {
                                alertSettings.entities = alertSettings.entities.filter(e => e !== entity);
                                entityItem.remove();
                            }
                        });
                    }

                    // Vyčištění inputu
                    entityInput.value = '';
                } else {
                    alert(`Entita "${entity}" je již v alertech.`);
                }
            } else {
                alert('Zadejte entitu.');
            }
        });
    }

    // Event listener pro testování emailových alertů
    const testEmailButton = dialog.querySelector('.test-email-alert');
    if (testEmailButton) {
        testEmailButton.addEventListener('click', function() {
            const emailInput = dialog.querySelector('#alert-email');
            if (emailInput && emailInput.value.trim()) {
                // Uložení emailu do nastavení
                alertSettings.email = emailInput.value.trim();

                // Odeslání testovacího emailu
                sendTestEmailAlert(alertSettings.email);
            } else {
                showNotification('Zadejte platnou emailovou adresu pro testování.', 'warning');
            }
        });
    }

    // Event listener pro uložení nastavení alertů
    const saveSettingsButton = dialog.querySelector('.save-alert-settings');
    if (saveSettingsButton) {
        saveSettingsButton.addEventListener('click', function() {
            const emailInput = dialog.querySelector('#alert-email');
            const frequencySelect = dialog.querySelector('#alert-frequency');
            const thresholdInput = dialog.querySelector('#alert-threshold');

            if (emailInput && frequencySelect && thresholdInput) {
                // Aktualizace nastavení alertů
                alertSettings.email = emailInput.value.trim();
                alertSettings.frequency = frequencySelect.value;
                alertSettings.threshold = parseInt(thresholdInput.value) || 3;

                // Informace pro uživatele
                showNotification('Nastavení alertů bylo úspěšně uloženo.', 'success');

                // Zavření dialogu
                dialog.remove();
            }
        });
    }

    // Přidání event listenerů pro tlačítka odstranění
    const removeKeywordButtons = dialog.querySelectorAll('.monitoring-item-remove[data-alert-keyword]');
    removeKeywordButtons.forEach(button => {
        button.addEventListener('click', function() {
            const keyword = this.getAttribute('data-alert-keyword');
            if (keyword) {
                alertSettings.keywords = alertSettings.keywords.filter(k => k !== keyword);
                this.closest('.monitoring-item').remove();
            }
        });
    });

    const removeEntityButtons = dialog.querySelectorAll('.monitoring-item-remove[data-alert-entity]');
    removeEntityButtons.forEach(button => {
        button.addEventListener('click', function() {
            const entity = this.getAttribute('data-alert-entity');
            if (entity) {
                alertSettings.entities = alertSettings.entities.filter(e => e !== entity);
                this.closest('.monitoring-item').remove();
            }
        });
    });

    // Přidání event listeneru pro kliknutí mimo obsah dialogu
    dialog.addEventListener('click', function(event) {
        if (event.target === dialog) {
            dialog.remove();
        }
    });
}

/**
 * Export zpráv
 */
function exportNews() {
    console.log('Export zpráv');

    // Kontrola, zda existují zprávy v cache
    if (!newsCache || Object.keys(newsCache).length === 0) {
        showNotification('Nejsou k dispozici žádné zprávy pro export.', 'warning');
        return;
    }

    try {
        // Vytvoření CSV dat s BOM (Byte Order Mark) pro správné rozpoznání UTF-8
        const BOM = '\uFEFF'; // Přidání BOM pro správné rozpoznání UTF-8 v Excelu
        let csvContent = BOM;

        // Přidání hlavičky s rozšířenými informacemi
        csvContent += 'Titulek;Popis;Zdroj;Web;Datum publikace;Čas vytvoření;URL;Kategorie;Jazyk;Sentiment;Osoby;Organizace;Lokace;Klíčová slova;Exportováno\n';

        // Aktuální datum a čas pro sloupec "Exportováno"
        const exportDate = new Date().toLocaleString('cs-CZ', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        // Přidání zpráv - použití středníku jako oddělovače místo čárky (lepší pro české texty)
        Object.values(newsCache).forEach(news => {
            // Formátování data publikace
            let formattedDate = '';
            let formattedTime = '';

            try {
                if (news.publishedAt) {
                    const pubDate = new Date(news.publishedAt);
                    if (!isNaN(pubDate.getTime())) {
                        formattedDate = pubDate.toLocaleDateString('cs-CZ', {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric'
                        });

                        formattedTime = pubDate.toLocaleTimeString('cs-CZ', {
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit'
                        });
                    } else {
                        formattedDate = news.publishedAt;
                    }
                }
            } catch (e) {
                console.error('Chyba při formátování data:', e);
                formattedDate = news.publishedAt || '';
            }

            // Extrakce domény z URL
            let domain = '';
            try {
                if (news.url) {
                    const urlObj = new URL(news.url);
                    domain = urlObj.hostname;
                }
            } catch (e) {
                console.error('Chyba při extrakci domény:', e);
                domain = '';
            }

            // Extrakce klíčových slov
            const keywords = extractKeywordsFromNews(news).join(', ');

            // Escapování hodnot
            const title = `"${(news.title || '').replace(/"/g, '""')}"`;
            const description = `"${(news.description || '').replace(/"/g, '""')}"`;
            const source = `"${(news.source || '').replace(/"/g, '""')}"`;
            const web = `"${domain}"`;
            const publishedDate = `"${formattedDate}"`;
            const publishedTime = `"${formattedTime}"`;
            const url = `"${(news.url || '').replace(/"/g, '""')}"`;
            const category = `"${(news.category || '').replace(/"/g, '""')}"`;
            const language = `"${(news.language || '').replace(/"/g, '""')}"`;
            const sentiment = `"${(news.sentiment || '').replace(/"/g, '""')}"`;

            // Entity
            const persons = `"${news.entities && news.entities.persons ? news.entities.persons.join(', ').replace(/"/g, '""') : ''}"`;
            const organizations = `"${news.entities && news.entities.organizations ? news.entities.organizations.join(', ').replace(/"/g, '""') : ''}"`;
            const locations = `"${news.entities && news.entities.locations ? news.entities.locations.join(', ').replace(/"/g, '""') : ''}"`;
            const keywordsFormatted = `"${keywords.replace(/"/g, '""')}"`;
            const exportDateFormatted = `"${exportDate}"`;

            // Přidání řádku
            csvContent += `${title};${description};${source};${web};${publishedDate};${publishedTime};${url};${category};${language};${sentiment};${persons};${organizations};${locations};${keywordsFormatted};${exportDateFormatted}\n`;
        });

        // Vytvoření Blob objektu s UTF-8 kódováním
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8' });

        // Vytvoření URL pro Blob
        const url = URL.createObjectURL(blob);

        // Vytvoření odkazu pro stažení
        const link = document.createElement('a');
        link.setAttribute('href', url);
        link.setAttribute('download', `zpravy-export-${formatDate(new Date())}.csv`);
        document.body.appendChild(link);

        // Stažení souboru
        link.click();

        // Odstranění odkazu a uvolnění URL
        setTimeout(() => {
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        }, 100);

        console.log('Export zpráv byl úspěšně dokončen');
        showNotification('Export zpráv do CSV byl úspěšně dokončen.', 'success');
    } catch (error) {
        console.error('Chyba při exportu zpráv:', error);
        showNotification('Při exportu zpráv došlo k chybě. Zkuste to prosím znovu.', 'error');
    }
}