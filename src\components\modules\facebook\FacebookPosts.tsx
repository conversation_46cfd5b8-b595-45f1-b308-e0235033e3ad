import { FacebookModuleData, FacebookPost } from "@/types/facebook";
import { Card, CardContent } from "@/components/ui/card";
import { ThumbsUp, MessageSquare, Share2, Calendar, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import { formatDate } from "@/lib/utils";

interface FacebookPostsProps {
  data: FacebookModuleData | null;
}

export default function FacebookPosts({ data }: FacebookPostsProps) {
  if (!data || !data.posts || data.posts.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="text-muted-foreground mb-2"><PERSON><PERSON><PERSON><PERSON> příspěvky k zobrazení</div>
        <p className="text-sm text-muted-foreground">
          Přidejte příspěvky v režimu úprav.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {data.posts.map((post) => (
        <PostCard key={post.id} post={post} />
      ))}
    </div>
  );
}

function PostCard({ post }: { post: FacebookPost }) {
  return (
    <Card>
      <CardContent className="pt-6">
        {/* Post Header */}
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center text-sm text-muted-foreground">
            <Calendar className="h-4 w-4 mr-1" />
            {typeof post.date === 'string' ? formatDate(post.date) : 'Neuvedeno'}
          </div>

          {post.link && (
            <Button variant="ghost" size="sm" asChild>
              <a href={post.link} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-4 w-4 mr-1" /> Odkaz
              </a>
            </Button>
          )}
        </div>

        {/* Post Content */}
        <div className="mb-4">
          <p className="whitespace-pre-line">{post.content}</p>
        </div>

        {/* Post Image */}
        {post.imageUrl && (
          <div className="relative w-full h-64 rounded-md overflow-hidden mb-4">
            <Image
              src={post.imageUrl}
              alt="Post image"
              fill
              className="object-cover"
              unoptimized
            />
          </div>
        )}

        {/* Post Stats */}
        <div className="flex flex-wrap gap-4 pt-2 border-t text-sm text-muted-foreground">
          <div className="flex items-center">
            <ThumbsUp className="h-4 w-4 mr-1 text-blue-600" />
            {post.likes} To se mi líbí
          </div>
          <div className="flex items-center">
            <MessageSquare className="h-4 w-4 mr-1 text-blue-600" />
            {post.comments} Komentářů
          </div>
          <div className="flex items-center">
            <Share2 className="h-4 w-4 mr-1 text-blue-600" />
            {post.shares} Sdílení
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
